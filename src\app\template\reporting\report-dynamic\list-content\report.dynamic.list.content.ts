import {Component, Inject, Injector, OnInit} from "@angular/core";
import {ComponentBase} from "../../../../component.base";
import {ColumnInfo, OptionTable} from "../../../common-module/table/table.component";
import {CONSTANTS} from "../../../../service/comon/constants";
import {ReportDynacmicService} from "../../../../service/report/ReportDynacmicService";
import {MenuItem} from "primeng/api";
import {FormBuilder} from "@angular/forms";
import { ReportDynamicFormControl } from "../components/report.dynamic.form.component";
import {ParameterInfo} from "../components/tab.report.dynamic.general";
import {ReportService} from "../../../../service/report/ReportService";
import {ComboLazyControl} from "../../../common-module/combobox-lazyload/combobox.lazyload";

@Component({
    selector: "report-dynamic-list-content",
    templateUrl: "./report.dynamic.list.content.html"
})
export class ReportDynamicListContentComponent extends ComponentBase implements OnInit {
    constructor(injector: Injector, private formBuilder: FormBuilder,
                @Inject(ReportDynacmicService) private reportDynamicService: ReportDynacmicService,
                private reportService: ReportService
    ) {
        super(injector);
    }
    columns: Array<ColumnInfo>;
    optionTable: OptionTable;
    dataSet: {
        content: Array<any>,
        total: number
    };
    pageNumber: number;
    pageSize: number;
    sort: string;
    searchInfo: {
        name: string | null,
        status: number | null,
        fromDate: Date|null,
        toDate: Date|null,
    }
    searchInfoDetail: any;
    formSearch: any;
    reportStatus: any;
    selectItems: any;
    items: MenuItem[];
    home: MenuItem
    maxDateFrom: Date|number|string|null = new Date();
    minDateTo: Date|number|string|null = null;
    maxDateTo: Date|number|string|null = new Date();
    objectPermissions = CONSTANTS.PERMISSIONS;
    modeForm: number = CONSTANTS.MODE_VIEW.CREATE;
    idReport: number = null;
    reportDynamicFormControl: ReportDynamicFormControl = new ReportDynamicFormControl();
    dataOrigin = [];
    reportDetail: any;
    paramTypes = CONSTANTS.PARAMETER_TYPE;
    dateTypes = CONSTANTS.DATE_TYPE;
    listParameters: Array<ParameterInfo> = [];
    menuTable: MenuItem[] = [];
    mapTable: {
        [key: string | number]: {
            selectItems: Array<any>,
            columns: ColumnInfo[],
            dataSet: {
                content: Array<any>,
                total: number
            },
            optionTable: OptionTable,
            loadData(page, size, sort, params):void,
            pageNumber: number,
            pageSize: number,
            sort: string,
            params: any,
            dataOrigin: Array<any>
        }
    } ={};
    tables: Array<any> = [];
    activeTable: any;
    defaultTableActive: MenuItem = null;
    isShowModalDetail: boolean = false;

    ngOnInit(): void {
        let me = this;
        this.items = [{ label: this.tranService.translate("global.menu.report"),}
                        ,{ label: this.tranService.translate("global.menu.dynamicreport"), routerLink: ["/reports/report-dynamic"]},
                        { label: this.tranService.translate("permission.RptContent.RptContent")},];
        this.home = { icon: 'pi pi-home', routerLink: '/' };
        this.selectItems = [];
        this.searchInfo = {
            name: null,
            status: null,
            fromDate: null,
            toDate:null,
        }
        this.formSearch = this.formBuilder.group(this.searchInfo);
        this.reportStatus = [
            {
                value: CONSTANTS.REPORT_STATUS.ACTIVE,
                name: this.tranService.translate("report.status.active")
            },
            {
                value: CONSTANTS.REPORT_STATUS.INACTIVE,
                name: this.tranService.translate("report.status.inactive")
            },
        ]
        this.columns = [
            {
                name: this.tranService.translate("report.label.reportName"),
                key: "name",
                size: "70%",
                align: "left",
                isShow: true,
                isSort: false,
                style:{
                    cursor: "pointer",
             color: "var(--mainColorText)"
                },
                funcGetRouting(item) {
                    return ['/reports/report-dynamic/report-content/'+item.id]
                },
            },
        ];
        this.optionTable = {
            hasClearSelected: true,
            hasShowChoose: false,
            hasShowIndex: false,
            hasShowToggleColumn: false,
            paginator: false
        }
        this.pageNumber = 0;
        this.pageSize= 5;
        this.sort = "name,asc";
        this.dataSet ={
            content: [],
            total: 0
        }
        this.getAll();
    }

    getAll(){
        let me = this;
        this.reportDynamicService.getAllReportContent((response)=> {
            me.dataOrigin = response || [];
            me.dataOrigin = me.dataOrigin.sort((a, b) => a.name.toUpperCase().localeCompare(b.name.toUpperCase()) > 0 ? 1 : -1);
            me.search(me.pageNumber, me.pageSize, me.sort, {});
        })
    }

    search(page, limit, sort, params){
        let me = this;
        this.pageNumber = page;
        this.pageSize = limit;

        if(this.optionTable.paginator == true){
            this.dataSet = {
                content: this.dataOrigin.slice(page*limit, page*limit + limit),
                total: this.dataOrigin.length
            }
        }else{
            this.dataSet = {
                content: [...this.dataOrigin],
                total: this.dataOrigin.length
            }
        }
    }

    preventCharacter(event){
        if(event.ctrlKey || event.altKey || event.shiftKey){
            return;
        }
        if(event.keyCode == 8 || event.keyCode == 13 || event.keyCode == 46 || event.keyCode == 37 || event.keyCode == 39){
            return;
        }
        if(event.keyCode < 48 || event.keyCode > 57){
            event.preventDefault();
        }
    }

    getReportDetail(){
        let me = this;
        me.reportService.getDetailReportDynamic(me.idReport, (response)=>{
            me.reportDetail = response;
            setTimeout(function(){
                me.loadPage();
            })
        })
    }

    loadPage(){
        this.items = [{ label: this.tranService.translate("global.menu.report")},
            { label: this.tranService.translate("global.menu.dynamicreport"),routerLink: '/reports/report-dynamic'},
            { label: this.tranService.translate("permission.RptContent.RptContent"),routerLink: '/reports/report-dynamic/report-content'},
            { label: this.reportDetail.name}
        ];
        this.home = { icon: 'pi pi-home', routerLink: '/' };
        this.loadListParams();
        this.loadTables();
    }

    loadListParams(){
        let me = this;
        if(this.reportDetail.filterParams){
            this.listParameters = JSON.parse(this.reportDetail.filterParams);
            this.listParameters.forEach(el => {
                if(el.prType == this.paramTypes.LIST_NUMBER || el.prType == this.paramTypes.LIST_STRING || (el.prType == this.paramTypes.STRING && el.isAutoComplete == true)){
                    el["control"] = new ComboLazyControl();
                }
            })
            this.searchInfoDetail = {};
            this.listParameters.forEach(el => {
                me.searchInfoDetail[el.prKey] = null;
            })
            this.formSearch = this.formBuilder.group(this.searchInfoDetail);
        }
    }

    loadTables(){
        let me = this;
        if(this.reportDetail.reportContents){
            this.reportDetail.reportContents.forEach(el => {
                me.menuTable.push({
                    id: el.id,
                    label: el.tableName,
                    command: ()=>{
                        me.activeTable = el.id;
                    }
                })
                let columnKeys = el.columnQueryResult.split(",");
                let columnDisplays = el.columnDisplay.split(",");
                let columns = [];
                columnKeys.forEach((el, index)=>{
                    columns.push({
                        key: el,
                        name: columnDisplays[index],
                    })
                })
                me.mapTable[el.id] = {
                    selectItems: [],
                    columns: columns.map(function(el): ColumnInfo {
                        let object: ColumnInfo = {
                            key: el.key,
                            name: el.name,
                            align: "left",
                            isShow: true,
                            isSort: false,
                            size: `calc((100% - 100px) / ${columns.length})`,
                        }
                        return object;
                    }),
                    dataSet: {
                        content: [],
                        total: 0
                    },
                    dataOrigin: [],
                    loadData(page, size, sort, params) {
                        me.loadDataTable(el.id, page, size, sort, params);
                    },
                    optionTable: {
                        hasClearSelected: true,
                        action: null,
                        hasShowChoose: false,
                        hasShowIndex: false,
                        hasShowJumpPage: true,
                        hasShowToggleColumn: false,
                        paginator: true
                    },
                    pageNumber: 0,
                    pageSize: 10,
                    params: {},
                    sort: `${columns[0].key},asc`
                }
            })
            this.defaultTableActive = this.menuTable[0];
            this.activeTable = this.defaultTableActive.id;
            this.tables = [...this.reportDetail.reportContents];
        }
    }

    loadDataTable(tableId, page, size, sort, param){
        this.mapTable[tableId].pageNumber = page;
        this.mapTable[tableId].pageSize = size;
        this.mapTable[tableId].sort = sort;
        this.mapTable[tableId].params = param;
        let dataSet = {
            content: this.mapTable[tableId].dataOrigin.slice(page * size, page*size + size),
            total: this.mapTable[tableId].dataOrigin.length
        }
        this.mapTable[tableId].dataSet = dataSet;
    }

    checkIsNumberOrNull(key){
        let me = this;
        if(this.searchInfoDetail[key] == null) return;
        if(isNaN(this.searchInfoDetail[key])){
            setTimeout(function(){
                me.searchInfoDetail[key] = null;
            })
        }
    }

    protected readonly CONSTANTS = CONSTANTS;
}
