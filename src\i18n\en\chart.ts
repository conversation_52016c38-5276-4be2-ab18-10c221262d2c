export default {
    titlePage: "Charts",
    label: {
        generalInfo: "General Information",
        chartConfig: "Chart Config",
        dataConfig: "Data Config",
        filterConfig: "Filter Config",
        chartName: "Chart Name",
        chartType: "Chart Type",
        chartSubType: "Chart Type Extra",
        query: "Query",
        typeQuery: "Type Query",
        keyLabel: "Key For Label",
        keyValue: "Key For Value",
        keyDataset: "Key For Dataset",
        datasetColor: "Dataset Color",
        datasetName: "Dataset Name",
        threshold: "Threshold",
        thresholdConfig: "Config Threshold",
        width: "Width",
        height: "Height",
        marginLeft: "Margin Left",
        marginRight: "Margin Right",
        align: "Align",
        apply: "Apply",
        sizing: "Config Size",
        left: "Left",
        right: "Right",
        center: "Center",
        justify: "Justify",
        description: "Description",
        thresholdConfigSlider: "Config Threshold Slider",
        keyMaxValue: "Key for Max Value",
    },
    type: {
        bar: "Bar Chart",
        bubble: "Bubble Chart",
        combo: "Combo Chart",
        doughnut: "Doughnut Chart",
        line: "Line Chart",
        pie: "Pie Chart",
        polar: "Polar Chart",
        radar: "Radar Chart",
        scatter: "Scatter Chart",
    },
    subType: {
        horizontalBar: "Horizontal Bar Chart",
        verticalBar: "Vertical Bar Chart",
        stackedBar: "Stacked Bar Chart",
        groupBar: "Group Bar Chart",
        multiAxis: "Multi Axis Chart",
        threshold: "Threshold Config Chart",
        sliderThreshold: "Config Slider Threshold",
    }
}
