<style>
    /* .col-3{
        padding: 10px;
    } */
</style>

<div class="vnpt flex flex-row justify-content-between align-items-center bg-white p-2 border-round">
    <div class="">
        <div class="text-xl font-bold mb-1">{{this.tranService.translate("global.menu.listroles")}}</div>
        <p-breadcrumb class="max-w-full col-7" [model]="items" [home]="home"></p-breadcrumb>
    </div>
    <div class="col-5 flex flex-row justify-content-end align-items-center">
        <p-button styleClass="p-button-info" [label]="tranService.translate('global.button.create')" icon="pi pi-plus"
                  *ngIf="checkAuthen([CONSTANTS.PERMISSIONS.ROLE.CREATE])"
                  [routerLink]="['/roles/create']" routerLinkActive="router-link-active"></p-button>
    </div>
</div>

<form [formGroup]="formSearchRoles" (ngSubmit)="onSubmitSearch()" class="pb-2 pt-3 vnpt-field-set">
    <p-panel [toggleable]="true" [header]="tranService.translate('global.text.filter')">
        <div class="grid">
            <!-- loai nhom quyen -->
            <div class="col-3">
                <span class="p-float-label">
                    <p-dropdown styleClass="w-full" [showClear]="true"
                                id="type" [autoDisplayFirst]="false"
                                [(ngModel)]="searchInfo.type"
                                formControlName="type"
                                [options]="roleType"
                                optionLabel="name"
                                optionValue="value"
                    ></p-dropdown>
                    <label for="type">{{tranService.translate("roles.label.usertype")}}</label>
                </span>
            </div>
            <!-- ten nhom quyen -->
            <div class="col-3">
                <span class="p-float-label">
                    <input pInputText
                            class="w-full"
                            pInputText id="name"
                            [(ngModel)]="searchInfo.name"
                            formControlName="name"
                    />
                    <label htmlFor="name">{{tranService.translate("roles.label.rolename")}}</label>
                </span>
            </div>
            <div class="col-3 pb-0">
                <p-button icon="pi pi-search"
                            styleClass="p-button-rounded p-button-secondary p-button-text button-search"
                            type="submit"
                ></p-button>
            </div>
        </div>
    </p-panel>
</form>

<table-vnpt
    [fieldId]="'id'"
    [(selectItems)]="selectItems"
    [columns]="columns"
    [dataSet]="dataSet"
    [options]="optionTable"
    [loadData]="search.bind(this)"
    [pageNumber]="pageNumber"
    [pageSize]="pageSize"
    [sort]="sort"
    [params]="searchInfo"
    [labelTable]="this.tranService.translate('global.menu.listroles')"
></table-vnpt>

<div class="flex justify-content-center dialog-vnpt">
    <p-dialog
        [header]="tranService.translate('global.menu.detailroles')"
        [(visible)]="isShowModalDetail"
        [modal]="true"
        [style]="{ width: '980px' }"
        [draggable]="false"
        [resizable]="false"
    >
        <div>
            <div class="flex flex-row justify-content-between">
                <div style="width: 49%;">
                    <!-- username -->
                    <div class="mt-1 ml-5 grid">
                        <span style="min-width: 200px;max-width: 200px;" class="inline-block col-fixed">{{tranService.translate("roles.label.rolename")}}</span>
                        <span class="col">{{roleInfo.name}}</span>
                    </div>
                    <!-- loai tai khoan -->
                    <div class="mt-1 ml-5 grid">
                        <span style="min-width: 200px;max-width: 200px;" class="inline-block col-fixed">{{tranService.translate("roles.label.usertype")}}</span>
                        <span class="col" >{{getType(roleInfo.type)}}</span>
                    </div>
                    <!-- trang thai -->
                    <div class="mt-1 ml-5 grid">
                        <span style="min-width: 200px;max-width: 200px;" class="inline-block col-fixed">{{tranService.translate("roles.label.status")}}</span>
                        <span class="col" >{{getStatus(roleInfo.status)}}</span>
                    </div>
                </div>
                <div style="width: 51%;">
                    <label for="roles" class=" col-fixed inline-block mt-1 " style="width:180px;">{{tranService.translate("roles.label.rolelist")}}<span class="text-red-500">*</span></label>
                    <div class="col">
                        <p-tree
                            disabled="true"
                            id="roles"
                            [value]="dataSetForDetail.content"
                            selectionMode="checkbox"
                            class="w-full md:w-30rem"
                            [(selection)]="roleInfo.roles"
                            [style]="{'max-height':'500px', 'overflow-y':'scroll'}"
                        ></p-tree>
                    </div>
                </div>
            </div>
<!--            <div class="flex flex-row justify-content-center align-items-center mt-6 mb-3">-->
<!--                <p-button [label]="tranService.translate('global.button.cancel')" styleClass="p-button-secondary p-button-outlined mr-2" (click)="closeForm()"></p-button>-->
<!--            </div>-->
        </div>
    </p-dialog>
</div>
