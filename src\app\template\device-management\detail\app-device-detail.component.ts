import {<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Inject, Injector, OnInit, Renderer2} from "@angular/core";
import {MenuItem} from "primeng/api";
import {ActivatedRoute} from "@angular/router";
import {DeviceService} from "../../../service/device/device-service.service";
import {FormBuilder} from "@angular/forms";
import {<PERSON><PERSON>ani<PERSON>zer, SafeResourceUrl} from '@angular/platform-browser';
import {CONSTANTS} from "../../../service/comon/constants";
import {ComponentBase} from "../../../component.base";
import {ColumnInfo, OptionTable} from "../../common-module/table/table.component";
import {ComboLazyControl} from "../../common-module/combobox-lazyload/combobox.lazyload";
import {DeviceTypeService} from "../../../service/device/device-type.service";
import {AccountService} from "../../../service/account/AccountService";
import {CommandService} from "../../../service/device/command.service";
import {AlertService} from "../../../service/alert/AlertService";
declare let L: any;
@Component({
    selector: "app-device-detail",
    templateUrl: './app-device-detail.component.html',
})
export class AppDeviceDetailComponent extends ComponentBase implements OnInit {
    safeUrl: SafeResourceUrl;
    items: MenuItem[];
    id = this.activeroute.snapshot.paramMap.get("id");
    home: MenuItem;
    deviceInfo: {
        deviceName: string | null,
        deviceTypeId: number | null,
        userEnterpriseId: number | null,
        userCustomerId: number | null,
        imei: string | null,
        serialNumber: string | null,
        msisdn: string | null,
        description: string | null,
        manufacture: string | null,
        deviceTypeName: string | null,
        modelCode: string | null,
        fullAddress: string | null,
        connectionStatus: number | null,
        lastStatusUpdatedAt: string | number,
        latitude: string | null,
        longitude: string | null,
        currentVolume: number | null,
        estimatedCost: number | null,
    }
    business: {
        name: string | null,
        taxCode: string | null,
        addressHeadOffice: string | null,
        representativeName: string | null,
        phone: string | null,
        email: string | null,
        addressContact: string | null,
    }
    individual: {
        name: string | null,
        phone: string | null,
        email: string | null,
        addressContact: string | null,
    }
    deviceType: {
        id: number | null,
        typeCode: string | null,
        typeName: string | null,
        modelCode: string | null,
        modelDescription: string | null,
        telemetryConfigSchema: string | null,
    }

    searchCmdInfo: {
        createdAtStart: Date | null,
        createdAtEnd: Date | null,
        commandId: string | null,
        commandName: string | null,
        commandType: string | null,
        data: string | null,
        status: string | null,
    };
    formSearch: any;
    //table cmd
    columnsCmd: Array<ColumnInfo>;
    dataSetCmd: {
        content: Array<any>,
        total: number
    };
    selectItemsCmd: Array<{ id: number, [key: string]: any }>;
    optionTableCmd: OptionTable;
    pageNumberCmd: number;
    pageSizeCmd: number;
    sortCmd: string;

    //table telemetry
    columnsTelemetry: Array<ColumnInfo>;
    dataSetTelemetry: {
        content: Array<any>,
        total: number
    };
    selectItemsTelemetry: Array<{ id: number, [key: string]: any }>;
    optionTableTelemetry: OptionTable;
    pageNumberTelemetry: number;
    pageSizeTelemetry: number;
    sortTelemetry: string;

    //table history alert
    columnsHistoryAlert: Array<ColumnInfo>;
    dataSetHistoryAlert: {
        content: Array<any>,
        total: number
    };
    selectItemsHistoryAlert: Array<{ id: number, [key: string]: any }>;
    optionTableHistoryAlert: OptionTable;
    pageNumberHistoryAlert: number;
    pageSizeHistoryAlert: number;
    sortHistoryAlert: string;

    activeTabIndex = 0;
    findCellIDDto: any;

    listStatus: Array<any>;
    listStatusCmd: Array<any>;
    listType: Array<any>;

    maxDateFrom: Date | number | string | null = null;
    minDateTo: Date | number | string | null = null;
    maxDateTo: Date | number | string | null = null;

    defaultLat = "21.046303";
    defaultLng = "105.791648";
    defaultAddress = '124 Hoàng Quốc Việt, Cầu Giấy, Hà Nội';

    address: string = 'Chưa chọn';
    lat: number | string | null
    lng: number | string | null
    map: any;
    marker: any;
    eventOptions: Array<any>;
    constructor(@Inject(DeviceService) private deviceService: DeviceService,
                @Inject(DeviceTypeService) private deviceTypeService: DeviceTypeService,
                @Inject(AccountService) private accountService: AccountService,
                @Inject(CommandService) private commandService: CommandService,
                @Inject(AlertService) private alertService: AlertService,
                private activeroute: ActivatedRoute,
                private sanitizer: DomSanitizer,
                private renderer: Renderer2,
                private el: ElementRef,
                private formBuilder: FormBuilder,
                injector: Injector,
    ) {
        super(injector)
    }

    ngOnInit(): void {
        let me = this;
        this.home = {icon: 'pi pi-home', routerLink: '/'};
        this.items = [{label: this.tranService.translate("global.menu.devicemgmt"), routerLink: "/devices"},
            {label: this.tranService.translate("global.menu.listdevice"), routerLink: "/devices"},
            {label: this.tranService.translate("global.menu.devicedetail")}];
        this.findCellIDDto = null
        // 124 Hoàng Quốc Việt
        const url = `https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3723.645764495224!2d105.78919517584175!3d21.046855287155687!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x3135ab303bc5f991%3A0x938485f81ec15900!2zMTI0IEhvw6BuZyBRdeG7kWMgVmnhu4d0LCBD4buVIE5odeG6vywgQ-G6p3UgR2nhuqV5LCBIw6AgTuG7mWkgMTAwMDAsIFZp4buHdCBOYW0!5e0!3m2!1svi!2s!4v1749690944689!5m2!1svi!2s`
        //Hà Nội
        // const url = `https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d42135.60828498614!2d105.78743105312334!3d21.020807357074563!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x3135ab9bd9861ca1%3A0xe7887f7b72ca17a9!2sHanoi%2C%20Vietnam!5e0!3m2!1sen!2s!4v1713255802111!5m2!1sen!2s`;
        this.safeUrl = this.sanitizer.bypassSecurityTrustResourceUrl(url);
        this.deviceInfo = {
            deviceName: null,
            deviceTypeId: null,
            imei: null,
            serialNumber: null,
            msisdn: null,
            description: null,
            manufacture: null,
            deviceTypeName: null,
            modelCode: null,
            fullAddress: null,
            connectionStatus: null,
            lastStatusUpdatedAt: null,
            userEnterpriseId: null,
            userCustomerId: null,
            latitude: null,
            longitude: null,
            currentVolume: null,
            estimatedCost: null,
        }
        this.business = {
            name: null,
            taxCode: null,
            addressHeadOffice: null,
            representativeName: null,
            phone: null,
            email: null,
            addressContact: null,
        }
        this.individual = {
            name: null,
            phone: null,
            email: null,
            addressContact: null,
        }
        this.searchCmdInfo = {
            createdAtStart: null,
            createdAtEnd: null,
            commandId: null,
            commandName: null,
            commandType: null,
            data: null,
            status: null,
        };
        this.deviceType = {
            id: null,
            typeCode: null,
            typeName: null,
            modelCode: null,
            modelDescription: null,
            telemetryConfigSchema: null
        }
        this.formSearch = this.formBuilder.group(this.searchCmdInfo);
        this.listStatusCmd = [
            {
                name: this.tranService.translate("device.statusCmd.archived"),
                value: CONSTANTS.DEVICE_STATUS_CMD.STORED
            },
            {
                name: this.tranService.translate("device.statusCmd.submitted"),
                value: CONSTANTS.DEVICE_STATUS_CMD.SENT
            },
            {
                name: this.tranService.translate("device.statusCmd.accepted"),
                value: CONSTANTS.DEVICE_STATUS_CMD.ACCEPTED
            },
            {
                name: this.tranService.translate("device.statusCmd.rejected"),
                value: CONSTANTS.DEVICE_STATUS_CMD.REJECT
            },
            {
                name: this.tranService.translate("device.statusCmd.conflict"),
                value: CONSTANTS.DEVICE_STATUS_CMD.CONFLICT
            },
            {
                name: this.tranService.translate("device.statusCmd.processed"),
                value: CONSTANTS.DEVICE_STATUS_CMD.PROCESSED
            },
        ]
        this.listStatus = [
            {name: this.tranService.translate("device.status.registered"), value: CONSTANTS.DEVICE_STATUS.REGISTERED},
            {name: this.tranService.translate("device.status.connected"), value: CONSTANTS.DEVICE_STATUS.CONNECTED},
            {
                name: this.tranService.translate("device.status.lostConnection"),
                value: CONSTANTS.DEVICE_STATUS.LOST_CONNECTION
            }
        ]

        this.pageNumberCmd = 0;
        this.pageSizeCmd = 10;
        this.sortCmd = "createdAt,desc";
        this.selectItemsCmd = [];
        this.optionTableCmd = {
            hasClearSelected: true,
            hasShowChoose: false,
            hasShowIndex: true,
            hasShowToggleColumn: true,
            action: null,
        }
        this.dataSetCmd = {
            content: [],
            total: 0,
        }
        this.columnsCmd = [
            {
                name: this.tranService.translate('device.label.timeSent'),
                key: "createdAt",
                size: "150px",
                align: "left",
                isShow: true,
                isSort: false,
                funcConvertText(value, item) {
                    return me.utilService.formatDateTime(value)
                }
            },
            {
                name: this.tranService.translate("device.label.cmdCode"),
                key: "commandId",
                size: "150px",
                align: "left",
                isShow: false,
                isSort: false,
            },
            {
                name: this.tranService.translate("device.label.cmdName"),
                key: "commandName",
                size: "150px",
                align: "left",
                isShow: true,
                isSort: false,
            },
            {
                name: this.tranService.translate("device.label.cmdType"),
                key: "commandType",
                size: "100px",
                align: "left",
                isShow: true,
                isSort: false,
            },
            {
                name: this.tranService.translate("device.label.data"),
                key: "data",
                size: "350px",
                align: "left",
                isShow: false,
                isSort: false,
                isShowTooltip: true,
                style: {
                    display: 'inline-block',
                    // maxWidth: '770px',
                    overflow: 'auto',
                }
            },
            {
                name: this.tranService.translate("device.label.status"),
                key: "status",
                size: "100px",
                align: "left",
                isShow: true,
                isSort: false,
                funcConvertText(value, item) {
                    return me.getNameStatusCmd(value)
                },
                funcGetClassname(value) {
                    return me.getClassStatusCmd(value)
                }
            },

        ]


        //Table telemetry
        this.pageNumberTelemetry = 0;
        this.pageSizeTelemetry = 10;
        this.sortTelemetry = "";
        this.selectItemsTelemetry = [];
        this.optionTableTelemetry = {
            hasClearSelected: true,
            hasShowChoose: false,
            hasShowIndex: true,
            hasShowToggleColumn: true,
            action: null,
        }
        this.dataSetTelemetry = {
            content: [],
            total: 0,
        }
        this.columnsTelemetry = []

        this.pageNumberHistoryAlert = 0;
        this.pageSizeHistoryAlert = 10;
        this.sortHistoryAlert = "";
        this.selectItemsHistoryAlert = [];
        this.optionTableHistoryAlert = {
            hasClearSelected: true,
            hasShowChoose: false,
            hasShowIndex: true,
            hasShowToggleColumn: true,
            action: null,
        }
        this.dataSetHistoryAlert = {
            content: [],
            total: 0,
        }
        this.eventOptions = [
            {
                name: this.tranService.translate("alert.eventType.exceededValue"),
                value: CONSTANTS.ALERT_EVENT_TYPE.EXCEEDED_VALUE
            },
        ];
        this.columnsHistoryAlert = [
            {
                name: this.tranService.translate("device.label.alertName"),
                key: "alertName",
                size: "150px",
                align: "left",
                isShow: true,
                isSort: false,
            },
            {
                name: this.tranService.translate("device.label.alertType"),
                key: "eventType",
                size: "150px",
                align: "left",
                isShow: true,
                isSort: false,
                funcConvertText(value, item) {
                    const type = me.eventOptions.find(item => item.value === value)
                    return type ? type.name : "";
                }
            },
            {
                name: this.tranService.translate("device.label.alertEmail"),
                key: "alertEmails",
                size: "250px",
                align: "left",
                isShow: true,
                isSort: false,
                funcConvertText(value, item) {
                    return (value || "").replace(/,/g, '<br>')
                },
                isSanitizedContent: true,
            },
            {
                name: this.tranService.translate("device.label.alertContent"),
                key: "emailContent",
                size: "250px",
                align: "left",
                isShow: true,
                isSort: false,
                isShowTooltip: true,
                style: {
                    display: 'inline-block',
                    maxWidth: '350px',
                    overflow: 'hidden',
                    textOverflow: 'ellipsis'
                },
                isSanitizedContent: true,
            },
            {
                name: this.tranService.translate("device.label.alertTime"),
                key: "raisedDate",
                size: "150px",
                align: "left",
                isShow: true,
                isSort: false,
            },
            // {
            //     name: this.tranService.translate("device.label.alertStatus"),
            //     key: "alertStatus",
            //     size: "150px",
            //     align: "left",
            //     isShow: true,
            //     isSort: false,
            // },
        ]

        this.lat = this.defaultLat;
        this.lng = this.defaultLng;
        this.address = this.defaultAddress;

        this.getDetailDevice();
    }

    getDeviceType(id) {
        let me = this;
        me.messageCommonService.onload()
        this.deviceTypeService.getDeviceType(id, (response) => {
            me.deviceType = {
                ...response
            }
            let config = JSON.parse(me.deviceType.telemetryConfigSchema);
            me.columnsTelemetry = [];
            if (Array.isArray(config)) {
                config.forEach(function (c) {
                    let col = {
                        name: me.utilService.getLabel(c),
                        key: me.utilService.getKey(c.key),
                        size: c.size,
                        align: "left",
                        isShow: c.isShow,
                        isSort: false,
                        funcConvertText(value, item) {
                            if (typeof value === "object") {
                                if (value !== null) {
                                    return JSON.stringify(value);
                                }
                                return ""
                            } else {
                                if (value == true) return me.tranService.translate('global.button.yes')
                                if (value == false) return me.tranService.translate('global.button.no')
                            }
                            return value;
                        }
                    }
                    me.columnsTelemetry.push(<ColumnInfo>col);
                })
            }
            me.messageCommonService.offload();
        }, null, () => {
            me.messageCommonService.offload();
        })
    }

    goUpdate() {
        this.router.navigate([`/devices/edit/${this.id}`]);
    }

    getDetailDevice() {
        let me = this;
        me.messageCommonService.onload()
        this.deviceService.detailDevice(Number(this.id), (response) => {
            me.deviceInfo = {
                ...response
            }
            // if (me.deviceInfo.connectionStatus == null){}
                // console.log(me.deviceInfo.deviceTypeId)
            if (me.deviceInfo.deviceTypeId) {
                // console.log(me.deviceInfo.deviceTypeId);
                me.getDeviceType(me.deviceInfo.deviceTypeId);
            }
            if (me.deviceInfo.userEnterpriseId) {
                me.getBusiness(me.deviceInfo.userEnterpriseId)
            }
            if (me.deviceInfo.userCustomerId) {
                me.getIndividual(me.deviceInfo.userCustomerId)
            }
            if (me.deviceInfo.latitude != null && me.deviceInfo.longitude != null && me.deviceInfo.fullAddress != null) {
                me.lat = me.deviceInfo.latitude
                me.lng = me.deviceInfo.longitude
                me.address = me.deviceInfo.fullAddress;
            }
            // me.getDetailUsageDevice();
        }, null, () => {
            me.messageCommonService.offload();
        })
    }

    getBusiness(id) {
        let me = this;
        me.messageCommonService.onload();
        me.accountService.getById(id, (response) => {
            me.business = {
                ...response
            }
        }, null, () => {
            me.messageCommonService.offload();
        })
    }

    getIndividual(id) {
        let me = this;
        me.messageCommonService.onload();
        me.accountService.getById(id, (response) => {
            me.individual = {
                ...response
            }
        }, null, () => {
            me.messageCommonService.offload();
        })
    }


    findCellId(cell_lac) {
        let me = this;
        this.deviceService.findCellId({loc: cell_lac.split(":")[1], cell: cell_lac.split(":")[0]}, (response) => {
            if (response && response.status == "ok") {
                me.findCellIDDto = {
                    lat: response.lat,
                    lng: response.lng
                }
                me.findAddress(response.lat, response.lng)
            }
        }, null, null)
    }

    findAddress(lat, lon) {
        let me = this;
        this.deviceService.findAddress(lat, lon, (response) => {
            me.deviceInfo.fullAddress = response.display_name
            const url = `https://www.google.com/maps?q=${me.findCellIDDto.lat},${me.findCellIDDto.lng}&output=embed`;
            me.safeUrl = me.sanitizer.bypassSecurityTrustResourceUrl(url);
        }, null, null)
    }

    getClassStatus(value) {
        if (value == CONSTANTS.DEVICE_STATUS.REGISTERED) {
            return ['p-2', 'text-cyan-800', "bg-cyan-100", "border-round", "inline-block"];
        } else if (value == CONSTANTS.DEVICE_STATUS.CONNECTED) {
            return ['p-2', "text-green-800", "bg-green-100", "border-round", "inline-block"];
        } else if (value == CONSTANTS.DEVICE_STATUS.LOST_CONNECTION) {
            return ['p-2', 'text-red-700', "bg-red-100", "border-round", "inline-block"];
        }
        return [];
    }

    getNameStatus(value) {
        let me = this;
        const status = me.listStatus.find(item => item.value === value);
        return status ? status.name : 'N/A';
    }

    getNameStatusCmd(value) {
        let me = this;
        const status = me.listStatusCmd.find(item => item.value === value);
        return status ? status.name : '';
    }

    getClassStatusCmd(value) {
        if (value == CONSTANTS.DEVICE_STATUS_CMD.STORED) {
            return ['p-2', 'text-orange-700', "bg-orange-100", "border-round","inline-block"];
        } else if (value == CONSTANTS.DEVICE_STATUS_CMD.SENT) {
            return ['p-2', 'text-indigo-600', "bg-indigo-100", "border-round", "inline-block"];
        } else if (value == CONSTANTS.DEVICE_STATUS_CMD.PROCESSED) {
            return ['p-2', "text-green-800", "bg-green-100", "border-round", "inline-block"];
        } else if (value == CONSTANTS.DEVICE_STATUS_CMD.REJECT) {
            return ['p-2', 'text-red-700', "bg-red-100", "border-round", "inline-block"];
        } else if (value == CONSTANTS.DEVICE_STATUS_CMD.CONFLICT) {
            return ['p-2', 'text-yellow-800', "bg-yellow-100", "border-round","inline-block"];
        } else if (value == CONSTANTS.DEVICE_STATUS_CMD.ACCEPTED) {
            return ['p-2', 'text-cyan-800', "bg-cyan-100", "border-round", "inline-block"];
        }
        return ['p-1', "border-round", "border-400", "text-color", "inline-block"];
    }

    onTabChange(event) {
        let me = this;
        if (me.activeTabIndex == 2 && me.columnsTelemetry.length > 0) {
            me.getTelemetry()
        } else if (me.activeTabIndex == 1) {
            me.onSubmitSearchCmd();
        } else if (me.activeTabIndex == 4) {
            me.initMap();
        } else if (me.activeTabIndex == 3) {
            me.searchHistoryAlert(me.pageNumberHistoryAlert, me.pageSizeHistoryAlert, me.sortHistoryAlert, {})
        }
    }

    getTelemetry(page?, size?, sort?, data?) {
        let me = this;

        let dataBody = {
            deviceId: Number(me.id),
            page: page || me.pageNumberTelemetry,
            size: size || me.pageSizeTelemetry,
            sort: sort || me.sortTelemetry,
        }
        console.log(dataBody);
        me.messageCommonService.onload();
        this.deviceService.getTelemetry(dataBody, (response) => {
            me.dataSetTelemetry.total = response.totalElements;
            if (me.dataSetTelemetry.total > 0) {
                me.dataSetTelemetry.content = [];
                response.content.forEach(function (c) {
                    let telemetry;
                    telemetry = JSON.parse(c.telemetryData);
                    let telemetryData = {...telemetry};
                    const listKey = CONSTANTS.ALERT.LIST_KEY_TELEMETRY.split(',');
                    listKey.forEach((key) => {
                        if (telemetry[key]) {
                            Object.keys(telemetry[key]).forEach(keyName => {
                                telemetryData[keyName] = telemetry[key][keyName];
                            })
                        }
                    })
                    me.dataSetTelemetry.content.push(telemetryData);

                })
            } else {
                me.dataSetTelemetry = {
                    content: [],
                    total: 0,
                }
            }
        }, null, () => {
            me.messageCommonService.offload();
        })
    }


    onSubmitSearchCmd() {
        let me = this;
        me.pageNumberCmd = 0;
        me.searchCmd(0, this.pageSizeCmd, this.sortCmd, this.searchCmdInfo);
    }

    searchCmd(page, limit, sort, params) {
        this.pageNumberCmd = page;
        this.pageSizeCmd = limit;
        this.sortCmd = sort;
        let me = this;
        let dataParams = {
            page,
            size: limit,
            sort
        }
        Object.keys(this.searchCmdInfo).forEach(key => {
            if (this.searchCmdInfo[key] != null) {
                if (key == "createdAtStart") {
                    dataParams["createdAtStart"] = me.searchCmdInfo.createdAtStart.getTime();
                } else if (key == "createdAtEnd") {
                    dataParams["createdAtEnd"] = me.searchCmdInfo.createdAtEnd.getTime()
                } else {
                    dataParams[key] = this.searchCmdInfo[key];
                }
            }
        })

        me.messageCommonService.onload();
        this.commandService.search(Number(me.id),dataParams, (response) => {
            me.dataSetCmd = {
                content: response.content,
                total: response.totalElements
            }
        }, null, () => {
            me.messageCommonService.offload();
        })
    }

    toDate(value: number) {
        if (value == null || value == 0) return null;
        return new Date(value);
    }

    onChangeDateFrom(value) {
        if (value) {
            this.minDateTo = value;
        } else {
            this.minDateTo = null
        }
    }

    onChangeDateTo(value) {
        if (value) {
            this.maxDateFrom = value;
        } else {
            this.maxDateFrom = new Date();
        }
    }
    initMap(): void {
        this.map = L.map('osm-map').setView([this.lat, this.lng], 13);

        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
            attribution: '&copy; OpenStreetMap contributors'
        }).addTo(this.map);

        // Gắn marker mặc định với tooltip
        this.marker = L.marker([this.lat, this.lng])
            .addTo(this.map)
            .bindTooltip(this.address, {permanent: true, direction: 'top'})
            .openTooltip();

        // this.map.on('click', async (e: any) => {
        //     const lat = e.latlng.lat;
        //     const lng = e.latlng.lng;
        //     const address = await this.reverseGeocode(lat, lng);
        //     this.setMarker(lat, lng, address);
        // });
    }

    async search(event: KeyboardEvent) {
        let me = this;
        event.preventDefault();
        let query = (event.target as HTMLInputElement).value;
        const res = await fetch(`https://nominatim.openstreetmap.org/search?format=json&q=${encodeURIComponent(query)}`);
        const results = await res.json();
        if (results.length > 0) {
            const r = results[0];
            const lat = parseFloat(r.lat);
            const lng = parseFloat(r.lon);
            this.setMarker(lat, lng, r.display_name);
            me.deviceInfo.fullAddress = r.display_name;
            me.deviceInfo.latitude = r.lat;
            me.deviceInfo.longitude = r.lon;
            this.map.setView([lat, lng], 15);
        } else {
            me.messageCommonService.warning(me.tranService.translate('device.text.notFoundLocation'))
        }
    }

    async reverseGeocode(lat: number, lng: number): Promise<string> {
        const res = await fetch(`https://nominatim.openstreetmap.org/reverse?format=json&lat=${lat}&lon=${lng}`);
        const data = await res.json();
        return data.display_name || 'Không tìm thấy địa chỉ';
    }

    setMarker(lat: number, lng: number, address: string) {
        this.lat = lat;
        this.lng = lng;
        this.address = address;

        if (this.marker) this.marker.remove();

        this.marker = L.marker([lat, lng])
            .addTo(this.map)
            .bindTooltip(address, {permanent: true, direction: 'top'})
            .openTooltip();
    }
    searchHistoryAlert(page, limit, sort, params) {
        let me = this;
        this.pageNumberHistoryAlert = page;
        this.pageSizeHistoryAlert = limit;
        this.sortHistoryAlert = sort;
        let dataParams = {
            page,
            size: limit,
            sort
        }
        dataParams['deviceId'] = me.id;
        me.messageCommonService.onload();
        this.alertService.getListAlertHistory(dataParams, (response) => {
            me.dataSetHistoryAlert = {
                content: response.content,
                total: response.totalElements
            }
        }, null, () => {
            me.messageCommonService.offload();
        })
    }
    getDetailUsageDevice() {
        let me = this;
        let list : number[] = []
        list.push(Number(me.id));
        me.messageCommonService.onload();
        me.deviceService.getUsage(list, resp => {
                for(let el of resp) {
                    if(me.id == el.id) {
                        me.deviceInfo.currentVolume = el.usage?.toLocaleString("vi-VN")
                        me.deviceInfo.estimatedCost = el.cost?.toLocaleString("vi-VN")
                    }
                }

        }, null, () => {
            me.messageCommonService.offload();
        })
    }
    protected readonly CONSTANTS = CONSTANTS;
    protected readonly Number = Number;
}
