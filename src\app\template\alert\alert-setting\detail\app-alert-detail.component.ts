import {AfterContentChecked, Component, Inject, Injector, OnInit} from '@angular/core';
import {AccountService} from "../../../../service/account/AccountService";
import {FormBuilder, FormControl} from "@angular/forms";
import {MenuItem} from "primeng/api";
import {CONSTANTS} from "../../../../service/comon/constants";
import {ComponentBase} from "../../../../component.base";
import {AlertService} from "../../../../service/alert/AlertService";
import {ComboLazyControl} from 'src/app/template/common-module/combobox-lazyload/combobox.lazyload';
import {DeviceTypeService} from "../../../../service/device/device-type.service";
import {DeviceModelService} from "../../../../service/device/device-model.service";
import {ru} from "suneditor/src/lang";
import {DeviceService} from "../../../../service/device/device-service.service";
import {isNumber} from "chart.js/helpers";


@Component({
    selector: 'app-alert-detail',
    templateUrl: './app-alert-detail.component.html',
})
export class AppAlertDetailComponent extends ComponentBase implements OnInit, AfterContentChecked {
    constructor(
        @Inject(AccountService) private accountService: AccountService,
        @Inject(DeviceTypeService) private deviceTypeService: DeviceTypeService,
        @Inject(DeviceModelService) private deviceModelService: DeviceModelService,
        @Inject(DeviceService) private deviceService: DeviceService,
        private formBuilder: FormBuilder,
        @Inject(AlertService) private alertService: AlertService,
        private injector: Injector) {
        super(injector);
    }

    items: MenuItem[];
    home: MenuItem;
    formAlert: any;
    alertInfo: {
        name: string | null,
        description: string | null,
        severity: string | null,
        emailList: string | null,
        emailContent: string | null,
        msisdnsNotify: string | null,
        smsContent: string | null,
        zaloNotify: string | null,
        zaloContent: string | null,
        eventType: number | null,
        deviceType: string | null,
        model: string | null,
        userCustomerId: number | null,
        userEnterpriseId: number | null,
        deviceId: number | null,
        sendingMethod: Array<any> | null,
        timeSend: string | null,
        deviceTypeId: number | null,
        status: number | null,
        businessName: string | null,
        individualName: string | null,
        deviceName: string | null,
    };
    userType: number;
    wallet: any;
    severityOptions: Array<any>;
    eventOptions: Array<any>;
    repeat: boolean = false;
    userInfo: any;
    readonly CONSTANTS = CONSTANTS;
    paramSearchCustomer = {};
    controlComboSelectDevice: ComboLazyControl = new ComboLazyControl();
    controlComboSelectType: ComboLazyControl = new ComboLazyControl();
    controlComboSelectModel: ComboLazyControl = new ComboLazyControl()
    controlComboSelectIndividual: ComboLazyControl = new ComboLazyControl()
    controlComboSelectBusiness: ComboLazyControl = new ComboLazyControl()
    paramSearchType: {
        modelCode: any;
    }
    paramSearchModel: {
        typeCode: any;
    }
    paramSearchBusiness: {
        type: 2,
        sort: 'id,asc'
        customerId: number | -1
    }
    paramSearchIndividual: {
        type: 3,
        sort: 'id,asc'
        managerId: number | -1
    }
    paramSearchUserCreated: {}
    normalPattern = /^[a-zA-Z0-9 ]*$/;
    vietnamesePattern = /^[^~`!@#\$%\^&*\(\)=\+\[\]\{\}\|\\,<>\/?]*$/;
    inputSchema: Array<any>
    optionSend = ['Email', 'SMS', 'Zalo'];
    alertId = this.route.snapshot.paramMap.get("id");
    statusTemp : any;
    ngOnInit(): void {
        let me = this;
        this.paramSearchType = {
            modelCode: "",
        }
        this.paramSearchModel = {
            typeCode: "",
        }
        this.paramSearchBusiness = {
            type: 2,
            sort: 'id,asc',
            customerId: -1
        }
        this.paramSearchIndividual = {
            type: 3,
            sort: 'id,asc',
            managerId: -1
        }
        this.paramSearchUserCreated = {
            sort: 'id,asc',
        }
        this.inputSchema = [];
        if (this.sessionService.userInfo.type != CONSTANTS.USER_TYPE.ADMIN) {
            this.paramSearchCustomer = {
                provinceCode: this.sessionService.userInfo.provinceCode
            }
        }
        this.userType = this.sessionService.userInfo.type;
        console.log(this.userType);
        this.items = [{
            label: this.tranService.translate("global.menu.alertList"),
            routerLink: "/alerts"
        }, {label: this.tranService.translate("global.titlepage.detailAlarm")}];
        this.home = {icon: 'pi pi-home', routerLink: '/'};
        this.alertInfo = {
            name: null,
            description: null,
            severity: null,
            emailList: null,
            emailContent: null,
            msisdnsNotify: null,
            smsContent: null,
            eventType: null,
            deviceType: null,
            model: null,
            deviceId: null,
            userEnterpriseId: null,
            userCustomerId: null,
            sendingMethod: [],
            timeSend: "immediate",
            zaloNotify: null,
            zaloContent: null,
            deviceTypeId: null,
            status: null,
            businessName: null,
            individualName: null,
            deviceName: null,
        }
        me.formAlert = me.formBuilder.group(this.alertInfo);
        this.severityOptions = [
            {name: this.tranService.translate("alert.severity.critical"), value: 0},
            {name: this.tranService.translate("alert.severity.major"), value: 1},
            {name: this.tranService.translate("alert.severity.minor"), value: 2},
            {name: this.tranService.translate("alert.severity.info"), value: 3}
        ]
        this.eventOptions = [
            {
                name: this.tranService.translate("alert.eventType.exceededValue"),
                value: CONSTANTS.ALERT_EVENT_TYPE.EXCEEDED_VALUE
            },
            {
                name: this.tranService.translate("alert.eventType.deviceAlert"),
                value: CONSTANTS.ALERT_EVENT_TYPE.DEVICE_ALERT
            },
        ];
        this.userInfo = this.sessionService.userInfo;
        this.getDetail();
        for (var control in this.formAlert.controls) {
            if (control == "status") continue;
            this.formAlert.controls[control].disable({ emitEvent: false });
        }
    }

    getDetail() {
        let me = this;
        let alertId = this.route.snapshot.paramMap.get("id");
        me.messageCommonService.onload()
        this.alertService.getById(parseInt(alertId), (response) => {
            me.alertInfo = {...response};
            me.alertInfo.timeSend = "immediate"
            if (response.sendingMethod) {
                me.alertInfo.sendingMethod = response.sendingMethod.split(',');
            } else {
                me.alertInfo.sendingMethod = []
            }
            if (me.alertInfo.deviceId) {
                me.getDetailDevice(me.alertInfo.deviceId)
            }
             if (me.alertInfo.userEnterpriseId) {
                me.getBusiness(me.alertInfo.userEnterpriseId)
            }
            if (me.alertInfo.userCustomerId) {
                me.getIndividual(me.alertInfo.userCustomerId)
            }
            if (isNumber(me.alertInfo.status)){
                me.statusTemp = me.alertInfo.status;
                me.formAlert.patchValue({
                    status: me.alertInfo.status
                });
            }
            let rule = JSON.parse(response.rule);
            me.getDetailDeviceType(rule)
        }, null, () => {
            me.messageCommonService.offload();
        })
    }
    getDetailDevice(id) {
        let me = this;
        // me.messageCommonService.onload()
        this.deviceService.detailDevice(id, (response) => {
            me.alertInfo.deviceName = String(response.deviceName) + ' - ' + String(response.imei);
        }, null, () => {
            me.messageCommonService.offload();
        })
    }
    getBusiness(id) {
        let me = this;
        me.messageCommonService.onload();
        me.accountService.getById(id, (response) => {
            me.alertInfo.businessName = response.name;
        }, null, () => {
            me.messageCommonService.offload();
        })
    }

    getIndividual(id) {
        let me = this;
        me.messageCommonService.onload();
        me.accountService.getById(id, (response) => {
            me.alertInfo.individualName = response.name;
        }, null, () => {
            me.messageCommonService.offload();
        })
    }
    onChangeStatus(event) {
        let me = this;
        setTimeout(function(){
            if(event.checked == CONSTANTS.ALERT_STATUS.ACTIVE) {
                me.alertInfo.status = CONSTANTS.ALERT_STATUS.INACTIVE;
            }else {
                me.alertInfo.status = CONSTANTS.ALERT_STATUS.ACTIVE;
            }
            me.changeStatus(event.checked)
        })
    }

    changeStatus(value){
        let me = this;

        me.messageCommonService.confirm(
            me.tranService.translate("global.message.titleConfirmChangeStatusAlert"),
            me.tranService.translate("global.message.confirmChangeStatusAlert"),
            {
                ok:()=>{
                    let dataBody = {
                        id : me.alertId,
                        status: value
                    }
                    me.alertService.changeStatus(dataBody,(response)=>{
                        me.messageCommonService.success(me.tranService.translate("global.message.changeStatusSuccess"));
                        me.alertInfo.status = value;
                        me.statusTemp = value;
                    })
                },
                cancel: ()=>{

                }
            }
        )
    }

    addFormControl() {
        let me = this;
        if (this.inputSchema.length > 0) {
            me.inputSchema.forEach(input => {
                me.formAlert.addControl(me.utilService.getKey(input.key), new FormControl(input.type == CONSTANTS.COMMAND_VAR_TYPE.CHECKBOX ? false : input.type == CONSTANTS.COMMAND_VAR_TYPE.NUMBER || input.type == null ? null : ''))
            })
        }
    }

    filValFormControl(rule) {
        let me = this;
        if (this.inputSchema.length > 0) {
            me.inputSchema.forEach(input => {
                const found = rule.find(rule => me.utilService.getKey(rule.key) === me.utilService.getKey(input.key));
                // console.log(found)
                me.formAlert.addControl(me.utilService.getKey(input.key), new FormControl({value: found?.value, disabled: true}))
            })
        }
    }

    removeFormControl() {
        let me = this;
        if (this.inputSchema.length > 0) {
            me.inputSchema.forEach(input => {
                me.formAlert.removeControl(me.utilService.getKey(input.key))
            })
        }
    }

    onSelectedType() {
        let me = this;
        me.paramSearchModel.typeCode = me.alertInfo.deviceType ? me.alertInfo.deviceType : "";
        if (me.alertInfo.deviceType == null || me.alertInfo.deviceType == undefined || me.alertInfo.deviceType == '') {
            me.inputSchema = []
        } else if (me.alertInfo.deviceType && me.alertInfo.model) {
            me.removeFormControl();
            me.getSchema();
        }
    }

    onSelectedModel() {
        let me = this;
        me.paramSearchType.modelCode = me.alertInfo.model ? me.alertInfo.model : "";
        if (me.alertInfo.model == null || me.alertInfo.model == undefined || me.alertInfo.model == '') {
            me.inputSchema = []
        } else if (me.alertInfo.deviceType && me.alertInfo.model) {
            me.removeFormControl();
            me.getSchema();
        }
    }

    onSelectedBusiness() {
        let me = this;
        me.paramSearchIndividual.managerId = me.alertInfo.userEnterpriseId ? me.alertInfo.userEnterpriseId : -1;
    }

    onSelectedIndividual() {
        let me = this;
        me.paramSearchBusiness.customerId = me.alertInfo.userCustomerId ? me.alertInfo.userCustomerId : -1;
    }

    getDetailDeviceType(rule) {
        let me = this;
        me.deviceTypeService.getDeviceType(me.alertInfo.deviceTypeId, resp => {
            try {
                me.alertInfo.deviceType = resp.typeCode;
                me.alertInfo.model = resp.modelCode
                let config = JSON.parse(resp.telemetryConfigSchema)
                if (config && config.length > 0) {
                    me.filterInputSchema(config)
                    me.filValFormControl(rule)
                } else {
                    me.inputSchema = []
                    me.alertInfo.deviceTypeId = null;
                }
                if (me.inputSchema.length == 0) {
                    me.messageCommonService.warning(me.tranService.translate('alert.text.noSchema'))
                }
            } catch (e) {
                console.log(e)
                me.inputSchema = []
                me.alertInfo.deviceTypeId = null;
                me.messageCommonService.warning(me.tranService.translate('alert.text.noSchema'))
            }
        })
    }

    getSchema() {
        let me = this;
        let dataBody = {
            typeCode: me.alertInfo.deviceType,
            modelCode: me.alertInfo.model,
        }
        me.messageCommonService.onload()
        me.deviceModelService.search(dataBody, (response) => {
            if (response.content.length > 0) {
                let detaiDeviceType = response.content[0];
                me.alertInfo.deviceTypeId = Number(detaiDeviceType.id);
                me.deviceTypeService.getDeviceType(me.alertInfo.deviceTypeId, resp => {
                    try {
                        let config = JSON.parse(resp.telemetryConfigSchema)
                        if (config && config.length > 0) {
                            me.filterInputSchema(config)
                            me.addFormControl()
                        } else {
                            me.inputSchema = []
                            me.alertInfo.deviceTypeId = null;
                            me.messageCommonService.warning(me.tranService.translate('alert.text.noSchema'))
                        }
                    } catch (e) {
                        console.log(e)
                        me.inputSchema = []
                        me.alertInfo.deviceTypeId = null;
                        me.messageCommonService.warning(me.tranService.translate('alert.text.noSchema'))
                    }
                })
            } else {
                me.inputSchema = []
                me.alertInfo.deviceTypeId = null;
                me.messageCommonService.warning(me.tranService.translate('alert.text.noSchema'))
            }
        }, null, () => {
            me.messageCommonService.offload();
        })
    }

    ngAfterContentChecked() {

    }

    // onSubmitUpdate() {
    //     let me = this;
    //     let dataBody = {}
    //     // Object.keys(this.formAlert.controls).forEach(key => {
    //     //     dataBody[key] = this.formAlert.get(key).value;
    //     //   });
    //     Object.keys(this.alertInfo).forEach(key => {
    //         if (this.alertInfo[key] != null) {
    //             dataBody[key] = this.alertInfo[key];
    //         }
    //     })
    //     dataBody['sendingMethod'] = dataBody['sendingMethod'].toString();
    //     let rule = []
    //     me.inputSchema.forEach(el => {
    //         let object: {}
    //         if (el.type == null || el.type == CONSTANTS.COMMAND_VAR_TYPE.NUMBER) {
    //             object = {
    //                 key: el.key,
    //                 value: Number(me.formAlert.get(el.key).value) || null,
    //             }
    //         } else {
    //             object = {
    //                 key: el.key,
    //                 value: me.formAlert.get(el.key).value || null,
    //             }
    //         }
    //         rule.push(object);
    //     })
    //     dataBody['rule'] = JSON.stringify(rule);
    //     // console.log(dataBody)
    //
    //     me.alertService.updateAlert(me.alertId,dataBody, (response) => {
    //         me.messageCommonService.success(me.tranService.translate("global.message.saveSuccess"));
    //         me.router.navigate(['/alerts']);
    //     }, null, () => {
    //         me.messageCommonService.offload();
    //     })
    //
    // }

    checkExistEmailList() {
        if (this.alertInfo.emailList == null || this.alertInfo.emailList == null ||
            this.alertInfo.emailList == '' || this.formAlert.controls.emailList.errors?.pattern) {
            return false;
        }
        const arr = this.alertInfo.emailList.split(',')
        let duplicate = false;
        const set = new Set();
        for (const el of arr) {
            if (!set.has(el)) {
                set.add(el)
            } else {
                duplicate = true;
            }
        }
        return duplicate;
    }

    checkExistSmsList() {
        if (this.alertInfo.msisdnsNotify == null || this.alertInfo.msisdnsNotify == null ||
            this.alertInfo.msisdnsNotify == '' || this.formAlert.controls.msisdnsNotify.errors?.pattern) {
            return false;
        }
        const arr = this.alertInfo.msisdnsNotify.split(',')
        let duplicate = false;
        const set = new Set();
        for (const el of arr) {
            if (!set.has(el)) {
                set.add(el)
            } else {
                duplicate = true;
            }
        }
        return duplicate;
    }

    check50Email() {
        if (this.alertInfo.emailList == null || this.alertInfo.emailList == null ||
            this.alertInfo.emailList == '' || this.formAlert.controls.emailList.errors?.pattern) {
            return false;
        }
        const arr = this.alertInfo.emailList.split(',')
        if (arr.length > 50) {
            return true;
        } else {
            return false;
        }
    }

    check50Sms() {
        if (this.alertInfo.msisdnsNotify == null || this.alertInfo.msisdnsNotify == null ||
            this.alertInfo.msisdnsNotify == '' || this.formAlert.controls.msisdnsNotify.errors?.pattern) {
            return false;
        }
        const arr = this.alertInfo.msisdnsNotify.split(',')
        if (arr.length > 50) {
            return true;
        } else {
            return false;
        }
    }

    checkDisableSave() {
        let me = this;
        // console.log(me.formAlert)
        const invalidControlsAlert = Object.keys(this.formAlert.controls)
            .filter(controlName => this.formAlert.controls[controlName].invalid);
        // console.log("Invalid fields in formAlert: ", invalidControlsAlert);
        if (this.formAlert.invalid
            || me.alertInfo.deviceTypeId == null
            || this.controlComboSelectBusiness.invalid
            || this.controlComboSelectIndividual.invalid
            || this.controlComboSelectModel.invalid
            || this.controlComboSelectType.invalid
            || this.controlComboSelectDevice.invalid
            || this.inputSchema.length == 0
            || this.alertInfo.sendingMethod.length == 0
            || me.alertInfo.sendingMethod.includes('Email') && (this.checkExistEmailList() || this.check50Email())
            || me.alertInfo.sendingMethod.includes('SMS') && (this.checkExistSmsList() || this.check50Sms())
            || me.alertInfo.sendingMethod.includes('Zalo') && (this.checkExistZaloList() || this.check50Zalo())
        ) {
            return true;
        } else {
            return false;
        }
    }

    checkExistZaloList() {
        if (this.alertInfo.zaloNotify == null || this.alertInfo.zaloNotify == null ||
            this.alertInfo.zaloNotify == '' || this.formAlert.controls.zaloNotify.errors?.pattern) {
            return false;
        }
        const arr = this.alertInfo.zaloNotify.split(',')
        let duplicate = false;
        const set = new Set();
        for (const el of arr) {
            if (!set.has(el)) {
                set.add(el)
            } else {
                duplicate = true;
            }
        }
        return duplicate;
    }

    check50Zalo() {
        if (this.alertInfo.zaloNotify == null || this.alertInfo.zaloNotify == null ||
            this.alertInfo.zaloNotify == '' || this.formAlert.controls.zaloNotify.errors?.pattern) {
            return false;
        }
        const arr = this.alertInfo.zaloNotify.split(',')
        if (arr.length > 50) {
            return true;
        } else {
            return false;
        }
    }

    changeTypeSendAlert() {
        let me = this;
        let arr = ["emailList", "emailContent", "msisdnsNotify", "smsContent", "zaloNotify", "zaloContent"]
        arr.forEach(key => {
            me.formAlert.removeControl(key)
        })
        if (me.alertInfo.sendingMethod.includes('Email')) {
            me.formAlert.addControl('emailList', new FormControl())
            me.formAlert.addControl('emailContent', new FormControl())
        }
        if (me.alertInfo.sendingMethod.includes('SMS')) {
            me.formAlert.addControl('msisdnsNotify', new FormControl())
            me.formAlert.addControl('smsContent', new FormControl())
        }
        if (me.alertInfo.sendingMethod.includes('Zalo')) {
            me.formAlert.addControl('zaloNotify', new FormControl())
            me.formAlert.addControl('zaloContent', new FormControl())
        }
    }

    goBack() {
        window.history.back();
    }

    getDisplayValue(value) {
        return value;
    }

    goUpdate() {
        this.router.navigate([`/alerts/update/${this.alertId}`]);
    }
    getSeverity(value) {
        let me = this;
        const severity = me.severityOptions.find(severity => severity.value == value)
        return severity ? severity.name : "";
    }
    getEventType(value) {
        let me = this;
        const eventType = me.eventOptions.find(eventType => eventType.value == value)
        return eventType ? eventType.name : "";
    }
    getValueFormControl(name) {
        let me = this;
        const value = me.formAlert.controls[name]?.value;
        return value ? value : "";
    }
    getContent(value): string {
        return value?.replace(/\n/g, '<br>') || "";
    }
    filterInputSchema(config) {
        let me = this;
        if (me.alertInfo.eventType == CONSTANTS.ALERT_EVENT_TYPE.EXCEEDED_VALUE) {
            me.inputSchema = config.filter((item) => item.isShowAlert);
        } else if (me.alertInfo.eventType == CONSTANTS.ALERT_EVENT_TYPE.DEVICE_ALERT){
            me.inputSchema = config.filter((item) => item.isAlert);
        }
    }
}
