import { Inject, Injectable } from "@angular/core";
import { HttpService } from "../comon/http.service";
import { callback } from "chart.js/types/helpers";
@Injectable()
export class ConfigChartService {
    private prefixApi: string;

    constructor(@Inject(HttpService) private httpService: HttpService) {
        this.prefixApi = "/dashboard";
        // this.prefixApi = "/config-chart";
    }

    public search(params: {
        [key: string]: any
    }, callback?: Function, errorCallBack?: Function, finallyCallback?: Function) {
        this.httpService.get(`${this.prefixApi}/search`, {timeout: 120000}, params, callback, errorCallBack, finallyCallback);
    }
    public detail(chartId: number|string, callback?:Function, errorCallBack?:Function, finallyCallback?:Function){
        this.httpService.get(`${this.prefixApi}/${chartId}`,{timeout: 120000},{}, callback, errorCallBack, finallyCallback);
    }

    public delete(chartId: number|string, callback?:Function, errorCallBack?:Function, finallyCallback?:Function){
        this.httpService.delete(`${this.prefixApi}/${chartId}`,{timeout: 120000},{}, callback, errorCallBack, finallyCallback);
    }

    public create(data: any, callback?: Function, errorCallBack?: Function, finallyCallback?: Function){
        this.httpService.post(`${this.prefixApi}`, {}, data, {timeout: 120000}, callback, errorCallBack, finallyCallback);
    }

    public update(data: any, callback?: Function, errorCallBack?: Function, finallyCallback?: Function){
        this.httpService.put(`${this.prefixApi}/${data.id}`, {timeout: 120000}, data, {}, callback, errorCallBack, finallyCallback);
    }

    public checkExists(name: string, callback?: Function, errorCallBack?: Function, finallyCallback?: Function){
        this.httpService.get(`${this.prefixApi}/checkName`, {timeout: 120000}, {name}, callback, errorCallBack, finallyCallback);
    }

    public getAll(callback?: Function, errorCallBack?: Function, finallyCallback?: Function){
        this.httpService.get(`${this.prefixApi}/getAll`, {timeout: 120000}, {}, callback, errorCallBack, finallyCallback);
    }

    public getContent(data, callback?: Function, errorCallBack?: Function, finallyCallback?: Function){
        this.httpService.post(`${this.prefixApi}/getContent`, {timeout: 300000}, data, {}, callback, errorCallBack, finallyCallback);
    }

    public getListConfig(userId, callback?: Function, errorCallBack?: Function, finallyCallback?: Function){
        this.httpService.get("/threshold-config/get-list-config/"+userId, {timeout: 120000}, {}, callback, errorCallBack, finallyCallback);
    }

    public saveConfigDashboard(data, callback?: Function, errorCallBack?: Function, finallyCallback?: Function){
        this.httpService.post("/threshold-config/create-config", {timeout: 120000}, data, {}, callback, errorCallBack, finallyCallback);
    }
}
