import { NgModule } from "@angular/core";
import { RouterModule } from "@angular/router";
import DataPage from "src/app/service/data.page";
import { TermPolicyListComponent } from "./app.term.policy.list.component";
import { TermPolicyHistoryComponent } from "./app.term.policy.history.component";
import {CONSTANTS} from "../../service/comon/constants";

@NgModule({
    imports: [
        RouterModule.forChild([
            {path: "", component: TermPolicyListComponent, data: new DataPage("global.menu.termpolicy", [CONSTANTS.PERMISSIONS.POLICY.PERSONAL])},
            {path: "history", component: TermPolicyHistoryComponent, data: new DataPage("global.menu.termpolicyhistory", [CONSTANTS.PERMISSIONS.POLICY.PERSONAL])},
        ])
    ],
    exports: [RouterModule]
})
export class TermPolicyRoutingModule {};
