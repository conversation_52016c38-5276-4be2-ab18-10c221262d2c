<div class="vnpt flex flex-row justify-content-between align-items-center bg-white p-2 border-round">
    <div class="">
        <div class="text-xl font-bold mb-1">{{ tranService.translate("global.menu.detailDevice") }}</div>
        <p-breadcrumb class="max-w-full col-7" [model]="items" [home]="home"></p-breadcrumb>
    </div>
    <div class="col-5 flex flex-row justify-content-end align-items-center">
        <p-button styleClass="p-button-info "
                  *ngIf="([CONSTANTS.PERMISSIONS.DEVICE.CREATE])"
                  (onClick)="goUpdate()">{{ tranService.translate("global.button.update") }}
        </p-button>
    </div>
</div>
<p-card styleClass="mt-3" [header]="tranService.translate('device.label.info')">

    <div class="flex flex-row justify-content-between">
        <div style="width: 32%;" class="flex flex-column justify-content-between align-items-center border-round-md bg-blue-50 pt-2">
            <div class="w-full field grid">
                <label htmlFor="statusConnect" class="col-fixed"
                       style="width:180px"><b>{{ tranService.translate("device.label.statusConnect") }}</b><span
                    class="text-red-500"></span></label>
                <div class="col">
                <span class="p-float-label">
                    <span
                          [class]="getClassStatus(deviceInfo.connectionStatus)">{{ getNameStatus(deviceInfo.connectionStatus) }}</span>
                </span>
                </div>
            </div>
            <div class="w-full field grid">
                <label htmlFor="lastConnected" class="col-fixed"
                       style="width:180px"><b>{{ tranService.translate("device.label.lastConnected") }}</b><span
                    class="text-red-500"></span></label>
                <div class="col">
                <span class="p-float-label">
<!--                    <input pInputText-->
<!--                           class="w-full"-->
<!--                           pInputText id="lastConnected"-->
<!--                           [value]="utilService.convertDateTimeToString(toDate(Number(deviceInfo.lastStatusUpdatedAt)))"-->
<!--                           readonly-->
<!--                    />-->
                    <span>{{utilService.convertDateTimeToString(toDate(Number(deviceInfo.lastStatusUpdatedAt)))}}</span>
                    <!--                    <label htmlFor="lastConnected">{{ tranService.translate("device.label.lastConnected") }}</label>-->
                </span>
                </div>
            </div>
            <div class="w-full field grid">
                <label htmlFor="name" class="col-fixed"
                       style="width:180px"><b>{{ tranService.translate("device.label.currentMonthlyFlow") }}</b><span
                    class="text-red-500"></span></label>
                <div class="col">
                <span class="p-float-label">
<!--                    <input pInputText-->
<!--                           class="w-full"-->
<!--                           pInputText id="usage"-->
<!--                           [(ngModel)]="deviceInfo.usage"-->
<!--                           readonly-->
<!--                    />-->
                    <span>{{deviceInfo.currentVolume}}</span>
                    <!--                    <label htmlFor="name">{{ tranService.translate("device.label.name") }}</label>-->
                </span>
                </div>
            </div>
            <div class="w-full field grid">
                <label htmlFor="name" class="col-fixed"
                       style="width:180px"><b>{{ tranService.translate("device.label.estimatedCost") }}</b><span
                    class="text-red-500"></span></label>
                <div class="col">
                <span class="p-float-label">
<!--                    <input pInputText-->
<!--                           class="w-full"-->
<!--                           pInputText id="cost"-->
<!--                           [(ngModel)]="deviceInfo.cost"-->
<!--                           readonly-->
<!--                    />-->
                    <span>{{deviceInfo.estimatedCost}}</span>
                    <!--                    <label htmlFor="name">{{ tranService.translate("device.label.name") }}</label>-->
                </span>
                </div>
            </div>



        </div>
        <div style="width: 32%" class="flex flex-column justify-content-between align-items-center pt-2">
            <div class="w-full field grid">
                <label htmlFor="name" class="col-fixed"
                       style="width:180px">{{ tranService.translate("device.label.name") }}<span
                    class="text-red-500"></span></label>
                <div class="col">
                <span class="p-float-label">
<!--                    <input pInputText-->
<!--                           class="w-full"-->
<!--                           pInputText id="name"-->
<!--                           [(ngModel)]="deviceInfo.deviceName"-->
<!--                           readonly-->
<!--                    />-->
                    <span>{{deviceInfo.deviceName}}</span>
                    <!--                    <label htmlFor="name">{{ tranService.translate("device.label.name") }}</label>-->
                </span>
                </div>
            </div>
            <div class="w-full field grid">
                <label htmlFor="name" class="col-fixed"
                       style="width:180px">{{ tranService.translate("device.label.type") }}<span
                    class="text-red-500"></span></label>
                <div class="col">
                <span class="p-float-label">
<!--                    <input pInputText-->
<!--                           class="w-full"-->
<!--                           pInputText id="type"-->
<!--                           [(ngModel)]="deviceType.typeName"-->
<!--                           readonly-->

<!--                    />-->
                    <span>{{deviceType.typeName}}</span>
                </span>
                </div>
            </div>
            <div class="w-full field grid">
                <label htmlFor="name" class="col-fixed"
                       style="width:180px">{{ tranService.translate("device.label.model") }}<span
                    class="text-red-500"></span></label>
                <div class="col">
                <span class="p-float-label">
<!--                    <input pInputText-->
<!--                           class="w-full"-->
<!--                           pInputText id="imei"-->
<!--                           [(ngModel)]="deviceType.modelCode"-->
<!--                           readonly-->

<!--                    />-->
                    <span>{{deviceType.modelCode}}</span>
                    <!--                    <label for="type">{{ tranService.translate("device.label.model") }}</label>-->
                </span>
                </div>
            </div>

<!--            <div class="w-full field grid">-->
<!--                <label htmlFor="imei" class="col-fixed"-->
<!--                       style="width:180px">{{ tranService.translate("device.label.imei") }}<span-->
<!--                    class="text-red-500"></span></label>-->
<!--                <div class="col">-->
<!--                <span class="p-float-label">-->
<!--                    <input pInputText-->
<!--                           class="w-full"-->
<!--                           pInputText id="imei"-->
<!--                           [(ngModel)]="deviceInfo.imei"-->
<!--                           readonly-->

<!--                    />-->
<!--                    &lt;!&ndash;                    <label htmlFor="imei">{{ tranService.translate("device.label.imei") }}</label>&ndash;&gt;-->
<!--                </span>-->
<!--                </div>-->
<!--            </div>-->
            <div class="w-full field grid">
                <label htmlFor="imei" class="col-fixed"
                       style="width:180px">{{ tranService.translate("device.label.imei") }}<span
                    class="text-red-500"></span></label>
                <div class="col">
                <span class="p-float-label">
<!--                    <input pInputText-->
<!--                           class="w-full"-->
<!--                           pInputText id="imei"-->
<!--                           [(ngModel)]="deviceInfo.imei"-->
<!--                           readonly-->
<!--                    />-->
                    <span>{{deviceInfo.imei}}</span>
                    <!--                    <label htmlFor="serial">{{ tranService.translate("device.label.serial") }}</label>-->
                </span>
                </div>
            </div>

        </div>
        <div style="width: 32%" class="pt-2">
            <div class="w-full field grid">
                <label htmlFor="msisdn" class="col-fixed"
                       style="width:180px">{{ tranService.translate("device.label.msisdn") }}<span
                    class="text-red-500"></span></label>
                <div class="col">
                <span class="p-float-label">
<!--                    <input pInputText-->
<!--                           class="w-full"-->
<!--                           pInputText id="msisdn"-->
<!--                           [(ngModel)]="deviceInfo.msisdn"-->
<!--                           readonly-->
<!--                    />-->
                    <span>{{deviceInfo.msisdn}}</span>
                    <!--                    <label htmlFor="msisdn">{{ tranService.translate("device.label.msisdn") }}</label>-->
                </span>
                </div>
            </div>
            <div class="w-full field grid">
                <label htmlFor="manufacturer" class="col-fixed"
                       style="width:180px">{{ tranService.translate("device.label.manufacturer") }}</label>
                <div class="col">
                <span class="p-float-label">
<!--                    <input pInputText-->
<!--                           class="w-full"-->
<!--                           pInputText id="manufacturer"-->
<!--                           [(ngModel)]="deviceInfo.manufacture"-->
<!--                           readonly-->
<!--                    />-->
                    <span>{{deviceInfo.manufacture}}</span>
                    <!--                    <label htmlFor="manufacturer">{{ tranService.translate("device.label.manufacturer") }}</label>-->
                </span>
                </div>
            </div>
            <div class="w-full field grid">
                <label htmlFor="description" class="col-fixed"
                       style="width:180px">{{ tranService.translate("device.label.description") }}</label>
                <div class="col">
                <span class="p-float-label">
<!--                    <textarea pInputText-->
<!--                              class="w-full"-->
<!--                              pInputText id="description"-->
<!--                              [(ngModel)]="deviceInfo.description"-->
<!--                              readonly-->
<!--                    ></textarea>-->
                    <span>{{deviceInfo.description}}</span>
                    <!--                    <label htmlFor="description">{{ tranService.translate("device.label.description") }}</label>-->
                </span>
                </div>
            </div>
        </div>

    </div>
    <p-tabView (onChange)="onTabChange($event)" [(activeIndex)]="activeTabIndex">
        <p-tabPanel header="{{tranService.translate('device.label.custInfo')}}">
            <div class="flex flex-row justify-content-between">
                <div style="width: 49%">
                    <p-card [header]="tranService.translate('device.label.infoBusinessMngt')">
                        <div class="w-full field grid">
                            <label htmlFor="businessName" class="col-fixed"
                                   style="width:180px">{{ tranService.translate("device.label.businessName") }}<span
                                class="text-red-500"></span></label>
                            <div class="col">
                <span class="p-float-label">
<!--                    <input pInputText-->
<!--                           class="w-full"-->
<!--                           pInputText id="businessName"-->
<!--                           [(ngModel)]="business.name"-->
<!--                           readonly-->
<!--                    />-->
                    <span>{{business.name}}</span>
                    <!--                    <label htmlFor="businessName">{{ tranService.translate("device.label.businessName") }}</label>-->
                </span>
                            </div>
                        </div>
                        <div class="w-full field grid">
                            <label htmlFor="taxCode" class="col-fixed"
                                   style="width:180px">{{ tranService.translate("device.label.taxCode") }}<span
                                class="text-red-500"></span></label>
                            <div class="col">
                <span class="p-float-label">
<!--                    <input pInputText-->
<!--                           class="w-full"-->
<!--                           pInputText id="taxCode"-->
<!--                           [(ngModel)]="business.taxCode"-->
<!--                           readonly-->
<!--                    />-->
                    <span>{{business.taxCode}}</span>
                    <!--                    <label htmlFor="taxCode">{{ tranService.translate("device.label.taxCode") }}</label>-->
                </span>
                            </div>
                        </div>
                        <div class="w-full field grid">
                            <label htmlFor="headOffice" class="col-fixed"
                                   style="width:180px">{{ tranService.translate("device.label.headOffice") }}<span
                                class="text-red-500"></span></label>
                            <div class="col">
                <span class="p-float-label">
<!--                    <input pInputText-->
<!--                           class="w-full"-->
<!--                           pInputText id="headOffice"-->
<!--                           [(ngModel)]="business.addressHeadOffice"-->
<!--                           readonly-->
<!--                    />-->
                    <span>{{business.addressHeadOffice}}</span>
                    <!--                    <label htmlFor="headOffice">{{ tranService.translate("device.label.headOffice") }}</label>-->
                </span>
                            </div>
                        </div>
                        <div class="w-full field grid">
                            <label htmlFor="representativeName" class="col-fixed"
                                   style="width:180px">{{ tranService.translate("device.label.representativeName") }}
                                <span
                                    class="text-red-500"></span></label>
                            <div class="col">
                <span class="p-float-label">
<!--                    <input pInputText-->
<!--                           class="w-full"-->
<!--                           pInputText id="representativeName"-->
<!--                           [(ngModel)]="business.representativeName"-->
<!--                           readonly-->
<!--                    />-->
                    <span>{{business.representativeName}}</span>
                    <!--                    <label htmlFor="representativeName">{{ tranService.translate("device.label.representativeName") }}</label>&ndash;&gt;-->
                </span>
                            </div>
                        </div>
                        <div class="w-full field grid">
                            <label htmlFor="subcriber" class="col-fixed"
                                   style="width:180px">{{ tranService.translate("device.label.subcriber") }}<span
                                class="text-red-500"></span></label>
                            <div class="col">
                <span class="p-float-label">
<!--                    <input pInputText-->
<!--                           class="w-full"-->
<!--                           pInputText id="subcriber"-->
<!--                           [(ngModel)]="business.phone"-->
<!--                           readonly-->
<!--                    />-->
                    <span>{{business.phone}}</span>
                    <!--                    <label htmlFor="subcriber">{{ tranService.translate("device.label.subcriber") }}</label>-->
                </span>
                            </div>
                        </div>
                        <div class="w-full field grid">
                            <label htmlFor="email" class="col-fixed"
                                   style="width:180px">{{ tranService.translate("device.label.email") }}<span
                                class="text-red-500"></span></label>
                            <div class="col">
                <span class="p-float-label">
<!--                    <input pInputText-->
<!--                           class="w-full"-->
<!--                           pInputText id="email"-->
<!--                           [(ngModel)]="business.email"-->
<!--                           readonly-->
<!--                    />-->
                    <span>{{business.email}}</span>
                    <!--                    <label htmlFor="email">{{ tranService.translate("device.label.email") }}</label>-->
                </span>
                            </div>
                        </div>
                        <div class="w-full field grid">
                            <label htmlFor="contactAddress" class="col-fixed"
                                   style="width:180px">{{ tranService.translate("device.label.contactAddress") }}<span
                                class="text-red-500"></span></label>
                            <div class="col">
                <span class="p-float-label">
<!--                    <input pInputText-->
<!--                           class="w-full"-->
<!--                           pInputText id="contactAddress"-->
<!--                           [(ngModel)]="business.addressContact"-->
<!--                    />-->
                    <span>{{business.addressContact}}</span>
                    <!--                    <label htmlFor="contactAddress">{{ tranService.translate("device.label.contactAddress") }}</label>-->
                </span>
                            </div>
                        </div>
                    </p-card>
                </div>
                <div style="width: 49%">
                    <p-card [header]="tranService.translate('device.label.infoIndividualMngt')">
                        <div class="w-full field grid">
                            <label htmlFor="individualName" class="col-fixed"
                                   style="width:180px">{{ tranService.translate("device.label.individualName") }}<span
                                class="text-red-500"></span></label>
                            <div class="col">
                <span class="p-float-label">
<!--                    <input pInputText-->
<!--                           class="w-full"-->
<!--                           pInputText id="individualName"-->
<!--                           [(ngModel)]="individual.name"-->
<!--                           readonly-->
<!--                    />-->
                    <span>{{individual.name}}</span>
                    <!--                    <label htmlFor="individualName">{{ tranService.translate("device.label.individualName") }}</label>-->

                </span>
                            </div>
                        </div>
                        <div class="w-full field grid">
                            <label htmlFor="subcriber" class="col-fixed"
                                   style="width:180px">{{ tranService.translate("device.label.subcriber") }}<span
                                class="text-red-500"></span></label>
                            <div class="col">
                <span class="p-float-label">
<!--                    <input pInputText-->
<!--                           class="w-full"-->
<!--                           pInputText id="subcriber"-->
<!--                           [(ngModel)]="individual.phone"-->
<!--                           readonly-->
<!--                    />-->
                    <span>{{individual.phone}}</span>
                    <!--                    <label htmlFor="subcriber">{{ tranService.translate("device.label.subcriber") }}</label>-->
                </span>
                            </div>
                        </div>
                        <div class="w-full field grid">
                            <label htmlFor="email" class="col-fixed"
                                   style="width:180px">{{ tranService.translate("device.label.email") }}<span
                                class="text-red-500"></span></label>
                            <div class="col">
                <span class="p-float-label">
<!--                    <input pInputText-->
<!--                           class="w-full"-->
<!--                           pInputText id="email"-->
<!--                           [(ngModel)]="individual.email"-->
<!--                           readonly-->
<!--                    />-->
                    <span>{{individual.email}}</span>
                    <!--                    <label htmlFor="email">{{ tranService.translate("device.label.email") }}</label>-->
                </span>
                            </div>
                        </div>
                        <div class="w-full field grid">
                            <label htmlFor="contactAddress" class="col-fixed"
                                   style="width:180px">{{ tranService.translate("device.label.contactAddress") }}<span
                                class="text-red-500"></span></label>
                            <div class="col">
                <span class="p-float-label">
<!--                    <input pInputText-->
<!--                           class="w-full"-->
<!--                           pInputText id="contactAddress"-->
<!--                           [(ngModel)]="individual.addressContact"-->
<!--                           readonly-->
<!--                    />-->
                    <span>{{individual.addressContact}}</span>
                    <!--                    <label htmlFor="contactAddress">{{ tranService.translate("device.label.contactAddress") }}</label>-->
                </span>
                            </div>
                        </div>
                    </p-card>
                </div>
            </div>
        </p-tabPanel>
        <p-tabPanel header="{{tranService.translate('device.label.listCmd')}}">
            <form [formGroup]="formSearch" (ngSubmit)="onSubmitSearchCmd()" class="w-full">
                <p-panel [toggleable]="true" [header]="tranService.translate('global.text.filter')">
                    <div class="flex flex-row justify-content-between justify-content-center">
                        <div class="grid w-full">
                            <div class="col-3">
                <span class="p-float-label">
                    <p-calendar styleClass="w-full"
                                id="createdAtStart"
                                formControlName="createdAtStart"
                                [(ngModel)]="searchCmdInfo.createdAtStart"
                                [showIcon]="true"
                                [showClear]="true"
                                dateFormat="dd/mm/yy"
                                [maxDate]="maxDateFrom"
                                (onSelect)="onChangeDateFrom(searchCmdInfo.createdAtStart)"
                                (onInput)="onChangeDateFrom(searchCmdInfo.createdAtStart)"
                    ></p-calendar>
                    <label htmlFor="name">{{ tranService.translate("device.label.timeSentFrom") }}</label>
                </span>
                            </div>
                            <div class="col-3">
                <span class="p-float-label">
                    <p-calendar styleClass="w-full"
                                id="createdAtEnd"
                                formControlName="createdAtEnd"
                                [(ngModel)]="searchCmdInfo.createdAtEnd"
                                [showIcon]="true"
                                [showClear]="true"
                                dateFormat="dd/mm/yy"
                                [minDate]="minDateTo"
                                [maxDate]="maxDateTo"
                                (onSelect)="onChangeDateTo(searchCmdInfo.createdAtEnd)"
                                (onInput)="onChangeDateTo(searchCmdInfo.createdAtEnd)"
                    ></p-calendar>
                    <label htmlFor="name">{{ tranService.translate("device.label.timeSentTo") }}</label>
                </span>
                            </div>
                            <div class="col-3">
                <span class="p-float-label">
                    <input class="w-full"
                           pInputText id="commandCode"
                           [(ngModel)]="searchCmdInfo.commandId"
                           formControlName="commandId"
                    />
                    <label htmlFor="cmdCode">{{ tranService.translate("device.label.cmdCode") }}</label>
                </span>
                            </div>
                            <div class="col-3">
                <span class="p-float-label">
                    <input class="w-full"
                           pInputText id="commandName"
                           [(ngModel)]="searchCmdInfo.commandName"
                           formControlName="commandName"
                    />
                    <label htmlFor="msisdn">{{ tranService.translate("device.label.cmdName") }}</label>
                </span>
                            </div>
                            <div class="col-3">
                <span class="p-float-label">
                    <input class="w-full"
                           pInputText
                           id="commandType"
                           [(ngModel)]="searchCmdInfo.commandType"
                           formControlName="commandType"
                    />
                    <label for="type">{{ tranService.translate("device.label.cmdType") }}</label>
                </span>
                            </div>
                            <div class="col-3">
                <span class="p-float-label">
                    <input class="w-full"
                           pInputText id="data"
                           [(ngModel)]="searchCmdInfo.data"
                           formControlName="data"
                    />
                    <label htmlFor="serial">{{ tranService.translate("device.label.data") }}</label>
                </span>
                            </div>

                            <div class="col-3">
                <span class="p-float-label">
                    <p-dropdown styleClass="w-full" [showClear]="true"
                                id="status" [autoDisplayFirst]="false"
                                [(ngModel)]="searchCmdInfo.status"
                                formControlName="status"
                                [options]="listStatusCmd"
                                optionLabel="name"
                                optionValue="value"
                    ></p-dropdown>
                    <label for="type">{{ tranService.translate("device.label.status") }}</label>
                </span>
                            </div>
                            <div class="col-3 pb-0">
                                <p-button icon="pi pi-search"
                                          styleClass="p-button-rounded p-button-secondary p-button-text button-search"
                                          type="submit"
                                ></p-button>
                            </div>
                        </div>
                    </div>
                </p-panel>
                <table-vnpt
                    [fieldId]="'id'"
                    [(selectItems)]="selectItemsCmd"
                    [columns]="columnsCmd"
                    [dataSet]="dataSetCmd"
                    [options]="optionTableCmd"
                    [loadData]="searchCmd.bind(this)"
                    [pageNumber]="pageNumberCmd"
                    [pageSize]="pageSizeCmd"
                    [sort]="sortCmd"
                    [params]="searchCmdInfo"
                ></table-vnpt>
            </form>
        </p-tabPanel>
        <p-tabPanel header="{{tranService.translate('device.label.telemetryData')}}">
            <div class="flex flex-row justify-content-between" *ngIf="columnsTelemetry.length > 0">
                <div class="relative w-full">
                    <table-vnpt
                        [fieldId]="'id'"
                        [(selectItems)]="selectItemsTelemetry"
                        [columns]="columnsTelemetry"
                        [dataSet]="dataSetTelemetry"
                        [options]="optionTableTelemetry"
                        [pageNumber]="pageNumberTelemetry"
                        [pageSize]="pageSizeTelemetry"
                        [sort]="sortTelemetry"
                        [loadData]="getTelemetry.bind(this)"
                    ></table-vnpt>
                </div>
            </div>
        </p-tabPanel>
        <p-tabPanel header="{{tranService.translate('device.label.historyAlert')}}">
            <div class="flex flex-row justify-content-between">
                <div class="relative w-full">
                    <table-vnpt
                        [fieldId]="'id'"
                        [(selectItems)]="selectItemsHistoryAlert"
                        [columns]="columnsHistoryAlert"
                        [dataSet]="dataSetHistoryAlert"
                        [options]="optionTableHistoryAlert"
                        [pageNumber]="pageNumberHistoryAlert"
                        [pageSize]="pageSizeHistoryAlert"
                        [sort]="sortHistoryAlert"
                        [loadData]="searchHistoryAlert.bind(this)"
                    ></table-vnpt>
                </div>
            </div>
        </p-tabPanel>
        <p-tabPanel header="{{tranService.translate('device.label.location')}}">
            <div class="flex flex-row justify-content-between">
                <div class="relative w-full m-3">
<!--                    <iframe [src]="safeUrl" class="w-full" style="border:0;" allowfullscreen="true" loading="lazy"-->
<!--                            height="675"-->
<!--                            referrerpolicy="no-referrer-when-downgrade"></iframe>-->
<!--                    <div class="w-full field grid pt-2">-->
<!--                        <label htmlFor="manufacturer" class="col-fixed"-->
<!--                               style="width:180px">{{ tranService.translate("device.label.location") }}</label>-->
<!--                        <div class="col">-->
<!--                <span class="p-float-label">-->
<!--                    <input pInputText-->
<!--                           class="w-full"-->
<!--                           pInputText-->
<!--                           (keydown.enter)="search($event)"-->
<!--                    />-->
<!--                    <label htmlFor="location">{{ tranService.translate("device.input.location") }}</label>-->
<!--                </span>-->
<!--                        </div>-->
<!--                    </div>-->
                    <div id="osm-map" style="height: 566px;" disabled="true"></div>
                </div>
            </div>
        </p-tabPanel>
<!--        <p-tabPanel header="{{tranService.translate('device.label.dashboard')}}">-->
<!--            <div class="flex flex-row justify-content-between">-->
<!--            </div>-->
<!--        </p-tabPanel>-->
    </p-tabView>
</p-card>

