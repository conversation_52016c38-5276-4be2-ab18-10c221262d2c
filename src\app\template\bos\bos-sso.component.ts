import {AfterContentChecked, Component, Injector, OnInit} from "@angular/core";
import {ComponentBase} from "../../component.base";
import {AccountService} from "../../service/account/AccountService";
import {CustomerService} from "../../service/customer/CustomerService";
import {ContractService} from "../../service/contract/ContractService";
import {BosService} from "../../service/sso/BosService";
import {CONSTANTS} from "../../service/comon/constants";

@Component({
    selector: "bos-sso",
    templateUrl: './bos-sso.component.html'
})
export class BosSsoComponent extends ComponentBase implements OnInit {
    constructor(
        public bosService: BosService,
        injector: Injector
    ) {
        super(injector);
    }
    ngOnInit() {
        this.route.queryParams.subscribe(params => {
            const token = params['token'];
            const subscription = params['subscription'];
            console.log('token', token);
            console.log('subscription', subscription);
            if (token && subscription) {
                this.handleLogIn(token, subscription);
            } else {
                // window.history.back();
            }

        })
    }
    handleLogIn(token, subscription) {
        let me = this;
        this.bosService.ssoLogin({bosToken: token, subscription: subscription}, (response) => {
            let token = response.id_token;
            localStorage.setItem("token", token)
            me.sessionService.updateToken(token);
            me.sessionService.current((response) => {
                me.sessionService.setData("userInfo",JSON.stringify(response));
                me.sessionService.userInfo = response;
                if(me.sessionService.userInfo?.type == CONSTANTS.USER_TYPE.BUSINESS) {
                    me.router.navigate(['/dashboard']);
                } else {
                    me.router.navigate(['/devices'])
                }
            });
        }, null, ()=>{
            me.messageCommonService.offload();
        })
    }
}
