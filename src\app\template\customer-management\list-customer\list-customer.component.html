<div
    class="vnpt flex flex-row justify-content-between align-items-center bg-white p-2 border-round"
>
<div class="">
    <div class="text-xl font-bold mb-1">{{tranService.translate("customer.label.listCustomer")}}</div>
    <p-breadcrumb class="max-w-full col-7" [model]="items" [home]="home"></p-breadcrumb>
</div>
</div>
<p-panel class="vnpt-field-set"
[styleClass]="'pt-3 pb-2'" [toggleable]="true" [header]="tranService.translate('global.text.filter')">
    <div class="grid">
        <div class="col-3">
            <span class="p-float-label">
                <input
                    class="w-full"
                    pInputText
                    id="customerCode"
                    (keyup.enter)="onSearch()"
                    [(ngModel)]="searchInfo.customerCode"
                />
                <label htmlFor="customerCode">{{
                    tranService.translate("customer.label.customerCode")
                }}</label>
            </span>
        </div>
        <div class="col-3">
            <span class="p-float-label">
                <input
                    class="w-full"
                    pInputText
                    id="customerName"
                    (keyup.enter)="onSearch()"
                    [(ngModel)]="searchInfo.customerName"
                />
                <label htmlFor="customerName">{{
                    tranService.translate("customer.label.customerName")
                }}</label>
            </span>
        </div>
        <div class="col-3">
            <span class="p-float-label">
                <p-dropdown styleClass="w-full" [showClear]="true"
                        id="type" [autoDisplayFirst]="false"
                        [(ngModel)]="searchInfo.customerType"
                        [options]="typeList"
                        optionLabel="name"
                        optionValue="value"
                ></p-dropdown>
                <label for="type">{{tranService.translate("customer.label.type")}}</label>
            </span>
        </div>
        <div class="col-3">
            <span class="p-float-label">
                <p-dropdown styleClass="w-full" [showClear]="true"
                        id="status" [autoDisplayFirst]="false"
                        [(ngModel)]="searchInfo.status"
                        [options]="statusList"
                        optionLabel="name"
                        optionValue="value"
                ></p-dropdown>
                <label for="status">{{tranService.translate("customer.label.status")}}</label>
            </span>
        </div>
        <div class="col-3">
            <span class="p-float-label">
                <input
                    class="w-full"
                    pInputText
                    id="taxCode"
                    (keyup.enter)="onSearch()"
                    [(ngModel)]="searchInfo.taxId"
                />
                <label htmlFor="taxCode">{{
                    tranService.translate("customer.label.taxCode")
                }}</label>
            </span>
        </div>
        <div class="col-3">
            <span class="p-float-label">
                <input
                    class="w-full"
                    pInputText
                    id="phoneNumber"
                    (keyup.enter)="onSearch()"
                    [(ngModel)]="searchInfo.phone"
                />
                <label htmlFor="phoneNumber">{{
                    tranService.translate("customer.label.phoneNumber")
                }}</label>
            </span>
        </div>
        <div class="col-3">
            <span class="p-float-label">
                <input class="w-full" pInputText id="email" [(ngModel)]="searchInfo.email"
                (keyup.enter)="onSearch()"/>
                <label htmlFor="email">{{tranService.translate("customer.label.email")}}</label>
            </span>
        </div>

        <div class="col-3 pb-0">
            <p-button icon="pi pi-search"
                        styleClass="p-button-rounded p-button-secondary p-button-text button-search"
                        (click)="onSearch()"
            ></p-button>
        </div>
    </div>
</p-panel>
<!-- <div>{{selectItems.length}}</div> -->
<table-vnpt
    [fieldId]="'id'"
    [(selectItems)]="selectItems"
    [columns]="columns"
    [dataSet]="dataSet"
    [options]="optionTable"
    [loadData]="search.bind(this)"
    [pageNumber]="pageNumber"
    [pageSize]="pageSize"
    [sort]="sort"
    [params]="searchInfo"
    [labelTable]="tranService.translate('customer.label.listCustomer')"
></table-vnpt>
<p-dialog [header]="contractheader" [(visible)]="isShowContract" [modal]="true" [style]="{ width: '90vw' }" [draggable]="false" [resizable]="false">
    <table-vnpt
        [fieldId]="'id'"
        [(selectItems)]="selectItemsContract"
        [columns]="columsContract"
        [dataSet]="dataSetContract"
        [options]="optionTableContract"
        [loadData]="searchContract.bind(this)"
        [pageNumber]="pageNumberContract"
        [pageSize]="pageSizeContract"
        [sort]="sortContract"
    ></table-vnpt>
</p-dialog>

<p-dialog [header]="tranService.translate('customer.label.infoCustomer')" [(visible)]="isShowModalDetail" [modal]="true" [style]="{ width: '980px' }" [draggable]="false" [resizable]="false" *ngIf="isShowModalDetail">
    <div>
        <button pButton class="p-button-outlined p-button-secondary" (click)="openListAccount()" >{{tranService.translate("customer.label.viewAccount")}}</button>
        <form action="" [formGroup]="updateCustomerForm">
            <div class="card my-3">
                <div class="grid">
                    <div class="col-3">
                        <div class="flex flex-column gap-2">
                            <label htmlFor="customerCode">{{tranService.translate("customer.label.customerCode")}}<span class="text-red-500">*</span></label>
                            <input pInputText formControlName="customerCode" id="customerCode"/>
                        </div>
                    </div>
                    <div class="col-3">
                        <div class="flex flex-column gap-2">
                            <label htmlFor="taxCode">{{tranService.translate("customer.label.taxCode")}}</label>
                            <input class="m-0" pInputText formControlName="taxId" id="taxCode"/>
                            <div *ngIf="isSubmit && updateCustomerForm.get('taxId').hasError('required')" class="text-red-500">
                                Bắt buộc
                            </div>
                            <div *ngIf="isSubmit && (updateCustomerForm.get('taxId').hasError('maxlength')||updateCustomerForm.get('taxId').hasError('minlength'))" class="text-red-500">
                                Độ dài
                            </div>
                            <div *ngIf="isSubmit && updateCustomerForm.get('taxId').hasError('invalidCharacters')" class="text-red-500">
                                Kí tự
                            </div>
                        </div>
                    </div>
                    <div class="col-3">
                        <div class="flex flex-column gap-2">
                            <label htmlFor="provinceCode">{{tranService.translate("customer.label.provinceCode")}}</label>
                            <input pInputText formControlName="provinceCode" id="provinceCode"/>
                        </div>
                    </div>
                    <div class="col-3">
                        <div class="flex flex-column gap-2">
                            <label for="type">{{tranService.translate("customer.label.type")}}</label>
                            <p-dropdown styleClass="w-full" [showClear]="true"
                                        id="type" [autoDisplayFirst]="false"
                                        formControlName="customerType"
                                        [options]="typeList"
                                        optionLabel="name"
                                        optionValue="value"
                            ></p-dropdown>
                        </div>
                    </div>
                    <div class="col-3">
                        <div class="flex flex-column gap-2">
                            <label for="status">{{tranService.translate("customer.label.status")}}</label>
                            <p-dropdown styleClass="w-full" [showClear]="true"
                                        id="status" [autoDisplayFirst]="false"
                                        formControlName="status"
                                        [options]="statusList"
                                        optionLabel="name"
                                        optionValue="value"
                            ></p-dropdown>
                        </div>
                    </div>
                </div>
            </div>
            <div class="card flex justify-content-center mb-3">
                <div class="grid w-full">
                    <div class="col-6">
                        <p-panel [header]="generalHeader" [toggleable]="true" styleClass="w-full">
                            <div class="field grid flex flex-row flex-nowrap pb-0 mb-0">
                                <label htmlFor="companyName"  style="min-width: 130px;" class="col-12 mb-2 md:col-2 md:mb-0">{{tranService.translate("customer.label.companyName")}}<span class="text-red-500">*</span></label>
                                <div class="col-12 md:col-10 flex-1 flex">
                                    <input pInputText formControlName="customerName" id="companyName" type="text" class="flex-1"/>
                                </div>
                            </div>
                            <div class="field grid flex flex-row flex-nowrap">
                                <div style="min-width: 130px;" class="col-12 mb-2 md:col-2 md:mb-0"></div>
                                <div class="col-12 md:col-10 flex-1 flex">
                                    <div *ngIf="isSubmit && (updateCustomerForm.get('customerName').hasError('maxlength')||updateCustomerForm.get('customerName').hasError('minlength'))" class="text-red-500">
                                        Độ dài
                                    </div>
                                    <div *ngIf="isSubmit && updateCustomerForm.get('customerName').hasError('invalidCharacters')" class="text-red-500">
                                        Kí tự
                                    </div>
                                </div>
                            </div>
                            <div class="field grid flex flex-row flex-nowrap pb-0">
                                <label htmlFor="phoneNumber"  style="min-width: 130px;" class="col-12 mb-2 md:col-2 md:mb-0">{{tranService.translate("customer.label.phoneNumber")}}</label>
                                <div class="col-12 md:col-10 flex-1 flex">
                                    <div class="p-inputgroup flex-1 flex">
                                        <span class="p-inputgroup-addon" style="border-radius: 12;">+84</span>
                                        <input type="text" pInputText formControlName="phone" id="phoneNumber" style="border-radius: 12;" class="flex-1"/>
                                    </div>
                                </div>
                            </div>
                            <div class="field grid flex flex-row flex-nowrap mb-0">
                                <label htmlFor="email"  style="min-width: 130px;" class="col-12 mb-2 md:col-2 md:mb-0">{{tranService.translate("customer.label.email")}}</label>
                                <div class="col-12 md:col-10 flex-1 flex">
                                    <input pInputText formControlName="email" id="email" type="email" class="flex-1"/>
                                </div>
                            </div>
                            <div class="field grid flex flex-row flex-nowrap">
                                <div style="min-width: 130px;" class="col-12 mb-2 md:col-2 md:mb-0"></div>
                                <div class="col-12 md:col-10 flex-1 flex">
                                    <div *ngIf="isSubmit && updateCustomerForm.get('email').hasError('email')" class="text-red-500">
                                        Email
                                    </div>
                                    <div *ngIf="isSubmit && (updateCustomerForm.get('email').hasError('maxlength')||updateCustomerForm.get('email').hasError('minlength'))" class="text-red-500">
                                        Độ dài
                                    </div>
                                    <div *ngIf="isSubmit && updateCustomerForm.get('email').hasError('invalidCharacters')" class="text-red-500">
                                        Kí tự
                                    </div>
                                </div>
                            </div>
                            <div class="field grid flex flex-row flex-nowrap">
                                <label htmlFor="birthday"  style="min-width: 130px;" class="col-12 mb-2 md:col-2 md:mb-0">{{tranService.translate("customer.label.birthday")}}</label>
                                <div class="col-12 md:col-10 flex-1 flex">
                                    <p-calendar styleClass="w-full" formControlName="birthday" id="birthday" type="text" class="flex-1"/>
                                </div>
                            </div>
                        </p-panel>
                    </div>
                    <div class="col-6">
                        <p-panel [header]="contactHeader" [toggleable]="true" styleClass="w-full">
                            <div class="field grid flex flex-row flex-nowrap mb-0">
                                <label htmlFor="fullName"  style="min-width: 130px;" class="col-12 mb-2 md:col-2 md:mb-0">{{tranService.translate("customer.label.fullName")}}</label>
                                <div class="col-12 md:col-10 flex-1 flex">
                                    <input pInputText formControlName="billName" id="fullName" type="text" class="flex-1"/>
                                </div>
                            </div>
                            <div class="field grid flex flex-row flex-nowrap">
                                <div style="min-width: 130px;" class="col-12 mb-2 md:col-2 md:mb-0"></div>
                                <div class="col-12 md:col-10 flex-1 flex">
                                    <div *ngIf="isSubmit && (updateCustomerForm.get('billName').hasError('maxlength')||updateCustomerForm.get('billName').hasError('minlength'))" class="text-red-500">
                                        Độ dài
                                    </div>
                                    <div *ngIf="isSubmit && updateCustomerForm.get('billName').hasError('invalidCharacters')" class="text-red-500">
                                        Kí tự
                                    </div>
                                </div>
                            </div>
                            <div class="field grid flex flex-row flex-nowrap">
                                <label htmlFor="phoneNumber"  style="min-width: 130px;" class="col-12 mb-2 md:col-2 md:mb-0">{{tranService.translate("customer.label.phoneNumber")}}</label>
                                <div class="col-12 md:col-10 flex-1 flex">
                                    <div class="p-inputgroup flex-1 flex">
                                        <span class="p-inputgroup-addon" style="border-radius: 12;">+84</span>
                                        <input type="text" formControlName="billPhone" style="border-radius: 12;" pInputText id="phoneNumber" class="flex-1"/>
                                    </div>
                                </div>
                            </div>
                            <div class="field grid flex flex-row flex-nowrap mb-0">
                                <label htmlFor="email"  style="min-width: 130px;" class="col-12 mb-2 md:col-2 md:mb-0">{{tranService.translate("customer.label.email")}}</label>
                                <div class="col-12 md:col-10 flex-1 flex">
                                    <input pInputText formControlName="billEmail" id="email" type="email" class="flex-1"/>
                                </div>
                            </div>
                            <div class="field grid flex flex-row flex-nowrap">
                                <div style="min-width: 130px;" class="col-12 mb-2 md:col-2 md:mb-0"></div>
                                <div class="col-12 md:col-10 flex-1 flex">
                                    <div *ngIf="isSubmit && updateCustomerForm.get('billEmail').hasError('email')" class="text-red-500">
                                        Email
                                    </div>
                                    <div *ngIf="isSubmit && (updateCustomerForm.get('billEmail').hasError('maxlength')||updateCustomerForm.get('billEmail').hasError('minlength'))" class="text-red-500">
                                        Độ dài
                                    </div>
                                    <div *ngIf="isSubmit && updateCustomerForm.get('billEmail').hasError('invalidCharacters')" class="text-red-500">
                                        Kí tự
                                    </div>
                                </div>
                            </div>
                            <div class="field grid flex flex-row flex-nowrap">
                                <label htmlFor="birthday"  style="min-width: 130px;" class="col-12 mb-2 md:col-2 md:mb-0">{{tranService.translate("customer.label.birthday")}}</label>
                                <div class="col-12 md:col-10 flex-1 flex">
                                    <p-calendar styleClass="w-full" id="birthday" type="text" formControlName="billBirthday" class="flex-1"/>
                                </div>
                            </div>
                        </p-panel>
                    </div>
                </div>
            </div>

            <div class="card flex justify-content-center align-items-center flex-column">
                <div class="grid w-full">
                    <div class="col-6">
                        <p-panel [header]="paymentHeader" [toggleable]="true">
                            <div class="field grid flex flex-row flex-nowrap mb-0">
                                <label htmlFor="street"  style="min-width: 130px;" class="col-12 mb-2 md:col-2 md:mb-0">{{tranService.translate("customer.label.street")}}</label>
                                <div class="col-12 md:col-10 flex-1 flex">
                                    <input pInputText formControlName="addrStreet" id="street" type="text" class="flex-1" [pTooltip]="updateCustomerForm.controls.addrStreet != null && updateCustomerForm.controls.addrStreet.getRawValue() != null ? updateCustomerForm.controls.addrStreet.getRawValue().toString() : ''" />
                                </div>
                            </div>
                            <div class="field grid flex flex-row flex-nowrap">
                                <div style="min-width: 130px;" class="col-12 mb-2 md:col-2 md:mb-0"></div>
                                <div class="col-12 md:col-10 flex-1 flex">
                                    <div *ngIf="isSubmit && (updateCustomerForm.get('addrStreet').hasError('maxlength')||updateCustomerForm.get('addrStreet').hasError('minlength'))" class="text-red-500">
                                        Độ dài
                                    </div>
                                    <div *ngIf="isSubmit && updateCustomerForm.get('addrStreet').hasError('invalidCharacters')" class="text-red-500">
                                        Kí tự
                                    </div>
                                </div>
                            </div>
                            <div class="field grid flex flex-row flex-nowrap mb-0">
                                <label htmlFor="district"  style="min-width: 130px;" class="col-12 mb-2 md:col-2 md:mb-0">{{tranService.translate("customer.label.district")}}</label>
                                <div class="col-12 md:col-10 flex-1 flex">
                                    <input pInputText id="district" formControlName="addrDist" type="text" class="flex-1"/>
                                </div>
                            </div>
                            <div class="field grid flex flex-row flex-nowrap">
                                <div style="min-width: 130px;" class="col-12 mb-2 md:col-2 md:mb-0"></div>
                                <div class="col-12 md:col-10 flex-1 flex">
                                    <div *ngIf="isSubmit && (updateCustomerForm.get('addrDist').hasError('maxlength')||updateCustomerForm.get('addrDist').hasError('minlength'))" class="text-red-500">
                                        Độ dài
                                    </div>
                                    <div *ngIf="isSubmit && updateCustomerForm.get('addrDist').hasError('invalidCharacters')" class="text-red-500">
                                        Kí tự
                                    </div>
                                </div>
                            </div>
                            <div class="field grid flex flex-row flex-nowrap mb-0">
                                <label htmlFor="city"  style="min-width: 130px;" class="col-12 mb-2 md:col-2 md:mb-0">{{tranService.translate("customer.label.city")}}</label>
                                <div class="col-12 md:col-10 flex-1 flex">
                                    <input pInputText formControlName="addrProvince" id="city" type="text" class="flex-1"/>
                                </div>
                            </div>
                            <div class="field grid flex flex-row flex-nowrap">
                                <div style="min-width: 130px;" class="col-12 mb-2 md:col-2 md:mb-0"></div>
                                <div class="col-12 md:col-10 flex-1 flex">
                                    <div *ngIf="isSubmit && (updateCustomerForm.get('addrProvince').hasError('maxlength')||updateCustomerForm.get('city').hasError('minlength'))" class="text-red-500">
                                        Độ dài
                                    </div>
                                    <div *ngIf="isSubmit && updateCustomerForm.get('addrProvince').hasError('invalidCharacters')" class="text-red-500">
                                        Kí tự
                                    </div>
                                </div>
                            </div>
                        </p-panel>
                    </div>
                    <div class="col-6">
                        <p-panel [header]="note" [toggleable]="true">
                            <div class="grid flex flex-column flex-nowrap">
                                <div class="p-3 pb-0 flex-1 flex">
                                    <textarea id="note" pInputText formControlName="note" type="text" rows="5" class="flex-1"></textarea>
                                </div>
                                <div style="padding-left: 1rem;" *ngIf="isSubmit && (updateCustomerForm.get('note').hasError('maxlength')||updateCustomerForm.get('note').hasError('minlength'))" class="text-red-500">
                                    Độ dài
                                </div>
                                <div style="padding-left: 1rem;" *ngIf="isSubmit && updateCustomerForm.get('note').hasError('invalidCharacters')" class="text-red-500">
                                    Kí tự
                                </div>
                            </div>
                        </p-panel>
                    </div>
                </div>
                <!-- <div class="flex justify-content-center gap-3">
                    <button pButton type="submit">Lưu</button>
                    <button pButton class="p-button-outlined p-button-secondary"> Huỷ</button>
                </div> -->
            </div>
        </form>
    </div>
</p-dialog>

<div class="flex justify-content-center dialog-push-group">
    <p-dialog [header]="tranService.translate('global.menu.listaccount')" [(visible)]="isShowListAccount" [modal]="true" [style]="{ width: '900px' }" [draggable]="false" [resizable]="false">
        <table-vnpt
            [fieldId]="'id'"
            [columns]="columnsAccount"
            [dataSet]="dataSetAccount"
            [options]="optionTableAccount"
            scrollHeight="300px"
        ></table-vnpt>
        <div class="flex flex-row justify-content-center align-items-center">
            <!-- <p-button styleClass="mr-2 p-button-secondary p-button-outlined" [label]="tranService.translate('global.button.cancel')" (click)="isShowListAccount = false"></p-button> -->
        </div>
    </p-dialog>
</div>
