/* You can add global styles to this file, and also import other style files */
*{
    font-size: 14px;
}
:root {
    // --blue: #1e90ff;
    // --white: #ffffff;
    --mainColor: #08539C;
    --mainColorText: blue
}
$gutter: 12px; //for primeflex grid system
// $sm	Breakpoint of screens such as phones.	576px
// $md	Breakpoint of screens such as tablets.	768px
// $lg	Breakpoint of screens such as notebook monitors.	992px
// $xl	Breakpoint of smaller screens such as desktop monitors.	1200px
// $gutter	Padding of a grid column.	.5rem
// $fieldMargin	Spacing of a field. Can be vertical of horizontal depending on form layout.	.5rem
// $fieldLabelMargin	Spacing of a field label. Can be vertical of horizontal depending on form layout.	.5rem
// $helperTextMargin	Top spacing of a helper text.	.5rem
// $spacer	Base value to use in spacing utilities, view spacing documentation for details.	.5rem
@import "assets/layout/styles/layout/layout.scss";

/* PrimeNG */
@import "../node_modules/primeng/resources/primeng.min.css";
@import "../node_modules/primeflex/primeflex.scss";
@import "../node_modules/primeicons/primeicons.css";
@import "../node_modules/suneditor/dist/css/suneditor.min";

@import "assets/styles/flags/flags.css";
@import "assets/styles/badges.scss";
@import "assets/styles/code.scss";

@import "assets/styles/custome-primeng.css";

// .p-inputtext{
//     padding: 6px 12px;
// }

// .vnpt-field-set .p-fieldset .p-fieldset-content {
//     padding-bottom: 0px;
// }

/* width */
::-webkit-scrollbar {
    width: 5px;
    height: 5px;
}

/* Track */
::-webkit-scrollbar-track {
    background: transparent;
}

/* Handle */
::-webkit-scrollbar-thumb {
    background: #888;
}

/* Handle on hover */
::-webkit-scrollbar-thumb:hover {
    background: #555;
}

.display-subElement {
    border: 1px solid #ccc;
    padding: 10px 15px;
    border-radius: 12px;
    background-color: #021C34;
    cursor: pointer; /* Prevent the click event from reaching underlying elements */
    .submenu-item{
        font-size: 16px;
        color: #959EAD;
        padding: 5px;
        border-radius: 5px;
    }
    .submenu-item:hover{
        background-color: #fff;
        color: #1616a7;
    }
    .active-route-subitem{
        color: #00e9e9;
        background-color: #032b4f;
    }
}

.active-route-small{
    .icon-small-menu{
        color: #fff;
        background-color: var(--mainColor);
    }
}

.active-route-big{
    color: white !important;
    // border-left: 1px solid white;
    .layout-menuitem-icon{
        color: white !important;
        background-color: #0a6fd0 !important;
    }
}

.icon-small-menu{
    color: #959EAD;
    font-size: 18px !important;
    background-color: #1c3349;
    display: flex;
    height: 40px;
    width: 40px;
    justify-content: center;
    align-items: center;
    border-radius: 4px;
    &:hover{
        color: #fff;
        background-color: var(--mainColor);
    }
    cursor: pointer;
}

.menu-big-list{
 overflow-y: auto;
 height: calc(100vh - (116px));
}
.menu-item-small{
    height: calc(100vh - (116px));
}

.menuItemExpanded{
    // .layout-menuitem-icon{
    //     padding: 10px;
    // }
    .active-route {
        .layout-menuitem-text{
            // font-weight: 500;
            // color: var(--primary-color);
            color: white !important;
        }
        // border-left: 1px solid white;
        .layout-menuitem-icon{
            color: white !important;
            background-color: #0a6fd0;
        }
    }
    ul{
        li{
            ul{
                .active-route{
                    border-left: 1px solid white;
                }
                li{
                    :hover{
                        background-color: white;
                        color: #1616a7!important;
                        .notification {
                            background-color: red;
                            color: white!important;
                        }
                    }
                }
            }
        }
    }
    .layout-menuitem-text{
        color: #959EAD;
        // font-size: 16px !important;
    }
    .layout-menuitem-icon{
        color: #959EAD;
        font-size: 16px !important;
        background-color: #1c3349;
        display: flex;
        height: 30px;
        width: 30px;
        justify-content: center;
        align-items: center;
        border-radius: 4px;
    }
    .layout-menuitem-icon::before{
        // padding: 40px;
    }
}

.menuItemCollaped{
    ul{
            // z-index: 1000;
            // width: 10000px;
    }
}

.font-footer-sbar{
    font-size: 12px;
}

.vnpt-field-set {
    .p-fieldset.p-fieldset-toggleable .p-fieldset-legend a {
        padding: 0.5rem;
    }
    // .p-fieldset .p-fieldset-legend{
    //     // margin-left: 92%;
    // }
    .p-panel .p-panel-header{
        background: #ffff;
    }
    .report-param-required-error {
        label  {
            margin-top: -1.15rem;
        }
    }
}

.header-cmp{
    background-color: var(--mainColor);
    color: white;
}

.menu-vnpt .p-tieredmenu{
    border: 1px solid transparent;
    width: 100%;
}

.table-column-vnpt{
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
}

.table-vnpt{
    .p-paginator-right-content {
        margin-left: 0 !important;
    }
}

.vnpt-toggle-column-empty{
    position: absolute;
    top: 0;
    left: 100%;
    z-index: 1;
    transform: translateX(-100%);
}

.vnpt-toggle-column-not-empty{
    position: absolute;
    top: 40px;
    left: 100%;
    z-index: 1;
    transform: translateX(-100%);
}

.p-menuitem-icon{
    font-size: x-large;
}

.box-table-nodata{
    height: 200px;
    background-color: white;
    text-align: center;
}

.vnpt .p-breadcrumb,.col-7,.col-5{
    border: none;
    padding: 0;
}


.vnpt {
    position: relative;
    .p-tieredmenu {
        width: fit-content;
        white-space: nowrap;
    }

    // .p-breadcrumb .p-menuitem-text{
    //     line-height: 1;
    //     font-size: 18px;
    //     font-weight: 500;
    //     text-transform: capitalize;
    // }

    .p-menuitem-icon {
        font-size: larger;
    }
}

.button-search{
    width: 36px !important;
    height: 36px !important;
    padding: 0 !important;
}

.button-toggle-column, .button-search{
    .pi{
        font-size: 24px;
    }
}

.p-autocomplete-dd .p-autocomplete-dropdown{

}


.dialog-push-group, .dialog-create-group, .dialog-vnpt{
    .p-dialog-mask {
        max-height: 100%;
        overflow: auto;
    }
    //.p-dialog{
    //    position: absolute;
    //    top: 120px !important;
    //}
    //.p-dialog-content{
    //    overflow-y:unset;
    //    // max-height: calc(100vh - 200px);
    //}
    .p-dialog {
        max-width: 95vw !important;
        max-height: 90vh !important; // Chiều cao tối đa = 90% màn hình
        overflow: hidden !important; // Giấu overflow ngoài
    }

    .p-dialog-content {
        overflow-y: auto !important;
        max-height: 70vh !important; // Giới hạn phần nội dung có thể cuộn
    }
}

.text-error-field{
    margin-top: -15px !important;
}

.sim-detail{
    .col, .col-fixed{
        padding: 0;
        padding-left: 1rem;
    }
    .p-splitter{
        margin-top: -1rem;
        border: none;
    }
}

.hidden{
    visibility: hidden;
    height: 0px;
    padding: 0px;
    margin: 0px;
}

.add-icon-size {
    font-size: 1.5em !important;
}

span.p-inputnumber{
    width: 100%;
}

.p-dialog {
    max-width: 95vw !important;
    max-height: 90vh !important; // Chiều cao tối đa = 90% màn hình
    overflow: hidden !important; // Giấu overflow ngoài
}

.p-dialog-content {
    overflow-y: auto !important;
    max-height: 70vh !important; // Giới hạn phần nội dung có thể cuộn
}
.p-datatable-wrapper::-webkit-scrollbar {
    height: 14px;
}
.p-datatable-wrapper::-webkit-scrollbar-thumb {
    background: #999;
    border-radius: 6px;
}


// // hinh nen
// ::-webkit-scrollbar{
//     width: 10px;
//     height: 10px;
//     border-radius: 5px;
// }

// // phan trong duoi thanh tien trinh
// ::-webkit-scrollbar-track{
//     background-color: #CCCCCC;
//     border-radius: 5px;
// }
// //phan tu cuon
// ::-webkit-scrollbar-thumb{
//     background-color: #555555;
//     border-radius: 5px;
// }
// ::-webkit-scrollbar-corner{
//     visibility: hidden;
// }
