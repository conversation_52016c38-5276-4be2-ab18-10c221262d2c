import { AfterContentChecked, Component, Injector, OnInit } from "@angular/core";
import { MenuItem } from "primeng/api";
import { ComponentBase } from "src/app/component.base";
import { ConfigChartService } from "src/app/service/charts/ConfigChartService";
import { CONSTANTS } from "src/app/service/comon/constants";
import { DynamicChartController } from "../common-module/charts/dynamic.chart.component";
import { FormBuilder } from "@angular/forms";

interface PositionConfig {
    // config drag position absolute
    top?: number;
    left?: number;
    widthChart?: number;
    heightChart?: number;
    //config drag row
    indexBudget?: number,
    indexInBudget?: number,
    width?: number,
    height?: number,
    marginLeft?: number,
    marginRight?: number,
    align?: string
}

interface ChartPolicy{
    id: number | null;
    idChart: any | null;
    status: number | null;
    configLevel: string | null;
    configPosition: string | null;

    chartConfig?: any;
    configLevelArray?: {below?: number, above?: number}[];
    configPositionObject?: PositionConfig
}

@Component({
    selector: "app-dashboard",
    templateUrl: "./app.dashboard.component.html",
})
export class AppDashboardComponent extends ComponentBase implements OnInit, AfterContentChecked {
    items: MenuItem[];
    home: MenuItem;
    charts: Array<any> = [];
    chartShows: Array<any> = [];
    modeView: any = CONSTANTS.MODE_VIEW.DETAIL;
    objectModeView = CONSTANTS.MODE_VIEW;

    indexBudget: number = 0;
    budgets: Array<Array<ChartPolicy>> = [];

    listChartPolicy: Array<ChartPolicy> = [];
    listDynamicChartController: {[key: number|string]:DynamicChartController} = {};

    chartPolicyDragged: ChartPolicy;

    chartPolicyForcus: ChartPolicy;
    //settingConfig
    isShowEditSetting: boolean = false;
    isShowEditSizing: boolean = false;
    configSizing: any;
    configSizingForm: any;
    aligns: Array<{name: string, value: string}> = [
        {name: this.tranService.translate('chart.label.left'), value: "justify-content-start"}, {name: this.tranService.translate('chart.label.center'), value: "justify-content-center"}, {name: this.tranService.translate('chart.label.right'), value: "justify-content-end"}, {name: this.tranService.translate('chart.label.justify'), value: "justify-content-between"}
    ];
    constructor(injector: Injector, private configChartService: ConfigChartService, private formBuilder: FormBuilder) {
        super(injector);
    }

    ngOnInit(): void {
        let me = this;
        this.home = {icon: 'pi pi-home', routerLink: '/'};
        this.items = [{label: 'Dashboard'}];
        this.chartShows = [];
        this.getListChart();
    }

    ngAfterContentChecked(): void {
        // if(this.isShowEditSetting === false){
        //     this.chartPolicyForcus = null;
        // }
        // if(this.isShowEditSizing === false){
        //     this.chartPolicyForcus = null;
        // }
    }

    getListChart(){
        let me = this;
        me.messageCommonService.onload();
        this.configChartService.getAll((response)=> {
            me.charts = response;
            //get list config
            me.configChartService.getListConfig(me.sessionService.userInfo.id, (response)=> {
                me.listChartPolicy = response.map(el => {
                    return {
                        configLevel: el.threshold,
                        configPosition: el.positionConfig,
                        id: el.id,
                        idChart: el.chartId,
                        status: el.status
                    }
                });
                me.fillInfoForChartPolicy();
                me.prepageDataShow();
                me.messageCommonService.offload();
            }, null, () => {
                me.messageCommonService.offload()
            })
        },null, (typeFinally) => {
            if(typeFinally=="error"){
                me.messageCommonService.offload();
            }
        })
    }

    fillInfoForChartPolicy(){
        let me = this;
        this.charts.forEach(chartConfig => {
            let index = me.listChartPolicy.findIndex(el => el.idChart == chartConfig.id);
            if(index != null && index != undefined && index >= 0){
                me.listChartPolicy[index].chartConfig = chartConfig;
            }else{
                me.listChartPolicy.push({
                    id: null,
                    idChart: chartConfig.id,
                    configLevel: null,
                    configPosition: null,
                    status: 1,
                    chartConfig
                })
            }
        })
        this.listChartPolicy.forEach(chartPolicy => {
            if(chartPolicy.configLevel){
                chartPolicy.configLevelArray = JSON.parse(chartPolicy.configLevel);
            }else{
                chartPolicy.configLevelArray = [{below: 0, above: null}];
            }
            if(chartPolicy.configPosition){
                chartPolicy.configPositionObject = JSON.parse(chartPolicy.configPosition);
            }else{
                chartPolicy.configPositionObject = me.createPositionDefault();
            }
        })
        this.listChartPolicy.sort((a,b) => a.configPositionObject.indexBudget > b.configPositionObject.indexBudget ? 1 : -1);
    }

    createPositionDefault():PositionConfig{
        return {
            top: 0,
            left: 0,
            widthChart: 300,
            heightChart: 400,
            align: 'justify-content-start', //justify-content-start justify-content-end justify-content-center justify-content-between
            height: 300,
            width: 400,
            indexBudget: 0,
            indexInBudget: 0,
            marginLeft: 0,
            marginRight: 0
        }
    }

    prepageDataShow(){
        let me = this;
        this.budgets = [];
        this.chartShows = [];
        this.listChartPolicy.forEach(chartPolicy => {
            if(chartPolicy.chartConfig){
                me.listDynamicChartController[chartPolicy.chartConfig.id] = new DynamicChartController();
            }
            if(chartPolicy.status == 1){
                me.chartShows.push(chartPolicy.chartConfig);
            }
            if(chartPolicy.configPositionObject.indexBudget > me.budgets.length - 1){
                me.budgets.push([]);
            }
            me.budgets[chartPolicy.configPositionObject.indexBudget].push(chartPolicy);
        })
        this.reorderBudget();
        setTimeout(() => {
            Object.keys(me.listDynamicChartController).forEach(chartId => {
                me.listDynamicChartController[chartId].reload(false, true);
            })
        })
    }

    reorderBudget() {
        for(let i = 0; i < this.budgets.length; i++){
            if(this.budgets[i].length == 0){
                this.budgets.splice(i, 1);
            }
        }
        this.budgets.forEach((budget, indexBudget) => {
            budget.forEach((chartPolicy, index) => {
                chartPolicy.configPositionObject.indexBudget = indexBudget;
                chartPolicy.configPositionObject.indexInBudget = index;
            })
        })
    }

    changeChartShow(){
        let me = this;
        let chartIdShows = (this.chartShows).map(el => el.id);
        this.budgets.forEach(budget => {
            budget.forEach(chartPolicy => {
                if(chartIdShows.includes(chartPolicy.idChart)){
                    chartPolicy.status = 1;
                    setTimeout(function(){
                        me.listDynamicChartController[chartPolicy.chartConfig.id].reload(false, false);
                    });
                }else{
                    chartPolicy.status = 0;
                }
            })
        })
    }

    saveConfig(){
        let me = this;
        me.messageCommonService.onload();
        me.reorderBudget();
        let dataSend = [];
        this.budgets.forEach(budget => {
            budget.forEach(chartPolicy => {
                chartPolicy.configLevel = JSON.stringify(chartPolicy.configLevelArray);
                chartPolicy.configPosition = JSON.stringify(chartPolicy.configPositionObject);
                dataSend.push({
                    id: chartPolicy.id,
                    chartId: chartPolicy.idChart,
                    userId: me.sessionService.userInfo.id,
                    status: chartPolicy.status,
                    threshold: chartPolicy.configLevel,
                    positionConfig: chartPolicy.configPosition
                });
            })
        });
        this.configChartService.saveConfigDashboard(dataSend, (response)=> {
            me.messageCommonService.offload();
            window.location.reload();
        }, null, () => me.messageCommonService.offload());
        // this.modeView = CONSTANTS.MODE_VIEW.DETAIL;
    }

    openEdit(){
        this.modeView = CONSTANTS.MODE_VIEW.UPDATE;
    }

    handleOpenSetting(chartId){
        let me = this;
        this.chartPolicyForcus = this.listChartPolicy[this.listChartPolicy.findIndex(el => el.idChart == chartId)];
        setTimeout(function(){
            me.isShowEditSetting = true;
        })
    }

    minusSetting(){
        this.chartPolicyForcus.configLevelArray.pop();
    }

    createSetting(){
        let lastThreshold = this.chartPolicyForcus.configLevelArray[this.chartPolicyForcus.configLevelArray.length - 1];
        if(lastThreshold!=null && lastThreshold.above > 0){
            this.chartPolicyForcus.configLevelArray.push({
                below: this.chartPolicyForcus.configLevelArray[this.chartPolicyForcus.configLevelArray.length - 1].above
            });
        }
    }

    handleOpenSizing(chartId){
        let me = this;
        this.chartPolicyForcus = this.listChartPolicy[this.listChartPolicy.findIndex(el => el.idChart == chartId)];
        this.isShowEditSizing = true;
        this.configSizing = {...this.chartPolicyForcus.configPositionObject};
        this.configSizingForm = this.formBuilder.group(this.configSizing);
    }

    closeDialog(){
        this.configSizingForm = null;
        this.chartPolicyForcus = null;
        this.configSizing = null;
    }

    applySizing(){
        this.chartPolicyForcus.configPositionObject = {...this.configSizing};
        this.listDynamicChartController[this.chartPolicyForcus.idChart].reload(false, false);
    }

    //drag drop
    createPanelDrop(){
        this.budgets.push([]);
    }

    drop(indexNew){
        if(this.modeView == CONSTANTS.MODE_VIEW.DETAIL) return;
        if(indexNew == this.chartPolicyDragged.configPositionObject.indexBudget) {
            this.chartPolicyDragged = null;
            return;
        };
        let me =this;
        if(this.chartPolicyDragged){
            let indexBudget = this.chartPolicyDragged.configPositionObject.indexBudget;
            let index = this.budgets[indexBudget].findIndex(chartPolicy => chartPolicy.idChart == me.chartPolicyDragged.idChart);
            this.budgets[indexBudget].splice(index, 1);
            this.chartPolicyDragged.configPositionObject.indexBudget = indexNew;
            this.budgets[indexNew].push(this.chartPolicyDragged);
            setTimeout(function(){
                me.listDynamicChartController[me.chartPolicyDragged.chartConfig.id].reload(false, false)
                me.chartPolicyDragged = null;
            })
        }
    }

    dragEnd(event){
        if(this.modeView == CONSTANTS.MODE_VIEW.DETAIL) return;
        this.chartPolicyDragged = null;
    }

    dragStart(chartPolicy){
        if(this.modeView == CONSTANTS.MODE_VIEW.DETAIL) return;
        this.chartPolicyDragged = chartPolicy;
    }
}
