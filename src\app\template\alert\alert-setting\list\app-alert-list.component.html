<div class="vnpt flex flex-row justify-content-between align-items-center bg-white p-2 border-round">
    <div class="">
        <div class="text-xl font-bold mb-1">{{ tranService.translate("global.menu.alertList") }}</div>
        <p-breadcrumb class="max-w-full col-7" [model]="items" [home]="home"></p-breadcrumb>
    </div>
    <div class="col-5 flex flex-row justify-content-end align-items-center">
        <p-button styleClass="p-button-info" [label]="tranService.translate('global.button.create')"
                  *ngIf="checkAuthen([CONSTANTS.PERMISSIONS.ALERT.CREATE])"
                  icon="pi pi-plus" [routerLink]="['/alerts/create']" routerLinkActive="router-link-active"></p-button>
    </div>
</div>

<form [formGroup]="formSearchAlert" (ngSubmit)="onSubmitSearch()" class="pb-2 pt-3 vnpt-field-set">
    <p-panel [toggleable]="true" [header]="tranService.translate('global.text.filter')">
        <div class="grid">
            <!-- Ten canh bao -->
            <div class="col-3">
                <span class="p-float-label">
                    <input pInputText
                           class="w-full"
                           pInputText id="name"
                           [(ngModel)]="searchInfo.name"
                           formControlName="name"
                    />
                    <label htmlFor="name">{{ tranService.translate("alert.label.name") }}</label>
                </span>
            </div>
            <!-- loai thiet bi -->
            <div class="col-3">
                <span class="p-float-label">
                    <label>{{ tranService.translate("device.label.type") }}</label>
                    <span class="relative">
                    <vnpt-select
                        [control]="controlComboSelectType"
                        class="w-full"
                        [(value)]="searchInfo.typeCode"
                        [placeholder]="tranService.translate('device.label.type')"
                        objectKey="deviceType"
                        paramKey="typeName"
                        keyReturn="typeCode"
                        displayPattern="${typeName}"
                        typeValue="primitive"
                        [isMultiChoice]="false"
                        (onchange)="onSelectedType()"
                        [paramDefault]="paramSearchType"
                        [pKeyFilter]="vietnamesePattern"
                    ></vnpt-select>
                </span>
                </span>
            </div>

            <div class="col-3">
                <span class="p-float-label">
                    <label>{{ tranService.translate("device.label.model") }}</label>
                    <span class="relative">
                    <vnpt-select
                        [control]="controlComboSelectModel"
                        class="w-full"
                        [(value)]="searchInfo.modelCode"
                        [placeholder]="tranService.translate('device.label.model')"
                        objectKey="deviceModel"
                        paramKey="modelCode"
                        keyReturn="modelCode"
                        displayPattern="${modelCode}"
                        typeValue="primitive"
                        [isMultiChoice]="false"
                        [paramDefault]="paramSearchModel"
                        [pKeyFilter]="vietnamesePattern"
                        (onchange)="onSelectedModel()"
                    ></vnpt-select>
                </span>
                </span>
            </div>
            <!-- loai -->
            <div class="col-3">
                <span class="p-float-label">
                <p-dropdown styleClass="w-full"
                            [showClear]="true" [autoDisplayFirst]="false"
                            id="ruleCategory"
                            [(ngModel)]="searchInfo.eventType"
                            formControlName="eventType"
                            [options]="eventOptions"
                            optionLabel="name"
                            optionValue="value"
                ></p-dropdown>
                    <label for="ruleCategory"> {{ tranService.translate('alert.label.rule') }}</label>
                </span>
            </div>
            <!-- Trang thai -->
            <div class="col-3">
                <span class="p-float-label">
                    <p-dropdown styleClass="w-full" [showClear]="true"
                                id="status" [autoDisplayFirst]="false"
                                [(ngModel)]="searchInfo.status"
                                formControlName="status"
                                [options]="statusAlert"
                                optionLabel="name"
                                optionValue="value"
                    ></p-dropdown>
                    <label htmlFor="status">{{ tranService.translate("alert.label.status") }}</label>
                </span>
            </div>
            <!-- muc do -->
            <div class="col-3">
                <span class="p-float-label">
                <p-dropdown styleClass="w-full"
                            [showClear]="true"
                            id="severity" [autoDisplayFirst]="false"
                            [(ngModel)]="searchInfo.severity"
                            formControlName="severity"
                            [options]="severityOptions"
                            optionLabel="name"
                            optionValue="value"
                ></p-dropdown>
                    <label for="severity"> {{ tranService.translate('alert.label.level') }}</label>
                </span>
            </div>

            <div class="col-3">
                <span class="p-float-label">
                    <input pInputText
                           class="w-full"
                           pInputText id="username"
                           [(ngModel)]="searchInfo.username"
                           formControlName="username"
                    />
                    <label for="username">{{ tranService.translate("alert.label.userCreated") }}</label>
                </span>
            </div>
<!--            <div class="col-3">-->
<!--                <span class="p-float-label">-->
<!--                    <label for="model">{{ tranService.translate("alert.label.userCreated") }}</label>-->
<!--                    <span class="relative">-->
<!--                    <vnpt-select-->
<!--                        id="model"-->
<!--                        [control]="comboSelectCustomerControl"-->
<!--                        class="w-full"-->
<!--                        [(value)]="searchInfo.userCreatedById"-->
<!--                        [placeholder]="tranService.translate('alert.label.userCreated')"-->
<!--                        objectKey="accountForSearchAlert"-->
<!--                        paramKey="username"-->
<!--                        keyReturn="id"-->
<!--                        displayPattern="${username}"-->
<!--                        typeValue="primitive"-->
<!--                        [isMultiChoice]="false"-->
<!--                        [pKeyFilter]="vietnamesePattern"-->
<!--                        [paramDefault]="paramSearchUserCreated"-->
<!--                    ></vnpt-select>-->
<!--                </span>-->
<!--                </span>-->
<!--            </div>-->
            <div class="col-3">
                <span class="p-float-label">
                    <p-calendar styleClass="w-full"
                                id="fromDate"
                                formControlName="fromDate"
                                [(ngModel)]="searchInfo.fromDate"
                                [showIcon]="true"
                                [showClear]="true"
                                dateFormat="dd/mm/yy"
                                [maxDate]="maxDateFrom"
                                (onSelect)="onChangeDateFrom(searchInfo.fromDate)"
                                (onInput)="onChangeDateFrom(searchInfo.fromDate)"
                    ></p-calendar>
                    <label htmlFor="name">{{ tranService.translate("alert.label.fromDate") }}</label>
                </span>
            </div>
            <div class="col-3">
                <span class="p-float-label">
                    <p-calendar styleClass="w-full"
                                id="toDate"
                                formControlName="toDate"
                                [(ngModel)]="searchInfo.toDate"
                                [showIcon]="true"
                                [showClear]="true"
                                dateFormat="dd/mm/yy"
                                [minDate]="minDateTo"
                                [maxDate]="maxDateTo"
                                (onSelect)="onChangeDateTo(searchInfo.toDate)"
                                (onInput)="onChangeDateTo(searchInfo.toDate)"
                    ></p-calendar>
                    <label htmlFor="name">{{ tranService.translate("alert.label.toDate") }}</label>
                </span>
            </div>


            <div class="col-3 pb-0">
                <p-button icon="pi pi-search"
                          styleClass="p-button-rounded p-button-secondary p-button-text button-search"
                          type="submit"
                ></p-button>
            </div>
        </div>
    </p-panel>
</form>

<table-vnpt
    [fieldId]="'id'"
    [(selectItems)]="selectItems"
    [columns]="columns"
    [dataSet]="dataSet"
    [options]="optionTable"
    [loadData]="search.bind(this)"
    [pageNumber]="pageNumber"
    [pageSize]="pageSize"
    [sort]="sort"
    [params]="searchInfo"
    [labelTable]="this.tranService.translate('global.menu.alertList')"
></table-vnpt>

