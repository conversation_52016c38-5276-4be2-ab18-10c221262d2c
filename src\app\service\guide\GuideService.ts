import { Inject, Injectable } from "@angular/core";
import { HttpService } from "../comon/http.service";

@Injectable({ providedIn: 'root'})
export class GuideService{
    private prefixApi: string;
    constructor(@Inject(HttpService) private httpService:HttpService) {
        this.prefixApi = "/docs";
    }

    public getProjectInfo(callback?: Function, errorCallback?:Function, finallyCallback?: Function){
        this.httpService.get(`${this.prefixApi}/projectInfo`, {}, {}, callback, errorCallback, finallyCallback);
    }

    public getListPage(callback?: Function, errorCallback?:Function, finallyCallback?: Function){
        this.httpService.get(`${this.prefixApi}/search`, {}, {}, callback, errorCallback, finallyCallback);
    }

    public getPageInfo(path: string, lang: string, callback?: Function, errorCallback?:Function, finallyCallback?: Function){
        this.httpService.get(`${this.prefixApi}/pageInfo`, {}, {path, lang}, callback, errorCallback, finallyCallback);
    }
}