<div class="vnpt flex flex-row justify-content-between align-items-center bg-white p-2 border-round">
    <div class="">
        <div class="text-xl font-bold mb-1">{{tranService.translate("global.menu.termpolicy")}}</div>
        <p-breadcrumb class="max-w-full col-7" [model]="items" [home]="home"></p-breadcrumb>
    </div>
    <div class="col-5 flex flex-row justify-content-end align-items-center">

    </div>
</div>

<div class="bg-white p-2 border-round mt-2">
    <p-tabView [scrollable]="true">
        <p-tabPanel header="{{tranService.translate('global.message.confirmationHistory')}}">
            <div *ngIf="formPersonalDataProtectionPolicy">
                <form [formGroup]="formPersonalDataProtectionPolicy">
                    <p-panel [toggleable]="false" [header]="tranService.translate('global.message.confirmationUserInfo')">
                        <div class="grid">
                            <!-- ten dang nhap -->
                            <div class="col-6">
                                <span class="p-float-label">
                                    <input pInputText
                                            class="w-full"
                                            pInputText id="username"
                                            [(ngModel)]="dataHistory.personalDataProtectionPolicy.username"
                                            formControlName="username"
                                            [readonly]="true"
                                    />
                                    <label htmlFor="username">{{tranService.translate("account.label.username")}}</label>
                                </span>
                            </div>
                            <!-- ten day du -->
                            <div class="col-6">
                                <span class="p-float-label">
                                    <input pInputText
                                            class="w-full"
                                            pInputText id="fullname"
                                            [(ngModel)]="dataHistory.personalDataProtectionPolicy.fullname"
                                            formControlName="fullname"
                                            [readonly]="true"
                                    />
                                    <label htmlFor="fullname">{{tranService.translate("account.label.fullname")}}</label>
                                </span>
                            </div>
                            <!-- email -->
                            <div class="col-6">
                                <span class="p-float-label">
                                    <input pInputText
                                            class="w-full"
                                            pInputText id="email"
                                            [(ngModel)]="dataHistory.personalDataProtectionPolicy.email"
                                            formControlName="email"
                                            [readonly]="true"
                                    />
                                    <label htmlFor="email">{{tranService.translate("account.label.email")}}</label>
                                </span>
                            </div>
                        </div>
                    </p-panel>
                    <div class="mt-4">
                        <p-panel [toggleable]="false" [header]="tranService.translate('global.message.confirmationDevice')">
                            <div class="grid">
                                <!-- loai thiet bi -->
                                <div class="col-6">
                                    <span class="p-float-label">
                                        <input pInputText
                                                class="w-full"
                                                pInputText id="deviceType"
                                                [(ngModel)]="dataHistory.personalDataProtectionPolicy.deviceType"
                                                formControlName="deviceType"
                                                [readonly]="true"
                                        />
                                        <label htmlFor="deviceType">{{tranService.translate("account.label.deviceType")}}</label>
                                    </span>
                                </div>
                                <!-- he dieu hanh -->
                                <div class="col-6">
                                    <span class="p-float-label">
                                        <input pInputText
                                                class="w-full"
                                                pInputText id="os"
                                                [(ngModel)]="dataHistory.personalDataProtectionPolicy.os"
                                                formControlName="os"
                                                [readonly]="true"
                                        />
                                        <label htmlFor="os">{{tranService.translate("account.label.os")}}</label>
                                    </span>
                                </div>
                                <!-- ip -->
                                <div class="col-6">
                                    <span class="p-float-label">
                                        <input pInputText
                                                class="w-full"
                                                pInputText id="ip"
                                                [(ngModel)]="dataHistory.personalDataProtectionPolicy.ip"
                                                formControlName="ip"
                                                [readonly]="true"
                                        />
                                        <label htmlFor="ip">{{tranService.translate("account.label.ip")}}</label>
                                    </span>
                                </div>
                                <!-- time confirm -->
                                <div class="col-6">
                                    <span class="p-float-label">
                                        <input pInputText
                                                class="w-full"
                                                pInputText id="time"
                                                [ngModel]="utilService.convertLongDateTImeToString(dataHistory.personalDataProtectionPolicy.time)"
                                                formControlName="time"
                                                [readonly]="true"
                                        />
                                        <label htmlFor="time">{{tranService.translate("account.label.time")}}</label>
                                    </span>
                                </div>
                            </div>
                        </p-panel>
                    </div>
                </form>
            </div>
            <div *ngIf="formPersonalDataProtectionPolicy" class="flex flex-row justify-content-between align-items-center mt-4">
                <p-button styleClass="mr-2 p-button-secondary p-button-outlined" [label]="tranService.translate('account.button.disagreePolicy')" (click)="confirmDisagreePersonalDataProtectionPolicy()"></p-button>
                <p-button styleClass="p-button-info" [label]="tranService.translate('account.button.viewPolicyProtectPersonalData')" [routerLink]="['/policies']" [queryParams]="{index: 0}" routerLinkActive="router-link-active" ></p-button>
            </div>
            <div *ngIf="!formPersonalDataProtectionPolicy">
                {{tranService.translate('account.text.disagreePolicy')}}
            </div>
        </p-tabPanel>
    </p-tabView>
</div>
<p-dialog [header]="this.tranService.translate('global.message.titleRejectPolicy')" [(visible)]="isShowDisagreePersonalDataProtectionPolicy" [modal]="true" [style]="{ width: '900px' }" [draggable]="false" [resizable]="false">
<div class="p-2" >

    <p class="text-justify">{{this.tranService.translate("global.message.messageRejectPolicy1")}}</p>

    <p class="text-justify">{{this.tranService.translate("global.message.messageRejectPolicy2")}}</p>

    <div class="flex flex-row justify-content-center align-items-center">
        <div>
            <p-checkbox [(ngModel)]="rejectPolicy" [binary]="true" inputId="binary"></p-checkbox>
            <span class="ml-2" style="vertical-align: 2px;">{{tranService.translate("global.message.messageRejectPolicy3")}} </span>
        </div>
        <div>
            <p-button class="p-button-secondary ml-8" [label]="tranService.translate('global.button.confirm')" [disabled]="!rejectPolicy" (onClick)="this.disagreePersonalDataProtectionPolicy()"></p-button>
        </div>
    </div>
</div>
</p-dialog>
