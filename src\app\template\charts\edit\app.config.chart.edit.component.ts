import {ChangeDetector<PERSON><PERSON>, Component, Inject, Injector, <PERSON><PERSON><PERSON>, OnInit} from "@angular/core";
import { FormBuilder } from "@angular/forms";
import { MenuItem } from "primeng/api";
import { ComponentBase } from "src/app/component.base";
import { ConfigChartService } from "src/app/service/charts/ConfigChartService";
import { ColumnInfo, OptionTable } from "../../common-module/table/table.component";
import { CONSTANTS } from "src/app/service/comon/constants";
import { ParameterInfo } from "../../reporting/report-dynamic/components/tab.report.dynamic.general";
import { ColumnInputInfo, OptionTableInput, TableInputControl } from "../../common-module/table/table.input.component";
import { ARRAY_SERVICE } from "../../common-module/combobox-lazyload/combobox.lazyload";
import { DynamicChartController, PositionInfo } from "../../common-module/charts/dynamic.chart.component";
import { Chart, ChartData, ChartDataset, ChartOptions, EasingFunction } from "chart.js";
import { commonChart } from "../../common-module/charts/common-chart";

@Component({
    templateUrl: './app.config.chart.edit.component.html',
    selector: "app-config-chart-edit"
})
export default class ConfigChartEditComponent extends ComponentBase implements OnInit {
    items: MenuItem[];
    home: MenuItem;

    // configExample: any = JSON.parse('{"id":null,"name":"Bieu do test","type":"combo","subType":"[\"multiAxis\",\"stack\"]","query":"cau truy van chung","typeQuery":"sql","datasetConfig":"[{\"id\":1,\"type\":\"bar\",\"datasetName\":\"default\",\"query\":\"\",\"typeQuery\":\"sql\",\"keyLabel\":\"label\",\"keyValue\":\"value\",\"keyValueX\":\"\",\"keyValueY\":\"\",\"keyValueR\":\"\",\"keyDataset\":\"dataset\",\"typeAnimation\":\"linear\",\"backgroundColors\":[\"#f42525FF\",\"#e94989FF\"],\"borderColors\":[],\"borderWidth\":0,\"hoverBackgroundColors\":[],\"hoverBorderColors\":[],\"hoverBorderWidth\":0,\"pointColors\":[],\"pointBorderColors\":[],\"pointBorderWidth\":0,\"barPercentage\":50,\"barThickness\":30,\"base\":0,\"barRadius\":0,\"categoryPercentage\":100,\"xAxisID\":\"\",\"yAxisID\":\"y\",\"stack\":\"group1\",\"isLineDash\":false,\"borderCapStyle\":\"\",\"borderJoinStyle\":\"\",\"fill\":false,\"tension\":0,\"pointStyle\":\"\",\"weight\":0},{\"id\":2,\"type\":\"bar\",\"datasetName\":\"cot le 1\",\"query\":\"\",\"typeQuery\":\"sql\",\"keyLabel\":\"label\",\"keyValue\":\"value\",\"keyValueX\":\"\",\"keyValueY\":\"\",\"keyValueR\":\"\",\"keyDataset\":\"dataset\",\"typeAnimation\":\"linear\",\"backgroundColors\":[\"#4a7cf2FF\"],\"borderColors\":[],\"borderWidth\":0,\"hoverBackgroundColors\":[],\"hoverBorderColors\":[],\"hoverBorderWidth\":0,\"pointColors\":[],\"pointBorderColors\":[],\"pointBorderWidth\":0,\"barPercentage\":50,\"barThickness\":30,\"base\":0,\"barRadius\":0,\"categoryPercentage\":100,\"xAxisID\":\"\",\"yAxisID\":\"y\",\"stack\":\"group1\",\"isLineDash\":false,\"borderCapStyle\":\"\",\"borderJoinStyle\":\"\",\"fill\":false,\"tension\":0,\"pointStyle\":\"\",\"weight\":0},{\"id\":3,\"type\":\"bar\",\"datasetName\":\"cot le 2\",\"query\":\"truy van rieng\",\"typeQuery\":\"sql\",\"keyLabel\":\"label\",\"keyValue\":\"value\",\"keyValueX\":\"\",\"keyValueY\":\"\",\"keyValueR\":\"\",\"keyDataset\":\"dataset\",\"typeAnimation\":\"linear\",\"backgroundColors\":[\"#4add36FF\"],\"borderColors\":[],\"borderWidth\":0,\"hoverBackgroundColors\":[],\"hoverBorderColors\":[],\"hoverBorderWidth\":0,\"pointColors\":[],\"pointBorderColors\":[],\"pointBorderWidth\":0,\"barPercentage\":50,\"barThickness\":30,\"base\":0,\"barRadius\":0,\"categoryPercentage\":100,\"xAxisID\":\"\",\"yAxisID\":\"y\",\"stack\":\"\",\"isLineDash\":false,\"borderCapStyle\":\"\",\"borderJoinStyle\":\"\",\"fill\":false,\"tension\":0,\"pointStyle\":\"\",\"weight\":0},{\"id\":4,\"type\":\"line\",\"datasetName\":\"line\",\"query\":\"\",\"typeQuery\":\"sql\",\"keyLabel\":\"label\",\"keyValue\":\"value\",\"keyValueX\":\"\",\"keyValueY\":\"\",\"keyValueR\":\"\",\"keyDataset\":\"dataset\",\"typeAnimation\":\"linear\",\"backgroundColors\":[\"#ec6922FF\"],\"borderColors\":[],\"borderWidth\":0,\"hoverBackgroundColors\":[],\"hoverBorderColors\":[],\"hoverBorderWidth\":0,\"pointColors\":[],\"pointBorderColors\":[],\"pointBorderWidth\":0,\"barPercentage\":50,\"barThickness\":30,\"base\":0,\"barRadius\":0,\"categoryPercentage\":100,\"xAxisID\":\"\",\"yAxisID\":\"y1\",\"stack\":\"\",\"isLineDash\":false,\"borderCapStyle\":\"\",\"borderJoinStyle\":\"\",\"fill\":false,\"tension\":0,\"pointStyle\":\"\",\"weight\":0}]","optionConfig":"{\"tooltip\":{\"mode\":\"index\",\"intersect\":true,\"backgroundColor\":null,\"bodyColor\":null,\"bodyAlign\":\"center\",\"bodyFont\":null,\"borderColor\":null,\"borderWidth\":0,\"bodySpacing\":0,\"boxHeight\":16,\"boxWidth\":16,\"radius\":8,\"caretSize\":2,\"titleAlign\":\"center\",\"titleColor\":null,\"titleFont\":null,\"footerColor\":null,\"patternTitle\":null,\"patternLabel\":null,\"patternFooter\":null},\"legend\":{\"titleColor\":null,\"titleDisplay\":false,\"titleFont\":null,\"titleText\":null,\"align\":\"center\",\"display\":true,\"labelColor\":null,\"labelFont\":null,\"boxWidth\":16,\"boxHeight\":16,\"position\":\"top\"},\"title\":{\"align\":\"center\",\"color\":null,\"display\":false,\"font\":null,\"position\":\"top\",\"text\":null},\"subTitle\":{\"align\":\"center\",\"color\":null,\"display\":false,\"font\":null,\"position\":\"top\",\"text\":null},\"scales\":{\"x\":{\"stacked\":false,\"beginAtZero\":false,\"position\":\"bottom\",\"tickColor\":null,\"rotation\":0,\"drawOnChartArea\":true,\"colorGrid\":null,\"borderGridColor\":null,\"isDash\":false,\"titleText\":null,\"titleAlign\":\"center\",\"titleDisplay\":true,\"titleColor\":null,\"titleFont\":null},\"y\":{\"stacked\":false,\"beginAtZero\":false,\"position\":\"bottom\",\"tickColor\":null,\"rotation\":0,\"drawOnChartArea\":true,\"colorGrid\":null,\"borderGridColor\":null,\"isDash\":false,\"titleText\":null,\"titleAlign\":\"center\",\"titleDisplay\":true,\"titleColor\":null,\"titleFont\":null},\"y1\":{\"stacked\":false,\"beginAtZero\":false,\"position\":\"bottom\",\"tickColor\":null,\"rotation\":0,\"drawOnChartArea\":true,\"colorGrid\":null,\"borderGridColor\":null,\"isDash\":false,\"titleText\":null,\"titleAlign\":\"center\",\"titleDisplay\":true,\"titleColor\":null,\"titleFont\":null}}}","filterParams":"[{\"prDisplayName\":\"Text\",\"prKey\":\"text\",\"prType\":1,\"id\":-1,\"valueList\":[],\"required\":false,\"dateType\":null,\"isAutoComplete\":false,\"isMultiChoice\":false,\"queryInfo\":{\"displayPattern\":null,\"input\":null,\"output\":null,\"objectKey\":null},\"displayPattern\":null,\"input\":null,\"objectKey\":null,\"output\":null},{\"prDisplayName\":\"Option\",\"prKey\":\"option\",\"prType\":3,\"id\":-2,\"valueList\":[{\"id\":null,\"display\":\"Value1\",\"value\":1},{\"id\":null,\"display\":\"Value2\",\"value\":2}],\"required\":false,\"dateType\":null,\"isAutoComplete\":false,\"isMultiChoice\":true,\"queryInfo\":{\"displayPattern\":null,\"input\":null,\"output\":null,\"objectKey\":null},\"displayPattern\":null,\"input\":null,\"objectKey\":null,\"output\":null},{\"prDisplayName\":\"Date\",\"prKey\":\"date\",\"prType\":2,\"id\":-3,\"valueList\":[],\"required\":false,\"dateType\":1,\"isAutoComplete\":false,\"isMultiChoice\":false,\"queryInfo\":{\"displayPattern\":null,\"input\":null,\"output\":null,\"objectKey\":null},\"displayPattern\":null,\"input\":null,\"objectKey\":null,\"output\":null}]"}');
    configExample: any = {
        "id": null,
        "name": "Bieu do test",
        "type": "combo",
        "subType": "[\"multiAxis\",\"stack\"]",
        "query": "cau truy van chung",
        "typeQuery": "sql",
        "datasetConfig": "[{\"id\":1,\"type\":\"bar\",\"datasetName\":\"default\",\"query\":\"\",\"typeQuery\":\"sql\",\"keyLabel\":\"label\",\"keyValue\":\"value\",\"keyValueX\":\"\",\"keyValueY\":\"\",\"keyValueR\":\"\",\"keyDataset\":\"dataset\",\"typeAnimation\":\"linear\",\"backgroundColors\":[\"#f42525FF\",\"#e94989FF\"],\"borderColors\":[],\"borderWidth\":0,\"hoverBackgroundColors\":[],\"hoverBorderColors\":[],\"hoverBorderWidth\":0,\"pointColors\":[],\"pointBorderColors\":[],\"pointBorderWidth\":0,\"barPercentage\":50,\"barThickness\":30,\"base\":0,\"barRadius\":0,\"categoryPercentage\":100,\"xAxisID\":\"\",\"yAxisID\":\"y\",\"stack\":\"group1\",\"isLineDash\":false,\"borderCapStyle\":\"\",\"borderJoinStyle\":\"\",\"fill\":false,\"tension\":0,\"pointStyle\":\"\",\"weight\":0},{\"id\":2,\"type\":\"bar\",\"datasetName\":\"cot le 1\",\"query\":\"\",\"typeQuery\":\"sql\",\"keyLabel\":\"label\",\"keyValue\":\"value\",\"keyValueX\":\"\",\"keyValueY\":\"\",\"keyValueR\":\"\",\"keyDataset\":\"dataset\",\"typeAnimation\":\"linear\",\"backgroundColors\":[\"#4a7cf2FF\"],\"borderColors\":[],\"borderWidth\":0,\"hoverBackgroundColors\":[],\"hoverBorderColors\":[],\"hoverBorderWidth\":0,\"pointColors\":[],\"pointBorderColors\":[],\"pointBorderWidth\":0,\"barPercentage\":50,\"barThickness\":30,\"base\":0,\"barRadius\":0,\"categoryPercentage\":100,\"xAxisID\":\"\",\"yAxisID\":\"y\",\"stack\":\"group1\",\"isLineDash\":false,\"borderCapStyle\":\"\",\"borderJoinStyle\":\"\",\"fill\":false,\"tension\":0,\"pointStyle\":\"\",\"weight\":0},{\"id\":3,\"type\":\"bar\",\"datasetName\":\"cot le 2\",\"query\":\"truy van rieng\",\"typeQuery\":\"sql\",\"keyLabel\":\"label\",\"keyValue\":\"value\",\"keyValueX\":\"\",\"keyValueY\":\"\",\"keyValueR\":\"\",\"keyDataset\":\"dataset\",\"typeAnimation\":\"linear\",\"backgroundColors\":[\"#4add36FF\"],\"borderColors\":[],\"borderWidth\":0,\"hoverBackgroundColors\":[],\"hoverBorderColors\":[],\"hoverBorderWidth\":0,\"pointColors\":[],\"pointBorderColors\":[],\"pointBorderWidth\":0,\"barPercentage\":50,\"barThickness\":30,\"base\":0,\"barRadius\":0,\"categoryPercentage\":100,\"xAxisID\":\"\",\"yAxisID\":\"y\",\"stack\":\"\",\"isLineDash\":false,\"borderCapStyle\":\"\",\"borderJoinStyle\":\"\",\"fill\":false,\"tension\":0,\"pointStyle\":\"\",\"weight\":0},{\"id\":4,\"type\":\"line\",\"datasetName\":\"line\",\"query\":\"\",\"typeQuery\":\"sql\",\"keyLabel\":\"label\",\"keyValue\":\"value\",\"keyValueX\":\"\",\"keyValueY\":\"\",\"keyValueR\":\"\",\"keyDataset\":\"dataset\",\"typeAnimation\":\"linear\",\"backgroundColors\":[\"#ec6922FF\"],\"borderColors\":[],\"borderWidth\":0,\"hoverBackgroundColors\":[],\"hoverBorderColors\":[],\"hoverBorderWidth\":0,\"pointColors\":[],\"pointBorderColors\":[],\"pointBorderWidth\":0,\"barPercentage\":50,\"barThickness\":30,\"base\":0,\"barRadius\":0,\"categoryPercentage\":100,\"xAxisID\":\"\",\"yAxisID\":\"y1\",\"stack\":\"\",\"isLineDash\":false,\"borderCapStyle\":\"\",\"borderJoinStyle\":\"\",\"fill\":false,\"tension\":0,\"pointStyle\":\"\",\"weight\":0}]",
        "optionConfig": "{\"tooltip\":{\"mode\":\"index\",\"intersect\":true,\"backgroundColor\":null,\"bodyColor\":null,\"bodyAlign\":\"center\",\"bodyFont\":null,\"borderColor\":null,\"borderWidth\":0,\"bodySpacing\":0,\"boxHeight\":16,\"boxWidth\":16,\"radius\":8,\"caretSize\":2,\"titleAlign\":\"center\",\"titleColor\":null,\"titleFont\":null,\"footerColor\":null,\"patternTitle\":null,\"patternLabel\":null,\"patternFooter\":null},\"legend\":{\"titleColor\":null,\"titleDisplay\":false,\"titleFont\":null,\"titleText\":null,\"align\":\"center\",\"display\":true,\"labelColor\":null,\"labelFont\":null,\"boxWidth\":16,\"boxHeight\":16,\"position\":\"top\"},\"title\":{\"align\":\"center\",\"color\":null,\"display\":false,\"font\":null,\"position\":\"top\",\"text\":null},\"subTitle\":{\"align\":\"center\",\"color\":null,\"display\":false,\"font\":null,\"position\":\"top\",\"text\":null},\"scales\":{\"x\":{\"stacked\":false,\"beginAtZero\":false,\"position\":\"bottom\",\"tickColor\":null,\"rotation\":0,\"drawOnChartArea\":true,\"colorGrid\":null,\"borderGridColor\":null,\"isDash\":false,\"titleText\":null,\"titleAlign\":\"center\",\"titleDisplay\":true,\"titleColor\":null,\"titleFont\":null},\"y\":{\"stacked\":false,\"beginAtZero\":false,\"position\":\"left\",\"tickColor\":null,\"rotation\":0,\"drawOnChartArea\":true,\"colorGrid\":null,\"borderGridColor\":null,\"isDash\":false,\"titleText\":null,\"titleAlign\":\"center\",\"titleDisplay\":true,\"titleColor\":null,\"titleFont\":null},\"y1\":{\"stacked\":false,\"beginAtZero\":false,\"position\":\"right\",\"tickColor\":null,\"rotation\":0,\"drawOnChartArea\":true,\"colorGrid\":null,\"borderGridColor\":null,\"isDash\":false,\"titleText\":null,\"titleAlign\":\"center\",\"titleDisplay\":true,\"titleColor\":null,\"titleFont\":null}}}",
        "filterParams": "[{\"prDisplayName\":\"Text\",\"prKey\":\"text\",\"prType\":1,\"id\":-1,\"valueList\":[],\"required\":false,\"dateType\":null,\"isAutoComplete\":false,\"isMultiChoice\":false,\"queryInfo\":{\"displayPattern\":null,\"input\":null,\"output\":null,\"objectKey\":null},\"displayPattern\":null,\"input\":null,\"objectKey\":null,\"output\":null},{\"prDisplayName\":\"Option\",\"prKey\":\"option\",\"prType\":3,\"id\":-2,\"valueList\":[{\"id\":null,\"display\":\"Value1\",\"value\":1},{\"id\":null,\"display\":\"Value2\",\"value\":2}],\"required\":false,\"dateType\":null,\"isAutoComplete\":false,\"isMultiChoice\":true,\"queryInfo\":{\"displayPattern\":null,\"input\":null,\"output\":null,\"objectKey\":null},\"displayPattern\":null,\"input\":null,\"objectKey\":null,\"output\":null},{\"prDisplayName\":\"Date\",\"prKey\":\"date\",\"prType\":2,\"id\":-3,\"valueList\":[],\"required\":false,\"dateType\":1,\"isAutoComplete\":false,\"isMultiChoice\":false,\"queryInfo\":{\"displayPattern\":null,\"input\":null,\"output\":null,\"objectKey\":null},\"displayPattern\":null,\"input\":null,\"objectKey\":null,\"output\":null}]"
    }


    dynamicConfig: {
        id: number|null,
        name: string|null,
        type: string|null,
        subType: string|null,
        query: string|null,//bigText
        schema: null,
        typeQuery: string|null,
        datasetConfig: string|null,//bigText
        optionConfig: string|null,//bigText
        filterParams: string|null//bigText
        description: string | null

        //calculate,
        subTypes?: Array<any>|null;
        filters?: Array<any>|null;
        datasetConfigs?: Array<any>|null;
        optionConfigEntity?: any|null;
    } = {
        id: null,
        name: null,
        type: null,
        subType: null,
        query: null,//bigText
        schema: null,
        typeQuery: null,
        datasetConfig: null,//bigText
        optionConfig: null,//bigText
        filterParams: null,//bigText
        description: null,

        //calculate,
        subTypes: [],
        filters: [],
        datasetConfigs: [],
        optionConfigEntity: []
    }

    // cau hinh params
    modeView: number = CONSTANTS.MODE_VIEW.CREATE;
    objectMode = CONSTANTS.MODE_VIEW;
    objectType = CONSTANTS.PARAMETER_TYPE;
    schemas: any[];
    parameterTypes: any[];
    dateTypes: any[];

    dynamicChartController: DynamicChartController = new DynamicChartController();
    dataSet: {
        content: Array<any>,
        total: number
    };
    isParamKeyExisted: boolean = false;
    isParamDisplayExisted: boolean = false;
    parameterInfo: ParameterInfo;
    formParameter: any;
    dataParams: {
        content: Array<{id: string|number|null, [key:string]:any}>,
        total: number
    }
    paramColumns: ColumnInfo[];
    optionTableListParam: OptionTable;
    modeParameter = CONSTANTS.MODE_VIEW.DETAIL;
    isShowDialogParameter: boolean = false;

    columnParamInput: ColumnInputInfo[];
    optionParamInput: OptionTableInput;
    paramInputControl: TableInputControl;
    indexFakeParameter = -1;

    listObjectKey: Array<any>;

    // end cau hinh params

    typeCharts: Array<{name: string, value: string}> = [];
    subTypeCharts: Array<{name: string, value: string}> = [];
    subTypeChartOrigins: Array<{name: string, value: string}> = [];
    typeQueries: Array<{name: string, value: string}> = [];
    keyTypeCharts: Array<{name: string, value: string}> = [];
    fontFamilys: Array<{name: string, value: string}> = [];
    tooltipModes: Array<{name: string, value: string}> = [{name: "Index", value: "index"}, {name: "Dataset", value: "dataset"}, {name: "Point", value: "point"},
    {name: "Nearest", value: "nearest"}, {name: "X", value: "x"}, {name: "Y", value: "y"}];
    actionTooltipCalculator: Array<any> = commonChart.CustomFunctionTooltip;
    actionLegendCalculator: Array<any> = commonChart.CustomFunctionLegend;
    aligns: Array<{name: string, value: string}> = [{name: "Center", value: "center"}, {name: "Start", value: "start"}, {name: "End", value: "end"}];
    titleAligns: Array<{name: string, value: string}> = [{name: "Center", value: "center"}, {name: "Left", value: "left"}, {name: "Right", value: "right"}];//dung kem vowi bodyAligns
    //"center" | "left" | "right" | "top" | "bottom" | "chartArea"
    layoutPositions: Array<{name: string, value: string}> = [{name: "Center", value: "center"}, {name: "Left", value: "left"}, {name: "Right", value: "right"},
    {name: "Top", value: "top"}, {name: "Bottom", value: "bottom"}, {name: "Chart Area", value: "chartArea"}];
    //'top' | 'left' | 'bottom' | 'right'
    positionTexts: Array<{name: string, value: string}> = [{name: "Left", value: "left"}, {name: "Right", value: "right"},
    {name: "Top", value: "top"}, {name: "Bottom", value: "bottom"}];
    //'normal' | 'italic' | 'oblique' | 'initial' | 'inherit'
    fontStyles: Array<{name: string, value: string}> = [{name: "Normal", value: "normal"}, {name: "Italic", value: "italic"}, {name: "Oblique", value: "oblique"},
    {name: "Initial", value: "initial"}, {name: "Inherit", value: "inherit"}];
    fontWeights: Array<{name: string, value: string}> = [{name: "Normal", value: "normal"}, {name: "Bold", value: "bold"}, {name: "Bolder", value: "bolder"},
    {name: "Lighter", value: "lighter"}, {name: "100", value: "100"},{name: "200", value: "200"},{name: "300", value: "300"},{name: "400", value: "400"},{name: "500", value: "500"},{name: "600", value: "600"},{name: "700", value: "700"},{name: "800", value: "800"},{name: "900", value: "900"}];

    //general config
    generalConfigForm: any;
    //param config dataChart
    dataConfig: any;
    dataConfigForm: any;
    isShowDialogDataConfig: boolean = false;
    //param config dataCommon
    dataConfigTableColumns: ColumnInfo[];
    dataConfigTableOption: OptionTable;
    dataCommonConfig: any;
    dataCommonConfigForm: any;
    indexDataConfig: number = 0;
    dataConfigContentTable: {
        content: Array<any>,
        total: number
    }

    isShowPreview: boolean = false;
    chartConfigPreview: any = {
        id: null,
        name: "",
        type: "",
        subType: "[]",
        query: "",
        typeQuery: "",
        datasetConfig: "",
        optionConfig: "",
        filterParams: ""
    };
    isChartExisted: boolean = false;

    //stype
    typeAnimations: Array<{name: string, value: string}> = [];
    borderCapStyles: Array<{name: string, value: string}> = [];
    borderJoinStyles: Array<{name: string, value: string}> = [];
    pointStyles: Array<{name: string, value: string}> = [];
    colorConfig: any;
    colorConfigForm: any = null;
    isShowChooseColor: boolean = false;

    //option config
    fontConfig: any;
    fontConfigForm: any;
    isShowConfigFont: boolean = false;
    calculatorFunctionTitle: string = null;
    calculatorFunctionLabel: string = null;
    calculatorFunctionFooter: string = null;
    calculatorFunctionLegendBody: string = null;

    tooltipConfigForm: any;
    legendConfigForm: any;
    titleConfigForm: any;
    subTitleConfigForm: any;
    scaleConfig: any;
    scaleConfigForm: any;
    listScale: Array<{name: string, value:string}> = [];
    scaleKey: string = null;

    inputRecentlyDate: {
        inputDateFrom: Date| null,
        inputDateTo: Date| null,
        minDateFrom: Date|null,
        maxDateFrom: Date| null,
        minDateTo: Date| null,
        maxDateTo: Date| null,
    }
    formInputRecentlyDate: any;
    /**
     * Đỏ - #A02C2D -> #F24444
     * Cam - #F0A35E -> #FA6E4F
     * Vàng - #F2D98D -> #F2CF59
     * Xanh lá đậm - #5BA666 -> #33cc33
     * Xanh lá nhạt - #80BF8A -> #99ff99
     * Xanh trời đậm - #80BDF2 -> #0099ff
     * Xanh trời nhạt - #A2CDF2 -> #97F2F3
     * Tím - #B8A0D9 - #8F60BF
     * Hồng - #F2A2CO -> #F277BB
     */
    backgroundColorDefault: string[] = ["#A02C2D", "#F0A35E", "#F2D98D", "#5BA666", "#80BF8A", "#80BDF2", "#A2CDF2", "#B8A0D9", "#F2A2C0"];
    hoverBackgroundColorDefault: string[] = ["#F24444","#FA6E4F","#F2CF59","#33cc33","#99ff99","#0099ff","#97F2F3","#8F60BF","#F277BB"];
    constructor(
        @Inject(ConfigChartService) private configChartService: ConfigChartService,
        private cdr: ChangeDetectorRef,
        private injector: Injector,
        private formBuilder: FormBuilder) {
        super(injector);
    }

    ngOnInit(): void {
        let me = this;
        this.home = { icon: 'pi pi-home', routerLink: '/' };
        this.items =[{label: this.tranService.translate("global.menu.charts")},
                        {label: this.tranService.translate("global.menu.chartList"), routerLink: ['/config-chart']},
                        {label: this.tranService.translate("global.button.edit")}];

        this.schemas = [
            // {value: CONSTANTS.SCHEMA.BILL, name: this.tranService.translate("report.schema.bill")},
            {value: CONSTANTS.SCHEMA.CORE, name: this.tranService.translate("report.schema.core")},
            // {value: CONSTANTS.SCHEMA.LOG, name: this.tranService.translate("report.schema.log")},
            // {value: CONSTANTS.SCHEMA.MONITOR, name: this.tranService.translate("report.schema.monitor")},
            // {value: CONSTANTS.SCHEMA.REPORT, name: this.tranService.translate("report.schema.report")},
            // {value: CONSTANTS.SCHEMA.RULE, name: this.tranService.translate("report.schema.rule")},
            // {value: CONSTANTS.SCHEMA.SIM, name: this.tranService.translate("report.schema.sim")},
            // {value: CONSTANTS.SCHEMA.ELASTICSEARCH, name: this.tranService.translate("report.schema.elasticsearch")},
        ];
        this.parameterTypes = [
            {value: CONSTANTS.PARAMETER_TYPE.NUMBER, name: this.tranService.translate("report.paramType.number")},
            {value: CONSTANTS.PARAMETER_TYPE.STRING, name: this.tranService.translate("report.paramType.string")},
            {value: CONSTANTS.PARAMETER_TYPE.DATE, name: this.tranService.translate("report.paramType.date")},
            {value: CONSTANTS.PARAMETER_TYPE.LIST_NUMBER, name: this.tranService.translate("report.paramType.listNumber")},
            {value: CONSTANTS.PARAMETER_TYPE.LIST_STRING, name: this.tranService.translate("report.paramType.listString")},
            {value: CONSTANTS.PARAMETER_TYPE.TIMESTAMP, name: this.tranService.translate("report.paramType.timestamp")},
            {value: CONSTANTS.PARAMETER_TYPE.RECENTLY_DATE_FROM, name: this.tranService.translate("report.paramType.recentlyDateFrom")},
            {value: CONSTANTS.PARAMETER_TYPE.RECENTLY_DATE_TO, name: this.tranService.translate("report.paramType.recentlyDateTo")},
        ]
        this.dateTypes = [
            {value: CONSTANTS.DATE_TYPE.MONTH, name: this.tranService.translate("report.datetype.month")},
            {value: CONSTANTS.DATE_TYPE.DATE, name: this.tranService.translate("report.datetype.date")},
            {value: CONSTANTS.DATE_TYPE.DATETIME, name: this.tranService.translate("report.datetype.datetime")},
        ]

        this.paramColumns =[{
            align: "left",
            isShow: true,
            isSort: false,
            key: "prKey",
            name: this.tranService.translate("report.label.paramKey"),
            size: "250px",
            className: "text-cyan-500 cursor-pointer",
            funcClick: (id, item)=>{
                me.isParamKeyExisted = false;
                me.isParamDisplayExisted = false;
                me.parameterInfo = {...item};
                if(me.parameterInfo.required == undefined || me.parameterInfo.required == null){
                    me.parameterInfo.required = false;
                }
                setTimeout(function(){
                    me.parameterInfo.valueList = item.valueList || [];
                    me.optionParamInput.mode = CONSTANTS.MODE_VIEW.DETAIL;
                    me.paramInputControl.reset();
                })
                if (item.prType == CONSTANTS.PARAMETER_TYPE.RECENTLY_DATE_TO) {
                    me.inputRecentlyDate.inputDateTo = this.getDateFromRecentlyDateString(item.prValue)
                } else if (item.prType == CONSTANTS.PARAMETER_TYPE.RECENTLY_DATE_FROM) {
                    me.inputRecentlyDate.inputDateFrom = this.getDateFromRecentlyDateString(item.prValue)
                }
                me.formParameter = me.formBuilder.group(me.parameterInfo);
                Object.keys(me.parameterInfo).forEach(key => {
                    me.formParameter.get(key).disable();
                })
                me.modeParameter = CONSTANTS.MODE_VIEW.DETAIL;
                me.isShowDialogParameter = true;
            }
        },{
            align: "left",
            isShow: true,
            isSort: false,
            key: "prType",
            name: this.tranService.translate("report.label.paramType"),
            size: "150px",
            funcConvertText(value) {
                if(value == CONSTANTS.PARAMETER_TYPE.NUMBER){
                    return me.tranService.translate("report.paramType.number");
                }else if(value == CONSTANTS.PARAMETER_TYPE.STRING){
                    return me.tranService.translate("report.paramType.string");
                }else if(value == CONSTANTS.PARAMETER_TYPE.DATE){
                    return me.tranService.translate("report.paramType.date");
                }else if(value == CONSTANTS.PARAMETER_TYPE.LIST_NUMBER){
                    return me.tranService.translate("report.paramType.listNumber");
                }else if(value == CONSTANTS.PARAMETER_TYPE.LIST_STRING){
                    return me.tranService.translate("report.paramType.listString");
                }else if(value == CONSTANTS.PARAMETER_TYPE.TIMESTAMP){
                    return me.tranService.translate("report.paramType.timestamp");
                } else if (value == CONSTANTS.PARAMETER_TYPE.RECENTLY_DATE_FROM) {
                    return me.tranService.translate("report.paramType.recentlyDateFrom");
                } else if (value == CONSTANTS.PARAMETER_TYPE.RECENTLY_DATE_TO) {
                    return me.tranService.translate("report.paramType.recentlyDateTo");
                }
                return "";
            },
        },{
            align: "left",
            isShow: true,
            isSort: false,
            key: "prDisplayName",
            name: this.tranService.translate("report.label.paramDisplay"),
            size: "350px"
        }];

        this.optionTableListParam = {
            hasClearSelected: true,
            hasShowChoose: false,
            hasShowIndex: false,
            hasShowJumpPage: false,
            paginator: false,
            hasShowToggleColumn: false,
            action: [{
                icon: "pi pi-external-link",
                tooltip: this.tranService.translate("global.button.edit"),
                func: (id, item) => {
                    me.isParamKeyExisted = false;
                    me.isParamDisplayExisted = false;
                    me.parameterInfo = {...item};
                    if (item.prType == CONSTANTS.PARAMETER_TYPE.RECENTLY_DATE_TO) {
                        me.inputRecentlyDate.inputDateTo = this.getDateFromRecentlyDateString(item.prValue)
                    } else if (item.prType == CONSTANTS.PARAMETER_TYPE.RECENTLY_DATE_FROM) {
                        me.inputRecentlyDate.inputDateFrom = this.getDateFromRecentlyDateString(item.prValue)
                    }
                    if(me.parameterInfo.required == undefined || me.parameterInfo.required == null){
                        me.parameterInfo.required = false;
                    }
                    setTimeout(function(){
                        me.parameterInfo = {...item};
                        me.parameterInfo.valueList = item.valueList || [];
                        me.optionParamInput.mode = CONSTANTS.MODE_VIEW.UPDATE;
                        me.paramInputControl.reset();
                    })
                    me.formParameter = me.formBuilder.group(me.parameterInfo);
                    me.modeParameter = CONSTANTS.MODE_VIEW.UPDATE;
                    me.optionParamInput.mode = CONSTANTS.MODE_VIEW.UPDATE;
                    me.paramInputControl.reset();
                    me.isShowDialogParameter = true;
                    me.cdr.detectChanges()
                },
                funcAppear(id, item) {
                    return me.modeView != CONSTANTS.MODE_VIEW.DETAIL;
                },
            },{
                icon: "pi pi-trash",
                tooltip: this.tranService.translate("global.button.delete"),
                func: (id, item) => {
                    for(let i = 0;i < me.dataParams.content.length;i++){
                        if(me.dataParams.content[i].id == id){
                            me.dataParams.content.splice(i, 1);
                            me.dynamicConfig.filterParams = JSON.stringify(me.dataParams.content);
                            break;
                        }
                    }
                },
                funcAppear(id, item) {
                    return me.modeView != CONSTANTS.MODE_VIEW.DETAIL;
                },
            }]
        }

        this.parameterInfo = {
            id: null,
            prDisplayName: null,
            prKey: null,
            prType: null,
            valueList: null,
            dateType: null,
            displayPattern: null,
            input: null,
            isAutoComplete: false,
            isMultiChoice: false,
            objectKey: null,
            output: null,
            queryInfo: null,
            required: false,
            prValue: null,
            queryParam: null,
        }
        this.formParameter = this.formBuilder.group(this.parameterInfo);

        this.inputRecentlyDate = {
            inputDateFrom: null,
            inputDateTo: null,
            minDateFrom: null,
            maxDateFrom: new Date(),
            minDateTo: null,
            maxDateTo: new Date(),
        }
        this.formInputRecentlyDate = this.formBuilder.group(this.inputRecentlyDate)
        //table of value parameters
        this.columnParamInput = [
            {
                align: 'left',
                key: "display",
                name: this.tranService.translate("report.label.valueDisplay"),
                size: "280px",
                type: CONSTANTS.PARAMETER_TYPE.STRING,
                validate: {
                    required: true,
                    maxLength: 255,
                    exists: true,
                    pattern: /^[^~`!@#\$%\^&*\(\)=\+\[\]\{\}\|\\,<>\/?]*$/,
                    messageErrorPattern: me.tranService.translate("global.message.formatContainVN")
                }
            },
            {
                align: 'left',
                key: "value",
                name: this.tranService.translate("report.label.valueDB"),
                size: "280px",
                type: this.parameterInfo.prType == CONSTANTS.PARAMETER_TYPE.LIST_STRING ? CONSTANTS.PARAMETER_TYPE.STRING : CONSTANTS.PARAMETER_TYPE.NUMBER,
                validate: {
                    required: true,
                    maxLength: 255,
                    pattern: /^[a-zA-Z0-9\-_]+$/,
                    messageErrorPattern: me.tranService.translate("global.message.formatCode"),
                    exists: true
                }
            }
        ]
        this.optionParamInput = {
            mode: CONSTANTS.MODE_VIEW.CREATE
        };
        this.paramInputControl = new TableInputControl();

        this.listObjectKey = ARRAY_SERVICE.map(el => {
            return {
                value: el.name,
                display: this.tranService.translate(el.display)
            }
        })


        this.typeCharts = [
            {
                name: this.tranService.translate("chart.type.bar"),
                value: CONSTANTS.CHART_TYPE.BAR
            },
            {
                name: this.tranService.translate("chart.type.bubble"),
                value: CONSTANTS.CHART_TYPE.BUBBLE
            },
            {
                name: this.tranService.translate("chart.type.combo"),
                value: CONSTANTS.CHART_TYPE.COMBO
            },
            {
                name: this.tranService.translate("chart.type.doughnut"),
                value: CONSTANTS.CHART_TYPE.DOUGHNUT
            },
            {
                name: this.tranService.translate("chart.type.line"),
                value: CONSTANTS.CHART_TYPE.LINE
            },
            {
                name: this.tranService.translate("chart.type.pie"),
                value: CONSTANTS.CHART_TYPE.PIE
            },
            {
                name: this.tranService.translate("chart.type.polar"),
                value: CONSTANTS.CHART_TYPE.POLAR
            },
            {
                name: this.tranService.translate("chart.type.radar"),
                value: CONSTANTS.CHART_TYPE.RADAR
            },
            {
                name: this.tranService.translate("chart.type.scatter"),
                value: CONSTANTS.CHART_TYPE.SCATTER
            }
        ]

        this.subTypeCharts = [
            {
                name: this.tranService.translate("chart.subType.horizontalBar"),
                value: CONSTANTS.CHART_SUB_TYPE.HORIZONTAL_BAR
            },
            {
                name: this.tranService.translate("chart.subType.verticalBar"),
                value: CONSTANTS.CHART_SUB_TYPE.VERTICAL_BAR
            },
            {
                name: this.tranService.translate("chart.subType.stackedBar"),
                value: CONSTANTS.CHART_SUB_TYPE.STACKED_BAR
            },
            {
                name: this.tranService.translate("chart.subType.groupBar"),
                value: CONSTANTS.CHART_SUB_TYPE.GROUP_BAR
            },
            {
                name: this.tranService.translate("chart.subType.multiAxis"),
                value: CONSTANTS.CHART_SUB_TYPE.MULTI_AXIS
            },
            {
                name: this.tranService.translate("chart.subType.threshold"),
                value: CONSTANTS.CHART_SUB_TYPE.THRESHOLD
            },
            {
                name: this.tranService.translate("chart.subType.sliderThreshold"),
                value: CONSTANTS.CHART_SUB_TYPE.SLIDER_THRESHOLD
            },
        ]

        this.subTypeChartOrigins = [...this.subTypeCharts];

        this.typeQueries = [
            {
                name: "SQL",
                value: "sql"
            },
            {
                name: "NOSQL",
                value: "nosql"
            },
            {
                name: "RSQL",
                value: "rsql"
            }
        ]

        //config chart

        this.dataConfigTableColumns = [
            {
                align: "left",
                isShow: true,
                isSort: false,
                key: "datasetName",
                name: this.tranService.translate("chart.label.datasetName"),
                size: "80%",
                style:{
                    cursor: "pointer",
             color: "var(--mainColorText)"
                },
                funcClick(id, item) {
                    me.prepareDataConfig(item)
                },
            }
        ];
        this.dataConfigTableOption = {
            hasClearSelected: true,
            action: [
                {
                    icon: "pi pi-pencil",
                    tooltip: this.tranService.translate("global.button.edit"),
                    func(id, item, ...args) {
                        me.prepareDataConfig(item);
                    },
                },
                {
                    icon: "pi pi-trash",
                    tooltip: this.tranService.translate("global.button.remove"),
                    func(id, item, ...args) {
                        me.dynamicConfig.datasetConfigs = me.dynamicConfig.datasetConfigs.filter(el => el.id != id);
                        me.dataConfigContentTable = {
                            content: me.dynamicConfig.datasetConfigs,
                            total: me.dynamicConfig.datasetConfigs.length
                        }
                    },
                }
            ],
            hasShowChoose: false,
            hasShowIndex: true,
            hasShowJumpPage: false,
            hasShowToggleColumn: false,
            paginator: false
        }

        //style
        this.typeAnimations = ['linear','easeInQuad','easeOutQuad','easeInOutQuad','easeInCubic','easeOutCubic','easeInOutCubic','easeInQuart',
            'easeOutQuart','easeInOutQuart','easeInQuint','easeOutQuint','easeInOutQuint','easeInSine','easeOutSine','easeInOutSine',
            'easeInExpo','easeOutExpo','easeInOutExpo','easeInCirc','easeOutCirc','easeInOutCirc','easeInElastic','easeOutElastic',
            'easeInOutElastic','easeInBack','easeOutBack','easeInOutBack','easeInBounce','easeOutBounce','easeInOutBounce'].map(el => {
                let name = "";
                for (let i = 0; i < el.length; i++) {
                    if(i == 0 || el.charCodeAt(i) <=90){
                        if(i == 0){
                            name = name + el.charAt(i).toUpperCase();
                        }else{
                            name = name + " " + el.charAt(i).toUpperCase();
                        }
                    }else{
                        name += el.charAt(i);
                    }
                }
                return {
                    name: name,
                    value: el
                }
        });
        this.borderCapStyles = ["butt", "round", "square"].map(el => {
                let name = "";
                for (let i = 0; i < el.length; i++) {
                    if(i == 0 || el.charCodeAt(i) <=90){
                        if(i == 0){
                            name = name + el.charAt(i).toUpperCase();
                        }else{
                            name = name + " " + el.charAt(i).toUpperCase();
                        }
                    }else{
                        name += el.charAt(i);
                    }
                }
                return {
                    name: name,
                    value: el
                }
        });
        this.borderJoinStyles = ["bevel", "miter", "round"].map(el => {
                let name = "";
                for (let i = 0; i < el.length; i++) {
                    if(i == 0 || el.charCodeAt(i) <=90){
                        if(i == 0){
                            name = name + el.charAt(i).toUpperCase();
                        }else{
                            name = name + " " + el.charAt(i).toUpperCase();
                        }
                    }else{
                        name += el.charAt(i);
                    }
                }
                return {
                    name: name,
                    value: el
                }
        });
        this.pointStyles = [ 'circle', 'cross','crossRot','dash','line','rect','rectRounded','rectRot','star','triangle'].map(el => {
            let name = "";
            for (let i = 0; i < el.length; i++) {
                if(i == 0 || el.charCodeAt(i) <=90){
                    if(i == 0){
                        name = name + el.charAt(i).toUpperCase();
                    }else{
                        name = name + " " + el.charAt(i).toUpperCase();
                    }
                }else{
                    name += el.charAt(i);
                }
            }
            return {
                name: name,
                value: el
            }
        });

        // option config
        this.fontFamilys = [
            {name: 'Georgia', value: 'Georgia'}, {name: 'Palatino Linotype', value: 'Palatino Linotype'}, {name: 'Times New Roman', value: 'Times New Roman'},{name: 'Serif', value: 'serif'},{name: 'Arial', value: 'Arial'},
            {name: 'Arial Black', value: 'Arial Black'},{name: 'Cursive', value: 'cursive'},{name: 'Impact', value: 'Impact'},{name: 'Lucida Sans Unicode', value: 'Lucida Sans Unicode'},{name: 'Tahoma', value: 'Tahoma'},
            {name: 'Trebuchet MS', value: 'Trebuchet MS'},{name: 'Helvetica', value: 'Helvetica'},{name: 'Verdana', value: 'Verdana'},{name: 'Sans Serif', value: 'sans-serif'},{name: 'Courier', value: 'Courier'},
            {name: 'Lucida Console', value: 'Lucida Console'},{name: 'Monospace', value: 'monospace'}
        ]

        this.getOne();
    }

    getOne(){
        let chartId = parseInt(this.route.snapshot.paramMap.get('chartId'));
        if(chartId == 0){
            this.dynamicConfig = {...this.configExample, id: chartId};
            this.initData();
        }else{
            let me = this;
            me.messageCommonService.onload();
            this.configChartService.detail(chartId, (response) => {
                me.dynamicConfig = {...response}
                me.initData();
                me.messageCommonService.offload();
            }, null, () => {
                me.messageCommonService.offload();
            })
        }
    }

    initData(){
        let me = this;
        if(this.dynamicConfig == null || this.dynamicConfig == undefined){
            this.dynamicConfig = {
                id: null,
                name: null,
                type: null,
                subType: null,
                query: null,//bigText,
                schema: null,
                typeQuery: null,
                datasetConfig: null,//bigText
                optionConfig: null,//bigText
                filterParams: null,//bigText
                description: null
            }
        }
        if(this.dynamicConfig.subType){
            this.dynamicConfig.subTypes = JSON.parse(this.dynamicConfig.subType);
        }else{
            this.dynamicConfig.subTypes = [];
        }
        if(this.dynamicConfig.filterParams){
            this.dynamicConfig.filters = JSON.parse(this.dynamicConfig.filterParams);
            this.dynamicConfig.filters.forEach((el, index)=>{
                el.id = index+1;
            })
        }else{
            this.dynamicConfig.filters = [];
        }
        this.dataParams = {
            content: this.dynamicConfig.filters,
            total: this.dynamicConfig.filters.length
        }
        if(this.dynamicConfig.datasetConfig){
            this.dynamicConfig.datasetConfigs = JSON.parse(this.dynamicConfig.datasetConfig);
            let indexs = this.dynamicConfig.datasetConfigs.map(el => el.id);
            this.indexDataConfig = Math.max(...indexs);
        }else{
            this.dynamicConfig.datasetConfigs = [];
        }
        this.dataConfigContentTable = {
            content: this.dynamicConfig.datasetConfigs,
            total: this.dynamicConfig.datasetConfigs.length
        }
        if(this.dynamicConfig.optionConfig){
            this.dynamicConfig.optionConfigEntity = JSON.parse(this.dynamicConfig.optionConfig);
            if(!('patternBody' in this.dynamicConfig.optionConfigEntity.legend)){
                this.dynamicConfig.optionConfigEntity.legend['patternBody'] = null
            }
        }else{
            this.dynamicConfig.optionConfigEntity = {
                tooltip: this.createTooltipConfigDefault(),
                legend: this.createLegendConfigDefault(),
                title: this.createTitleConfigDefault(),
                subTitle: this.createSubTitleConfigDefault(),
                scales: {
                    x: this.createScaleXConfigDelfault(),
                    y: this.createScaleYConfigDefault()
                },
                boxvalue: this.createBoxValueConfigDefault()
            }
        }
        if(!this.dynamicConfig.optionConfigEntity.boxvalue){
            this.dynamicConfig.optionConfigEntity["boxvalue"] = this.createBoxValueConfigDefault();
        }
        this.generalConfigForm = this.formBuilder.group({
            chartName: this.dynamicConfig.name,
            chartType: this.dynamicConfig.type,
            description: this.dynamicConfig.description,
            chartSubType: this.subTypeCharts.filter(el => this.dynamicConfig.subTypes.includes(el.value))
        })
        this.tooltipConfigForm = this.formBuilder.group({...this.dynamicConfig.optionConfigEntity.tooltip, calculatorFunctionTitle: this.calculatorFunctionTitle,
            calculatorFunctionLabel: this.calculatorFunctionLabel,
            calculatorFunctionFooter: this.calculatorFunctionFooter});
        this.legendConfigForm = this.formBuilder.group({
            ...this.dynamicConfig.optionConfigEntity.legend,
            calculatorFunctionLegendBody: this.calculatorFunctionLegendBody
        });
        this.titleConfigForm = this.formBuilder.group(this.dynamicConfig.optionConfigEntity.title);
        this.subTitleConfigForm = this.formBuilder.group(this.dynamicConfig.optionConfigEntity.subTitle);
        me.listScale = [];
        Object.keys(this.dynamicConfig.optionConfigEntity.scales).forEach(el => {
            me.listScale.push({
                name: el.toUpperCase(),
                value: el
            })
        })
    }

    createDynamicChart(){
        let dataSend = {
            id: this.dynamicConfig.id,
            name: this.dynamicConfig.name,
            type: this.dynamicConfig.type,
            subType: JSON.stringify(this.dynamicConfig.subTypes),
            query: this.dynamicConfig.query,
            typeQuery: this.dynamicConfig.typeQuery,
            datasetConfig: JSON.stringify(this.dynamicConfig.datasetConfigs),
            optionConfig: JSON.stringify(this.dynamicConfig.optionConfigEntity),
            filterParams: this.dynamicConfig.filterParams
        };
        // let dataSend = {
        //     id: this.configExample.id,
        //     name: this.configExample.name,
        //     type: this.configExample.type,
        //     subType: JSON.stringify(this.configExample.subTypes),
        //     query: this.configExample.query,
        //     typeQuery: this.configExample.typeQuery,
        //     datasetConfig: JSON.stringify(this.configExample.datasetConfigs),
        //     optionConfig: JSON.stringify(this.configExample.optionConfigEntity),
        //     filterParams: this.configExample.filterParams
        // };
        return dataSend;
    }

    openPreview(){
        let me = this;
        this.chartConfigPreview = {
            id: this.dynamicConfig.id,
            name: this.dynamicConfig.name,
            type: this.dynamicConfig.type,
            subType: JSON.stringify(this.dynamicConfig.subTypes),
            description: this.dynamicConfig.description,
            query: this.dynamicConfig.query,
            typeQuery: this.dynamicConfig.typeQuery,
            datasetConfig: JSON.stringify(this.dynamicConfig.datasetConfigs),
            optionConfig: JSON.stringify(this.dynamicConfig.optionConfigEntity),
            filterParams: JSON.stringify(this.dataParams.content)
        };

        this.isShowPreview = true;
        if(this.dynamicConfig.id == 0){
            setTimeout(()=> me.dynamicChartController.reload(true, true));
        }else{
            setTimeout(()=> me.dynamicChartController.reload(false, true));
        }
    }

    save(){
        let me = this;
        let dataSend = {
            id: this.dynamicConfig.id,
            name: this.dynamicConfig.name,
            type: this.dynamicConfig.type,
            schema: this.dynamicConfig.schema,
            subType: JSON.stringify(this.dynamicConfig.subTypes),
            description: this.dynamicConfig.description,
            query: this.dynamicConfig.query,
            typeQuery: this.dynamicConfig.typeQuery,
            datasetConfig: JSON.stringify(this.dynamicConfig.datasetConfigs),
            optionConfig: JSON.stringify(this.dynamicConfig.optionConfigEntity),
            filterParams: JSON.stringify(this.dataParams.content)
        };
        if(dataSend.id == 0){

        }else {
            this.messageCommonService.onload();
            this.configChartService.update(dataSend, (response)=>{
                me.messageCommonService.success(me.tranService.translate("global.message.success"))
                this.messageCommonService.offload();
                this.router.navigate(["/config-chart"]);
            }, null, ()=>{
                this.messageCommonService.offload();
            })
        }
    }

    closeForm(){
        this.router.navigate(['/config-chart']);
    }

    changeIsAutoComplete(){

    }

    changeParamType(){
        let me = this;
        this.columnParamInput = [
            {
                align: 'left',
                key: "display",
                name: this.tranService.translate("report.label.valueDisplay"),
                size: "280px",
                type: CONSTANTS.PARAMETER_TYPE.STRING,
                validate: {
                    required: true,
                    maxLength: 255,
                    exists: true,
                    pattern: /^[^~`!@#\$%\^&*\(\)=\+\[\]\{\}\|\\,<>\/?]*$/,
                    messageErrorPattern: me.tranService.translate("global.message.formatContainVN")
                }
            },
            {
                align: 'left',
                key: "value",
                name: this.tranService.translate("report.label.valueDB"),
                size: "280px",
                type: this.parameterInfo.prType == CONSTANTS.PARAMETER_TYPE.LIST_STRING ? CONSTANTS.PARAMETER_TYPE.STRING : CONSTANTS.PARAMETER_TYPE.NUMBER,
                validate: {
                    required: true,
                    maxLength: 255,
                    pattern: /^[a-zA-Z0-9\-_]+$/,
                    messageErrorPattern: me.tranService.translate("global.message.formatCode"),
                    exists: true
                }
            }
        ]
        if(this.parameterInfo.prType != CONSTANTS.PARAMETER_TYPE.LIST_NUMBER && this.parameterInfo.prType != CONSTANTS.PARAMETER_TYPE.LIST_STRING){
            this.parameterInfo.isMultiChoice = false;
        }
        if(this.parameterInfo.prType != CONSTANTS.PARAMETER_TYPE.STRING && this.parameterInfo.prType != CONSTANTS.PARAMETER_TYPE.LIST_STRING){
            this.parameterInfo.isAutoComplete = false;
        }
        this.parameterInfo.valueList = [];
        if(this.paramInputControl.reset){
            this.paramInputControl.reset();
        }
    }

    checkExistParamKey(){
        for(let i = 0;i< this.dynamicConfig.filters.length;i++){
            if(this.dynamicConfig.filters[i].prKey == this.parameterInfo.prKey && this.dynamicConfig.filters[i].id != this.parameterInfo.id){
                this.isParamKeyExisted = true;
                return;
            }
        }
        this.isParamKeyExisted = false;
    }

    checkExistParamDisplay(){
        for(let i = 0;i< this.dynamicConfig.filters.length;i++){
            if(this.dynamicConfig.filters[i].prDisplayName == this.parameterInfo.prDisplayName && this.dynamicConfig.filters[i].id != this.parameterInfo.id){
                this.isParamDisplayExisted = true;
                return;
            }
        }
        this.isParamDisplayExisted = false;
    }

    getHeaderParameter(){
        if(this.modeParameter == CONSTANTS.MODE_VIEW.CREATE){
            return this.tranService.translate("report.text.createParameter");
        }else if(this.modeParameter == CONSTANTS.MODE_VIEW.UPDATE){
            return this.tranService.translate("report.text.updateParameter");
        }else{
            return this.tranService.translate("report.text.detailParameter");
        }
    }

    openCreateParameter(){
        this.parameterInfo = {
            prDisplayName: null,
            prKey: null,
            prType: null,
            id: null,
            valueList: [],
            required: false,
            dateType: null,
            isAutoComplete: false,
            isMultiChoice: false,
            queryInfo: null,
            displayPattern: null,
            input: null,
            objectKey: null,
            output: null,
            prValue: null,
            queryParam: null,
        }
        this.formParameter = this.formBuilder.group(this.parameterInfo);
        this.isParamDisplayExisted = false;
        this.isParamKeyExisted = false;
        this.optionParamInput.mode = CONSTANTS.MODE_VIEW.CREATE;
        this.modeParameter = CONSTANTS.MODE_VIEW.CREATE;
        this.isShowDialogParameter = true;
        this.paramInputControl.reset();
    }
    saveParameter(){
        if(this.checkInvalidFormParameter() == true) return;
        let data = {...this.parameterInfo};
        data.queryInfo = {
            displayPattern: data.displayPattern,
            input: data.input,
            output: data.output,
            objectKey: data.objectKey,
            queryParam: data.queryParam
        }
        this.isShowDialogParameter = false;
        if(data.id == null){
            data.id = this.indexFakeParameter --;
            this.dynamicConfig.filters.push(data);
        }else{
            for(let i = 0;i < this.dynamicConfig.filters.length; i++){
                if(this.dynamicConfig.filters[i].id == data.id){
                    this.dynamicConfig.filters[i] = data;
                    break;
                }
            }
        }
        this.dynamicConfig.filterParams = JSON.stringify(this.dynamicConfig.filters);
    }

    checkInvalidFormParameter():boolean{
        if(this.formParameter.invalid) return true;
        if(this.isParamDisplayExisted || this.isParamKeyExisted) return true;
        if(this.parameterInfo.prType == CONSTANTS.PARAMETER_TYPE.LIST_NUMBER || this.parameterInfo.prType == CONSTANTS.PARAMETER_TYPE.LIST_STRING){
            if(this.parameterInfo.isAutoComplete == false){
                if(this.parameterInfo.valueList == null || this.parameterInfo.valueList == undefined || this.parameterInfo.valueList.length == 0) return true;
            }
        }
        return false;
    }


    //config chart
    checkExistChart(){

    }

    changeChartType(){
        if(this.dynamicConfig.type == null) return;
        let subType = [{
            name: this.tranService.translate("chart.subType.threshold"),
            value: CONSTANTS.CHART_SUB_TYPE.THRESHOLD
        }];
        if(this.dynamicConfig.type == CONSTANTS.CHART_TYPE.BAR){
            subType.push({
                name: this.tranService.translate("chart.subType.horizontalBar"),
                value: CONSTANTS.CHART_SUB_TYPE.HORIZONTAL_BAR
            },
            {
                name: this.tranService.translate("chart.subType.verticalBar"),
                value: CONSTANTS.CHART_SUB_TYPE.VERTICAL_BAR
            })
        }
        if([CONSTANTS.CHART_TYPE.BAR, CONSTANTS.CHART_TYPE.LINE, CONSTANTS.CHART_TYPE.COMBO].includes(this.dynamicConfig.type)){
            subType.push({
                name: this.tranService.translate("chart.subType.multiAxis"),
                value: CONSTANTS.CHART_SUB_TYPE.MULTI_AXIS
            },
                {
                    name: this.tranService.translate("chart.subType.sliderThreshold"),
                    value: CONSTANTS.CHART_SUB_TYPE.SLIDER_THRESHOLD
                },
            )
            if(this.dynamicConfig.type != CONSTANTS.CHART_TYPE.LINE){
                subType.push({
                    name: this.tranService.translate("chart.subType.stackedBar"),
                    value: CONSTANTS.CHART_SUB_TYPE.STACKED_BAR
                },
                {
                    name: this.tranService.translate("chart.subType.groupBar"),
                    value: CONSTANTS.CHART_SUB_TYPE.GROUP_BAR
                })
            }
        }
        if(this.dynamicConfig.type == CONSTANTS.CHART_TYPE.COMBO){
            this.keyTypeCharts= this.typeCharts.filter(el => el.value != CONSTANTS.CHART_TYPE.COMBO);
        }else{
            this.keyTypeCharts= this.typeCharts.filter(el => el.value == this.dynamicConfig.type);
        }
        this.subTypeCharts = [...subType]
    }

    changeChartSubType(){
        if(this.dynamicConfig.type == null) return;
        if(this.dynamicConfig.subTypes == null) return;
        if(this.dynamicConfig.subTypes.includes(CONSTANTS.CHART_SUB_TYPE.HORIZONTAL_BAR)
            && this.dynamicConfig.subTypes.includes(CONSTANTS.CHART_SUB_TYPE.VERTICAL_BAR)){
            this.dynamicConfig.subTypes = this.dynamicConfig.subTypes.filter(el => el != CONSTANTS.CHART_SUB_TYPE.HORIZONTAL_BAR);
        }
    }

    prepareDataConfig(dataConfig?: any){
        if(!this.dynamicConfig.type) return;
        if(dataConfig){
            this.dataConfig = {...dataConfig};
        }else{
            this.dataConfig = {
                id: null,
                type: null,
                datasetName: "",
                query: "",
                typeQuery: "sql",
                schema: null,
                keyLabel: "",
                keyValue: "",
                keyValueX: "",
                keyValueY: "",
                keyValueR: "",
                keyDataset: "",
                typeAnimation: "",
                backgroundColors: [],
                borderColors:  [],
                borderWidth: 0,
                hoverBackgroundColors: [],
                hoverBorderColors: [],
                hoverBorderWidth: 0,
                pointColors: [],
                pointBorderColors: [],
                pointBorderWidth: 0,
                barPercentage: 0.5,
                barThickness: 30,
                base: 0,
                barRadius: 0,
                categoryPercentage: 1,
                xAxisID: "",
                yAxisID: "",
                stack: "",
                isLineDash: false,
                borderCapStyle: "",
                borderJoinStyle: "",
                fill: false,
                tension: 0,
                pointStyle: "",
                weight: 0,
                // keyMaxValue: "",
            }
        }
        let dataForm = {...this.dataConfig};
        if(!dataForm["schema"]){
            dataForm["schema"] = null;
        }
        delete dataForm.backgroundColors;
        delete dataForm.borderColors;
        delete dataForm.hoverBackgroundColors;
        delete dataForm.hoverBorderColors;
        delete dataForm.pointColors;
        delete dataForm.pointBorderColors;

        this.dataConfigForm = this.formBuilder.group(dataForm);
        this.isShowDialogDataConfig = true;
    }

    createTooltipConfigDefault(){
        return {
            mode: "index",
            intersect: true,
            backgroundColor: null,
            bodyColor: null,
            bodyAlign: "center",
            bodyFont: null,
            borderColor: null,
            borderWidth: 0,
            bodySpacing: 0,
            boxHeight: 16,
            boxWidth: 16,
            radius: 8,
            caretSize: 2,
            titleAlign: "center",
            titleColor: null,
            titleFont: null,
            footerColor: null,
            patternTitle: null,
            patternLabel: null,
            patternFooter: null
        }
    }

    createLegendConfigDefault(){
        return {
            titleColor: null,
            titleDisplay: false,
            titleFont: null,
            titleText: null,
            align: "center",
            display: true,
            labelColor: null,
            labelFont: null,
            boxWidth: 16,
            boxHeight: 16,
            position: "top",
            patternBody: null
        }
    }

    createTitleConfigDefault(){
        return {
            align: "center",
            color: null,
            display: false,
            font: null,
            position: 'top',
            text: null
        }
    }

    createSubTitleConfigDefault(){
        return {
            align: "center",
            color: null,
            display: false,
            font: null,
            position: 'top',
            text: null
        }
    }

    createScaleXConfigDelfault(){
        let scale = {
            stacked: false,
            beginAtZero: false,
            position: 'bottom',
            tickColor: null,
            rotation: 0,
            drawOnChartArea: true,
            colorGrid: null,
            borderGridColor: null,
            isDash: false,
            titleText: null,
            titleAlign: "center",
            titleDisplay: true,
            titleColor: null,
            titleFont: null
        }
        return scale;
    }

    createScaleYConfigDefault(isAddScale: boolean = false){
        let scale = {
            stacked: false,
            beginAtZero: false,
            position: 'bottom',
            tickColor: null,
            rotation: 0,
            drawOnChartArea: true,
            colorGrid: null,
            borderGridColor: null,
            isDash: false,
            titleText: null,
            titleAlign: "center",
            titleDisplay: true,
            titleColor: null,
            titleFont: null
        }

        if(isAddScale){
            let me = this;
            me.listScale = [];
            let max = null;
            Object.keys(this.dynamicConfig.optionConfigEntity.scales).forEach(el => {
                if(max == null){
                    max = el;
                }else if(el > max){
                    max = el;
                }
                me.listScale.push({
                    name: el.toUpperCase(),
                    value: el
                })
            });
            if(max.indexOf("y") >= 0){
                let key = `y1`;
                if(max != 'y'){
                    key = `y${parseInt(max.replace('y','')) + 1}`;
                }

                this.dynamicConfig.optionConfigEntity.scales[key] = scale;
                this.listScale.push({
                    name: key.toUpperCase(),
                    value: key
                });
                setTimeout(function(){
                    me.scaleKey = key;
                    me.changeScaleKey();
                })
            }
        }
        return scale;
    }

    createBoxValueConfigDefault(){
        return {
            isShowBoxValue: false
        }
    }

    saveDataConfig(){
        if(this.dataConfig.id > 0){
            for(let i = 0;i < this.dynamicConfig.datasetConfigs.length;i++){
                if(this.dataConfig.id == this.dynamicConfig.datasetConfigs[i].id){
                    this.dynamicConfig.datasetConfigs[i] = this.dataConfig;
                }
            }
        }else{
            this.dataConfig.id = ++ this.indexDataConfig;
            this.dynamicConfig.datasetConfigs.push(this.dataConfig);
        }
        this.dataConfigContentTable = {
            content: this.dynamicConfig.datasetConfigs,
            total: this.dynamicConfig.datasetConfigs.length
        }
        this.isShowDialogDataConfig = false;
    }

    getStringColor(color: string, opacity: number){
        if(color == null) return null;
        let valueHex = ['0','1','2','3','4','5','6','7','8','9','A','B','C','D','E','F'];
        return `${color}${valueHex[Math.floor(opacity/16)]}${valueHex[opacity%16]}`;
    }

    parseColor(value: string){
        let valueHex = ['0','1','2','3','4','5','6','7','8','9','A','B','C','D','E','F'];
        let color = value.substring(0,7);
        let opacity = value.substring(7,value.length);
        let opacityNumber = 255;
        if(opacity){
            opacityNumber = valueHex.findIndex(el => el == opacity.charAt(0)) * 16 + valueHex.findIndex(el => el == opacity.charAt(1));
        }
        return {color, opacityNumber};
    }

    getValueByPath(path: string){
        let arrKeys = path.split(".");
        let value = this;
        if(arrKeys.length > 0){
            arrKeys.forEach(key => value = value[key]);
        }
        return value;
    }

    setValueByPath(path: string, value:any){
        let arrKeys = path.split(".");
        let object = this;
        if(arrKeys.length > 0){
            arrKeys.forEach((key, index) => {
                if(index != arrKeys.length - 1){
                    object = object[key]
                }else{
                    object[arrKeys[arrKeys.length - 1]] = value;
                }
            });
        }
    }

    openChooseColorBox(index,key, path){
        this.colorConfig = {
            index,
            key,
            color: "",
            opacity: 255,
            path
        }
        let value = null;
        if(index != null && index != undefined && key != null && key != undefined){
            value = this.dataConfig[key][index];
        }else if(path != null && path != undefined){
            value = this.getValueByPath(path);
        }
        if(value){
            let obj = this.parseColor(value);
            let color = obj.color, opacityNumber = obj.opacityNumber;
            this.colorConfig.color = color;
            this.colorConfig.opacity = opacityNumber;
        }
        this.colorConfigForm = this.formBuilder.group(this.colorConfig);
        this.isShowChooseColor = true;
    }

    addDefaultHoverBackgroundColor(key){
        if(key != null && key != undefined){
            this.dataConfig[key] = [...this.hoverBackgroundColorDefault];
        }
    }

    addDefaultBackgroundColor(key){
        if(key != null && key != undefined){
            this.dataConfig[key] = [...this.backgroundColorDefault];
        }
    }

    saveColor(){
        let color = this.getStringColor(this.colorConfig.color, this.colorConfig.opacity);
        if(this.colorConfig.key != null && this.colorConfig.key != undefined){
            if(this.colorConfig.index != null && this.colorConfig.index != undefined){
                this.dataConfig[this.colorConfig.key][this.colorConfig.index] = color;
            }else{
                this.dataConfig[this.colorConfig.key].push(color);
            }
        }else if(this.colorConfig.path != null && this.colorConfig.path != undefined){
            this.setValueByPath(this.colorConfig.path, color);
        }
        this.isShowChooseColor = false;
    }

    openConfigFontBox(path){
        let config = this.getValueByPath(path);
        if(config == null || config == undefined){
            this.fontConfig = {
                family: null,
                lineHeight: 1,
                size: 12,
                style: null,
                weight: null,
                path
            }
        }else{
            this.fontConfig = {...config, path};
        }
        this.fontConfigForm = this.formBuilder.group(this.fontConfig);
        this.isShowConfigFont = true;
    }

    saveFont(){
        let path = this.fontConfig.path;
        delete this.fontConfig.path;
        this.setValueByPath(path, {...this.fontConfig});
        this.isShowConfigFont = false;
    }

    changeScaleKey(){
        this.scaleConfigForm = null;
        this.scaleConfig = {...this.dynamicConfig.optionConfigEntity.scales[this.scaleKey]};
        let dataForm = {
            stacked: this.scaleConfig.stacked,
            beginAtZero: this.scaleConfig.beginAtZero,
            position: this.scaleConfig.position,
            rotation: this.scaleConfig.rotation,
            drawOnChartArea: this.scaleConfig.drawOnChartArea,
            isDash: this.scaleConfig.isDash,
            titleText: this.scaleConfig.titleText,
            titleAlign: this.scaleConfig.titleAlign,
            titleDisplay: this.scaleConfig.titleDisplay
        }
        this.scaleConfigForm = this.formBuilder.group(dataForm);
    }

    deleteScale(){
        let me = this;
        delete this.dynamicConfig.optionConfigEntity.scales[this.scaleKey];
        this.messageCommonService.success(this.tranService.translate("global.message.success"));
        me.scaleKey = null;
        me.scaleConfigForm = null;
        me.listScale = [];
        Object.keys(me.dynamicConfig.optionConfigEntity.scales).forEach(el => {
            me.listScale.push({
                name: el.toUpperCase(),
                value: el
            })
        });
    }

    saveScale(){
        this.dynamicConfig.optionConfigEntity.scales[this.scaleKey] = this.scaleConfig;
        this.messageCommonService.success(this.tranService.translate("global.message.success"))
    }

    onDateRecentlyDateFrom(event: Date) {
        const today = new Date();

        today.setHours(0, 0, 0, 0);
        const selectedDate = new Date(event);
        selectedDate.setHours(0, 0, 0, 0);
        const diffTime = selectedDate.getTime() - today.getTime();
        const diffDays = Math.round(diffTime / (1000 * 60 * 60 * 24)); // Difference in days

        // console.log(diffDays)
        let formattedDate = '';
        if (diffDays === 0) {
            formattedDate = '@BeginToday';
        } else if (diffDays > 0) {
            formattedDate = `@BeginToday` + `+` + diffDays;
        } else {
            formattedDate = `@BeginToday` + diffDays;
        }

        this.parameterInfo.prValue = formattedDate;
    }

    onDateRecentlyDateTo(event: Date) {
        // console.log("onDateRecentlyDateTo")
        const today = new Date(); // Get today's date

        today.setHours(0, 0, 0, 0);  // Remove time portion of today
        const selectedDate = new Date(event);
        selectedDate.setHours(0, 0, 0, 0);  // Remove time portion of selected date

        const diffTime = selectedDate.getTime() - today.getTime();
        const diffDays = Math.round(diffTime / (1000 * 60 * 60 * 24)); // Difference in days

        // console.log(diffDays)
        let formattedDate = '';
        if (diffDays === 0) {
            formattedDate = '@EndToday';
        } else if (diffDays > 0) {
            formattedDate = `@EndToday` + `+` + diffDays;
        } else {
            formattedDate = `@EndToday` + diffDays;
        }

        this.parameterInfo.prValue = formattedDate;
        // this.changeForm();
    }

    changeForm() {
        Object.keys(this.formParameter.controls).forEach(key => {
            const control = this.formParameter.get(key);
            if (control.invalid) {
                console.log('Field:', key, 'is invalid. Errors:', control.errors);
            }
        });
        console.log(this.parameterInfo)
        console.log(this.formParameter)
    }

    onChangeRecentlyDateFrom(value) {
        if (value) {
            this.inputRecentlyDate.minDateTo = value;
            const maxDateTo = new Date(value);
            maxDateTo.setDate(maxDateTo.getDate() + CONSTANTS.MAX_DATE_SELECTION_RANGE);
            if (maxDateTo > new Date()) {
                this.inputRecentlyDate.maxDateTo = new Date()
            } else {
                this.inputRecentlyDate.maxDateTo = maxDateTo;
            }
        } else {
            this.inputRecentlyDate.minDateTo = null;
            this.inputRecentlyDate.maxDateTo = new Date();
        }
    }

    onChangeRecentlyDateTo(value) {
        if (value) {
            this.inputRecentlyDate.maxDateFrom = value;
            const minDateFrom = new Date(value);
            minDateFrom.setDate(minDateFrom.getDate() - CONSTANTS.MAX_DATE_SELECTION_RANGE);
            this.inputRecentlyDate.minDateFrom = minDateFrom;
        } else {
            this.inputRecentlyDate.maxDateFrom = new Date();
            this.inputRecentlyDate.minDateFrom = null;
        }
    }
    getDateFromRecentlyDateString(inputString: string): Date | null {
        let me = this;
        const match = inputString.match(/@(BeginToday|EndToday)([-+]?\d+)/);
        if (match) {
            let offset = me.countDiffDays(inputString)
            const today = new Date();
            const type = match[1]; // "BeginToday" hoặc "EndToday"

            today.setDate(today.getDate() + offset);

            if (type === "BeginToday") {
                // Nếu là @BeginToday, trả về thời gian đầu ngày (00:00:00)
                today.setHours(0, 0, 0, 0);
            } else if (type === "EndToday") {
                // Nếu là @EndToday, trả về thời gian cuối ngày (23:59:59)
                today.setHours(23, 59, 59, 999);
            }
            return today;
        }

        return null;
    }
    countDiffDays (inputString): number   {
        const match = inputString.match(/-?\d+/);
        if (match) {
            const number = parseInt(match[0], 10);
            return number;
        } else {
            console.log("No number found in the string.");
            return 0;
        }
    }

    protected readonly CONSTANTS = CONSTANTS;
}
