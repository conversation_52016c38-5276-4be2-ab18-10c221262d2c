.captcha {
    box-sizing: border-box;
    border-color: 1px solid #CDC9C9;
    padding: 12px;
    position: relative;
    width: 380px;
    height: fit-content;
    background-color: white;
}

.button-reload-captcha{
    position: absolute;
    top: 10px;
    left: 10px;
    z-index: 4;
    background-color: rgba(255, 255, 255, 0.5);
    padding: 2px 4px;
    border-radius: 4px;
    cursor: pointer;
}

.mainImage{
    width: 100%;
    height: 250px;
    background-size: 100% 250px;
}

.box-empty {
    width: 40px;
    height: 40px;
    border: 2px solid Gainsboro;
    box-shadow: 0px 0px 2px 2px rgb(130, 205, 228);
    background-color: white;
    position: absolute;
    top: 200px;
    left: 200px;
    z-index: 1;
    background-size: 380px 250px;
}

.box-fill {
    width: 38px;
    height: 38px;
    border: 1px solid Gainsboro;
    box-shadow: 0px 0px 1px 1px rgb(130, 205, 228);
    background-color: white;
    position: absolute;
    top: 0;
    left: 0;
    z-index: 2;
    background-size: 380px 250px;
}

.tray-move{
    width: 100%;
    line-height: 14px;
    font-size: 14px;
    padding: 12px 0px;
    border: 1px solid Gainsboro;
    background-color: #F5F5F5;
    align-items: center;
    text-align: center;
}

.button-move{
    position: absolute;
    z-index: 1;
    border: 1px solid Gainsboro;
    background-color: white;
    width: 40px;
    height: 40px;
    box-shadow: 0px 0px 2px 2px LightGray;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 24px;
    cursor: pointer;
}
.button-move{
    position: absolute;
    top: 0;
    left: 0;
    z-index: 1;
    border: 1px solid Gainsboro;
    background-color: white;
    width: 40px;
    height: 40px;
    box-shadow: 0px 0px 2px 2px LightGray;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 24px;
    cursor: pointer;
}