import { Inject, Injectable } from "@angular/core";
import { HttpService } from "../comon/http.service";

@Injectable()
export class ProvinceAddressService {
    private prefixApi: string;
    constructor(@Inject(HttpService) private httpService:HttpService) {
        this.prefixApi = "/address";
    }
    public search(params: any, callback: Function, errorCallBack?: Function, finallyCallback?: Function){
        this.httpService.get(`${this.prefixApi}/province`, {}, params,callback, errorCallBack, finallyCallback);
    }
}
