<div class="vnpt flex flex-row justify-content-between align-items-center bg-white p-2 border-round">
    <div class="">
        <div class="text-xl font-bold mb-1">{{ tranService.translate("global.menu.alerthistory") }}</div>
        <p-breadcrumb class="max-w-full col-7" [model]="items" [home]="home"></p-breadcrumb>
    </div>
</div>
<form [formGroup]="formSearchAlertHistory" (ngSubmit)="onSubmitSearch()" class="pb-2 pt-3 vnpt-field-set">
    <p-panel [toggleable]="true" [header]="tranService.translate('global.text.filter')">
        <div class="grid">
            <!-- so thue bao -->
            <div class="col-3">
                <span class="p-float-label">
                    <label for="model">{{ tranService.translate("device.label.name") }}</label>
                    <span class="relative">
                        <vnpt-select
                            [control]="controlComboSelectDevice"
                            class="w-full"
                            [(value)]="searchInfo.deviceId"
                            [placeholder]="tranService.translate('device.label.name')"
                            objectKey="device"
                            paramKey="deviceName"
                            keyReturn="id"
                            displayPattern="${deviceName}"
                            typeValue="primitive"
                            [isMultiChoice]="false"
                        ></vnpt-select>
                </span>
                </span>
            </div>
            <!-- goi cuoc data pool -->
            <!--            <div class="col-3">-->
            <!--               <span class="p-float-label">-->
            <!--                    <input pInputText-->
            <!--                           class="w-full"-->
            <!--                           pInputText id="imei"-->
            <!--                           [(ngModel)]="searchInfo.imei"-->
            <!--                           formControlName="imei"-->
            <!--                    />-->
            <!--                    <label htmlFor="imei">{{tranService.translate("device.label.imei")}}</label>-->
            <!--               </span>-->
            <!--            </div>-->
            <div class="col-3">
                <span class="p-float-label">
                    <label for="model">{{ tranService.translate("customer.label.customerName") }}</label>
                    <span class="relative">
                    <vnpt-select
                        id="model"
                        [control]="comboSelectCustomerControl"
                        class="w-full"
                        [(value)]="searchInfo.userId"
                        [placeholder]="tranService.translate('customer.label.customerName')"
                        objectKey="account"
                        paramKey="name"
                        keyReturn="id"
                        displayPattern="${name}"
                        typeValue="primitive"
                        [isMultiChoice]="false"
                    ></vnpt-select>
                </span>
                </span>
            </div>
            <!-- dieu kien -->
            <div class="col-3">
                <span class="p-float-label">
                <p-dropdown styleClass="w-full"
                            [showClear]="true"
                            id="eventType" [autoDisplayFirst]="false"
                            [(ngModel)]="searchInfo.eventType"
                            formControlName="eventType"
                            [options]="eventOptions"
                            optionLabel="name"
                            optionValue="value"
                ></p-dropdown>
                    <label for="eventType"> {{ tranService.translate('alert.label.rule') }}</label>
                </span>
            </div>
            <div class="col-3 pb-0">
                <span class="p-float-label">
                    <p-calendar styleClass="w-full"
                                id="dateFrom"
                                [(ngModel)]="searchInfo.fromDate"
                                formControlName="fromDate"
                                [showIcon]="true"
                                [showClear]="true"
                                dateFormat="dd/mm/yy"
                                [maxDate]="maxDateFrom"
                                (onSelect)="onChangeDateFrom(searchInfo.fromDate)"
                                (onInput)="onChangeDateFrom(searchInfo.fromDate)"
                    ></p-calendar>
                    <label htmlFor="dateFrom">{{ tranService.translate("alert.label.fromdate") }}</label>
                </span>
            </div>

            <div class="col-3 pb-0">
                <span class="p-float-label">
                    <p-calendar styleClass="w-full"
                                id="dateTo"
                                [(ngModel)]="searchInfo.toDate"
                                formControlName="toDate"
                                [showIcon]="true"
                                [showClear]="true"
                                dateFormat="dd/mm/yy"
                                [minDate]="minDateTo"
                                [maxDate]="maxDateTo"
                                (onSelect)="onChangeDateTo(searchInfo.toDate)"
                                (onInput)="onChangeDateTo(searchInfo.toDate)"
                    />
                    <label htmlFor="dateTo">{{ tranService.translate("alert.label.todate") }}</label>
                </span>
            </div>

            <!--            button search-->
            <div class="col-3 pb-0">
                <p-button icon="pi pi-search"
                          styleClass="p-button-rounded p-button-secondary p-button-text button-search"
                          type="submit"
                ></p-button>
            </div>
        </div>
    </p-panel>
</form>

<table-vnpt
    [fieldId]="'id'"
    [(selectItems)]="selectItems"
    [columns]="columns"
    [dataSet]="dataSet"
    [options]="optionTable"
    [loadData]="search.bind(this)"
    [pageNumber]="pageNumber"
    [pageSize]="pageSize"
    [sort]="sort"
    [params]="searchInfo"
    [labelTable]="this.tranService.translate('global.menu.alerthistory')"
></table-vnpt>
