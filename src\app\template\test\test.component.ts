import { AfterContentChecked, Component, ElementRef, OnInit } from "@angular/core";
import { CONSTANTS } from "src/app/service/comon/constants";
import { SimService } from "src/app/service/sim/SimService";

@Component({
    selector: "app-test",
    templateUrl: "./test.component.html"
})
export class TestComponent implements OnInit, AfterContentChecked{
    constructor(private simService: SimService, private eRef: ElementRef){
        
    }
    heightBoxDrag: number = 100;
    widthBoxDrag: number = 100;
    listObjectDrag = [];
    boxDragged: any;
    minX: number = 0;
    minY: number = 0;
    typeDrag: string;
    ngOnInit(): void {
        for(let i = 0;i<10;i++){
            this.listObjectDrag.push({
                id: i,
                top: Math.round(Math.random() * 800),
                left: Math.round(Math.random() * 2800),
                width: Math.round(Math.random() * 100+100),
                height: Math.round(Math.random() * 100+100),
                backgroundColor: CONSTANTS.COLOURS[Object.keys(CONSTANTS.COLOURS)[Math.floor(Math.random()*Object.keys(CONSTANTS.COLOURS).length)]],
                zIndex: i
            })
        }
        this.calculateHeight();
        this.calculateWidth();
        this.calculateBorderline();
    }

    ngAfterContentChecked(): void {
        this.calculateHeight();
        this.calculateWidth();
    }

    calculateHeight(){
        let me = this;
        let max = 0;
        this.listObjectDrag.forEach(el => {
            let value = el.top + el.height;
            if(value > max){
                max = value;
            }
        })
        this.heightBoxDrag = max;
    }
    calculateWidth(){
        let me = this;
        let max = 0;
        this.listObjectDrag.forEach(el => {
            let value = el.left + el.width;
            if(value > max){
                max = value;
            }
        })
        this.widthBoxDrag = max;
    }
    calculateBorderline(){
        let boxWrapper:Element = this.eRef.nativeElement.querySelector("#boxWrapper");
        this.minX = boxWrapper.getBoundingClientRect().left;
        this.minY = boxWrapper.getBoundingClientRect().top;
    }

    onResize(event){
        this.calculateBorderline();
    }

    drop(event){
        this.typeDrag = null;
    }

    dragStart(event, box){
        if(!this.typeDrag){
            this.boxDragged = box;
            this.boxDragged["offsetX"] = event.offsetX;
            this.boxDragged["offsetY"] = event.offsetY;
            this.boxDragged["pageX"] = event.pageX;
            this.boxDragged['pageY'] = event.pageY;
            this.eRef.nativeElement.querySelector(`div#abc${this.boxDragged.id}`).style.opacity = 0.001;
        }
    }

    dragXStart(event, box){
        let el: Element = this.eRef.nativeElement.querySelector(`#abc${box.id}`);
        this.boxDragged = box;
        this.boxDragged["offsetX"] = event.offsetX;
        this.boxDragged["offsetY"] = event.offsetY;
        this.boxDragged["pageX"] = el.getBoundingClientRect().left;
        this.boxDragged['pageY'] = el.getBoundingClientRect().top;
        this.typeDrag = 'scale-x';
        console.log("scale-x,",this.boxDragged.pageX);
    }

    dragYStart(event, box){
        let el: Element = this.eRef.nativeElement.querySelector(`#abc${box.id}`);
        this.boxDragged = box;
        this.boxDragged["offsetX"] = event.offsetX;
        this.boxDragged["offsetY"] = event.offsetY;
        this.boxDragged["pageX"] = el.getBoundingClientRect().left;
        this.boxDragged['pageY'] = el.getBoundingClientRect().top;
        this.typeDrag = 'scale-y';
        console.log("scale-y,",this.boxDragged.pageY)
    }

    dragXYStart(event, box){
        let el: Element = this.eRef.nativeElement.querySelector(`#abc${box.id}`);
        this.boxDragged = box;
        this.boxDragged["offsetX"] = event.offsetX;
        this.boxDragged["offsetY"] = event.offsetY;
        this.boxDragged["pageX"] = el.getBoundingClientRect().left;
        this.boxDragged['pageY'] = el.getBoundingClientRect().top;
        this.typeDrag = 'scale-xy';
        console.log("scale-xy")
    }

    dragEnd(event){
        this.eRef.nativeElement.querySelector(`div#abc${this.boxDragged.id}`).style.opacity = 1;
    }

    drag(event){
        if(!this.typeDrag){
            if(event.offsetX < 0 || event.offsetY < 0) return;
                let x = event.pageX - this.boxDragged.offsetX;
                let y = event.pageY - this.boxDragged.offsetY;
                let top = y - this.minY;
                let left = x - this.minX;
                // console.log(x, y, this.minX, this.minY, top, left);
            if(top >= 12 && left >= 12){
                this.boxDragged.top = top;
                this.boxDragged.left = left;
                return;
            }else if(top >= 12){
                this.boxDragged.top = top;
            }else if(left >= 12){
                this.boxDragged.left = left;
            }
            event.preventDefault();
        }
    }

    dragX(event){
        console.log(event.pageX, this.boxDragged.pageX)
        this.boxDragged.width = event.pageX - this.boxDragged.pageX;
    }

    dragY(event){
        console.log(event.pageY, this.boxDragged.pageY)
        this.boxDragged.height = event.pageY - this.boxDragged.pageY;
    }

    dragXY(event){
        this.boxDragged.width = event.pageX - this.boxDragged.pageX;
        this.boxDragged.height = event.pageY - this.boxDragged.pageY;
    }
}