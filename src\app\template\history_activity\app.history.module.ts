import {NgModule} from "@angular/core";
import {AccountService} from "src/app/service/account/AccountService";
import {CommonModule} from "@angular/common";
import {BreadcrumbModule} from "primeng/breadcrumb";
import {FieldsetModule} from "primeng/fieldset";
import {FormsModule, ReactiveFormsModule} from "@angular/forms";
import {InputTextModule} from "primeng/inputtext";
import {ButtonModule} from "primeng/button";
import {CommonVnptModule} from "../common-module/common.module";
import {SplitButtonModule} from "primeng/splitbutton";
import {AutoCompleteModule} from "primeng/autocomplete";
import {CalendarModule} from "primeng/calendar";
import {DropdownModule} from "primeng/dropdown";
import {CardModule} from "primeng/card";
import {PanelModule} from "primeng/panel";
import {DialogModule} from "primeng/dialog";
import {InputTextareaModule} from 'primeng/inputtextarea';
import {MultiSelectModule} from 'primeng/multiselect';
import {ChipsModule} from 'primeng/chips';
import {AppHistoryRouting} from "./app.history-routing";
import {ListHistoryActivityComponent} from "./list/app.list.history.component";
import {LogsService} from "../../service/activity-history/LogsService";
import {TableModule} from "primeng/table";
import {CustomerService} from "../../service/customer/CustomerService";
import {SimService} from "../../service/sim/SimService";


@NgModule({
  imports: [
    AppHistoryRouting,
    CommonModule,
    BreadcrumbModule,
    FieldsetModule,
    FormsModule,
    ReactiveFormsModule,
    InputTextModule,
    ButtonModule,
    CommonVnptModule,
    SplitButtonModule,
    AutoCompleteModule,
    CalendarModule,
    DropdownModule,
    CardModule,
    DialogModule,
    InputTextareaModule,
    MultiSelectModule,
    PanelModule,
    ChipsModule,
    TableModule
  ],
  declarations: [
    ListHistoryActivityComponent
  ],
  exports: [],
  providers: [
    AccountService,
    LogsService,
    CustomerService,
    SimService
  ]
})
export class AppHistoryModule {
}
