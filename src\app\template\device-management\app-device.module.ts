import { CommonModule } from "@angular/common";
import { NgModule } from "@angular/core";
import { DeviceRoutingModule } from "./app-device-routing.module";
import { AppDeviceListComponent } from "./list/app-device-list.component";
import { DeviceService } from "src/app/service/device/device-service.service";
import { BreadcrumbModule } from "primeng/breadcrumb";
import { FieldsetModule } from "primeng/fieldset";
import { FormsModule, ReactiveFormsModule } from "@angular/forms";
import { InputTextModule } from "primeng/inputtext";
import { ButtonModule } from "primeng/button";
import { CommonVnptModule } from "../common-module/common.module";
import { CalendarModule } from "primeng/calendar";
import {AppDeviceDetailComponent} from "./detail/app-device-detail.component";
import {AppDeviceUpdateComponent} from "./edit/app-device-update.component";
import {AppDeviceCreateComponent} from "./create/app-device-create.component";
import {DropdownModule} from "primeng/dropdown";
import {AutoCompleteModule} from "primeng/autocomplete";
import {SplitButtonModule} from "primeng/splitbutton";
import {DialogModule} from "primeng/dialog";
import {CardModule} from "primeng/card";
import {PanelModule} from "primeng/panel";
import {CheckboxModule} from "primeng/checkbox";
import {ToggleButtonModule} from "primeng/togglebutton";
import {TabViewModule} from "primeng/tabview";
import {KeyFilterModule} from "primeng/keyfilter";

@NgModule({
    imports: [
        CommonModule,
        DeviceRoutingModule,
        BreadcrumbModule,
        FieldsetModule,
        FormsModule,
        ReactiveFormsModule,
        InputTextModule,
        ButtonModule,
        CommonVnptModule,
        CalendarModule,
        DropdownModule,
        AutoCompleteModule,
        SplitButtonModule,
        DialogModule,
        CardModule,
        PanelModule,
        CheckboxModule,
        ToggleButtonModule,
        TabViewModule,
        KeyFilterModule,
    ],
    declarations: [
        AppDeviceListComponent,
        AppDeviceDetailComponent,
        AppDeviceUpdateComponent,
        AppDeviceCreateComponent,
    ],
    providers: [
        DeviceService
    ]
})
export class AppDeviceModule{}
