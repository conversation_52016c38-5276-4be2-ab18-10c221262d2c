import {Component, Inject, Injector, is<PERSON><PERSON><PERSON><PERSON>, OnInit} from "@angular/core";
import {FormBuilder, FormControl, FormGroup, Validators} from "@angular/forms";
import {MenuItem} from "primeng/api";
import {DeviceService} from "src/app/service/device/device-service.service";
import {ColumnInfo, OptionTable} from "../../common-module/table/table.component";
import {CONSTANTS} from "../../../service/comon/constants";
import {ComponentBase} from "../../../component.base";
import {DomSanitizer, SafeResourceUrl} from "@angular/platform-browser";
import {ComboLazyControl} from "../../common-module/combobox-lazyload/combobox.lazyload";
import {CommandService} from "../../../service/device/command.service";

@Component({
    selector: "app-device-list",
    templateUrl: "./app-device-list.component.html"
})
export class AppDeviceListComponent extends ComponentBase implements OnInit {
    constructor(
        @Inject(DeviceService) private deviceService: DeviceService,
        @Inject(CommandService) private commandService: CommandService,
        private sanitizer: DomSanitizer,
        private formBuilder: FormBuilder,
        injector: Injector) {
        super(injector);
    }

    id = Number(this.route.snapshot.queryParamMap.get('id'));
    type  = Number(this.route.snapshot.queryParamMap.get('type'));
    items: MenuItem[];
    home: MenuItem
    searchInfo: {
        deviceName: string | null,
        imei: string | null,
        msisdn: string | null,
        deviceType: string | null,
        model: string | null,
        serialNumber: string | null,
        enterpriseUserId: number | null,
        customerUserId: number | null,
        connectionStatus: string | null,
    };
    safeUrl: SafeResourceUrl;
    deviceInfo: {
        imei: string | null,
        location: string | null,
        msisdn: number | null,
        country: string | null,
        category: string | null,
        expiredDate: Date | string | null,
        deviceType: string | null,
        iotLink: number | null,
    }
    findCellIDDto: any;
    msisdn: number;
    formDetailDevice: any;
    detailSim: any = {};
    detailStatusSim: any = {};
    columns: Array<ColumnInfo>;
    dataSet: {
        content: Array<any>,
        total: number
    };
    selectItems: Array<{ id: number, [key: string]: any }>;
    optionTable: OptionTable;
    pageNumber: number;
    pageSize: number;
    sort: string;
    formSearch: any;
    maxDateFrom: Date | number | string | null = null;
    minDateTo: Date | number | string | null = null;
    maxDateTo: Date | number | string | null = null;
    listStatus: Array<any>;
    listType: Array<any>;
    controlComboSelectType: ComboLazyControl = new ComboLazyControl();
    controlComboSelectModel: ComboLazyControl = new ComboLazyControl()
    controlComboSelectIndividual: ComboLazyControl = new ComboLazyControl()
    controlComboSelectBusiness: ComboLazyControl = new ComboLazyControl()
    paramSearchType: {
        modelCode: any;
    }
    paramSearchModel: {
        typeCode: any;
    }
    paramSearchBusiness: {
        type: 2,
        sort: 'id,asc'
        customerId: number | -1
    }
    paramSearchIndividual: {
        type: 3,
        sort: 'id,asc'
        managerId: number | -1
    }

    inputSchema: Array<any>
    isShowSendCommand = false;
    formData: any;
    userType: number;
    command: {
        deviceId: number | null,
        commandId: number | null,
        data: any | string | null,
    }
    listOptionCommand: Array<any>;
    normalPattern = /^[a-zA-Z0-9 ]*$/;
    vietnamesePattern = /^[^~`!@#\$%\^&*\(\)=\+\[\]\{\}\|\\,<>\/?]*$/;

    ngOnInit(): void {
        let me = this;
        this.home = {icon: 'pi pi-home', routerLink: '/'};
        this.items = [{label: this.tranService.translate("global.menu.devicemgmt")}, {label: this.tranService.translate("global.menu.listdevice")},];
        this.searchInfo = {
            deviceName: null,
            imei: null,
            msisdn: null,
            deviceType: null,
            model: null,
            serialNumber: null,
            enterpriseUserId: this.type===2?this.id:null,
            customerUserId: this.type===3?this.id:null,
            connectionStatus: null,
        }
        this.userType = this.sessionService.userInfo.type;
        console.log("id: "+ this.id +"; type : "+ this.type)
        this.detailSim = {};
        this.deviceInfo = {
            imei: null,
            location: null,
            msisdn: null,
            country: null,
            category: null,
            expiredDate: null,
            deviceType: null,
            iotLink: null,
        }
        this.detailStatusSim = {
            statusData: null,
            statusReceiveCall: null,
            statusSendCall: null,
            statusWorldCall: null,
            statusReceiveSms: null,
            statusSendSms: null
        }
        this.listStatus = [
            {name: this.tranService.translate("device.status.registered"), value: CONSTANTS.DEVICE_STATUS.REGISTERED},
            {name: this.tranService.translate("device.status.connected"), value: CONSTANTS.DEVICE_STATUS.CONNECTED},
            {
                name: this.tranService.translate("device.status.lostConnection"),
                value: CONSTANTS.DEVICE_STATUS.LOST_CONNECTION
            }
        ]
        this.paramSearchType = {
            modelCode: "",
        }
        this.paramSearchModel = {
            typeCode: "",
        }
        this.paramSearchBusiness = {
            type: 2,
            sort: 'id,asc',
            customerId: -1
        }
        this.paramSearchIndividual = {
            type: 3,
            sort: 'id,asc',
            managerId: -1
        }

        const url = `https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d42135.***********!2d105.78743105312334!3d21.020807357074563!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x3135ab9bd9861ca1%3A0xe7887f7b72ca17a9!2sHanoi%2C%20Vietnam!5e0!3m2!1sen!2s!4v1713255802111!5m2!1sen!2s`;
        this.safeUrl = this.sanitizer.bypassSecurityTrustResourceUrl(url);
        this.formSearch = this.formBuilder.group(this.searchInfo);
        this.columns = [
            {
                name: this.tranService.translate("device.label.individualName"),
                key: "customerName",
                size: "200px",
                align: "left",
                isShow: true,
                isSort: true,
                isShowTooltip: true,
            },
            {
                name: this.tranService.translate("device.label.currentMonthlyFlow"),
                key: "currentVolume",
                size: "100px",
                align: "left",
                isShow: true,
                isSort: false,
                funcConvertText(value, item) {
                    if (value == 0) return null;
                    return value;
                }
            },
            {
                name: this.tranService.translate("device.label.estimatedCost"),
                key: "estimatedCost",
                size: "100px",
                align: "left",
                isShow: true,
                isSort: false,
                funcConvertText(value, item) {
                    if (value == 0) return null;
                    return value;
                }
            },
            {
                name: this.tranService.translate("device.label.name"),
                key: "deviceName",
                size: "150px",
                align: "left",
                isShow: true,
                isSort: true,
                style:{
                    cursor: me.checkAuthen([CONSTANTS.PERMISSIONS.DEVICE.VIEW_DETAIL]) ? "pointer" : "auto",
                    color: me.checkAuthen([CONSTANTS.PERMISSIONS.DEVICE.VIEW_DETAIL]) ? "var(--mainColorText)" : "",
                },
                funcClick(id, item) {
                    if (me.checkAuthen([CONSTANTS.PERMISSIONS.DEVICE.VIEW_DETAIL])) {
                        me.router.navigate([`/devices/detail/${item.id}`]);
                    }
                },
            },
            {
                name: this.tranService.translate("device.label.imei"),
                key: "imei",
                size: "150px",
                align: "left",
                isShow: true,
                isSort: true,
            },
            // {
            //     name: this.tranService.translate("device.label.serial"),
            //     key: "serialNumber",
            //     size: "250px",
            //     align: "left",
            //     isShow: false,
            //     isSort: true,
            // },
            {
                name: this.tranService.translate("device.label.type"),
                key: "deviceType",
                size: "200px",
                align: "left",
                isShow: true,
                isSort: true,
            },
            {
                name: this.tranService.translate("device.label.model"),
                key: "model",
                size: "150px",
                align: "left",
                isShow: true,
                isSort: true,
            },

            {
                name: this.tranService.translate("device.label.msisdn"),
                key: "msisdn",
                size: "100px",
                align: "left",
                isShow: true,
                isSort: true,
            },

            {
                name: this.tranService.translate("device.label.lastConnected"),
                key: "lastConnected",
                size: "150px",
                align: "left",
                isShow: true,
                isSort: true,
                funcConvertText(value) {
                    if (value == null) return "";
                    return me.utilService.convertDateTimeToString(new Date(value));
                }
            },
            {
                name: this.tranService.translate("device.label.statusConnect"),
                key: "connectionStatus",
                size: "175px",
                align: "left",
                isShow: true,
                isSort: true,
                funcGetClassname: (value) => {
                    return me.getClassStatus(value)
                },
                funcConvertText: function (value) {
                    return me.getNameStatus(value)
                },

            },
            {
                name: this.tranService.translate("device.label.businessName"),
                key: "enterpriseName",
                size: "250px",
                align: "left",
                isShow: true,
                isSort: true,
            },
        ]
        this.selectItems = [];
        this.optionTable = {
            hasClearSelected: true,
            hasShowChoose: false,
            hasShowIndex: true,
            hasShowToggleColumn: true,
            action: [
                // {
                //     icon: "pi pi-eye",
                //     tooltip: this.tranService.translate("global.button.view"),
                //     func: function (id, item) {
                //         me.router.navigate([`/devices/detail/${item.id}`]);
                //     },
                //     funcAppear: function (id, item) {
                //         return me.checkAuthen([CONSTANTS.PERMISSIONS.DEVICE.VIEW_DETAIL]);
                //     }
                // },
                {
                    icon: "pi pi-pencil",
                    tooltip: this.tranService.translate("global.button.edit"),
                    func: function (id, item) {
                        me.router.navigate([`/devices/edit/${item.id}`]);
                    },
                    funcAppear: function (id, item) {
                        return me.checkAuthen([CONSTANTS.PERMISSIONS.DEVICE.UPDATE]);
                    }
                },
                {
                    icon: "pi pi-cog",
                    tooltip: this.tranService.translate("global.button.control"),
                    func: function (id, item) {
                        me.messageCommonService.onload();
                        me.commandService.getBydeviceType(Number(item.deviceTypeId), (response) => {
                            if (response.length > 0) {
                                me.command.deviceId = id;
                                me.listOptionCommand = response;
                                me.isShowSendCommand = true;
                            } else {
                                me.messageCommonService.warning(me.tranService.translate("device.text.errorInputShema"))
                            }
                        }, null, () => {
                            me.messageCommonService.offload();
                        })
                    },
                    funcAppear: function (id, item) {
                        return me.checkAuthen([CONSTANTS.PERMISSIONS.DEVICE.SEND_COMMAND]) && (item.connectionStatus == CONSTANTS.DEVICE_STATUS.CONNECTED || item.connectionStatus == CONSTANTS.DEVICE_STATUS.LOST_CONNECTION);
                    }
                },
                {
                    icon: "pi pi-trash",
                    tooltip: this.tranService.translate("global.button.delete"),
                    func: function (id, item) {
                        me.messageCommonService.confirm(
                            me.tranService.translate("global.message.titleConfirmDeleteDevice"),
                            me.tranService.translate("global.message.confirmDeleteDevice"),
                            {
                                ok: () => {
                                    me.messageCommonService.onload();
                                    me.deviceService.deleleDevice(item.id, (response) => {
                                        me.messageCommonService.success(me.tranService.translate("global.message.deleteSuccess"));
                                        me.search(me.pageNumber, me.pageSize, me.sort, me.searchInfo);
                                    }, null, () => {
                                        me.messageCommonService.offload();
                                    })
                                },
                                cancel: () => {
                                    // me.messageCommonService.error(me.tranService.translate("global.message.deleteFail"));
                                }
                            }
                        )
                    },
                    funcAppear: function (id, item) {
                        return me.checkAuthen([CONSTANTS.PERMISSIONS.DEVICE.DELETE]);
                    }
                }
            ]
        }
        this.pageNumber = 0;
        this.pageSize = 10;
        this.sort = "lastConnected,desc";

        this.dataSet = {
            content: [],
            total: 0,
        }
        this.inputSchema = [];
        this.command = {
            deviceId: null,
            commandId: null,
            data: null,
        }
        this.listOptionCommand = [];
        this.formData = new FormGroup({})
        this.search(this.pageNumber, this.pageSize, this.sort, this.searchInfo)
    }

    onSubmitSearch() {
        let me = this;
        me.pageNumber = 0;
        me.search(0, this.pageSize, this.sort, this.searchInfo);
    }


    search(page, limit, sort, params) {
        this.pageNumber = page;
        this.pageSize = limit;
        this.sort = sort;
        let me = this;
        let dataParams = {
            page,
            size: limit,
            sort
        }
        Object.keys(this.searchInfo).forEach(key => {
                if (this.searchInfo[key] != null) {
                    dataParams[key] = this.searchInfo[key];
                }
            }
        )
        me.messageCommonService.onload();
        this.deviceService.search(dataParams, (response) => {
            me.dataSet = {
                content: response.content,
                total: response.totalElements
            }
            // let list : number[] = []
            // me.dataSet.content.forEach(el => {
            //     list.push(Number(el.id));
            // })
            // me.deviceService.getUsage(list, resp => {
            //     for (const device of me.dataSet.content) {
            //             for(let el of resp) {
            //                 if(device.id == el.id) {
            //                     device.currentVolume = el.usage?.toLocaleString("vi-VN")
            //                     device.estimatedCost = el.cost?.toLocaleString("vi-VN")
            //                 }
            //             }
            //         }
            // })
        }, null, () => {
            me.messageCommonService.offload();
        })
    }

    calWaterBill(usage: number) {
        if (usage <= 0) return null;

        let total = 0;
        let remain = usage;

        // Bậc 1: 0 - 10m3
        const level1 = Math.min(remain, 10);
        total += level1 * CONSTANTS.WATER_BILL.LEVEL_1;
        remain -= level1;

        if (remain > 0) {
            // Bậc 2: 11 - 20m3
            const level2 = Math.min(remain, 10);
            total += level2 * CONSTANTS.WATER_BILL.LEVEL_2;
            remain -= level2;
        }

        if (remain > 0) {
            // Bậc 3: 21 - 30m3
            const level3 = Math.min(remain, 10);
            total += level3 * CONSTANTS.WATER_BILL.LEVEL_3;
            remain -= level3;
        }

        if (remain > 0) {
            // Bậc 4: > 30m3
            total += remain * CONSTANTS.WATER_BILL.LEVEL_4;
        }
        const roundTotal = Math.floor(total);
        return roundTotal.toLocaleString("vi-VN");
    }


    navigateToCreateDevice() {
        let me = this;
        if (me.userType == CONSTANTS.USER_TYPE.INDIVIDUAL) {
            if (me.dataSet.total >= 1) {
                me.messageCommonService.warning(me.tranService.translate('device.text.oneDevice'))
                return;
            }
        }
        this.router.navigate(['/devices/create']);
    }

    convertDateString(dateString: string): string {
        // Create a new Date object from the string
        const date = new Date(dateString);

        // Extract day, month, and year
        const day = date.getDate();
        const month = date.getMonth() + 1; // Months are zero-based
        const year = date.getFullYear();

        // Format day and month to two digits
        const formattedDay = day < 10 ? `0${day}` : `${day}`;
        const formattedMonth = month < 10 ? `0${month}` : `${month}`;

        // Return the formatted date string
        return `${formattedDay}/${formattedMonth}/${year}`;
    };

    findAddress(lat, lon) {
        let me = this;
        me.deviceService.findAddress(lat, lon, (response) => {
            me.deviceInfo.location = response.display_name
            const url = `https://www.google.com/maps?q=${me.findCellIDDto.lat},${me.findCellIDDto.lng}&output=embed`;
            me.safeUrl = me.sanitizer.bypassSecurityTrustResourceUrl(url);
        }, null, null)
    }

    getClassStatus(value) {
        if (value == CONSTANTS.DEVICE_STATUS.REGISTERED) {
            return ['p-2', 'text-cyan-800', "bg-cyan-100", "border-round", "inline-block"];
        } else if (value == CONSTANTS.DEVICE_STATUS.CONNECTED) {
            return ['p-2', "text-green-800", "bg-green-100", "border-round", "inline-block"];
        } else if (value == CONSTANTS.DEVICE_STATUS.LOST_CONNECTION) {
            return ['p-2', 'text-red-700', "bg-red-100", "border-round", "inline-block"];
        }
        return [];
    }

    getNameStatus(value) {
        let me = this;
        const status = me.listStatus.find(item => item.value === value);
        return status ? status.name : '';
        return "";
    }

    onSelectedType() {
        let me = this;
        me.paramSearchModel.typeCode = me.searchInfo.deviceType ? me.searchInfo.deviceType : "";
    }

    onSelectedModel() {
        let me = this;
        me.paramSearchType.modelCode = me.searchInfo.model ? me.searchInfo.model : "";
    }

    onSelectedBusiness() {
        let me = this;
        me.paramSearchIndividual.managerId = me.searchInfo.enterpriseUserId ? me.searchInfo.enterpriseUserId : -1;
    }

    onSelectedIndividual() {
        let me = this;
        me.paramSearchBusiness.customerId = me.searchInfo.customerUserId ? me.searchInfo.customerUserId : -1;
    }

    sendCommand() {
        let me = this;
        me.messageCommonService.onload();
        var object = {};
        me.inputSchema.forEach(function (el) {
            if (el.type == CONSTANTS.COMMAND_VAR_TYPE.NUMBER) {
                object[el.key] = Number(me.formData[el.key]);
            } else if (el.type == CONSTANTS.COMMAND_VAR_TYPE.STRING) {
                object[el.key] = String(me.formData[el.key]);
            } else {
                object[el.key] = me.formData[el.key];
            }
        });
        me.command.data = JSON.stringify(object);
        me.commandService.sencCommand(me.command, (response) => {
            // console.log(response);
            if (response.status == 'SENT') {
                me.messageCommonService.success(me.tranService.translate("device.text.sendCommandSucces"));
            } else if (response.status == 'STORED') {
                me.messageCommonService.success(me.tranService.translate("device.text.storedCommand"));
            }
            me.isShowSendCommand = false;
        }, null, () => {
            me.messageCommonService.offload();
        })
    }

    disableSendCommand() {
        let me = this;
        if (me.command.commandId == null || me.command.deviceId == null) {
            return true;
        }
        let result = false;
        me.inputSchema.forEach(el => {
            if (el.type == CONSTANTS.COMMAND_VAR_TYPE.STRING || el.type == CONSTANTS.COMMAND_VAR_TYPE.NUMBER) {
                if (me.formData[el.key] == undefined || me.formData[el.key] == null || me.formData[el.key] == '') {
                    result = true;
                }
            }
        })
        return result;
    }

    onSelectTypeCommand() {
        let me = this;
        let command = me.listOptionCommand.find(item => item.id == me.command.commandId)
        try {
            me.removeFormControl();
            me.inputSchema = JSON.parse(command.inputSchema)
            // console.log(me.inputSchema)
            this.inputSchema.forEach(input => {
                this.formData.addControl(input.key, new FormControl(null, Validators.required));
            });
            console.log(me.formData)
        } catch (e) {
            me.isShowSendCommand = false;
            me.messageCommonService.warning(me.tranService.translate("device.text.errorInputShema"))
        }
    }
    closeSendCommand() {
        let me = this;
        me.command = {
            deviceId: null,
            commandId: null,
            data: null,
        }
        me.inputSchema = []
    }
    removeFormControl() {
        let me = this;
        if (this.inputSchema.length > 0) {
            me.inputSchema.forEach(input => {
                me.formData.removeControl(input.key)
            })
        }
    }

    protected readonly CONSTANTS = CONSTANTS;
}
