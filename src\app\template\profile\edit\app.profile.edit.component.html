<form [formGroup]="formAccount" (ngSubmit)="onSubmitCreate()">
<div class="vnpt flex flex-row justify-content-between align-items-center bg-white p-2 border-round">
    <p-breadcrumb class="max-w-full col-7" [model]="items" [home]="home"></p-breadcrumb>
    <div class="col-5 flex flex-row justify-content-end align-items-center">
        <div class="flex flex-row justify-content-center align-items-center mr-2">
            <p-button [label]="tranService.translate('global.button.cancel')" styleClass="p-button-secondary p-button-outlined mr-2" (click)="closeForm()"></p-button>
            <p-button [label]="tranService.translate('global.button.save')" styleClass="p-button-info" type="submit" [disabled]="formAccount.invalid || isPhoneExisted || isUsernameExisted || isEmailExisted"></p-button>
        </div>
    </div>
</div>
    <p-card styleClass="mt-3">
        <p-tabView (onChange)="onTabChange($event)">
            <p-tabPanel header="{{tranService.translate('account.label.loginInfo')}}">
                <div class="flex flex-row justify-content-between">
                    <div style="width: 49%;">
                        <!-- username -->
                        <div class="w-full field grid">
                            <label htmlFor="username" class="col-fixed" style="width:180px">{{tranService.translate(
                                "account.label.username")}}</label>
                            <div class="col">
                                {{accountResponse.username}}
                            </div>
                        </div>

                    </div>
                    <div style="width: 49%;">
<!--                        &lt;!&ndash; trạng thái &ndash;&gt;-->
<!--                        <div class="w-full field grid">-->
<!--                            <label for="userType" class="col-fixed" style="width:180px">{{tranService.translate("account.label.status")}}</label>-->
<!--                            <div class="col flex align-items-center gap-6">-->
<!--                                <label *ngIf="accountResponse.status===1" class="bg-green-100 border-round inline-block p-2 text-center text-green-800 ng-star-inserted">{{tranService.translate("account.userstatus.active")}}</label>-->
<!--                                <label *ngIf="accountResponse.status===0" class="bg-red-100 border-round inline-block p-2 text-center text-red-700 ng-star-inserted">{{tranService.translate("account.userstatus.lock")}}</label>-->
<!--                            </div>-->
<!--                        </div>-->
                        <!-- loai tai khoan -->
                        <div class="w-full field grid">
                            <label for="userType" class="col-fixed" style="width:180px">{{tranService.translate("account.label.userType")}}</label>
                            <div class="col">
                                <span>{{getStringUserType(accountResponse.type)}}</span>
                            </div>
                        </div>
                        <!-- nhom quyen -->
                        <div class="w-full field grid">
                            <label htmlFor="role" class="col-fixed" style="width:180px">{{tranService.translate(
                                "account.label.role")}}</label>
                            <div class="col" style="max-width: calc(100% - 180px) !important;">
                                <div *ngFor="let item of accountResponse.roles">
                                    <div>{{ item.roleName}}</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </p-tabPanel>
        </p-tabView>
    </p-card>
    <p-card styleClass="mt-3">
        <p-tabView (onChange)="onTabChange($event)">
            <p-tabPanel *ngIf="accountInfo.userType == optionUserType.BUSINESS" header="{{tranService.translate('account.label.businessInfo')}}">
                <div class="flex flex-row justify-content-between">
                    <div style="width: 49%;">
                        <!-- businessName -->
                        <div class="w-full field grid">
                            <label htmlFor="name" class="col-fixed" style="width:180px">{{tranService.translate(
                                "account.label.businessName")}}<span class="text-red-500">*</span></label>
                            <div class="col">
                                <input class="w-full"
                                       pInputText id="name"
                                       [(ngModel)]="accountInfo.name"
                                       formControlName="name"
                                       [required]="true"
                                       [maxLength]="50"
                                       pattern="^[A-Za-zÀ-Ỹà-ỹ0-9\s\.,/&\-\/()]+$"
                                       [placeholder]="tranService.translate('account.text.inputBusinessName')"
                                       (ngModelChange)="checkExistAccount('name')"
                                />
                            </div>
                        </div>
                        <!-- error businessName -->
                        <div class="w-full field grid text-error-field">
                            <label htmlFor="name" class="col-fixed" style="width:180px"></label>
                            <div class="col">
                                <small class="text-red-500"
                                       *ngIf="formAccount.controls.name.dirty && formAccount.controls.name.errors?.required">{{tranService.translate(
                                    "global.message.required")}}</small>
                                <small class="text-red-500" *ngIf="formAccount.controls.name.errors?.maxLength">{{tranService.translate(
                                    "global.message.maxLength", {len: 50})}}</small>
                                <small class="text-red-500" *ngIf="formAccount.controls.name.errors?.pattern">{{tranService.translate(
                                    "global.message.formatBusinessName")}}</small>
                            </div>
                        </div>
                        <!-- taxCode -->
                        <div class="w-full field grid">
                            <label htmlFor="taxCode" class="col-fixed" style="width:180px">{{tranService.translate("account.label.taxCode")}}
                                <span class="text-red-500">*</span></label>
                            <div class="col">
                                <input class="w-full"
                                       pInputText id="taxCode"
                                       [(ngModel)]="accountInfo.taxCode"
                                       formControlName="taxCode"
                                       [required]="true"
                                       [maxLength]="14"
                                       pattern="^\d{10}(-\d{3})?$"
                                       [placeholder]="tranService.translate('account.text.inputTaxCode')"
                                />
                            </div>
                        </div>
                        <!-- error taxCode -->
                        <div class="w-full field grid text-error-field">
                            <label htmlFor="taxCode" class="col-fixed" style="width:180px"></label>
                            <div class="col">
                                <small class="text-red-500"
                                       *ngIf="formAccount.controls.taxCode.dirty && formAccount.controls.taxCode.errors?.required">{{tranService.translate(
                                    "global.message.required")}}</small>
                                <small class="text-red-500" *ngIf="formAccount.controls.taxCode.errors?.maxLength">{{tranService.translate(
                                    "global.message.maxLength", {len: 14})}}</small>
                                <small class="text-red-500" *ngIf="formAccount.controls.taxCode.errors?.pattern">{{tranService.translate(
                                    "global.message.formatTaxCode")}}</small>
                            </div>
                        </div>
                        <!-- provincesHeadOffice -->
                        <div class="w-full field grid">
                            <label htmlFor="provincesHeadOffice" class="col-fixed"
                                   style="width:180px">{{ tranService.translate("account.label.provincesHeadOffice") }}<span class="text-red-500">*</span></label>
                            <div class="col" style="max-width: calc(100% - 180px) !important;">
                                <p-dropdown styleClass="w-full"
                                            id="provincesHeadOffice"
                                            [showClear]="true" [filter]="true" filterBy="name"
                                            [autoDisplayFirst]="false"
                                            [required]="true"
                                            [(ngModel)]="accountInfo.provincesHeadOffice"
                                            formControlName="provincesHeadOffice"
                                            [options]="listProvinces"
                                            (onChange)="getListWardsHeadOffice()"
                                            optionLabel="name"
                                            optionValue="code"
                                            [placeholder]="tranService.translate('account.text.selectProvincesHeadOffice')"
                                ></p-dropdown>
                            </div>
                        </div>
                        <!-- error provincesHeadOffice -->
                        <div class="w-full field grid text-error-field">
                            <label htmlFor="provincesHeadOffice" class="col-fixed" style="width:180px"></label>
                            <div class="col">
                                <small class="text-red-500"
                                       *ngIf="formAccount.controls.provincesHeadOffice.dirty && formAccount.controls.provincesHeadOffice.errors?.required">{{tranService.translate(
                                    "global.message.required")}}</small>
                            </div>
                        </div>
                        <!-- wardsHeadOffice -->
                        <div class="w-full field grid">
                            <label htmlFor="wardsHeadOffice" class="col-fixed"
                                   style="width:180px">{{ tranService.translate("account.label.wardsHeadOffice") }}<span class="text-red-500">*</span></label>
                            <div class="col" style="max-width: calc(100% - 180px) !important;">
                                <p-dropdown styleClass="w-full"
                                            id="wardsHeadOffice"
                                            [showClear]="true" [filter]="true" filterBy="name"
                                            [autoDisplayFirst]="false"
                                            [required]="true"
                                            [(ngModel)]="accountInfo.wardsHeadOffice"
                                            formControlName="wardsHeadOffice"
                                            [options]="listWardsHeadOffice"
                                            optionLabel="name"
                                            optionValue="code"
                                            [placeholder]="tranService.translate('account.text.selectWardsHeadOffice')"
                                ></p-dropdown>
                            </div>
                        </div>
                        <!-- error wardsHeadOffice -->
                        <div class="w-full field grid text-error-field">
                            <label htmlFor="wardsHeadOffice" class="col-fixed" style="width:180px"></label>
                            <div class="col">
                                <small class="text-red-500"
                                       *ngIf="formAccount.controls.wardsHeadOffice.dirty && formAccount.controls.wardsHeadOffice.errors?.required">{{tranService.translate(
                                    "global.message.required")}}</small>
                            </div>
                        </div>
                        <!-- addressHeadOffice -->
                        <div class="w-full field grid">
                            <label htmlFor="addressHeadOffice" class="col-fixed" style="width:180px">{{tranService.translate("account.label.addressHeadOffice")}}
                            </label>
                            <div class="col">
                                <input class="w-full"
                                       pInputText id="addressHeadOffice"
                                       [(ngModel)]="accountInfo.addressHeadOffice"
                                       formControlName="addressHeadOffice"
                                       [required]="true"
                                       [maxLength]="255"
                                       [pattern]="addressPattern"
                                       [placeholder]="tranService.translate('account.text.inputAddressHeadOffice')"
                                />
                            </div>
                        </div>
                        <!-- error addressHeadOffice -->
                        <div class="w-full field grid text-error-field">
                            <label htmlFor="addressHeadOffice" class="col-fixed" style="width:180px"></label>
                            <div class="col">
                                <small class="text-red-500" *ngIf="formAccount.controls.addressHeadOffice.errors?.maxLength">{{tranService.translate(
                                    "global.message.maxLength", {len: 255})}}</small>
                                <small class="text-red-500" *ngIf="formAccount.controls.addressHeadOffice.errors?.pattern">{{tranService.translate(
                                    "global.message.invalidAddress")}}</small>
                            </div>
                        </div>
                        <!-- description -->
                        <div class="w-full field grid">
                            <label htmlFor="description" class="col-fixed" style="width:180px;height: fit-content;">{{tranService.translate(
                                "account.label.description")}}</label>
                            <div class="col">
                            <textarea class="w-full" style="resize: none;"
                                      rows="5"
                                      [autoResize]="false"
                                      pInputTextarea id="description"
                                      [(ngModel)]="accountInfo.description"
                                      formControlName="description"
                                      [maxlength]="255"
                                      [placeholder]="tranService.translate('sim.text.inputDescription')"
                            ></textarea>
                            </div>
                        </div>
                        <!-- error description -->
                        <div class="w-full field grid text-error-field">
                            <label htmlFor="description" class="col-fixed" style="width:180px"></label>
                            <div class="col">
                                <small class="text-red-500" *ngIf="formAccount.controls.description.errors?.maxLength">{{tranService.translate(
                                    "global.message.maxLength", {len: 255})}}</small>
                            </div>
                        </div>
                    </div>
                    <div style="width: 49%;">

                        <!-- ten nguoi dai dien -->
                        <div class="w-full field grid">
                            <label htmlFor="representativeName" class="col-fixed" style="width:180px">{{tranService.translate("account.label.representativeName")}}<span class="text-red-500">*</span></label>
                            <div class="col">
                                <input class="w-full"
                                       pInputText id="representativeName"
                                       [(ngModel)]="accountInfo.representativeName"
                                       required
                                       [pattern]="vietnamesePattern"
                                       formControlName="representativeName"
                                       [placeholder]="tranService.translate('account.text.inputRepresentativeName')"
                                />
                            </div>
                        </div>
                        <!-- error ten nguoi dai dien -->
                        <div class="w-full field grid text-error-field">
                            <label htmlFor="representativeName" class="col-fixed" style="width:180px"></label>
                            <div class="col">
                                <small class="text-red-500"
                                       *ngIf="formAccount.controls.representativeName.dirty && formAccount.controls.representativeName.errors?.required">{{tranService.translate(
                                    "global.message.required")}}</small>
                                <small class="text-red-500" *ngIf="formAccount.controls.representativeName.errors?.pattern">{{tranService.translate(
                                    "global.message.wrongFormatNameVietNam")}}</small>
                            </div>
                        </div>
                        <!-- phone -->
                        <div class="w-full field grid">
                            <label htmlFor="phone" class="col-fixed" style="width:180px">{{tranService.translate("account.label.phone")}}</label>
                            <div class="col">
                                <input class="w-full"
                                       pInputText id="phone"
                                       [(ngModel)]="accountInfo.phone"
                                       formControlName="phone"
                                       pattern="^((\+?[1-9][0-9])|0?)[1-9][0-9]{8,9}$"
                                       [placeholder]="tranService.translate('account.text.inputPhone')"
                                />
                            </div>
                        </div>
                        <!-- error phone -->
                        <div class="w-full field grid text-error-field">
                            <label htmlFor="phone" class="col-fixed" style="width:180px"></label>
                            <div class="col">
                                <small class="text-red-500" *ngIf="formAccount.controls.phone.errors?.pattern">{{tranService.translate(
                                    "global.message.invalidPhone")}}</small>
                            </div>
                        </div>
                        <!-- email -->
                        <div class="w-full field grid">
                            <label htmlFor="email" class="col-fixed" style="width:180px">{{tranService.translate("account.label.email")}}<span
                                class="text-red-500">*</span></label>
                            <div class="col">
                                <input class="w-full"
                                       pInputText id="email"
                                       [(ngModel)]="accountInfo.email"
                                       formControlName="email"
                                       [required]="true"
                                       [maxLength]="255"
                                       pattern="^[a-z0-9]+[a-z0-9\-\._]*[a-z0-9]+@([a-z0-9]+[a-z0-9\-\._]*[a-z0-9]+)+(\.[a-z]{2,})$"
                                       [placeholder]="tranService.translate('account.text.inputEmail')"
                                       (ngModelChange)="checkExistAccount('email')"
                                />
                            </div>
                        </div>
                        <!-- error email -->
                        <div class="w-full field grid text-error-field">
                            <label htmlFor="email" class="col-fixed" style="width:180px"></label>
                            <div class="col">
                                <small class="text-red-500"
                                       *ngIf="formAccount.controls.email.dirty && formAccount.controls.email.errors?.required">{{tranService.translate(
                                    "global.message.required")}}</small>
                                <small class="text-red-500" *ngIf="formAccount.controls.email.errors?.maxLength">{{tranService.translate(
                                    "global.message.maxLength", {len: 255})}}</small>
                                <small class="text-red-500" *ngIf="formAccount.controls.email.errors?.pattern">{{tranService.translate(
                                    "global.message.invalidEmail")}}</small>
                                <small class="text-red-500" *ngIf="isEmailExisted">{{tranService.translate("global.message.exists",
                                    {type: tranService.translate("account.label.email").toLowerCase()})}}</small>
                            </div>
                        </div>
                        <!-- provincesContact -->
                        <div class="w-full field grid">
                            <label htmlFor="provincesContact" class="col-fixed"
                                   style="width:180px">{{ tranService.translate("account.label.provincesContact") }}<span class="text-red-500">*</span></label>
                            <div class="col" style="max-width: calc(100% - 180px) !important;">
                                <p-dropdown styleClass="w-full"
                                            id="provincesContact"
                                            [showClear]="true" [filter]="true" filterBy="name"
                                            [autoDisplayFirst]="false"
                                            [(ngModel)]="accountInfo.provincesContact"
                                            formControlName="provincesContact"
                                            [options]="listProvinces"
                                            (onChange)="getListWardsContact()"
                                            optionLabel="name"
                                            [required]="true"
                                            optionValue="code"
                                            [placeholder]="tranService.translate('account.text.selectProvincesContact')"
                                ></p-dropdown>
                            </div>
                        </div>
                        <!-- error provincesContact -->
                        <div class="w-full field grid text-error-field">
                            <label htmlFor="provincesContact" class="col-fixed" style="width:180px"></label>
                            <div class="col">
                                <small class="text-red-500"
                                       *ngIf="formAccount.controls.provincesContact.dirty && formAccount.controls.provincesContact.errors?.required">{{tranService.translate(
                                    "global.message.required")}}</small>
                            </div>
                        </div>
                        <!-- wardsContact -->
                        <div class="w-full field grid">
                            <label htmlFor="wardsContact" class="col-fixed"
                                   style="width:180px">{{ tranService.translate("account.label.wardsContact") }}<span class="text-red-500">*</span></label>
                            <div class="col" style="max-width: calc(100% - 180px) !important;">
                                <p-dropdown styleClass="w-full"
                                            id="wardsContact"
                                            [showClear]="true" [filter]="true" filterBy="name"
                                            [autoDisplayFirst]="false"
                                            [(ngModel)]="accountInfo.wardsContact"
                                            formControlName="wardsContact"
                                            [options]="listWardsContact"
                                            optionLabel="name"
                                            [required]="true"
                                            optionValue="code"
                                            [placeholder]="tranService.translate('account.text.selectWardsContact')"
                                ></p-dropdown>
                            </div>
                        </div>
                        <!-- error wardsContact -->
                        <div class="w-full field grid text-error-field">
                            <label htmlFor="wardsContact" class="col-fixed" style="width:180px"></label>
                            <div class="col">
                                <small class="text-red-500"
                                       *ngIf="formAccount.controls.wardsContact.dirty && formAccount.controls.wardsContact.errors?.required">{{tranService.translate(
                                    "global.message.required")}}</small>
                            </div>
                        </div>
                        <!-- địa chỉ liên hệ -->
                        <div class="w-full field grid">
                            <label htmlFor="addressContact" class="col-fixed" style="width:180px">{{tranService.translate("account.label.addressContact")}}</label>
                            <div class="col">
                                <input class="w-full"
                                       pInputText id="addressContact"
                                       [(ngModel)]="accountInfo.addressContact"
                                       formControlName="addressContact"
                                       [pattern]="addressPattern"
                                       [maxLength]="255"
                                       [placeholder]="tranService.translate('account.text.inputAddressContact')"
                                />
                            </div>
                        </div>
                        <!-- error addressContact -->
                        <div class="w-full field grid text-error-field">
                            <label htmlFor="addressContact" class="col-fixed" style="width:180px"></label>
                            <div class="col">
                                <small class="text-red-500" *ngIf="formAccount.controls.addressContact.errors?.maxLength">{{tranService.translate(
                                    "global.message.maxLength", {len: 255})}}</small>
                                <small class="text-red-500" *ngIf="formAccount.controls.addressContact.errors?.pattern">{{tranService.translate(
                                    "global.message.invalidAddress")}}</small>
                            </div>
                        </div>
                    </div>
                </div>
            </p-tabPanel>
            <p-tabPanel *ngIf="accountInfo.userType == optionUserType.INDIVIDUAL" header="{{tranService.translate('account.label.individualInfo')}}">
                <div class="flex flex-row justify-content-between">
                    <div style="width: 49%;">
                        <!-- customerName -->
                        <div class="w-full field grid">
                            <label htmlFor="customerName" class="col-fixed" style="width:180px">{{tranService.translate(
                                "account.label.customerName")}}<span class="text-red-500">*</span></label>
                            <div class="col">
                                <input class="w-full"
                                       pInputText id="name"
                                       [(ngModel)]="accountInfo.name"
                                       formControlName="name"
                                       [required]="true"
                                       [maxLength]="255"
                                       [pattern]="vietnamesePattern"
                                       [placeholder]="tranService.translate('account.text.inputCustomerName')"
                                />
                            </div>
                        </div>
                        <!-- error customerName -->
                        <div class="w-full field grid text-error-field">
                            <label htmlFor="customerName" class="col-fixed" style="width:180px"></label>
                            <div class="col">
                                <small class="text-red-500"
                                       *ngIf="formAccount.controls.name.dirty && formAccount.controls.name.errors?.required">{{tranService.translate(
                                    "global.message.required")}}</small>
                                <small class="text-red-500" *ngIf="formAccount.controls.name.errors?.maxLength">{{tranService.translate(
                                    "global.message.maxLength", {len: 255})}}</small>
                                <small class="text-red-500" *ngIf="formAccount.controls.name.errors?.pattern">{{tranService.translate(
                                    "global.message.wrongFormatNameVietNam")}}</small>
                            </div>
                        </div>
                        <!-- phone -->
                        <div class="w-full field grid">
                            <label htmlFor="phone" class="col-fixed" style="width:180px">{{tranService.translate("account.label.phone")}}</label>
                            <div class="col">
                                <input class="w-full"
                                       pInputText id="phone"
                                       [(ngModel)]="accountInfo.phone"
                                       formControlName="phone"
                                       pattern="^((\+?[1-9][0-9])|0?)[1-9][0-9]{8,9}$"
                                       [placeholder]="tranService.translate('account.text.inputPhone')"
                                />
                            </div>
                        </div>
                        <!-- error phone -->
                        <div class="w-full field grid text-error-field">
                            <label htmlFor="phone" class="col-fixed" style="width:180px"></label>
                            <div class="col">
                                <small class="text-red-500" *ngIf="formAccount.controls.phone.errors?.pattern">{{tranService.translate(
                                    "global.message.invalidPhone")}}</small>
                            </div>
                        </div>
                        <!-- email -->
                        <div class="w-full field grid">
                            <label htmlFor="email" class="col-fixed" style="width:180px">{{tranService.translate("account.label.email")}}<span
                                class="text-red-500">*</span></label>
                            <div class="col">
                                <input class="w-full"
                                       pInputText id="email"
                                       [(ngModel)]="accountInfo.email"
                                       formControlName="email"
                                       [required]="true"
                                       [maxLength]="255"
                                       pattern="^[a-z0-9]+[a-z0-9\-\._]*[a-z0-9]+@([a-z0-9]+[a-z0-9\-\._]*[a-z0-9]+)+(\.[a-z]{2,})$"
                                       [placeholder]="tranService.translate('account.text.inputEmail')"
                                       (ngModelChange)="checkExistAccount('email')"
                                />
                            </div>
                        </div>
                        <!-- error email -->
                        <div class="w-full field grid text-error-field">
                            <label htmlFor="email" class="col-fixed" style="width:180px"></label>
                            <div class="col">
                                <small class="text-red-500"
                                       *ngIf="formAccount.controls.email.dirty && formAccount.controls.email.errors?.required">{{tranService.translate(
                                    "global.message.required")}}</small>
                                <small class="text-red-500" *ngIf="formAccount.controls.email.errors?.maxLength">{{tranService.translate(
                                    "global.message.maxLength", {len: 255})}}</small>
                                <small class="text-red-500" *ngIf="formAccount.controls.email.errors?.pattern">{{tranService.translate(
                                    "global.message.invalidEmail")}}</small>
                                <small class="text-red-500" *ngIf="isEmailExisted">{{tranService.translate("global.message.exists",
                                    {type: tranService.translate("account.label.email").toLowerCase()})}}</small>
                            </div>
                        </div>
                        <!-- provincesContact -->
                        <div class="w-full field grid">
                            <label htmlFor="provincesContact" class="col-fixed"
                                   style="width:180px">{{ tranService.translate("account.label.provincesContact") }}<span class="text-red-500">*</span></label>
                            <div class="col" style="max-width: calc(100% - 180px) !important;">
                                <p-dropdown styleClass="w-full"
                                            id="provincesContact"
                                            [showClear]="true" [filter]="true" filterBy="name"
                                            [autoDisplayFirst]="false"
                                            [(ngModel)]="accountInfo.provincesContact"
                                            formControlName="provincesContact"
                                            [options]="listProvinces"
                                            [required]="true"
                                            (onChange)="getListWardsContact()"
                                            optionLabel="name"
                                            optionValue="code"
                                            [placeholder]="tranService.translate('account.text.selectProvincesContact')"
                                ></p-dropdown>
                            </div>
                        </div>
                        <!-- error provincesContact -->
                        <div class="w-full field grid text-error-field">
                            <label htmlFor="provincesContact" class="col-fixed" style="width:180px"></label>
                            <div class="col">
                                <small class="text-red-500"
                                       *ngIf="formAccount.controls.provincesContact.dirty && formAccount.controls.provincesContact.errors?.required">{{tranService.translate(
                                    "global.message.required")}}</small>
                            </div>
                        </div>
                        <!-- wardsContact -->
                        <div class="w-full field grid">
                            <label htmlFor="wardsContact" class="col-fixed"
                                   style="width:180px">{{ tranService.translate("account.label.wardsContact") }}<span class="text-red-500">*</span></label>
                            <div class="col" style="max-width: calc(100% - 180px) !important;">
                                <p-dropdown styleClass="w-full"
                                            id="wardsContact"
                                            [showClear]="true" [filter]="true" filterBy="name"
                                            [autoDisplayFirst]="false"
                                            [(ngModel)]="accountInfo.wardsContact"
                                            formControlName="wardsContact"
                                            [options]="listWardsContact"
                                            optionLabel="name"
                                            [required]="true"
                                            optionValue="code"
                                            [placeholder]="tranService.translate('account.text.selectWardsContact')"
                                ></p-dropdown>
                            </div>
                        </div>
                        <!-- error wardsContact -->
                        <div class="w-full field grid text-error-field">
                            <label htmlFor="wardsContact" class="col-fixed" style="width:180px"></label>
                            <div class="col">
                                <small class="text-red-500"
                                       *ngIf="formAccount.controls.wardsContact.dirty && formAccount.controls.wardsContact.errors?.required">{{tranService.translate(
                                    "global.message.required")}}</small>
                            </div>
                        </div>
                        <!-- địa chỉ liên hệ -->
                        <div class="w-full field grid">
                            <label htmlFor="addressContact" class="col-fixed" style="width:180px">{{tranService.translate("account.label.addressContact")}}</label>
                            <div class="col">
                                <input class="w-full"
                                       pInputText id="addressContact"
                                       [(ngModel)]="accountInfo.addressContact"
                                       formControlName="addressContact"
                                       [pattern]="addressPattern"
                                       [maxLength]="255"
                                       [placeholder]="tranService.translate('account.text.inputAddressContact')"
                                />
                            </div>
                        </div>
                        <!-- error addressContact -->
                        <div class="w-full field grid text-error-field">
                            <label htmlFor="addressContact" class="col-fixed" style="width:180px"></label>
                            <div class="col">
                                <small class="text-red-500"
                                       *ngIf="formAccount.controls.addressContact.dirty && formAccount.controls.addressContact.errors?.required">{{tranService.translate(
                                    "global.message.required")}}</small>
                                <small class="text-red-500" *ngIf="formAccount.controls.addressContact.errors?.maxLength">{{tranService.translate(
                                    "global.message.maxLength", {len: 255})}}</small>
                                <small class="text-red-500" *ngIf="formAccount.controls.addressContact.errors?.pattern">{{tranService.translate(
                                    "global.message.invalidAddress")}}</small>
                            </div>
                        </div>
                    </div>
                    <div style="width: 49%;">
                        <!-- Danh sách tài khoản khách hàng để chọn tài khoản khách hàng root -->
                        <div class="w-full field grid" *ngIf="accountInfo.userType == optionUserType.INDIVIDUAL && userType == CONSTANTS.USER_TYPE.ADMIN">
                            <label htmlFor="roles" class="col-fixed" style="width:180px">{{tranService.translate(
                                "account.label.accountRootId")}}<span class="text-red-500">*</span></label>
                            <div class="col" style="max-width: calc(100% - 180px) !important;">
                                <vnpt-select
                                    [control]="controlComboSelectCustomerAccount"
                                    class="w-full"
                                    [(value)]="accountInfo.accountRootId"
                                    [placeholder]="tranService.translate('account.text.selectAccountRootId')"
                                    objectKey="account"
                                    paramKey="username"
                                    keyReturn="id"
                                    [required]="true"
                                    displayPattern="${name} - ${username}"
                                    typeValue="primitive"
                                    [paramDefault]="paramSearchAccountRootId"
                                    [loadData]="loadCustomerAccount.bind(this)"
                                    [isMultiChoice]="false"
                                ></vnpt-select>
                            </div>
                        </div>
                        <!-- description -->
                        <div class="w-full field grid">
                            <label htmlFor="description" class="col-fixed" style="width:180px;height: fit-content;">{{tranService.translate(
                                "account.label.description")}}</label>
                            <div class="col">
                            <textarea class="w-full" style="resize: none;"
                                      rows="5"
                                      [autoResize]="false"
                                      pInputTextarea id="description"
                                      [(ngModel)]="accountInfo.description"
                                      formControlName="description"
                                      [maxlength]="255"
                                      [placeholder]="tranService.translate('sim.text.inputDescription')"
                            ></textarea>
                            </div>
                        </div>
                        <!-- error description -->
                        <div class="w-full field grid text-error-field">
                            <label htmlFor="description" class="col-fixed" style="width:180px"></label>
                            <div class="col">
                                <small class="text-red-500" *ngIf="formAccount.controls.description.errors?.maxLength">{{tranService.translate(
                                    "global.message.maxLength", {len: 255})}}</small>
                            </div>
                        </div>
                    </div>
                </div>
            </p-tabPanel>
            <p-tabPanel *ngIf="accountInfo.userType == optionUserType.ADMIN" header="{{tranService.translate('account.label.generalInfo')}}">
                <div class="flex flex-row justify-content-between">
                    <div style="width: 49%;">
                        <!-- customerName -->
                        <div class="w-full field grid">
                            <label htmlFor="customerName" class="col-fixed" style="width:180px">{{tranService.translate(
                                "account.label.customerName")}}<span class="text-red-500">*</span></label>
                            <div class="col">
                                <input class="w-full"
                                       pInputText id="name"
                                       [(ngModel)]="accountInfo.name"
                                       formControlName="name"
                                       [required]="true"
                                       [maxLength]="255"
                                       [pattern]="vietnamesePattern"
                                       [placeholder]="tranService.translate('account.text.inputCustomerName')"
                                />
                            </div>
                        </div>
                        <!-- error customerName -->
                        <div class="w-full field grid text-error-field">
                            <label htmlFor="customerName" class="col-fixed" style="width:180px"></label>
                            <div class="col">
                                <small class="text-red-500"
                                       *ngIf="formAccount.controls.name.dirty && formAccount.controls.name.errors?.required">{{tranService.translate(
                                    "global.message.required")}}</small>
                                <small class="text-red-500" *ngIf="formAccount.controls.name.errors?.maxLength">{{tranService.translate(
                                    "global.message.maxLength", {len: 255})}}</small>
                                <small class="text-red-500" *ngIf="formAccount.controls.name.errors?.pattern">{{tranService.translate(
                                    "global.message.wrongFormatNameVietNam")}}</small>
                            </div>
                        </div>
                        <!-- phone -->
                        <div class="w-full field grid">
                            <label htmlFor="phone" class="col-fixed" style="width:180px">{{tranService.translate("account.label.phone")}}</label>
                            <div class="col">
                                <input class="w-full"
                                       pInputText id="phone"
                                       [(ngModel)]="accountInfo.phone"
                                       formControlName="phone"
                                       pattern="^((\+?[1-9][0-9])|0?)[1-9][0-9]{8,9}$"
                                       [placeholder]="tranService.translate('account.text.inputPhone')"
                                />
                            </div>
                        </div>
                        <!-- error phone -->
                        <div class="w-full field grid text-error-field">
                            <label htmlFor="phone" class="col-fixed" style="width:180px"></label>
                            <div class="col">
                                <small class="text-red-500" *ngIf="formAccount.controls.phone.errors?.pattern">{{tranService.translate(
                                    "global.message.invalidPhone")}}</small>
                            </div>
                        </div>
                        <!-- email -->
                        <div class="w-full field grid">
                            <label htmlFor="email" class="col-fixed" style="width:180px">{{tranService.translate("account.label.email")}}<span
                                class="text-red-500">*</span></label>
                            <div class="col">
                                <input class="w-full"
                                       pInputText id="email"
                                       [(ngModel)]="accountInfo.email"
                                       formControlName="email"
                                       [required]="true"
                                       [maxLength]="255"
                                       pattern="^[a-z0-9]+[a-z0-9\-\._]*[a-z0-9]+@([a-z0-9]+[a-z0-9\-\._]*[a-z0-9]+)+(\.[a-z]{2,})$"
                                       [placeholder]="tranService.translate('account.text.inputEmail')"
                                       (ngModelChange)="checkExistAccount('email')"
                                />
                            </div>
                        </div>
                        <!-- error email -->
                        <div class="w-full field grid text-error-field">
                            <label htmlFor="email" class="col-fixed" style="width:180px"></label>
                            <div class="col">
                                <small class="text-red-500"
                                       *ngIf="formAccount.controls.email.dirty && formAccount.controls.email.errors?.required">{{tranService.translate(
                                    "global.message.required")}}</small>
                                <small class="text-red-500" *ngIf="formAccount.controls.email.errors?.maxLength">{{tranService.translate(
                                    "global.message.maxLength", {len: 255})}}</small>
                                <small class="text-red-500" *ngIf="formAccount.controls.email.errors?.pattern">{{tranService.translate(
                                    "global.message.invalidEmail")}}</small>
                                <small class="text-red-500" *ngIf="isEmailExisted">{{tranService.translate("global.message.exists",
                                    {type: tranService.translate("account.label.email").toLowerCase()})}}</small>
                            </div>
                        </div>
                        <!-- provincesContact -->
                        <div class="w-full field grid">
                            <label htmlFor="provincesContact" class="col-fixed"
                                   style="width:180px">{{ tranService.translate("account.label.provincesContact") }}<span class="text-red-500">*</span></label>
                            <div class="col" style="max-width: calc(100% - 180px) !important;">
                                <p-dropdown styleClass="w-full"
                                            id="provincesContact"
                                            [showClear]="true" [filter]="true" filterBy="name"
                                            [autoDisplayFirst]="false"
                                            [(ngModel)]="accountInfo.provincesContact"
                                            formControlName="provincesContact"
                                            [options]="listProvinces"
                                            [required]="true"
                                            (onChange)="getListWardsContact()"
                                            optionLabel="name"
                                            optionValue="code"
                                            [placeholder]="tranService.translate('account.text.selectProvincesContact')"
                                ></p-dropdown>
                            </div>
                        </div>
                        <!-- error provincesContact -->
                        <div class="w-full field grid text-error-field">
                            <label htmlFor="provincesContact" class="col-fixed" style="width:180px"></label>
                            <div class="col">
                                <small class="text-red-500"
                                       *ngIf="formAccount.controls.provincesContact.dirty && formAccount.controls.provincesContact.errors?.required">{{tranService.translate(
                                    "global.message.required")}}</small>
                            </div>
                        </div>
                        <!-- wardsContact -->
                        <div class="w-full field grid">
                            <label htmlFor="wardsContact" class="col-fixed"
                                   style="width:180px">{{ tranService.translate("account.label.wardsContact") }}<span class="text-red-500">*</span></label>
                            <div class="col" style="max-width: calc(100% - 180px) !important;">
                                <p-dropdown styleClass="w-full"
                                            id="wardsContact"
                                            [showClear]="true" [filter]="true" filterBy="name"
                                            [autoDisplayFirst]="false"
                                            [(ngModel)]="accountInfo.wardsContact"
                                            formControlName="wardsContact"
                                            [options]="listWardsContact"
                                            optionLabel="name"
                                            [required]="true"
                                            optionValue="code"
                                            [placeholder]="tranService.translate('account.text.selectWardsContact')"
                                ></p-dropdown>
                            </div>
                        </div>
                        <!-- error wardsContact -->
                        <div class="w-full field grid text-error-field">
                            <label htmlFor="wardsContact" class="col-fixed" style="width:180px"></label>
                            <div class="col">
                                <small class="text-red-500"
                                       *ngIf="formAccount.controls.wardsContact.dirty && formAccount.controls.wardsContact.errors?.required">{{tranService.translate(
                                    "global.message.required")}}</small>
                            </div>
                        </div>
                        <!-- địa chỉ liên hệ -->
                        <div class="w-full field grid">
                            <label htmlFor="addressContact" class="col-fixed" style="width:180px">{{tranService.translate("account.label.addressContact")}}</label>
                            <div class="col">
                                <input class="w-full"
                                       pInputText id="addressContact"
                                       [(ngModel)]="accountInfo.addressContact"
                                       formControlName="addressContact"
                                       [pattern]="addressPattern"
                                       [maxLength]="255"
                                       [placeholder]="tranService.translate('account.text.inputAddressContact')"
                                />
                            </div>
                        </div>
                        <!-- error addressContact -->
                        <div class="w-full field grid text-error-field">
                            <label htmlFor="addressContact" class="col-fixed" style="width:180px"></label>
                            <div class="col">
                                <small class="text-red-500"
                                       *ngIf="formAccount.controls.addressContact.dirty && formAccount.controls.addressContact.errors?.required">{{tranService.translate(
                                    "global.message.required")}}</small>
                                <small class="text-red-500" *ngIf="formAccount.controls.addressContact.errors?.maxLength">{{tranService.translate(
                                    "global.message.maxLength", {len: 255})}}</small>
                                <small class="text-red-500" *ngIf="formAccount.controls.addressContact.errors?.pattern">{{tranService.translate(
                                    "global.message.invalidAddress")}}</small>
                            </div>
                        </div>
                    </div>
                    <div style="width: 49%;">
                        <!-- Danh sách tài khoản khách hàng để chọn tài khoản khách hàng root -->
                        <div class="w-full field grid" *ngIf="accountInfo.userType == optionUserType.INDIVIDUAL && userType == CONSTANTS.USER_TYPE.ADMIN">
                            <label htmlFor="roles" class="col-fixed" style="width:180px">{{tranService.translate(
                                "account.label.accountRootId")}}<span class="text-red-500">*</span></label>
                            <div class="col" style="max-width: calc(100% - 180px) !important;">
                                <vnpt-select
                                    [control]="controlComboSelectCustomerAccount"
                                    class="w-full"
                                    [(value)]="accountInfo.accountRootId"
                                    [placeholder]="tranService.translate('account.text.selectAccountRootId')"
                                    objectKey="account"
                                    paramKey="username"
                                    keyReturn="id"
                                    [required]="true"
                                    displayPattern="${name} - ${username}"
                                    typeValue="primitive"
                                    [paramDefault]="paramSearchAccountRootId"
                                    [loadData]="loadCustomerAccount.bind(this)"
                                    [isMultiChoice]="false"
                                ></vnpt-select>
                            </div>
                        </div>
                        <!-- description -->
                        <div class="w-full field grid">
                            <label htmlFor="description" class="col-fixed" style="width:180px;height: fit-content;">{{tranService.translate(
                                "account.label.description")}}</label>
                            <div class="col">
                            <textarea class="w-full" style="resize: none;"
                                      rows="5"
                                      [autoResize]="false"
                                      pInputTextarea id="description"
                                      [(ngModel)]="accountInfo.description"
                                      formControlName="description"
                                      [maxlength]="255"
                                      [placeholder]="tranService.translate('sim.text.inputDescription')"
                            ></textarea>
                            </div>
                        </div>
                        <!-- error description -->
                        <div class="w-full field grid text-error-field">
                            <label htmlFor="description" class="col-fixed" style="width:180px"></label>
                            <div class="col">
                                <small class="text-red-500" *ngIf="formAccount.controls.description.errors?.maxLength">{{tranService.translate(
                                    "global.message.maxLength", {len: 255})}}</small>
                            </div>
                        </div>
                    </div>
                </div>
            </p-tabPanel>
        </p-tabView>
    </p-card>
</form>

