import {Inject, Injector, OnInit} from '@angular/core';
import {Component} from '@angular/core';;
import {ComponentBase} from 'src/app/component.base';
import {LayoutService} from "src/app/service/app.layout.service";
import {CONSTANTS} from 'src/app/service/comon/constants';

interface ItemMenu {
    label: string,
    icon?: string,
    routerLink?: Array<string>,
    items?: Array<ItemMenu>,
    routerLinkActiveOptions?: {
        paths?: string,
        queryParams?: string,
        matrixParams?: string,
        fragment?: string
    },
    badge?: string,
    url?: Array<string>,
    target?: string
}

@Component({
    selector: 'app-menu',
    templateUrl: './app.menu.component.html'
})
export class AppMenuComponent extends ComponentBase implements OnInit {
    // model: ItemMenu[] = [];
    model: {
        label: string,
        items: any[] // MenuItem[]
    }[] = [];

    constructor(public layoutService: LayoutService, injector: Injector) {
        super(injector);
    }

    oldScrollTop = 0;

    handleOpenBigSidebar() {
        this.layoutService.changeSize('big')
        let node = document.querySelector(".display-subElement");
        node?.remove();
    }

    handleScroll(event: any) {
        let node = document.querySelector(".display-subElement");
        node?.remove();
        // let delTa = this.oldScrollTop - event.target["scrollTop"];
        // this.oldScrollTop = event.target["scrollTop"];
        // let node = document.querySelector(".display-subElement")as HTMLDivElement;
        // if (node) {
        //   let currentTop = parseInt(node.style.top || "0", 10);
        //   let newTop = currentTop + delTa;

        //   // Giới hạn vị trí top để không bị quá mức cho phép
        // //   if (newTop < 0) {
        // //     newTop = 0;
        // //   }

        //   // Gán lại giá trị top cho phần tử
        //   node.style.top = newTop + "px";
        // }
    }

    isActiveRoute(item: any): boolean {
        if (item.routerLink) {
            const url = item.routerLink.join('/') || '';
            return this.router.isActive(url, false) || this.router.url.includes(url) || this.router.url + "/" == url;
        }

        if (item.items && item.items.length > 0) {
            return item.items.some(subItem => this.isActiveRoute(subItem));
        }

        return false;
    }


    ngOnInit() {
        // console.log(this.isActiveRoute("/accounts"))
        let userType = this.sessionService.userInfo.type;

        this.model = [
            {
                label: "",
                items: [
                    //dashboard
                    {
                        label: this.tranService.translate("global.menu.dashboard"),
                        icon: "pi pi-fw pi-chart-bar",
                        routerLink: ["/dashboard"],
                        visible: this.checkAuthen([CONSTANTS.PERMISSIONS.DYNAMICCHART.VIEW_LIST])
                    },
                    //device management
                    {
                        label: this.tranService.translate("global.menu.devicemgmt"),
                        icon: "pi pi-fw pi-calculator",
                        items: [
                            {
                                label: this.tranService.translate("global.menu.listdevice"),
                                routerLink: ["/devices"],
                                visible: this.checkAuthen([CONSTANTS.PERMISSIONS.DEVICE.VIEW_LIST]),
                            },
                        ],
                        visible: this.checkAuthen([CONSTANTS.PERMISSIONS.DEVICE.VIEW_LIST]),
                    },
                    //Cảnh báo
                    {
                        label: this.tranService.translate("global.menu.rule"),
                        icon: "pi pi-fw pi-bell",
                        items: [
                            {
                                label: this.tranService.translate("global.menu.alertSettings"),
                                routerLink: ["/alerts"],
                                visible: this.checkAuthen([CONSTANTS.PERMISSIONS.ALERT.VIEW_LIST])

                            },
                            // {
                            //     label: this.tranService.translate("global.menu.alertReceivingGroup"),
                            //     routerLink: ["/alerts/receiving-group"],
                            //     visible: this.checkAuthen([CONSTANTS.PERMISSIONS.ALERT_RECEIVING_GROUP.VIEW_LIST])
                            // },
                            {
                                label: this.tranService.translate("global.menu.alertHistory"),
                                routerLink: ["/alerts/history"],
                                visible: this.checkAuthen([CONSTANTS.PERMISSIONS.ALERT_HISTORY.VIEW_LIST]),
                            }
                        ],
                        visible: this.checkAuthen([CONSTANTS.PERMISSIONS.ALERT.VIEW_LIST, CONSTANTS.PERMISSIONS.ALERT_RECEIVING_GROUP.VIEW_LIST, CONSTANTS.PERMISSIONS.ALERT_HISTORY.VIEW_LIST])
                    },
                    //account management
                    {
                        label: this.tranService.translate("global.menu.accountmgmt"),
                        icon: "pi pi-fw pi-users",
                        items: [
                            {
                                label: this.tranService.translate("global.menu.listaccount"),
                                routerLink: ["/accounts"],
                                visible: this.checkAuthen([CONSTANTS.PERMISSIONS.ACCOUNT.VIEW_LIST])
                            },
                            {
                                label: this.tranService.translate("global.menu.listroles"),
                                routerLink: ["/roles"],
                                visible: this.checkAuthen([CONSTANTS.PERMISSIONS.ROLE.VIEW_LIST])
                            },
                            {
                                label: this.tranService.translate("global.menu.listpermissions"),
                                routerLink: ["/permissions"],
                                visible: this.checkAuthen([CONSTANTS.PERMISSIONS.PERMISSION.VIEW_LIST])
                            },
                            // {
                            //     label: this.tranService.translate("global.menu.termpolicy"),
                            //     routerLink: ["/policies"],
                            //     visible: userType == CONSTANTS.USER_TYPE.INDIVIDUAL || userType == CONSTANTS.USER_TYPE.BUSINESS
                            // },
                            // {
                            //     label: this.tranService.translate("global.menu.termpolicyhistory"),
                            //     routerLink: ["/policies/history"],
                            //     visible: userType == CONSTANTS.USER_TYPE.INDIVIDUAL || userType == CONSTANTS.USER_TYPE.BUSINESS
                            // },
                            // {
                            //     label: this.tranService.translate("logs.menu.log"),
                            //     routerLink: ["/history-activity/list"],
                            //     visible: (userType == CONSTANTS.USER_TYPE.ADMIN || userType == CONSTANTS.USER_TYPE.BUSINESS
                            //         || userType == CONSTANTS.USER_TYPE.BUSINESS) && this.checkAuthen([CONSTANTS.PERMISSIONS.LOG.VIEW_LIST])
                            // },
                            // {
                            //     label: this.tranService.translate("global.menu.apiLogs"),
                            //     routerLink: ["/accounts/logApi"],
                            //     visible: this.checkAuthen([CONSTANTS.PERMISSIONS.ACCOUNT.SEARCH_LOG_API])
                            // },
                        ],
                        visible: this.checkAuthen([CONSTANTS.PERMISSIONS.ACCOUNT.VIEW_LIST, CONSTANTS.PERMISSIONS.ROLE.VIEW_LIST, CONSTANTS.PERMISSIONS.PERMISSION.VIEW_LIST])
                    },
                    //customer management
                    // {
                    //     label: this.tranService.translate("global.menu.customermgmt"),
                    //     icon: "pi pi-fw pi-users",
                    //     items: [
                    //         {
                    //             label: this.tranService.translate("global.menu.listcustomer"),
                    //             routerLink: ["/customers"],
                    //             visible: this.checkAuthen([CONSTANTS.PERMISSIONS.CUSTOMER.VIEW_LIST])
                    //         },
                    //     ],
                    //     visible: this.checkAuthen([CONSTANTS.PERMISSIONS.CUSTOMER.VIEW_LIST])
                    // },

                    //extra service
                    {
                        label: this.tranService.translate("global.menu.extraservice"),
                        icon: "pi pi-fw pi-th-large",
                        routerLink: ["/extra-services"],
                        visible: false
                    },
                    //logs
                    {
                        label: this.tranService.translate("global.menu.log"),
                        icon: "pi pi-fw pi-book",
                        routerLink: ["/logs"],
                        visible: false
                    },
                    //report
                    {
                        label: this.tranService.translate("global.menu.report"),
                        icon: "pi pi-fw pi-file-excel",
                        items: [
                            {
                                label: this.tranService.translate("global.menu.dynamicreport"),
                                routerLink: ["/reports/report-dynamic/"],
                                visible: this.checkAuthen([CONSTANTS.PERMISSIONS.REPORT_DYNAMIC.VIEW_LIST])
                            },
                            {
                                label: this.tranService.translate("permission.RptContent.RptContent"),
                                routerLink: ["/reports/report-dynamic/report-content"]
                            },
                            {
                                label: this.tranService.translate("global.menu.dynamicreportgroup"),
                                routerLink: ["/reports/group-report-dynamic/"],
                                visible: this.checkAuthen([CONSTANTS.PERMISSIONS.GROUP_REPORT_DYNAMIC.VIEW_LIST])
                            },
                        ],
                        visible: this.checkAuthen([CONSTANTS.PERMISSIONS.REPORT_DYNAMIC.VIEW_LIST, CONSTANTS.PERMISSIONS.GROUP_REPORT_DYNAMIC.VIEW_LIST], true)
                    },

                    //configuration
                    {
                        label: this.tranService.translate("global.menu.configuration"),
                        icon: "pi pi-fw pi-cog",
                        routerLink: ["/configuration"],
                        visible: false
                    },
                    //troubleshoot
                    {
                        label: this.tranService.translate("global.menu.troubleshoot"),
                        icon: "pi pi-fw pi-wrench",
                        routerLink: ["/troubleshoot"],
                        visible: false
                    },
                    //config chart
                    {
                        label: this.tranService.translate("global.menu.charts"),
                        icon: "pi pi-fw pi-th-large",
                        routerLink: ["/config-chart"],
                        visible: this.checkAuthen([CONSTANTS.PERMISSIONS.CONFIG_DYNAMIC_CHART.VIEW_LIST])
                    },
                    //guide
                    // {
                    //     label: this.tranService.translate("global.menu.manual"),
                    //     icon: "pi pi-fw pi-globe",
                    //     // items: [
                    //     //     {
                    //     //         label: this.tranService.translate("global.menu.manual"),
                    //     //         routerLink: ["/manual"]
                    //     //     },
                    //     // ],
                    //     routerLink: ["/docs"],
                    //     // target: "_blank"
                    // },
                ]
            },
        ];
    }
}
