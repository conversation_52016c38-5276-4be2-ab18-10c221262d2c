:host >>> .p-button-label{
    font-weight: 400;
}

.p-splitbutton .p-button.p-button-icon-only{
    padding: 6px 14px;
}

.p-calendar .p-button{
    width: 36px;
}

.p-calendar .p-button.p-button-icon-only{
    padding: 6px 14px;
}

p-dropdown.left-side .p-overlay{
    left: 100% !important;
    transform: translateX(-100%);
}

p-splitbutton .p-tieredmenu-overlay{
    position: absolute;
    top:0;
    left: 100% !important;
    transform: translateX(-100%);
}

.custom-dropdown-right .p-overlay {
    left: unset !important;
    right: 0;
    width: fit-content;
    min-width: 250px !important;
}
/* .multiselect-align-right{
    position: relative;
}

.multiselect-align-right .p-overlay{
    left: unset !important;
    right: 0 !important;
} */
.p-datepicker{
    padding:3px;
    font-size: 1.1rem!important;
}
.p-datepicker table span {
    width: 33px;
    height: 33px;
    padding: 0;
}

.p-datepicker table td {
    padding: 3px;
}

.p-datepicker .p-datepicker-header {
    padding: 3px;
}
.p-datepicker .p-datepicker-buttonbar {
    padding: 3px;
}