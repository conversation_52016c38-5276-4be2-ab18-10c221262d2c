import { AfterContent<PERSON>hecked, Component, OnInit } from "@angular/core";
import { FormBuilder } from "@angular/forms";
import { ActivatedRoute, Router } from "@angular/router";
import { MenuItem } from "primeng/api";
import { AutoCompleteCompleteEvent } from "primeng/autocomplete";
import { AccountService } from "src/app/service/account/AccountService";
import { CONSTANTS } from "src/app/service/comon/constants";
import { DebounceInputService } from "src/app/service/comon/debounce.input.service";
import { MessageCommonService } from "src/app/service/comon/message-common.service";
import { TranslateService } from "src/app/service/comon/translate.service";
import { UtilService } from "src/app/service/comon/util.service";
import {SessionService} from "../../../service/session/SessionService";
import { CustomerService } from "src/app/service/customer/CustomerService";
import {ColumnInfo, OptionTable} from "../../common-module/table/table.component";
import {ContractService} from "../../../service/contract/ContractService";
import {EnumValue} from "@angular/compiler-cli/src/ngtsc/partial_evaluator";
import {ComboLazyControl} from "../../common-module/combobox-lazyload/combobox.lazyload";

@Component({
    selector: "app-account-edit",
    templateUrl: './app.profile.edit.component.html'
})
export class AppProfileEditComponent implements OnInit, AfterContentChecked{
    constructor(private route: ActivatedRoute,
                private router: Router,
                public utilService: UtilService,
                public accountService: AccountService,
                private customerService: CustomerService,
                private contractService: ContractService,
                public tranService: TranslateService,
                public messageCommonService: MessageCommonService,
                private formBuilder: FormBuilder,
                private debounceService: DebounceInputService,
                private sessionService: SessionService) {

    }
    userInfo : any
    items: Array<MenuItem>;
    home: MenuItem;
    accountInfo: {
        username: string| null,
        password: string|null,
        email: string|null,
        phone: string|null,
        status: number|null,
        statusBoolean: boolean|null,
        provincesHeadOffice: number,
        wardsHeadOffice: number,
        userType: number| null,
        province: any,
        roles: Array<any>,
        description: string|null,
        name: string|null,
        taxCode: string|null,
        provincesContact: number,
        wardsContact: number,
        addressHeadOffice: string|null,
        representativeName: string|null,
        addressContact: string|null,
        manager: any,
        //Lưu lại list customer cuối cùng để gửi đi
        customers: Array<any>
        customerAccounts : null
        accountRootId: null
    };
    formAccount: any;
    statusAccounts: Array<any>;
    listRole: Array<any>;
    listProvinces: Array<any>;
    listWardsHeadOffice: Array<any>;
    listWardsContact: Array<any>;
    addressPattern = /^[a-zA-ZÀ-Ỹà-ỹ0-9\s,.\-\/]+$/;
    vietnamesePattern = /^[a-zA-ZÀ-Ỹà-ỹ\s]*$/;
    roles:any
    controlComboSelectCustomerAccount : ComboLazyControl = new ComboLazyControl();
    paramSearchAccountRootId : {type: number} = {type: 2};
    userType: number;
    optionUserType: any;
    isUsernameExisted: boolean = false;
    isEmailExisted: boolean = false;
    isPhoneExisted: boolean = false;
    oldUserType: number | null = null;
    accountResponse: any;
    paginationCustomer: {
        page: number|null,
        size: number|null,
        sortBy: string|null,
    }
    paramQuickSearchCustomer: {
        keyword: string|null,
        accountRootId: number| null,
    }
    dataSetCustomer: {
        content: Array<any>,
        total: number,
    }
    paginationContract: {
        page: number|null,
        size: number|null,
        sortBy: string|null,
    }
    paramQuickSearchContract: {
        keyword: string|null,
        accountRootId: number| null,
        customerIds: Array<{ id: number }>|null,
    }
    dataSetContract: {
        content: Array<any>,
        total: number,
    }
    columnInfoCustomer: Array<ColumnInfo>;
    optionTableCustomer: OptionTable;
    columnInfoContract: Array<ColumnInfo>;
    optionTableContract: OptionTable;
    accountId: number | string;

    isShowSecretKey = true
    listModule = []
    //sẽ lưu lại list api sau khi đã chọn
    selectItemGrantApi: Array<any> = []
    paginationGrantApi: {
        page: number|null,
        size: number|null,
        sortBy: string|null,
    }
    columnInfoGrantApi: Array<ColumnInfo>;

    dataSetGrantApi: {
        content: Array<any>,
        total: number,
    }
    optionTableGrantApi: OptionTable;

    paramsSearchGrantApi = {api : null, module : null}

    genGrantApi = {clientId: '', secretKey: ''}

    statusGrantApi : any;

    isChangeSecretKey : boolean = false;

    ngOnInit(): void {
        this.userInfo = this.sessionService.userInfo;
        this.userType = this.userInfo.type;
        this.accountId = this.userInfo.id;
        this.optionUserType = CONSTANTS.USER_TYPE;
        this.items = [
            { label: this.tranService.translate("global.menu.account"), routerLink:"/profile"  },
            { label: this.tranService.translate("global.menu.editAccount") }
        ];
        this.home = { icon: 'pi pi-home', routerLink: '/' };

        let fullTypeAccount = [
            {name: this.tranService.translate("account.usertype.admin"),value:CONSTANTS.USER_TYPE.ADMIN},
            {name: this.tranService.translate("account.usertype.individual"),value:CONSTANTS.USER_TYPE.INDIVIDUAL},
            {name: this.tranService.translate("account.usertype.business"),value:CONSTANTS.USER_TYPE.BUSINESS},
        ]
        this.statusAccounts = fullTypeAccount;
        this.accountInfo = {
            username: null,
            password: null,
            userType: this.statusAccounts[0].value,
            status: null,
            statusBoolean: null,
            roles: null,
            name: null,
            taxCode: null,
            provincesHeadOffice: null,
            wardsHeadOffice: null,
            addressHeadOffice: null,
            description: null,
            representativeName: null,
            email: null,
            phone: null,
            provincesContact:null,
            wardsContact:null,
            addressContact: null,
            province: this.sessionService.userInfo.provinceCode,
            manager: null,
            customers: null,
            customerAccounts : null,
            accountRootId: null
        }
        this.getListProvince();
        this.paginationGrantApi = {
            page: 0,
            size: 10,
            sortBy: "id,desc",
        }
        this.columnInfoGrantApi = [
            {
                name: "API",
                key: "name",
                size: "30%",
                align: "left",
                isShow: true,
                isSort: true,
            },
            {
                name: "Module",
                key: "module",
                size: "50%",
                align: "left",
                isShow: true,
                isSort: true,
            }
        ]

        this.dataSetGrantApi = {
            content: [],
            total: 0,
        }

        this.optionTableGrantApi = {
            hasClearSelected: false,
            hasShowChoose: false,
            hasShowIndex: true,
            hasShowToggleColumn: false,
        }
        this.getDetail();
        this.paramQuickSearchCustomer = {
            keyword: null,
            accountRootId: Number(this.accountId),
        }
        this.columnInfoCustomer = [
            {
                name: this.tranService.translate("customer.label.customerCode"),
                key: "code",
                size: "30%",
                align: "left",
                isShow: true,
                isSort: false,
            },
            {
                name: this.tranService.translate("customer.label.customerName"),
                key: "name",
                size: "50%",
                align: "left",
                isShow: true,
                isSort: false,
            },
        ]
        this.dataSetCustomer = {
            content: [],
            total: 0,
        }
        this.paginationCustomer = {
            page: 0,
            size: 10,
            sortBy: "name,asc;id,asc",
        }
        this.optionTableCustomer = {
            hasClearSelected: false,
            hasShowChoose: false,
            hasShowIndex: true,
            hasShowToggleColumn: false,
        }
        this.paramQuickSearchContract = {
            keyword: null,
            accountRootId: Number(this.accountId),
            customerIds: [],
        }
        this.columnInfoContract = [
            {
                name: this.tranService.translate("customer.label.customerCode"),
                key: "customerCode",
                size: "30%",
                align: "left",
                isShow: true,
                isSort: false,
            },
            {
                name: this.tranService.translate("customer.label.customerName"),
                key: "customerName",
                size: "50%",
                align: "left",
                isShow: true,
                isSort: false,
            },
            {
                name: this.tranService.translate("contract.label.contractCode"),
                key: "contractCode",
                size: "50%",
                align: "left",
                isShow: true,
                isSort: false,
            },
        ]
        this.dataSetContract = {
            content: [],
            total: 0,
        }
        this.paginationContract = {
            page: 0,
            size: 10,
            sortBy: "customerName,asc;id,asc",
        }
        this.optionTableContract = {
            hasClearSelected: false,
            hasShowChoose: false,
            hasShowIndex: true,
            hasShowToggleColumn: false,
        }
    }

    ngAfterContentChecked(): void {
        if(this.accountInfo.userType != this.oldUserType && this.formAccount){
            this.oldUserType = this.accountInfo.userType;
            this.formAccount.get("province").reset();
            this.formAccount.get("customers").reset();
        }
    }

    checkExistAccount(type){
        let email = null;
        let username = null;
        if(type == "username"){
            this.isUsernameExisted = false;
            username = this.accountInfo.username;
            if(username == this.accountResponse.username) return;
        }else if(type == "email"){
            this.isEmailExisted = false;
            email = this.accountInfo.email;
            if(email == this.accountResponse.email) return;
        }

        let me = this;

        this.debounceService.set(type, this.accountService.checkAccount.bind(this.accountService), email, username,(response)=>{
            if(response >= 1){
                if(type == "username"){
                    me.isUsernameExisted = true;
                }else{
                    me.isEmailExisted = true;
                }
            }else{
                if(type == "username"){
                    me.isUsernameExisted = false;
                }else{
                    me.isEmailExisted = false;
                }
            }
        })
    }

    onSubmitCreate(){
        let dataBody = {
            username: this.accountInfo.username,
            password: this.accountInfo.password===undefined?null:this.accountInfo.password.trim(),
            description: this.accountInfo.description,
            email: this.accountInfo.email,
            phone: this.accountInfo.phone,
            type: this.accountInfo.userType,
            name: this.accountInfo.name,
            taxCode: this.accountInfo.taxCode,
            addressContact: this.accountInfo.addressContact,
            addressHeadOffice: this.accountInfo.addressHeadOffice,
            representativeName: this.accountInfo.representativeName,
            status: this.accountInfo.statusBoolean===true?1:0,
            roleLst: (this.accountInfo.roles || this.roles[0] || []).map(el => el.id),
            accountRootId: this.accountInfo.accountRootId,
            provinceCodeAddress: this.accountInfo.provincesContact,
            wardCodeAddress: this.accountInfo.wardsContact,
            provinceCodeOffice: this.accountInfo.provincesHeadOffice,
            wardCodeOffice: this.accountInfo.wardsHeadOffice
        }
        if(dataBody.phone != null){
            if(dataBody.phone.startsWith('0')){
                dataBody.phone = "84"+dataBody.phone.substring(1, dataBody.phone.length);
            }else if(dataBody.phone.length == 9 || dataBody.phone.length == 10){
                dataBody.phone = "84"+dataBody.phone;
            }
        }
        this.messageCommonService.onload();
        let me = this;
        this.accountService.updateProfile(dataBody, (response)=>{
            me.messageCommonService.success(me.tranService.translate("global.message.saveSuccess"));
            me.router.navigate(['/profile']);
        }, null, ()=>{
            me.messageCommonService.offload();
        })
    }

    closeForm(){
        this.router.navigate(['/profile'])
    }

    getDetail(){
        let me = this;
        let accountid = this.userInfo.id;
        this.accountService.viewProfile( (response)=>{
            me.accountResponse = response;
            me.accountInfo.username = response.username;
            me.accountInfo.password = response.password;
            me.accountInfo.name = response.name;
            me.accountInfo.email = response.email;
            me.accountInfo.description = response.description;
            me.accountInfo.phone = response.phone;
            me.accountInfo.userType = response.type;
            me.accountInfo.taxCode = response.taxCode;
            me.accountInfo.addressHeadOffice = response.addressHeadOffice;
            me.accountInfo.provincesHeadOffice = response.provinceCodeOffice;
            me.accountInfo.provincesContact = response.provinceCodeAddress;

            if(me.accountInfo.provincesHeadOffice !== null) this.getListWardsHeadOffice();
            if(me.accountInfo.provincesContact !== null) this.getListWardsContact();

            me.accountInfo.wardsHeadOffice = response.wardCodeOffice;
            me.accountInfo.wardsContact = response.wardCodeAddress;
            me.accountInfo.representativeName = response.representativeName;
            me.accountInfo.description = response.description;
            me.accountInfo.addressContact = response.addressContact;
            me.accountInfo.status = response.status;
            me.accountInfo.statusBoolean = response.status === 1;
            me.accountInfo.accountRootId = response.manager?.id;
            if(response.roles.length>0){
                if(response.type === 3) {
                    me.accountInfo.roles = (response.roles || []).map(role => ({
                        id: role.roleId,
                        name: role.roleName
                    }))
                } else {
                    me.accountInfo.roles = [(response.roles || []).map(role => ({
                        id: role.roleId,
                        name: role.roleName
                    }))]
                }
            }
            this.roles = this.accountInfo.roles

            me.formAccount = me.formBuilder.group(me.accountInfo);
            me.formAccount.controls.username.disable({emitEvent:false});
        })
    }

    getListProvince(){
        this.accountService.getProvince( (response)=>{
            this.listProvinces = response;
        }, null, (searchError)=>{
            // me.messageCommonService.offload(); // Tắt loading nếu API search lỗi
            // console.error("Lỗi khi tìm kiếm tài khoản:", searchError);
        })
    }

    getListWardsHeadOffice(){
        let dataParams = {
            provinceCode: this.accountInfo.provincesHeadOffice
        }
        if (this.accountInfo.provincesHeadOffice !== null){
            this.accountService.getWard(dataParams, (response)=>{
                this.listWardsHeadOffice = response;
            }, null, (searchError)=>{
                // console.error("Lỗi khi tìm kiếm tài khoản:", searchError);
            })
        } else {
            this.listWardsHeadOffice=null;
        }

    }

    getListWardsContact() {
        let dataParams = {
            provinceCode: this.accountInfo.provincesContact
        }
        if (this.accountInfo.provincesContact !== null) {
            this.accountService.getWard(dataParams, (response) => {
                this.listWardsContact = response;
            }, null, (searchError) => {
                // console.error("Lỗi khi tìm kiếm tài khoản:", searchError);
            })
        } else {
            this.listWardsContact = null;
        }
    }

    loadCustomerAccount(params, callback) {
        return this.accountService.getCustomerAccount(params, callback)
    }

    getStringCustomers(){
        return (this.accountResponse.customers || []).map(el => el.customerName + ' - ' + el.customerCode).toLocaleString();
    }

    getStringUserType(value) {
        if(value == CONSTANTS.USER_TYPE.ADMIN){
            return this.tranService.translate("account.usertype.admin");
        }else if(value == CONSTANTS.USER_TYPE.INDIVIDUAL){
            return this.tranService.translate("account.usertype.individual");
        }else if(value == CONSTANTS.USER_TYPE.BUSINESS){
            return this.tranService.translate("account.usertype.business");
        }else if(value == CONSTANTS.USER_TYPE.BUSINESS){
            return this.tranService.translate("account.usertype.business");
        }else if(value == CONSTANTS.USER_TYPE.BUSINESS){
            return this.tranService.translate("account.usertype.business");
        }else{
            return "";
        }
    }

    getStringRoles(){
        return (this.accountResponse.roles || []).map(el => el.roleName).toLocaleString()
    }
    onTabChange(event) {
        const tabName = event.originalEvent.target.innerText;
        let me = this;
        if (event && tabName.includes(this.tranService.translate('account.text.grantApi'))) {
            me.onSearchGrantApi()
        } else if (event && tabName.includes(this.tranService.translate('global.menu.listbill'))) {
            me.onSearchContract()
        } else if (event && tabName.includes(this.tranService.translate('global.menu.listcustomer'))) {
            me.onSearchCustomer()
        }
    }
    onSearchCustomer(back?) {
        let me = this;
        if (back) {
            me.paginationCustomer.page = 0;
        }
        me.searchCustomer(me.paginationCustomer.page, me.paginationCustomer.size, me.paginationCustomer.sortBy, me.paramQuickSearchCustomer);
    }
    onSearchContract(back?) {
        let me = this;
        if (back) {
            me.paginationContract.page = 0;
        }
        me.searchContract(me.paginationContract.page, me.paginationContract.size, me.paginationContract.sortBy, me.paramQuickSearchContract);
    }
    searchCustomer(page, limit, sort, params){
        let me = this;
        this.paginationCustomer.page = page;
        this.paginationCustomer.size = limit;
        this.paginationCustomer.sortBy = sort;
        let dataParams = {
            page,
            size: limit,
            sort
        }
        Object.keys(this.paramQuickSearchCustomer).forEach(key => {
            if(this.paramQuickSearchCustomer[key] != null){
                dataParams[key] = this.paramQuickSearchCustomer[key];
            }
        })
        me.messageCommonService.onload();
        this.customerService.quickSearchCustomer(dataParams, this.paramQuickSearchCustomer,(response)=>{
            me.dataSetCustomer = {
                content: response.content,
                total: response.totalElements
            }
        }, null, ()=>{
            me.messageCommonService.offload();
        })
        // console.log(this.selectItemCustomer)
    }
    searchContract(page, limit, sort, params){
        let me = this;
        this.paginationContract.page = page;
        this.paginationContract.size = limit;
        this.paginationContract.sortBy = sort;
        let dataParams = {
            page,
            size: limit,
            sort
        }
        // Object.keys(this.paramQuickSearchContract).forEach(key => {
        //     if(this.paramQuickSearchContract[key] != null){
        //         dataParams[key] = this.paramQuickSearchContract[key];
        //     }
        // })
        me.messageCommonService.onload();
        this.contractService.quickSearchContract(dataParams, this.paramQuickSearchContract,(response)=>{
            me.dataSetContract = {
                content: response.content,
                total: response.totalElements
            }
            // console.log(response)
        }, null, ()=>{
            me.messageCommonService.offload();
        })
    }
    resetPaginationCustomerAndContract() {
        this.paginationCustomer = {
            page: 0,
            size: 10,
            sortBy: "name,asc;id,asc",
        }
        this.paginationContract = {
            page: 0,
            size: 10,
            sortBy: "customerName,asc;id,asc",
        }
    }

    searchGrantApi(page, limit, sort, params){
        let me = this;
        this.paginationGrantApi.page = page;
        this.paginationGrantApi.size = limit;
        this.paginationGrantApi.sortBy = sort;
        let dataParams = {
            page,
            size: limit,
            sort,
            selectedApiIds: this.selectItemGrantApi.map(el=>el.id).join(',')
        }
        Object.keys(this.paramsSearchGrantApi).forEach(key => {
            if(this.paramsSearchGrantApi[key] != null){
                dataParams[key] = this.paramsSearchGrantApi[key];
            }
        })
        console.log(dataParams)
        me.messageCommonService.onload();
        this.accountService.searchGrantApi(dataParams,(response)=>{
            me.dataSetGrantApi = {
                content: response.content,
                total: response.totalElements
            }
        }, null, ()=>{
            me.messageCommonService.offload();
        })
        let copyParam = {...dataParams};
        copyParam.size = *********;
        this.accountService.searchGrantApi(copyParam,(response)=>{
            me.listModule = [...new Set(response.content.map(el=>el.module))]
            me.listModule = me.listModule.map(el=>({
                name : el,
                value : el
            }))
        }, null, ()=>{
            me.messageCommonService.offload();
        })
    }

    generateToken(n) {
        var chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
        var token = '';
        for(var i = 0; i < n; i++) {
            token += chars[Math.floor(Math.random() * chars.length)];
        }
        return token;
    }

    genToken(){
        this.genGrantApi.secretKey = this.generateToken(20);
    }

    onSearchGrantApi(back?) {
        let me = this;
        console.log(me.paramsSearchGrantApi)
        if(back) {
            me.paginationGrantApi.page = 0;
        }
        me.searchGrantApi(me.paginationGrantApi.page, me.paginationGrantApi.size, me.paginationGrantApi.sortBy, me.paramsSearchGrantApi);
    }

    protected readonly CONSTANTS = CONSTANTS;
}
