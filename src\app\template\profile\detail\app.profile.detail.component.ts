import {AfterContentChecked, Component, Injector, OnInit} from "@angular/core";
import { MenuItem } from "primeng/api";
import { AccountService } from "src/app/service/account/AccountService";
import { CONSTANTS } from "src/app/service/comon/constants";
import {ComponentBase} from "../../../component.base";
import {CustomerService} from "../../../service/customer/CustomerService";
import {ContractService} from "../../../service/contract/ContractService";
import {ColumnInfo, OptionTable} from "../../common-module/table/table.component";

@Component({
    selector: "app-account-detail",
    templateUrl: './app.profile.detail.component.html'
})
export class AppProfileDetailComponent extends ComponentBase implements OnInit, AfterContentChecked{
    constructor(
                public accountService: AccountService,
                private customerService: CustomerService,
                private contractService: ContractService,
                injector: Injector
                ) {
        super(injector);
    }
    items: Array<MenuItem>;
    home: MenuItem;
    userInfo : any
    accountInfo: {
        accountName: string| null,
        fullName: string|null,
        email: string|null,
        phone: string|null,
        userType: number| null,
        province: any,
        roles: Array<any>,
        description: string|null,
        manager: any,
        customers: Array<any>
    };
    listRole: Array<any>;
    listProvince: Array<any>;
    listCustomer: Array<any>;
    optionUserType: any;
    oldUserType: number | null = null;
    accountResponse: any;

    paginationCustomer: {
        page: number|null,
        size: number|null,
        sortBy: string|null,
    }
    paramQuickSearchCustomer: {
        keyword: string|null,
        accountRootId: number| null,
    }
    dataSetCustomer: {
        content: Array<any>,
        total: number,
    }
    paginationContract: {
        page: number|null,
        size: number|null,
        sortBy: string|null,
    }
    paramQuickSearchContract: {
        keyword: string|null,
        accountRootId: number| null,
        customerIds: Array<{ id: number }>|null,
    }
    dataSetContract: {
        content: Array<any>,
        total: number,
    }
    userType: number;
    columnInfoCustomer: Array<ColumnInfo>;
    optionTableCustomer: OptionTable;
    columnInfoContract: Array<ColumnInfo>;
    optionTableContract: OptionTable;
    accountId: number | string;

    isShowSecretKey = true
    listModule = []
    //sẽ lưu lại list api sau khi đã chọn
    selectItemGrantApi: Array<any> = []
    paginationGrantApi: {
        page: number|null,
        size: number|null,
        sortBy: string|null,
    }
    columnInfoGrantApi: Array<ColumnInfo>;

    dataSetGrantApi: {
        content: Array<any>,
        total: number,
    }
    optionTableGrantApi: OptionTable;

    paramsSearchGrantApi = {api : null, module : null}

    genGrantApi = {clientId: '', secretKey: ''}

    statusGrantApi : any;


    ngOnInit(): void {
        this.userInfo = this.sessionService.userInfo;
        this.accountId = this.userInfo.id;
        this.userType = this.sessionService.userInfo.type;
        this.optionUserType = CONSTANTS.USER_TYPE;
        this.items = [
            { label: this.tranService.translate("global.menu.account")},
            { label: this.tranService.translate("global.menu.detailAccount") }
        ];
        this.home = { icon: 'pi pi-home', routerLink: '/' };

        let fullTypeAccount = [
            {name: this.tranService.translate("account.usertype.admin"),value:CONSTANTS.USER_TYPE.ADMIN, accepts:[CONSTANTS.USER_TYPE.ADMIN]},
            {name: this.tranService.translate("account.usertype.individual"),value:CONSTANTS.USER_TYPE.INDIVIDUAL,accepts:[CONSTANTS.USER_TYPE.ADMIN,CONSTANTS.USER_TYPE.BUSINESS,CONSTANTS.USER_TYPE.BUSINESS, CONSTANTS.USER_TYPE.BUSINESS, CONSTANTS.USER_TYPE.INDIVIDUAL]},
            {name: this.tranService.translate("account.usertype.business"),value:CONSTANTS.USER_TYPE.BUSINESS,accepts:[CONSTANTS.USER_TYPE.ADMIN]},
            {name: this.tranService.translate("account.usertype.business"),value:CONSTANTS.USER_TYPE.BUSINESS,accepts:[CONSTANTS.USER_TYPE.ADMIN,CONSTANTS.USER_TYPE.BUSINESS]},
            {name: this.tranService.translate("account.usertype.business"),value:CONSTANTS.USER_TYPE.BUSINESS,accepts:[CONSTANTS.USER_TYPE.ADMIN,CONSTANTS.USER_TYPE.BUSINESS,CONSTANTS.USER_TYPE.BUSINESS]},
        ]
        this.accountInfo = {
            accountName: null,
            fullName: null,
            email: null,
            phone: null,
            userType: null,
            province: null,
            roles: null,
            description: null,
            manager: null,
            customers: null
        }
        this.paginationGrantApi = {
            page: 0,
            size: 10,
            sortBy: "id,desc",
        }
        this.columnInfoGrantApi = [
            {
                name: "API",
                key: "name",
                size: "30%",
                align: "left",
                isShow: true,
                isSort: true,
            },
            {
                name: "Module",
                key: "module",
                size: "50%",
                align: "left",
                isShow: true,
                isSort: true,
            }
        ]

        this.dataSetGrantApi = {
            content: [],
            total: 0,
        }

        this.optionTableGrantApi = {
            hasClearSelected: false,
            hasShowChoose: false,
            hasShowIndex: true,
            hasShowToggleColumn: false,
        }
        this.accountResponse = {}
        this.getListProvince();
        this.getDetail();

        this.paramQuickSearchCustomer = {
            keyword: null,
            accountRootId: Number(this.accountId),
        }
        this.columnInfoCustomer = [
            {
                name: this.tranService.translate("customer.label.customerCode"),
                key: "code",
                size: "30%",
                align: "left",
                isShow: true,
                isSort: false,
            },
            {
                name: this.tranService.translate("customer.label.customerName"),
                key: "name",
                size: "50%",
                align: "left",
                isShow: true,
                isSort: false,
            },
        ]
        this.dataSetCustomer = {
            content: [],
            total: 0,
        }
        this.paginationCustomer = {
            page: 0,
            size: 10,
            sortBy: "name,asc;id,asc",
        }
        this.optionTableCustomer = {
            hasClearSelected: false,
            hasShowChoose: false,
            hasShowIndex: true,
            hasShowToggleColumn: false,
        }
        this.paramQuickSearchContract = {
            keyword: null,
            accountRootId: Number(this.accountId),
            customerIds: [],
        }
        this.columnInfoContract = [
            {
                name: this.tranService.translate("customer.label.customerCode"),
                key: "customerCode",
                size: "30%",
                align: "left",
                isShow: true,
                isSort: false,
            },
            {
                name: this.tranService.translate("customer.label.customerName"),
                key: "customerName",
                size: "50%",
                align: "left",
                isShow: true,
                isSort: false,
            },
            {
                name: this.tranService.translate("contract.label.contractCode"),
                key: "contractCode",
                size: "50%",
                align: "left",
                isShow: true,
                isSort: false,
            },
        ]
        this.dataSetContract = {
            content: [],
            total: 0,
        }
        this.paginationContract = {
            page: 0,
            size: 10,
            sortBy: "customerName,asc;id,asc",
        }
        this.optionTableContract = {
            hasClearSelected: false,
            hasShowChoose: false,
            hasShowIndex: true,
            hasShowToggleColumn: false,
        }
    }

    ngAfterContentChecked(): void {

    }

    getDetail(){
        let me = this;
        let accountid = this.userInfo.id;
        // console.log(accountid)
        me.messageCommonService.onload();
        this.accountService.viewProfile((response)=>{
            me.accountResponse = response;
            me.accountInfo.accountName = response.username;
            me.accountInfo.fullName = response.fullName;
            me.accountInfo.email = response.email;
            me.accountInfo.description = response.description;
            me.accountInfo.phone = response.phone;
            me.accountInfo.province = response.provinceCode;
            me.accountInfo.userType = response.type;
            // me.getListRole(false);
            if (me.accountInfo.userType == CONSTANTS.USER_TYPE.INDIVIDUAL) {
                me.resetPaginationCustomerAndContract()
                me.paramQuickSearchCustomer.accountRootId = Number(me.accountId)
                me.paramQuickSearchContract.accountRootId = Number(me.accountId)
                me.paramQuickSearchContract.customerIds = (me.accountResponse.customers|| []).map(customer => customer.customerId)
            }
            me.statusGrantApi = response.statusApi
            me.selectItemGrantApi = response.listApiId? response.listApiId.map(el=> ({id: el})) : [{id:-99}]
            me.genGrantApi.secretKey = response.secretId
            me.genGrantApi.clientId = response.username
        }, null, ()=>{
            me.messageCommonService.offload();
        })
    }

    getListRole(isClear){
        this.accountService.getListRole(this.accountInfo.userType, (response)=>{
            this.listRole = response.map(el => {
                return {
                    id: el.id,
                    name: el.name
                }
            });
            if(isClear){
                this.accountInfo.roles = null;
            }else{
                this.accountInfo.roles = this.listRole.filter(el => (this.accountResponse.roles||[]).includes(el.id));
            }
        })
    }


    getListProvince(){
        this.accountService.getListProvince((response)=>{
            this.listProvince = response.map(el => {
                return {
                    id: el.code,
                    name: `${el.name} (${el.code})`
                }
            })
        })
    }

    goToEdit(){
        this.router.navigate([`/profile/edit/`]);
    }

    getStringCustomers(){
        return (this.accountResponse.customers || []).map(el => el.customerName + ' - ' + el.customerCode).toLocaleString();
    }

    getStringRoles(){
        return (this.accountResponse.roles || []).map(el => el.roleName).toLocaleString()
    }

    getStringUserType(value) {
        if(value == CONSTANTS.USER_TYPE.ADMIN){
            return this.tranService.translate("account.usertype.admin");
        }else if(value == CONSTANTS.USER_TYPE.INDIVIDUAL){
            return this.tranService.translate("account.usertype.individual");
        }else if(value == CONSTANTS.USER_TYPE.BUSINESS){
            return this.tranService.translate("account.usertype.business");
        }else if(value == CONSTANTS.USER_TYPE.BUSINESS){
            return this.tranService.translate("account.usertype.business");
        }else if(value == CONSTANTS.USER_TYPE.BUSINESS){
            return this.tranService.translate("account.usertype.business");
        }else{
            return "";
        }
    }
    goToChangePass() {
        this.router.navigate(['/profile/change-password'])
    }

    onTabChange(event) {
        const tabName = event.originalEvent.target.innerText;
        let me = this;
        if (event && tabName.includes(this.tranService.translate('account.text.grantApi'))) {
            me.onSearchGrantApi()
        } else if (event && tabName.includes(this.tranService.translate('global.menu.listbill'))) {
            me.onSearchContract()
        } else if (event && tabName.includes(this.tranService.translate('global.menu.listcustomer'))) {
            me.onSearchCustomer()
        }
    }
    onSearchCustomer(back?) {
        let me = this;
        if (back) {
            me.paginationCustomer.page = 0;
        }
        me.searchCustomer(me.paginationCustomer.page, me.paginationCustomer.size, me.paginationCustomer.sortBy, me.paramQuickSearchCustomer);
    }
    onSearchContract(back?) {
        let me = this;
        if (back) {
            me.paginationContract.page = 0;
        }
        me.searchContract(me.paginationContract.page, me.paginationContract.size, me.paginationContract.sortBy, me.paramQuickSearchContract);
    }
    searchCustomer(page, limit, sort, params){
        let me = this;
        this.paginationCustomer.page = page;
        this.paginationCustomer.size = limit;
        this.paginationCustomer.sortBy = sort;
        let dataParams = {
            page,
            size: limit,
            sort
        }
        Object.keys(this.paramQuickSearchCustomer).forEach(key => {
            if(this.paramQuickSearchCustomer[key] != null){
                dataParams[key] = this.paramQuickSearchCustomer[key];
            }
        })
        me.messageCommonService.onload();
        this.customerService.quickSearchCustomer(dataParams, this.paramQuickSearchCustomer,(response)=>{
            me.dataSetCustomer = {
                content: response.content,
                total: response.totalElements
            }
        }, null, ()=>{
            me.messageCommonService.offload();
        })
        // console.log(this.selectItemCustomer)
    }
    searchContract(page, limit, sort, params){
        let me = this;
        this.paginationContract.page = page;
        this.paginationContract.size = limit;
        this.paginationContract.sortBy = sort;
        let dataParams = {
            page,
            size: limit,
            sort
        }
        // Object.keys(this.paramQuickSearchContract).forEach(key => {
        //     if(this.paramQuickSearchContract[key] != null){
        //         dataParams[key] = this.paramQuickSearchContract[key];
        //     }
        // })
        me.messageCommonService.onload();
        this.contractService.quickSearchContract(dataParams, this.paramQuickSearchContract,(response)=>{
            me.dataSetContract = {
                content: response.content,
                total: response.totalElements
            }
            // console.log(response)
        }, null, ()=>{
            me.messageCommonService.offload();
        })
    }
    getStringUserStatus(value) {
        if(value == CONSTANTS.USER_STATUS.ACTIVE){
            return this.tranService.translate("account.userstatus.active");
        }else if(value == CONSTANTS.USER_STATUS.INACTIVE){
            return this.tranService.translate("account.userstatus.inactive");
        }else{
            return "";
        }
    }
    resetPaginationCustomerAndContract() {
        this.paginationCustomer = {
            page: 0,
            size: 10,
            sortBy: "name,asc;id,asc",
        }
        this.paginationContract = {
            page: 0,
            size: 10,
            sortBy: "customerName,asc;id,asc",
        }
    }

    searchGrantApi(page, limit, sort, params){
        let me = this;
        this.paginationGrantApi.page = page;
        this.paginationGrantApi.size = limit;
        this.paginationGrantApi.sortBy = sort;
        let dataParams = {
            page,
            size: limit,
            sort,
            selectedApiIds: this.selectItemGrantApi.map(el=>el.id).join(',')
        }
        Object.keys(this.paramsSearchGrantApi).forEach(key => {
            if(this.paramsSearchGrantApi[key] != null){
                dataParams[key] = this.paramsSearchGrantApi[key];
            }
        })
        console.log(dataParams)
        me.messageCommonService.onload();
        this.accountService.searchGrantApi(dataParams,(response)=>{
            me.dataSetGrantApi = {
                content: response.content,
                total: response.totalElements
            }
        }, null, ()=>{
            me.messageCommonService.offload();
        })
        let copyParam = {...dataParams};
        copyParam.size = *********;
        this.accountService.searchGrantApi(copyParam,(response)=>{
            me.listModule = [...new Set(response.content.map(el=>el.module))]
            me.listModule = me.listModule.map(el=>({
                name : el,
                value : el
            }))
        }, null, ()=>{
            me.messageCommonService.offload();
        })
    }

    generateToken(n) {
        var chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
        var token = '';
        for(var i = 0; i < n; i++) {
            token += chars[Math.floor(Math.random() * chars.length)];
        }
        return token;
    }

    genToken(){
        this.genGrantApi.secretKey = this.generateToken(20);
    }

    onSearchGrantApi(back?) {
        let me = this;
        console.log(me.paramsSearchGrantApi)
        if(back) {
            me.paginationGrantApi.page = 0;
        }
        me.searchGrantApi(me.paginationGrantApi.page, me.paginationGrantApi.size, me.paginationGrantApi.sortBy, me.paramsSearchGrantApi);
    }

    protected readonly CONSTANTS = CONSTANTS;
}
