import { Inject, Injectable } from "@angular/core";
import { HttpService } from "../comon/http.service";

@Injectable()
export class ReportReceivingGroupService {
    private prefixApi: string;
    constructor(@Inject(HttpService) private httpService:HttpService) {
        this.prefixApi = "/report/email-group";
    }

    public searchReportReceivingGroup(params:any,callback: Function, errorCallBack?: Function, finallyCallback?: Function){
        this.httpService.get(`${this.prefixApi}/search`,{},params,callback,errorCallBack,finallyCallback);
    }

    public getDetailReceivingGroup(id:number, callback: Function, errorCallback?:Function, finallyCallback?: Function){
        this.httpService.get(this.prefixApi+"/"+id, {}, {}, callback ,errorCallback, finallyCallback);
    }

    public creteReportReceivingGroup(body:any, callback:Function, errorCallback?:Function, finallyCallback?: Function){
        this.httpService.post(this.prefixApi,{},body,{},callback, errorCallback, finallyCallback);
    }
    
    public updateReportReceivingGroup(id:number, body:any,callback:Function, errorCallback?:Function, finallyCallback?: Function){
        this.httpService.put(`${this.prefixApi}/${id}`,{},body,{},callback, errorCallback, finallyCallback);
    }

    public checkName(param,callback){
        this.httpService.get(this.prefixApi+"/checkName",{},param,callback)
    }

    public deleteReportGroup(id, callback){
        this.httpService.delete(`${this.prefixApi}/${id}`,{},{},callback)
    }

    public deleleListReceivingGroup(listId: Array<number>, callback?: Function) {
        this.httpService.post(`${this.prefixApi}/delete-many`, {}, listId,{}, callback);
    }
}
