import { NgModule } from "@angular/core";
import { RouterModule } from "@angular/router";
import ConfigChartListComponent from "./list/app.config.chart.list.component";
import DataPage from "src/app/service/data.page";
import ConfigChartCreateComponent from "./create/app.config.chart.create.component";
import ConfigChartEditComponent from "./edit/app.config.chart.edit.component";
import { CONSTANTS } from "src/app/service/comon/constants";

@NgModule({
    imports: [
        RouterModule.forChild([
            {path: "", component: ConfigChartListComponent, data: new DataPage("global.menu.chartList",[CONSTANTS.PERMISSIONS.CONFIG_DYNAMIC_CHART.VIEW_LIST])},
            {path: "detail/:chartId", component: ConfigChartListComponent, data: new DataPage("global.menu.chartList",[CONSTANTS.PERMISSIONS.CONFIG_DYNAMIC_CHART.VIEW_DETAIL])},
            {path: "create", component: ConfigChartCreateComponent, data: new DataPage("global.menu.chartList",[CONSTANTS.PERMISSIONS.CONFIG_DYNAMIC_CHART.CREATE])},
            {path: "update/:chartId", component: ConfigChartEditComponent, data: new DataPage("global.menu.chartList",[CONSTANTS.PERMISSIONS.CONFIG_DYNAMIC_CHART.UPDATE])},
        ])
    ],
    exports: [RouterModule]
})
export default class AppConfigChartRouting {}