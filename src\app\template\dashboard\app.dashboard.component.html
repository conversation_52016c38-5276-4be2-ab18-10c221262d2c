<style>
    :host ::ng-deep {
        [pDraggable] {
            cursor: move;
        }
    }
</style>
<div class="vnpt flex flex-row justify-content-between align-items-center bg-white p-2 border-round">
    <div class="">
        <div class="text-xl font-bold mb-1">Dashboard</div>
        <p-breadcrumb class="max-w-full col-7" [model]="items" [home]="home"></p-breadcrumb>
    </div>
    <div>
        <p-toolbar styleClass="border-none bg-white" [style]="{padding: 0}">
            <div class="p-toolbar-group-start">
                <p-button *ngIf="modeView == objectModeView.UPDATE" styleClass="p-button-info" icon="pi pi-plus" (click)="createPanelDrop()"></p-button>
            </div>
            <div class="p-toolbar-group-center">
                <p-multiSelect *ngIf="modeView == objectModeView.UPDATE" 
                                [overlayVisible]="false" 
                                [style]="{maxWidth:'150px'}" 
                                [options]="charts" 
                                [(ngModel)]="chartShows" 
                                optionLabel="name"
                                (ngModelChange)="changeChartShow()"
                ></p-multiSelect>
            </div>
            <div class="p-toolbar-group-end">
                <p-button *ngIf="modeView == objectModeView.UPDATE" styleClass="p-button-info" [label]="tranService.translate('global.button.save')" (click)="saveConfig()"></p-button>
                <p-button *ngIf="modeView == objectModeView.DETAIL" styleClass="p-button-info" [label]="tranService.translate('global.button.edit')" (click)="openEdit()"></p-button>
            </div>
        </p-toolbar>
    </div>
</div>

<div class="flex flex-row flex-wrap my-1" style="height: fit-content;" *ngIf="(chartShows || []).length > 0">
    <div *ngFor="let budget of budgets; let indexBudget = index" class="mt-2 w-full">
        <div [class]="['flex', 'flex-row', 'flex-wrap', 'w-full', (budget || []).length > 0 ? budget[0].configPositionObject.align: '']" 
            [style]="modeView == objectModeView.DETAIL ? {} : {minHeight: '100px', border: '1px dashed #555555',backgroundColor: '#BBBBBB', padding: '8px'}"
            pDroppable (onDrop)="drop(indexBudget)"
        >
            <div *ngFor="let chartPolicy of budget;let indexInBudget = index" pDraggable (onDragStart)="dragStart(chartPolicy,indexInBudget)" (onDragEnd)="dragEnd()"
                [style]="{
                    marginLeft: chartPolicy.configPositionObject.marginLeft+'px',
                    marginRight: chartPolicy.configPositionObject.marginRight+'px'
                }"
            >
                <div *ngIf="chartPolicy.chartConfig && chartPolicy.status == 1" class="mb-2">
                    <dynamic-chart-vnpt 
                        [mode]="modeView"
                        [chartConfig]="chartPolicy.chartConfig" 
                        [control]="listDynamicChartController[chartPolicy.chartConfig.id]" 
                        [width]="chartPolicy.configPositionObject.width" 
                        [height]="chartPolicy.configPositionObject.height"
                        [handleOpenSetting]="handleOpenSetting.bind(this)"
                        [handleOpenSizing]="handleOpenSizing.bind(this)"
                        [isShowButtonExtra]="modeView == objectModeView.UPDATE"
                    ></dynamic-chart-vnpt>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="setting flex justify-content-center" *ngIf="chartPolicyForcus">
    <p-dialog (onHide)="closeDialog()" [header]="tranService.translate('chart.label.thresholdConfig')" [(visible)]="isShowEditSetting" [modal]="true" [style]="{ width: '700px',top: '-282px' }" [draggable]="false" [resizable]="false">
        <div class="w-full field grid p-0 m-0">
            <div class="w-full field grid" *ngFor="let threshold of chartPolicyForcus.configLevelArray;let i = index">
                <label htmlFor="threshold" class="col-fixed" style="width:150px;max-width: 150px;overflow: hidden; text-overflow: ellipsis;">
                    {{tranService.translate("chart.label.threshold")}}&nbsp;{{i + 1}}
                </label>
                <div class="col flex flex-row flex-wrap justify-content-start align-items-center">
                    <p-inputNumber
                    class="mr-2" disabled="true"
                    [(ngModel)]="chartPolicyForcus.configLevelArray[i].below" 
                    inputId="minmax" mode="decimal" [disabled]="true"
                > </p-inputNumber>
                <p-inputNumber
                    class="mr-2" 
                    [(ngModel)]="chartPolicyForcus.configLevelArray[i].above" 
                    inputId="minmax" mode="decimal" [disabled]="i < (chartPolicyForcus.configLevelArray.length - 1)"
                    [min]="chartPolicyForcus.configLevelArray[i].below + 1"
                > </p-inputNumber>
                <p-button (click)="minusSetting()" styleClass="mr-2 p-button-info" icon="pi pi-minus" *ngIf="i != 0 && i == chartPolicyForcus.configLevelArray.length - 1"></p-button>
                <p-button (click)="createSetting()" icon="pi pi-plus" styleClass="p-button-info" *ngIf="i == chartPolicyForcus.configLevelArray.length - 1 " [disabled]="!chartPolicyForcus.configLevelArray[i].above || chartPolicyForcus.configLevelArray[i].above <= chartPolicyForcus.configLevelArray[i].below"></p-button>
                </div>
            </div>
        </div> 
    </p-dialog>
</div>

<div class="sizing flex justify-content-center dialog-vnpt" *ngIf="configSizingForm">
    <p-dialog [header]="tranService.translate('chart.label.sizing')" (onHide)="closeDialog()" [(visible)]="isShowEditSizing" [modal]="true" [style]="{ width: '700px',top: '0px' }" [draggable]="false" [resizable]="false">
        <form [formGroup]="configSizingForm">
            <div class="w-full field grid p-0 m-0">
                <div class="w-full field grid">
                    <label htmlFor="scaleX" class="col-fixed" style="width:150px">{{tranService.translate('chart.label.width')}}</label>
                    <div class="col flex flex-row flex-wrap justify-content-start align-items-center">
                        <p-inputNumber class="flex-1" [(ngModel)]="configSizing.width" inputId="minmax" mode="decimal" [min]="300" [max]="2000" formControlName="width"
                        suffix=" px"> </p-inputNumber>
                    </div>
                </div>
                <div class="w-full field grid">
                    <label htmlFor="scaleY" class="col-fixed" style="width:150px">{{tranService.translate('chart.label.height')}}</label>
                    <div class="col flex flex-row flex-wrap justify-content-start align-items-center">
                        <p-inputNumber class="flex-1" [(ngModel)]="configSizing.height" inputId="minmax" mode="decimal" [min]="100" [max]="2000" formControlName="height"
                        suffix=" px"> </p-inputNumber>
                    </div>
                </div>
                <div class="w-full field grid">
                    <label htmlFor="marginLeft" class="col-fixed" style="width:150px">{{tranService.translate('chart.label.marginLeft')}}</label>
                    <div class="col flex flex-row flex-wrap justify-content-start align-items-center">
                        <p-inputNumber class="flex-1" [(ngModel)]="configSizing.marginLeft"  mode="decimal" [min]="0" formControlName="marginLeft" suffix=" px"
                        > </p-inputNumber>
                    </div>
                </div>
                <div class="w-full field grid">
                    <label htmlFor="marginRight" class="col-fixed" style="width:150px">{{tranService.translate('chart.label.marginRight')}}</label>
                    <div class="col flex flex-row flex-wrap justify-content-start align-items-center">
                        <p-inputNumber class="flex-1" [(ngModel)]="configSizing.marginRight"  mode="decimal" [min]="0" formControlName="marginRight" suffix=" px"
                        > </p-inputNumber>
                    </div>
                </div>
                <div class="w-full field grid">
                    <label htmlFor="align" class="col-fixed" style="width:150px">{{tranService.translate('chart.label.align')}}</label>
                    <div class="col flex flex-row flex-wrap justify-content-start align-items-center">
                        <p-dropdown class="w-full"
                                [showClear]="false"
                                id="align" [autoDisplayFirst]="false"
                                [(ngModel)]="configSizing.align"
                                formControlName="align"
                                [options]="aligns"
                                optionLabel="name"
                                optionValue="value"
                                [placeholder]="tranService.translate('chart.label.align')"
                        ></p-dropdown>
                    </div>
                </div>
            </div>
            <div class="flex flex-row justify-content-center">
                <p-button (click)="applySizing()" styleClass="p-button-info">{{tranService.translate('chart.label.apply')}}</p-button>
            </div>
        </form>
    </p-dialog>
</div>
