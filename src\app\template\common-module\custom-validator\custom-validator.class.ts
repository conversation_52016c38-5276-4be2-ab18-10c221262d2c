import {AbstractControl, ValidationErrors} from '@angular/forms';

export class CustomValidator {
    static cannotContainSpace(control: AbstractControl): ValidationErrors | null {
        if (control.value) {
            if ((control.value as string).indexOf(' ') >= 0) {
                return {cannotContainSpace: true};
            }
            return null;
        }
        return null;
    }

    static numeric(control: AbstractControl) {
        const val = control.value;

        if (val !== '' && val !== null) {
            if (!val.toString().match(/^[0-9]+(\.?[0-9]+)?$/)) {
                return {invalidNumber: true};
            }

            if (val.toString().match(/^[0-9]+(\.?[0-9]+)?$/) && val < 0) {
                return {invalidNumber: true};
            }

        }
        return null;
    }

    static isInvalidDataUsage(control: AbstractControl): ValidationErrors | null {
        if(control.value) {
            const value = Number(control.value);
            if (isNaN(value) || value < 100 || value % 100 !== 0) {
                return {isInvalidDataUsage: true};
            }
        }
        return null;
    }

    static isInvalidSmsUsage(control: AbstractControl): ValidationErrors | null {
        if(control.value) {
            const value = Number(control.value);
            if (isNaN(value) || value < 10 || value % 5 !== 0) {
                return {isInvalidDataUsage: true};
            }
        }
        return null;
    }


    static onlySpace(control: AbstractControl) {
        const val = control.value;
        if (val !== null) {
            if (!val.replace(/\s/g, '').length) {
                return {onlySpace: true};
            }
        }
        return null;
    }

    static isBoolean(control: AbstractControl): ValidationErrors | null {
        let booleanValue: string[] = ['true', 'false', '0', '1']
        if (control.value) {
            if (!booleanValue.includes(control.value)) {
                return {isBoolean: true};
            }
            return null;
        }
        return null;
    }

    static isInt(control: AbstractControl): ValidationErrors | null {
        if (control.value !== undefined && control.value !== null && control.value !== '') {
            if (control.value.includes('.')) return {isInt: true};
            const value = Number(control.value);
            const MAX_INT = 2147483647;
            const MIN_INT = -2147483648;
            if (!Number.isInteger(value) || value > MAX_INT || value < MIN_INT) {
                return {isInt: true};
            }
        }
        return null;
    }

    static isUnsignedInt(control: AbstractControl): ValidationErrors | null {
        if (control.value !== undefined && control.value !== null && control.value !== '') {
            if (control.value.includes('.')) return {isUnsignedInt: true};

            const value = Number(control.value);
            const MAX_INT = 65535;
            const MIN_INT = 0;
            if (!Number.isInteger(value) || value < MIN_INT || value > MAX_INT) {
                return {isUnsignedInt: true};
            }
        }
        return null;
    }

    static isUnsignedLong(control: AbstractControl): ValidationErrors | null {
        if (control.value !== undefined && control.value !== null && control.value !== '') {
            if (control.value.includes('.')) return {isUnsignedLong: true};

            const value = Number(control.value);
            const MAX_UNSIGNED_LONG = 4294967295;
            const MIN_UNSIGNED_LONG = 0;
            if (!Number.isInteger(value) || value < 0 || value > MAX_UNSIGNED_LONG || value < MIN_UNSIGNED_LONG) {
                return {isUnsignedLong: true};
            }
        }
        return null;
    }
}
