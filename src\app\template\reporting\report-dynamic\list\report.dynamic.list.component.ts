import {Component, Inject, Injector, OnInit} from "@angular/core";
import {ComponentBase} from "../../../../component.base";
import {ColumnInfo, OptionTable} from "../../../common-module/table/table.component";
import {CONSTANTS} from "../../../../service/comon/constants";
import {ReportDynacmicService} from "../../../../service/report/ReportDynacmicService";
import {MenuItem} from "primeng/api";
import {FormBuilder} from "@angular/forms";
import { ReportDynamicFormControl } from "../components/report.dynamic.form.component";

@Component({
    selector: "report-dynamic-list",
    templateUrl: "./report.dynamic.list.component.html"
})
export class ReportDynamicListComponent extends ComponentBase implements OnInit {
    constructor(injector: Injector, private formBuilder: FormBuilder,
                @Inject(ReportDynacmicService) private reportDynamicService: ReportDynacmicService) {
        super(injector);
    }
    columns: Array<ColumnInfo>;
    optionTable: OptionTable;
    dataSet: {
        content: Array<any>,
        total: number
    };
    pageNumber: number;
    pageSize: number;
    sort: string;
    searchInfo: {
        name: string | null,
        status: number | null,
        fromDate: Date|null,
        toDate: Date|null,
    }
    formSearch: any;
    reportStatus: any;
    selectItems: any;
    items: MenuItem[];
    home: MenuItem
    maxDateFrom: Date|number|string|null = new Date();
    minDateTo: Date|number|string|null = null;
    maxDateTo: Date|number|string|null = new Date();
    objectPermissions = CONSTANTS.PERMISSIONS;
    modeForm: number = CONSTANTS.MODE_VIEW.CREATE;
    idReport: number = null;
    reportDynamicFormControl: ReportDynamicFormControl = new ReportDynamicFormControl();
    ngOnInit(): void {
        let me = this;
        this.items = [{ label: this.tranService.translate("global.menu.report"),},{ label: this.tranService.translate("global.menu.dynamicreport")},];
        this.home = { icon: 'pi pi-home', routerLink: '/' };
        this.selectItems = [];
        this.searchInfo = {
            name: null,
            status: null,
            fromDate: null,
            toDate:null,
        }
        this.formSearch = this.formBuilder.group(this.searchInfo);
        this.reportStatus = [
            {
                value: CONSTANTS.REPORT_STATUS.ACTIVE,
                name: this.tranService.translate("report.status.active")
            },
            {
                value: CONSTANTS.REPORT_STATUS.INACTIVE,
                name: this.tranService.translate("report.status.inactive")
            },
        ]
        this.columns = [
            {
                name: this.tranService.translate("report.label.reportName"),
                key: "name",
                size: "70%",
                align: "left",
                isShow: true,
                isSort: false,
                style:{
                    cursor: "pointer",
                    color: "var(--mainColorText)"
                },
                funcClick: (id, item)=>{
                    if(me.checkAuthen([CONSTANTS.PERMISSIONS.REPORT_DYNAMIC.VIEW_DETAIL])){
                        me.idReport = id;
                        me.modeForm = CONSTANTS.MODE_VIEW.DETAIL;
                        setTimeout(function(){
                            me.reportDynamicFormControl.reload();
                        })
                    }
                },
            },
            {
                name: this.tranService.translate("report.label.reportStatus"),
                key: "status",
                size: "250px",
                align: "left",
                isShow: true,
                isSort: false,
                funcGetClassname: (value) => {
                    if (value == CONSTANTS.REPORT_STATUS.ACTIVE) {
                        return ['p-2', 'text-green-800', "bg-green-100", "border-round", "inline-block"];
                    } else if (value == CONSTANTS.REPORT_STATUS.INACTIVE) {
                        return ['p-2', 'text-red-700',"bg-red-100", "border-round", "inline-block"];
                    }
                    return [];
                },
                funcConvertText: (value) => {
                    if (value == CONSTANTS.REPORT_STATUS.ACTIVE) {
                        return me.tranService.translate("report.status.active");
                    } else if(value == CONSTANTS.REPORT_STATUS.INACTIVE) {
                        return  me.tranService.translate("report.status.inactive")
                    }
                    return "";
                },
                style: {
                    color: "white"
                },
            },
            {
                name: this.tranService.translate("report.label.updateDate"),
                key: "updatedDate",
                size: "250px",
                align: "left",
                isShow: true,
                isSort: false,
                funcConvertText(value){
                    if(value == null) return "";
                    return me.utilService.convertDateToString(new Date(value));
                }
            },
        ];
        this.optionTable = {
            hasClearSelected: true,
            hasShowChoose: false,
            hasShowIndex: true,
            hasShowToggleColumn: false,
            action: [
                {
                    icon: "pi pi-pencil",
                    tooltip: this.tranService.translate("global.button.edit"),
                    func: function(id, item){
                        me.idReport = id;
                        me.modeForm = CONSTANTS.MODE_VIEW.UPDATE;
                        setTimeout(function(){
                            me.reportDynamicFormControl.reload();
                        })
                    },
                    funcAppear: function(id, item) {
                        return me.checkAuthen([CONSTANTS.PERMISSIONS.REPORT_DYNAMIC.UPDATE]);
                    }
                },
                // {
                //     icon: "pi pi-external-link",
                //     tooltip: this.tranService.translate("permission.RptContent.RptContent"),
                //     func: function(id, item){
                //         me.router.navigate(['/reports/report-dynamic/report-content/'+id])
                //     },
                //     funcAppear: function(id, item) {
                //         return me.checkAuthen([`getReport_${item.id}`]) && item.status == CONSTANTS.REPORT_STATUS.ACTIVE;
                //     }
                // },
                {
                    icon: "pi pi-lock",
                    tooltip: this.tranService.translate("global.button.changeStatus"),
                    func: function(id, item){
                        me.messageCommonService.confirm(
                            me.tranService.translate("global.message.titleConfirmChangeStatusReport"),
                            me.tranService.translate("global.message.confirmChangeStatusReport"),
                            {
                                ok:()=>{
                                    let params = {
                                        status: 0,
                                    }
                                    me.messageCommonService.onload();
                                    me.reportDynamicService.changeStatus(id, params,(response)=>{
                                        me.messageCommonService.success(me.tranService.translate("global.message.changeStatusSuccess"));
                                        me.search(me.pageNumber, me.pageSize, me.sort, me.searchInfo);
                                    }, null, ()=>{
                                        me.messageCommonService.offload();
                                    })
                                },
                                cancel: ()=>{
                                    // me.messageCommonService.error(me.tranService.translate("global.message.changeStatusFail"));
                                }
                            }
                        )
                    },
                    funcAppear: function(id, item) {
                        return item.status == CONSTANTS.REPORT_STATUS.ACTIVE && me.checkAuthen([CONSTANTS.PERMISSIONS.REPORT_DYNAMIC.UPDATE]);;
                    }
                },
                {
                    icon: "pi pi-lock-open",
                    tooltip: this.tranService.translate("global.button.changeStatus"),
                    func: function(id, item){
                        me.messageCommonService.confirm(
                            me.tranService.translate("global.message.titleConfirmChangeStatusReport"),
                            me.tranService.translate("global.message.confirmChangeStatusReport"),
                            {
                                ok:()=>{
                                    let params = {
                                        status: 1,
                                    }
                                    me.messageCommonService.onload();
                                    me.reportDynamicService.changeStatus(id, params, (response)=>{
                                        me.messageCommonService.success(me.tranService.translate("global.message.changeStatusSuccess"));
                                        me.search(me.pageNumber, me.pageSize, me.sort, me.searchInfo);
                                    }, null, ()=>{
                                        me.messageCommonService.offload();
                                    })
                                },
                                cancel: ()=>{
                                    // me.messageCommonService.error(me.tranService.translate("global.message.changeStatusFail"));
                                }
                            }
                        )
                    },
                    funcAppear: function(id, item) {
                        return item.status == CONSTANTS.REPORT_STATUS.INACTIVE && me.checkAuthen([CONSTANTS.PERMISSIONS.REPORT_DYNAMIC.UPDATE]);;
                    }
                },
                {
                    icon: "pi pi-trash",
                    tooltip: this.tranService.translate("global.button.delete"),
                    func: function(id, item){
                        me.messageCommonService.confirm(
                            me.tranService.translate("global.message.titleConfirmDeleteReport"),
                            me.tranService.translate("global.message.confirmDeleteReport"),
                            {
                                ok:()=>{
                                    me.messageCommonService.onload();
                                    me.reportDynamicService.deleleReport(id, (response)=>{
                                        me.messageCommonService.success(me.tranService.translate("global.message.deleteSuccess"));
                                        me.search(me.pageNumber, me.pageSize, me.sort, me.searchInfo);
                                    }, null, ()=>{
                                        me.messageCommonService.offload();
                                    })
                                },
                                cancel: ()=>{
                                    // me.messageCommonService.error(me.tranService.translate("global.message.deleteFail"));
                                }
                            }
                        )
                    },
                    funcAppear: function(id, item) {
                        return item.isHasChild !== true && me.checkAuthen([CONSTANTS.PERMISSIONS.REPORT_DYNAMIC.DELETE]);;
                    }
                },
            ]
        }
        this.pageNumber = 0;
        this.pageSize= 10;
        this.sort = "createdDate,desc";
        this.dataSet ={
            content: [],
            total: 0
        }
        this.search(this.pageNumber, this.pageSize, this.sort, this.searchInfo);
    }
    openCreateReport() {
        let me = this;
        me.idReport = null;
        this.modeForm = CONSTANTS.MODE_VIEW.CREATE;
        setTimeout(function(){
            me.reportDynamicFormControl.reload("truongdx");
        })
    }
    onSubmitSearch(){
        let me = this;
        me.pageNumber = 0;
        me.search(0, this.pageSize, this.sort, this.searchInfo);
    }
    onChangeDateFrom(value){
        if(value){
            this.minDateTo = value;
        }else{
            this.minDateTo = null
        }
    }

    onChangeDateTo(value){
        if(value){
            this.maxDateFrom = value;
        }else{
            this.maxDateFrom = new Date();
        }
    }
    search(page, limit, sort, params){
        let me = this;
        this.pageNumber = page;
        this.pageSize = limit;

        let dataParams = {
            page,
            size: limit,
            sort
        }
        Object.keys(this.searchInfo).forEach(key => {
            if(this.searchInfo[key] != null) {
                if (key == "fromDate") {
                    dataParams["fromDate"] = this.searchInfo.fromDate.getTime();
                } else if (key == "toDate") {
                    dataParams["toDate"] = this.searchInfo.toDate.getTime();
                } else {
                    dataParams[key] = this.searchInfo[key];
                }
            }
        })
        me.messageCommonService.onload();
        me.reportDynamicService.search(dataParams, (response)=>{
            me.dataSet = {
                content: response.content,
                total: response.totalElements
            }
        }, null, ()=>{
            me.messageCommonService.offload();
        })
    }

    protected readonly CONSTANTS = CONSTANTS;
}
