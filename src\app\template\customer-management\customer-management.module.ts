import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { CustomerManagementRoutingModule } from './customer-management-routing.module';
import { ListCustomerComponent } from './list-customer/list-customer.component';
import { DetailCustomerComponent } from './detail-customer/detail-customer.component';
import { UpdateCustomerComponent } from './update-customer/update-customer.component';
import { BreadcrumbModule } from 'primeng/breadcrumb';
import { FieldsetModule } from 'primeng/fieldset';
import { ButtonModule } from 'primeng/button';
import { TableModule } from 'primeng/table';
import { CommonVnptModule } from '../common-module/common.module';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { InputTextModule } from 'primeng/inputtext';
import { DropdownModule } from 'primeng/dropdown';
import { PanelModule } from 'primeng/panel';
import { InputTextareaModule } from 'primeng/inputtextarea';
import { CalendarModule } from 'primeng/calendar';
import { DialogModule } from 'primeng/dialog';
import { CustomerService } from 'src/app/service/customer/CustomerService';
import { AccountService } from 'src/app/service/account/AccountService';

@NgModule({
  declarations: [
    ListCustomerComponent,
    DetailCustomerComponent,
    UpdateCustomerComponent
  ],
  imports: [
    CommonModule,
    CustomerManagementRoutingModule,
    BreadcrumbModule,
    FieldsetModule,
    ButtonModule,
    TableModule,
    FormsModule,
    CommonVnptModule,
    ReactiveFormsModule,
    InputTextModule,
    DropdownModule,
    PanelModule,
    DialogModule,
    InputTextareaModule,
    CalendarModule 
  ],
  providers: [CustomerService, AccountService]
})
export class CustomerManagementModule { }
