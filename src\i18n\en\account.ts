export default {
    label: {
        username: "<PERSON>rna<PERSON>",
        fullname: "Full Name",
        cusName: "Business / Customer Name",
        totalUsage: "Current Monthly Total Flow (m3)",
        provisionalAmount: "Total Estimated Cost (VND)",
        provinces: "Province",
        wards: "Commune",
        userType: "User Type",
        cusType: "Customer type",
        email: "Email",
        provinceCode: "Province Code",
        time: "Time",
        status: "Status",
        phone: "Phone Number",
        description: "Description",
        manager: "Manager Level",
        province: "Province/City",
        role: "Role",
        permission:{
            name: "Permission Name",
            object: "Object"
        },
        customerName: "Customer Name",
        password: "Password",
        oldPass : "Old password",
        newPass : "New password",
        confirmPass: "Confirm password",
        submitChangePass : "Agree & Sign In",
        linkExpired : "This password recovery has expired",
        cmpForgotPass : "ONE METER - Forgot Password",
        deviceType: "Device Type",
        os: "Operating System",
        ip: "IP Address",
        managerName : "Manager",
        customerAccount : "Superior Customer Accouunt",
        accountRootId : "Business customer management",
        generalInfo: "General Information",
        loginInfo: "Login Info",
        businessInfo: "Business Info",
        businessName: "Business Name",
        taxCode: "Tax Code",
        addressHeadOffice: "Head Office Address",
        provincesHeadOffice: "Head Office Province",
        wardsHeadOffice: "Head Office Commune",
        provincesContact: "Contact Province",
        wardsContact: "Contact Commune",
        representativeName: "Representative Name",
        addressContact: "Detailed Contact Address",
        individualInfo: "Customer Information",
        addCustomerAccount : "Add Customer Accouunt",
        showCustomerAccount : "Show Customer Accouunt",
        notiChangePass: "Password has expired. Please change your password to continue using. The new password is valid for 6 months from the date of the last password change."

    },
    text: {
        detailaccount: "Detail Account",
        infoAccount: "Info Account",
        active: "Active",
        inactive: "Inactive",
        account: "Account",
        titleChangeManageLevel: "Change Manager Level",
        selectAccount: "Select Account",
        inputUsername: "Input Account Name",
        inputFullname: "Input Full Name",
        inputPassword: "Enter Password",
        inputEmail: "Input Email",
        inputPhone: "Input Phone Number",
        inputBusinessName: "Enter Business Name",
        inputCustomerName: "Enter Customer Name",
        inputRepresentativeName: "Enter Representative Name",
        inputAddressContact: "Enter Contact Address",
        inputAddressHeadOffice: "Enter Head Office Address",
        inputTaxCode: "Enter Tax Code",
        selectAccountRootId: "Select Business Management Customer",
        selectUserType: "Select User Type",
        selectRoles: "Select Roles",
        selectProvincesHeadOffice: "Select Head Office Province",
        selectWardsHeadOffice: "Select Head Office Commune",
        selectProvincesContact: "Select Contact Province",
        selectWardsContact: "Select Contact Commune",
        selectManager: "Select Manager Level",
        selectProvince: "Select Province/City",
        selectCustomers: "Select Customers",
        disagreePolicy: "You haven't agree to this policy yet",
        typeSelectAll: "Change All",
        typeSelectList: "Change By List",
        selectGDV : "Select manager",
        selectCustomerAccount : "Select superior customer account",
        addCustomer: "Add Customer",
        addContract: "Add Contract Code",
        grantApi : "Grant API",
        module : "Module",
        gen : "Gen",
        working: "Working",
        notWorking : "Not Working"
    },
    usertype: {
        admin: "Admin",
        individual: "Individual Customer",
        business: "Business Customer",
    },
    userstatus: {
        active: "Active",
        inactive: "Inactive",
        lock: "Temporarily locked"
    },
    button: {
        disagreePolicy: "Disagree Policy",
        viewPolicyProtectPersonalData: "View Personal Data Protection Policy"
    },
    message: {
        customerRequired: "Must select at least one customer",
        managerRequired: '“Manage” cannot be left blank'
    }
}
