import { NgModule } from '@angular/core';
import { HashLocationStrategy, LocationStrategy } from '@angular/common';
import { AppComponent } from './app.component';
import { AppRoutingModule } from './app-routing.module';
import { AppLayoutModule } from './template/layout/app.layout.module';
import { CommonVnptModule } from "./template/common-module/common.module";
import { DEFAULT_TIMEOUT, TimeoutInterceptor } from './service/comon/http.service';
import { HTTP_INTERCEPTORS } from '@angular/common/http';
import { TermPolicyModule } from './template/term-policy/app.term.policy.module';
import {UpdatePasswordExpiredModule} from "./template/pages/update-password-expired/update-password-expired.module";
@NgModule({
    declarations: [
        AppComponent,
        // ComponentBase
    ],

    imports: [
        AppRoutingModule,
        AppLayoutModule,
        CommonVnptModule,
        TermPolicyModule,
        UpdatePasswordExpiredModule
    ],
    providers: [
        { provide: LocationStrategy, useClass: HashLocationStrategy },
        [{ provide: HTTP_INTERCEPTORS, useClass: TimeoutInterceptor, multi: true }],
        [{ provide: DEFAULT_TIMEOUT, useValue: 30000 }]
    ],
    bootstrap: [AppComponent]
})
export class AppModule { }
