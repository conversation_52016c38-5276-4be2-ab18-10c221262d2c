<div *ngIf="formCrontabInfo">
    <form [formGroup]="formCrontabInfo" (ngSubmit)="onSubmit()">
        <p-card>
            <!-- query -->
            <div class="w-full field grid">
                <label htmlFor="query" class="col-fixed" style="width:180px; height: fit-content;">{{tranService.translate("report.label.query")}}</label>
                <div class="col" style="width: calc(100% - 210px)">
                    <textarea  class="w-full" style="resize: none;"
                        rows="3"
                        [autoResize]="false"
                        pInputTextarea id="query" 
                        [(ngModel)]="crontabInfo.query" 
                        formControlName="query"
                        [placeholder]="tranService.translate('report.text.inputQuery')"
                    ></textarea>
                </div>
                <span class="col-fixed pi pi-clone text-xl cursor-pointer" style="width:30px;height: fit-content" [pTooltip]="tranService.translate('global.button.copy')" (click)="copyText($event)"></span>
            </div>
            <!-- error query -->
            <div class="w-full field grid text-error-field">
                <label htmlFor="name" class="col-fixed" style="width:180px"></label>
                <div class="col">
                    <small class="text-red-500" *ngIf="formCrontabInfo.controls.query.dirty && formCrontabInfo.controls.query.errors?.required">{{tranService.translate("global.message.required")}}</small>
                </div>
            </div>
            <!-- schema -->
            <div class="w-full field grid">
                <label for="schema" class="col-fixed" style="width:180px">{{tranService.translate("report.label.schema")}}<span *ngIf="crontabInfo.query != null && crontabInfo.query != ''" class="text-red-500">*</span></label>
                <div class="col" style="max-width: 500px;">
                    <p-dropdown styleClass="w-full" showClear="true"
                            id="schema" [autoDisplayFirst]="false"
                            [(ngModel)]="crontabInfo.schema" appendTo="body"
                            formControlName="schema"
                            [options]="schemas"
                            optionLabel="name"
                            optionValue="value"
                            [required]="crontabInfo.query != null && crontabInfo.query != ''"
                            [placeholder]="tranService.translate('report.text.selectSchema')"
                    ></p-dropdown>
                </div>
            </div>
            <!-- error schema -->
            <div class="w-full field grid text-error-field">
                <label htmlFor="name" class="col-fixed" style="width:180px"></label>
                <div class="col">
                    <small class="text-red-500" *ngIf="formCrontabInfo.controls.schema.dirty && formCrontabInfo.controls.schema.errors?.required">{{tranService.translate("global.message.required")}}</small>
                </div>
            </div>
            <!-- time  -->
            <div class="w-full field grid" *ngIf="crontabInfo.typeSchedule == 0">
                <label for="timeOnce" class="col-fixed" style="width:180px">{{tranService.translate("report.label.timeOnce")}}</label>
                <div class="col" style="max-width: 500px;">
                    <p-calendar styleClass="w-full"
                        id="timeOnce"
                        [(ngModel)]="crontabInfo.timeOnce"
                        [showClear]="true"
                        [showIcon]="true"
                        hourFormat="hh:mm:ss"
                        [showTime]="true" [showSeconds]="true"
                        formControlName="timeOnce" appendTo="body"
                        [placeholder]="tranService.translate('report.text.selectHourSummary')"
                    />
                </div>
            </div>
            <!-- error time -->
            <div class="w-full field grid text-error-field">
                <label htmlFor="name" class="col-fixed" style="width:180px"></label>
                <div class="col">
                    <small class="text-red-500" *ngIf="formCrontabInfo.controls.timeOnce.dirty && formCrontabInfo.controls.timeOnce.errors?.required">{{tranService.translate("global.message.required")}}</small>
                </div>
            </div>
            <!-- start Time  -->
            <div class="w-full field grid" [class]="crontabInfo.typeSchedule == 1 ? '' : 'hidden'">
                <label for="startTime" class="col-fixed" style="width:180px">{{tranService.translate("report.label.startTime")}}<span *ngIf="crontabInfo.endTime != null" class="text-red-500">*</span></label>
                <div class="col" style="max-width: 500px;">
                    <p-inputNumber class="w-full" 
                        id="startTime"
                        [(ngModel)]="crontabInfo.startTime"
                        formControlName="startTime"
                        [min]="0"
                        [max]="getMaxStartTime()"
                        [placeholder]="tranService.translate('report.text.selectStartTime')"
                        [required]="crontabInfo.endTime != null && crontabInfo.typeSchedule == 1"
                    > </p-inputNumber>
                </div>
            </div>
            <!-- error start time -->
            <div class="w-full field grid text-error-field">
                <label htmlFor="name" class="col-fixed" style="width:180px"></label>
                <div class="col">
                    <small class="text-red-500" *ngIf="formCrontabInfo.controls.startTime.dirty && formCrontabInfo.controls.startTime.errors?.required">{{tranService.translate("global.message.required")}}</small>
                </div>
            </div>
            <!-- end time  -->
            <div class="w-full field grid" [class]="crontabInfo.typeSchedule == 1 ? '' : 'hidden'">
                <label for="endTime" class="col-fixed" style="width:180px">{{tranService.translate("report.label.endTime")}}<span *ngIf="crontabInfo.startTime != null" class="text-red-500">*</span></label>
                <div class="col" style="max-width: 500px;">
                    <p-inputNumber class="w-full"
                        id="endTime"
                        [(ngModel)]="crontabInfo.endTime"
                        formControlName="endTime"
                        [max]="23"
                        [min]="getMinEndTime()"
                        [placeholder]="tranService.translate('report.text.selectEndTime')"
                        [required]="crontabInfo.startTime != null && crontabInfo.typeSchedule == 1"
                    > </p-inputNumber>
                </div>
            </div>
            <!-- error end time -->
            <div class="w-full field grid text-error-field">
                <label htmlFor="name" class="col-fixed" style="width:180px"></label>
                <div class="col">
                    <small class="text-red-500" *ngIf="formCrontabInfo.controls.endTime.dirty && formCrontabInfo.controls.endTime.errors?.required">{{tranService.translate("global.message.required")}}</small>
                </div>
            </div>
            <!-- type schedule  -->
            <div class="w-full field grid">
                <label for="timeOnce" class="col-fixed" style="width:180px">{{tranService.translate("report.label.typeSchedule")}}</label>
                <div class="col flex flex-row align-items-center justify-content-between" style="max-width: 500px;">
                    <div>
                        <p-radioButton name="typeSchedule" [value]="0" [(ngModel)]="crontabInfo.typeSchedule" formControlName="typeSchedule"></p-radioButton>
                        <label class="ml-2">{{tranService.translate("report.label.runOne")}}</label>
                    </div>
                    <div>
                        <p-radioButton name="typeSchedule" [value]="1" [(ngModel)]="crontabInfo.typeSchedule" formControlName="typeSchedule"></p-radioButton>
                        <label class="ml-2">{{tranService.translate("report.label.runRepeat")}}</label>
                    </div>
                </div>
            </div>
        </p-card>
        <p-card styleClass="border-1 border-solid surface-border" *ngIf="crontabInfo.typeSchedule == 1">
            <!-- cycle -->
            <div class="w-full field grid">
                <label for="cycle" class="col-fixed" style="width:180px">{{tranService.translate("report.label.numberRepeatHour")}}</label>
                <div class="col" style="max-width: 500px;">
                    <p-dropdown styleClass="w-full" showClear="true" 
                            id="cycle" [autoDisplayFirst]="false" appendTo="body"
                            [(ngModel)]="crontabInfo.cycle"
                            formControlName="cycle"
                            [options]="cycles"
                            optionLabel="name"
                            optionValue="value"
                            [placeholder]="tranService.translate('report.text.selectCycle')"
                    ></p-dropdown>
                </div>
            </div>
            <!-- error schema -->
            <div class="w-full field grid text-error-field">
                <label htmlFor="cycle" class="col-fixed" style="width:180px"></label>
                <div class="col">
                    <small class="text-red-500" *ngIf="formCrontabInfo.controls.cycle.dirty && formCrontabInfo.controls.cycle.errors?.required">{{tranService.translate("global.message.required")}}</small>
                </div>
            </div>
            <div class="flex flex-row justify-content-between" style="height: fit-content;">
                <!-- select day in month -->
                <div class="w-3" [style]="{'min-width': '220px'}">
                    <p-card styleClass="border-1 border-solid surface-border h-full w-full">
                        <div class="flex flex-row justify-content-start w-full">
                            <p-checkbox 
                                name="allDayInMonth"
                                binary="true"
                                [(ngModel)]="crontabInfo.allDayInMonth"
                                formControlName="allDayInMonth"
                                [trueValue]="1" (onChange)="toggleSelect(0)"
                                [falseValue]="0">
                            </p-checkbox>
                            <label class="ml-2">{{tranService.translate("report.label.dayInMonth")}}</label>
                        </div>
                        <p-divider type="solid"></p-divider>
                        <div class="flex flex-row justify-content-start flex-wrap">
                            <div class="w-4 mb-2" style="min-width: 60px;" *ngFor="let day of fullDayInMonth">
                                <p-checkbox 
                                    name="dayInMonth"
                                    [value]="day"
                                    [(ngModel)]="crontabInfo.dayInMonth"
                                    formControlName="dayInMonth">
                                </p-checkbox>
                                <label class="ml-2">{{day}}</label>
                            </div>
                        </div>
                    </p-card>
                </div>
                <!-- select day in week  -->
                <div class="w-3" [style]="{'min-width': '220px'}">
                    <p-card styleClass="border-1 border-solid surface-border h-full w-full">
                        <div class="flex flex-row justify-content-start w-full">
                            <p-checkbox 
                                name="allDayInWeek"
                                binary="true"
                                [(ngModel)]="crontabInfo.allDayInWeek"
                                formControlName="allDayInWeek"
                                [trueValue]="1" (onChange)="toggleSelect(1)"
                                [falseValue]="0">
                            </p-checkbox>
                            <label class="ml-2">{{tranService.translate("report.label.dayInWeek")}}</label>
                        </div>
                        <p-divider type="solid"></p-divider>
                        <div class="flex flex-row justify-content-start flex-wrap">
                            <div class="w-full mb-2" style="min-width: 180px;" *ngFor="let day of fullDayInWeek">
                                <p-checkbox 
                                    name="dayInWeek"
                                    [value]="day.value"
                                    [(ngModel)]="crontabInfo.dayInWeek"
                                    formControlName="dayInWeek">
                                </p-checkbox>
                                <label class="ml-2">{{day.name}}</label>
                            </div>
                        </div>
                    </p-card>
                </div>
                <!-- select month  -->
                <div class="w-3" [style]="{'min-width': '220px'}">
                    <p-card styleClass="border-1 border-solid surface-border h-full w-full">
                        <div class="flex flex-row justify-content-start w-full">
                            <p-checkbox 
                                name="allMonth"
                                binary="true"
                                [(ngModel)]="crontabInfo.allMonth"
                                formControlName="allMonth"
                                [trueValue]="1" (onChange)="toggleSelect(2)"
                                [falseValue]="0">
                            </p-checkbox>
                            <label class="ml-2">{{tranService.translate("report.label.monthInYear")}}</label>
                        </div>
                        <p-divider type="solid"></p-divider>
                        <div class="flex flex-row justify-content-start flex-wrap">
                            <div class="w-full mb-2" style="min-width: 180px;" *ngFor="let month of fullMonth">
                                <p-checkbox 
                                    name="month"
                                    [value]="month.value"
                                    [(ngModel)]="crontabInfo.month"
                                    formControlName="month">
                                </p-checkbox>
                                <label class="ml-2">{{month.name}}</label>
                            </div>
                        </div>
                    </p-card>
                </div>
            </div>
        </p-card>

        <div class="flex flex-row justify-content-center align-items-center mt-3">
            <p-button styleClass="mr-2 p-button-secondary p-button-outlined" [label]="tranService.translate('global.button.cancel')" (click)="cancel()"></p-button>
            <p-button type="submit" *ngIf="modeView != objectMode.DETAIL" styleClass="p-button-info" [label]="tranService.translate('global.button.save')" [disabled]="formCrontabInfo.invalid"></p-button>
        </div>
    </form>
</div>