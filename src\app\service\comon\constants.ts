import statusHttp from "./status.http"
import permissions from "./permissions"
export const CONSTANTS = {
    MAX_TIME_HTTP_WAIT: 30 * 1000,
    NUMBER_IMAGE_CAPTCHA: 50,
    HTTP_STATUS: statusHttp,
    PERMISSIONS: permissions,
    MAX_ROW_EXPORT: 1000000,
    MAX_ROW_EXCEL_EXPORT: 100000,
    OBSERVABLE:{
        KEY_MESSAGE_COMMON: "message-common",
        KEY_INPUT_FILE_VNPT: "input-file-vnpt",
        KEY_LOAD_CONFIRM_POLICY_HISTORY: "load-confirm-policy-history",
        KEY_EXPIRED_PASSWORD: "expired-password",
        UPDATE_SHARE_INFO: "update-share-info"
    },
    SIM_STATUS: {
        READY: 1,
        ACTIVATED: 2,
        INACTIVED: 3,
        DEACTIVATED: 4,
        PURGED: 5,
        PROCESSING: 6,
    },
    USER_TYPE: {
        ADMIN: 1,
        BUSINESS: 2,
        INDIVIDUAL: 3,
    },
    USER_STATUS: {
        ACTIVE: 1,
        INACTIVE: 0
    },

    ROlES_STATUS: {
        ACTIVE: 1,
        INACTIVE: 0
    },
    ROLE_TYPE: {
        ALL: 0,
        ADMIN: 1,
        BUSINESS: 2,
        INDIVIDUAL: 3,
    },

    RATING_PLAN_STATUS: {
        CREATE_NEW: 2,
        PENDING: 3,
        ACTIVATED: 1,
        DEACTIVATED: 5,
        INACTIVE: 0,
        REJECTED: 4,
        REMOVED: -1
    },

    RATING_PLAN_CYCLE: {
        DAY: 1,
        MONTH: 3
    },

    SUBSCRIPTION_TYPE: {
        POSTPAID: 0,
        PREPAID: 1
    },

    RATING_PLAN_SCOPE: {
        NATION_WIDE: 0,
        CUSTOMER: 1,
        PROVINCE: 2
    },

    CUSTOMER_TYPE: {
        INTERPRISE: 2,
        PERSONAL: 1,
        AGENCY: 0
    },

    CYCLE_TIME_UNITS: {
        DAY: 1,
        WEEK: 2,
        MONTH: 3,
        YEAR: 4
    },

    RELOAD: {
        YES: 1,
        NO: 0
    },

    FLEXIBLE: {
        YES: 1,
        NO: 0
    },
    IP_TYPE: {
        DYNAMIC: 1,
        STATIC: 2
    },

    ALERT_STATUS: {
        INACTIVE: 0,
        ACTIVE: 1,
    },

    ALERT_STATUS_SIM: {
        ALL: 0,
        OUT_PLAN: 1,
        OUT_LINE: 2,
        DISCONNECTED: 3,
        NEW_CONNECTION: 4,
        // PURGED: 5,
    },
    GROUP_SCOPE: {
        GROUP_ADMIN: 0,
        GROUP_PROVINCE: 1,
        GROUP_CUSTOMER: 2
    },
    CUSTOMER_STATUS: {
        ACTIVE:1,
        INACTIVE:0,
        CREATE_NEW: 2
    },
    MODE_VIEW: {
        CREATE: 0,
        UPDATE: 1,
        DETAIL: 2
    },
    REPORT_STATUS: {
        ACTIVE: 1,
        INACTIVE: 0
    },
    WALLET_ACITVE_TYPE: {
        BUY: 0,
        SHARE: 1,
        ACCURACY:2,
        REGISTER_NO_OTP:3,
        CANCEL_REGISTER_NO_OTP:4,
    },
    REPORT_PREVIEW: {
        ENABLE: 1,
        DISABLE: 0
    },
    PARAMETER_TYPE: {
        NUMBER: 0,
        STRING: 1,
        DATE: 2,
        LIST_NUMBER: 3,
        LIST_STRING: 4,
        TIMESTAMP: 5,
        RECENTLY_DATE_FROM: 6,
        RECENTLY_DATE_TO: 7,
    },
    SCHEMA: {
        //coremgmt, simmgmt, rulemgmt, billing,  logging, monitoring, reporting
        CORE: "coremgmt",
        // SIM: "simmgmt",
        // RULE: "rulemgmt",
        // BILL: "billing",
        // LOG: "logging",
        // MONITOR: "monitoring",
        // REPORT: "reporting",
        // ELASTICSEARCH: "elasticsearch"
    },
    DEVICE: {
        SUCCESS: 200,
        WRONG_FOMAT: 201,
        FILE_TO_BIG: 202,
        COLUMN_INVALID: 203,
        FILE_IS_EMPTY: 204,
        MAX_ROW_FILE_IMPORT: 205,
        MSISDN_NOTEXITS: 211,
        MSISDN_ASSIGN : 212,
        MSISDN_INVALD: 213,
        MSISDN_IS_EMPTY: 214,
        MSISDN_IS_DUPLICATE: 215,
        IMEI_IS_DUPLITE: 216,
        EXPRIRED_DATE_INVALID: 217,
        MSISDN_NOT_PERMISSION: 218,
        IMEI_IS_EXSIT: 219,
        IMEI_LEN: 220,
        DEVICE_TYPE_LEN: 221,
        COUNTRY_LEN: 222,
        HAS_ERROR: 300,
    },
    ALERT_SEVERITY: {
        CRITICAL: 0,
        MAJOR: 1,
        MINOR: 2,
        INFO: 3,
    },
    DATE_TYPE: {
        MONTH: 2,
        DATE: 1,
        DATETIME: 0
    },
    POLICY: {
        PERSONAL_DATA_PROTECTION_POLICY: 1
    },
    POLICY_STATUS: {
        AGREE: 0,
        DISARGREE: 1
    },
    CHART_TYPE: {
        BAR: "bar",
        BUBBLE: "bubble",
        DOUGHNUT: "doughnut",
        PIE: "pie",
        LINE: "line",
        COMBO: "combo",
        POLAR: "polarArea",
        RADAR: "radar",
        SCATTER: "scatter"
    },
    CHART_SUB_TYPE: {
        VERTICAL_BAR: "vertical",
        HORIZONTAL_BAR: "horizontal",
        STACKED_BAR: "stack",
        GROUP_BAR: "group",
        MULTI_AXIS: "multiAxis",
        THRESHOLD: "threshold",
        SLIDER_THRESHOLD: "sliderThreshold"
    },
    REQUEST_STATUS: {
        NEW: 0,
        RECEIVED : 1,
        IN_PROGRESS : 2,
        REJECT : 3,
        DONE : 4
    },
    REQUEST_TYPE: {
        TEST_SIM: 1,
        REPLACE_SIM: 0,
        ORDER_SIM: 2,
        ACTIVE_SIM: 3,
        DIAGNOSE: 4,
    },
    KEY_NOTIFY : {
        TEST_SIM : 'test-sim',
        REPLACE_SIM : 'replace-sim',
        ORDER_SIM: 'order-sim',
        TICKET : 'ticket',
        ACTIVE_SIM: 'active-sim',
        DIAGNOSE: 'diagnose',
    },
    SIM_TICKET_STATUS: {
        NOT_ACTIVATED: 0,
        AWAITING_ACTIVATION: 1,
        ACTIVATED: 2,
    },
    SIM_TYPE: {
      UNKNOWN: 0,
      ESIM: 1,
      SIM: 2,
    },

    COLOURS: {'--blue-50': '#f5f9ff','--blue-100': '#d0e1fd','--blue-200': '#abc9fb','--blue-300': '#85b2f9','--blue-400': '#609af8','--blue-500': '#3b82f6','--blue-600': '#326fd1','--blue-700': '#295bac','--blue-800': '#204887','--blue-900': '#183462','--green-50': '#f4fcf7','--green-100': '#caf1d8','--green-200': '#a0e6ba','--green-300': '#76db9b','--green-400': '#4cd07d','--green-500': '#22c55e','--green-600': '#1da750','--green-700': '#188a42','--green-800': '#136c34','--green-900': '#0e4f26','--yellow-50': '#fefbf3','--yellow-100': '#faedc4','--yellow-200': '#f6de95','--yellow-300': '#f2d066','--yellow-400': '#eec137','--yellow-500': '#eab308','--yellow-600': '#c79807','--yellow-700': '#a47d06','--yellow-800': '#816204','--yellow-900': '#5e4803','--cyan-50': '#f3fbfd','--cyan-100': '#c3edf5','--cyan-200': '#94e0ed','--cyan-300': '#65d2e4','--cyan-400': '#35c4dc','--cyan-500': '#06b6d4','--cyan-600': '#059bb4','--cyan-700': '#047f94','--cyan-800': '#036475','--cyan-900': '#024955','--pink-50': '#fef6fa','--pink-100': '#fad3e7','--pink-200': '#f7b0d3','--pink-300': '#f38ec0','--pink-400': '#f06bac','--pink-500': '#ec4899','--pink-600': '#c93d82','--pink-700': '#a5326b','--pink-800': '#822854','--pink-900': '#5e1d3d','--indigo-50': '#f7f7fe','--indigo-100': '#dadafc','--indigo-200': '#bcbdf9','--indigo-300': '#9ea0f6','--indigo-400': '#8183f4','--indigo-500': '#6366f1','--indigo-600': '#5457cd','--indigo-700': '#4547a9','--indigo-800': '#363885','--indigo-900': '#282960','--teal-50': '#f3fbfb','--teal-100': '#c7eeea','--teal-200': '#9ae0d9','--teal-300': '#6dd3c8','--teal-400': '#41c5b7','--teal-500': '#14b8a6','--teal-600': '#119c8d','--teal-700': '#0e8174','--teal-800': '#0b655b','--teal-900': '#084a42','--orange-50': '#fff8f3','--orange-100': '#feddc7','--orange-200': '#fcc39b','--orange-300': '#fba86f','--orange-400': '#fa8e42','--orange-500': '#f97316','--orange-600': '#d46213','--orange-700': '#ae510f','--orange-800': '#893f0c','--orange-900': '#642e09','--bluegray-50': '#f7f8f9','--bluegray-100': '#dadee3','--bluegray-200': '#bcc3cd','--bluegray-300': '#9fa9b7','--bluegray-400': '#818ea1','--bluegray-500': '#64748b','--bluegray-600': '#556376','--bluegray-700': '#465161','--bluegray-800': '#37404c','--bluegray-900': '#282e38','--purple-50': '#fbf7ff','--purple-100': '#ead6fd','--purple-200': '#dab6fc','--purple-300': '#c996fa','--purple-400': '#b975f9','--purple-500': '#a855f7','--purple-600': '#8f48d2','--purple-700': '#763cad','--purple-800': '#5c2f88','--purple-900': '#432263','--red-50': '#fff5f5','--red-100': '#ffd0ce','--red-200': '#ffaca7','--red-300': '#ff8780','--red-400': '#ff6259','--red-500': '#ff3d32','--red-600': '#d9342b','--red-700': '#b32b23','--red-800': '#8c221c','--red-900': '#661814','--primary-50': '#f7f7fe','--primary-100': '#dadafc','--primary-200': '#bcbdf9','--primary-300': '#9ea0f6','--primary-400': '#8183f4','--primary-500': '#6366f1','--primary-600': '#5457cd','--primary-700': '#4547a9','--primary-800': '#363885','--primary-900': '#282960'},

    SERVICE_TYPE : {
        PREPAID : 0,
        POSTPAID : 1,
    },

    ALERT_ACTION_TYPE:{
        ALERT: 0,
        API: 1
    },

    ALERT_RULE_CATEGORY:{
        MONITORING: 0,
        MANAGEMENT: 1
    },

    ALERT_EVENT_TYPE:{
        DEVICE_ALERT: 1,
        EXCEEDED_VALUE : 2,
    },
    ALERT_UNIT: {
        PERCENT: 1,
        MB: 2,
        SMS: 3,
    },
    RECHARGE_TYPE: {
        TOPUP: 'topup',
        EZPAY: 'ezpay',
    },
    RECHARGE_STATUS: {
        PENDING: 0,
        PAID: 1,
    },
    DEVICE_STATUS: {
        REGISTERED: 1,
        CONNECTED: 2,
        LOST_CONNECTION: 3,
    },
    DEVICE_STATUS_CMD: {
        STORED: 0,
        SENT: 1,
        ACCEPTED: 2,
        REJECT: 3,
        CONFLICT: 4,
        PROCESSED: 5,
    },
    DEVICE_TYPE: {
        WATER_METTER: 1,
        ELECTRIC_METTER: 2,
        SMART_WATCH: 3,
        DIGITAL_WATCH: 4,
    },
    COMMAND_VAR_TYPE: {
      STRING: 1,
      NUMBER: 2,
      CHECKBOX: 3,
    },
    WATER_BILL: {
        //0–10 m3
        LEVEL_1: 5973,
        //11–20 m3
        LEVEL_2: 7052,
        //21–30 m3
        LEVEL_3: 8669,
        //>30 m3
        LEVEL_4: 15929,
    },
    IMPORT_ERROR: {
        emailDuplicated: 'Trùng email',
        phoneDuplicated: 'Trùng SĐT',
        partitionCodeDuplicated: 'Trùng mã phân vùng',
        invalidData: 'Không có quyền thao tác với đối tượng này',
        colMissing: 'File tải lên thiếu cột',
        extraCol: 'File tải lên thừa cột',
        wrongFormat: 'Sai định dạng file mẫu',
        wrongFormatEmail: 'Sai định dạng Email',
        wrongFormatPhone: 'Sai định dạng SĐT',
        wrongFormatTraffic: 'Sai định dạng lưu lượng chia sẻ',
        nameTooLong: 'Họ tên không được lớn hơn 50 ký tự',
        emailTooLong: 'Email không được lớn hơn 100 ký tự',
        notVNPTPhoneNumber: 'Vui lòng nhập số điện thoại thuộc nhà mạng Vinaphone để thực hiện chia sẻ!',
        existPhoneNumber: 'Số điện thoại đã tồn tại trong danh sách chia sẻ. Vui lòng kiểm tra lại!',
        emptyPhoneNumber: 'Số điện thoại không được bỏ trống',
        exceedStorage: 'Lưu lượng chia sẻ vượt quá lưu lượng còn lại của ví',
        minimumTraffic: 'Lưu lượng chia sẻ tối thiểu là 50p thoại, 10 SMS, Data 100MB',
        minimumTrafficMinutes: 'Lưu lượng chia sẻ tối thiểu là 50p thoại',
        minimumTrafficSMS: 'Lưu lượng chia sẻ tối thiểu SMS là 10 SMS',
        minimumTrafficData: 'Lưu lượng chia sẻ tối thiểu data là 100MB',
        wrongTraffic: 'Lưu lượng data phải là bội số của 100 MB, lưu lượng SMS là bội số của 5 SMS',
        wrongTrafficSMS: 'Lưu lượng SMS là bội số của 5 SMS',
        wrongTrafficData: 'Lưu lượng data phải là bội số của 100 MB',
        wrongShareAutoFormat: 'Thông tin chia sẻ tự động phải là "Có/Không"',
        invalidName: 'Sai định dạng ô họ tên. Chỉ cho phép dấu cách, chữ tiếng Việt (a-z, A-Z, 0-9, - _)'
    },
    DIAGNOSE: {
        COVERAGE: {
            EXCELLENT: 5,
            GOOD: 4,
            AVERAGE: 3,
            POOR: 2,
            BAD: 1,
        },
        TYPE_NETWORK: {
            UMTS: 3,
            LTE: 4,
        }
    },
    METHOD_AUTO_SHARE: {
        PAY_CODE: 0,
        SUB_CODE: 1,
        NONE: 2,
    },
    RENEWAL_STATUS: {
        NOT_DUE_YET: 0,
        DUE: 1,
        EXPIRED: 2,
    },
    SHARE_GROUP: {
        //Giới hạn số thuê bao trong nhóm chia sẻ, cập nhật khi lên production là 3000
        LIMIT: 3000,
        // Giới hạn số thuê bao được thêm vào nhóm chia sẻ trong một lần, cập nhật khi lên production là 50
        LIMIT_ADD: 50,
    },
    // [STC_CMP-1018] T-1 Khoảng thời gian cho phép chọn tối đa là 31 ngày
    MAX_DATE_SELECTION_RANGE: 30,

    ACTIVITY_HISTORY: {
        STATUS: {
            PROCESSING: 2,
            COMPLETED: 3,
            SUCCESSFUL: 1,
            FAILED: 0,
        },
        TYPE: {
            SHARED_TRAFFIC: 1,
        },
        PHONE_SHARED_STATUS: {
            SUCCESS: 1,
            FAILED: 0,
        }

    },
    LANGUAGE: {
        VIETNAMESE: 'vi',
        ENGLISH: 'en',
    },
    ALERT_LIMIT: {
        //Giới hạn số email gửi cảnh báo
        EMAIL: 10,
        //Giới hạn số thuê bao gửi cảnh báo
        SMS: 10,
        //Giới hạn số thuê bao gửi cảnh báo
        ZALO: 10,
    },
    ALERT: {
      //  prefix key trong thông tin cấu hình, bỏ prefix key để lấy giá trị key
      PREFIX_KEY_DEVICE_ALARM: 'alarms.',
      //  danh sách object key cần lấy ra giá trị từ bản tin telemetry để hiển thị, ngăn cách nhau bằng dấu phẩy
      LIST_KEY_TELEMETRY: 'alarms',
    }
}

export const isVinaphoneNumber = (number) =>
    number.startsWith('091') ||
    number.startsWith('094') ||
    number.startsWith('081') ||
    number.startsWith('082') ||
    number.startsWith('083') ||
    number.startsWith('084') ||
    number.startsWith('085') ||
    number.startsWith('088');
