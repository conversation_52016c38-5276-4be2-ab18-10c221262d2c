<div class="vnpt flex flex-row justify-content-between align-items-center bg-white p-2 border-round">
    <div class="">
        <div class="text-xl font-bold mb-1">{{ tranService.translate("global.menu.listdevice") }}</div>
        <p-breadcrumb class="max-w-full col-7" [model]="items" [home]="home"></p-breadcrumb>
    </div>
    <div class="col-5 flex flex-row justify-content-end align-items-center">
        <p-button [label]="tranService.translate('global.button.add')" (click)="navigateToCreateDevice()"
                  styleClass="p-button-info mr-2" *ngIf="checkAuthen([CONSTANTS.PERMISSIONS.DEVICE.CREATE])"></p-button>
    </div>
</div>

<form [formGroup]="formSearch" (ngSubmit)="onSubmitSearch()" class="pb-2 pt-3 vnpt-field-set">
    <p-panel [toggleable]="true" [header]="tranService.translate('global.text.filter')">
        <div class="grid">
            <!-- name -->
            <div class="col-3">
                <span class="p-float-label">
                    <input pInputText
                           class="w-full"
                           pInputText id="name"
                           [(ngModel)]="searchInfo.deviceName"
                           formControlName="deviceName"
                           [pKeyFilter]="vietnamesePattern"
                    />
                    <label htmlFor="name">{{ tranService.translate("device.label.name") }}</label>
                </span>
            </div>
            <!-- loai thiet bi -->
            <div class="col-3">
                <span class="p-float-label">
                    <label for="type">{{ tranService.translate("device.label.type") }}</label>
                    <span class="relative">
                    <vnpt-select
                        [control]="controlComboSelectType"
                        class="w-full"
                        [(value)]="searchInfo.deviceType"
                        [placeholder]="tranService.translate('device.label.type')"
                        objectKey="deviceType"
                        paramKey="typeName"
                        keyReturn="typeCode"
                        displayPattern="${typeName}"
                        typeValue="primitive"
                        [isMultiChoice]="false"
                        (onchange)="onSelectedType()"
                        [paramDefault]="paramSearchType"
                        [pKeyFilter]="vietnamesePattern"
                    ></vnpt-select>
                </span>
                </span>
            </div>

            <div class="col-3">
                <span class="p-float-label">
                    <label for="model">{{ tranService.translate("device.label.model") }}</label>
                    <span class="relative">
                    <vnpt-select
                        [control]="controlComboSelectModel"
                        class="w-full"
                        [(value)]="searchInfo.model"
                        [placeholder]="tranService.translate('device.label.model')"
                        objectKey="deviceModel"
                        paramKey="modelCode"
                        keyReturn="modelCode"
                        displayPattern="${modelCode}"
                        typeValue="primitive"
                        [isMultiChoice]="false"
                        (onSelectItem)="onSelectedModel()"
                        (onClear)="onSelectedModel()"
                        [paramDefault]="paramSearchModel"
                        [pKeyFilter]="vietnamesePattern"
                    ></vnpt-select>
                </span>
                </span>
            </div>
            <!-- imei -->
            <div class="col-3">
                <span class="p-float-label">
                    <input class="w-full"
                           pInputText id="imei"
                           [(ngModel)]="searchInfo.imei"
                           formControlName="imei"
                           [pKeyFilter]="normalPattern"
                    />
                    <label htmlFor="imei">{{ tranService.translate("device.label.imei") }}</label>
                </span>
            </div>
<!--            <div class="col-3">-->
<!--                <span class="p-float-label">-->
<!--                    <input class="w-full"-->
<!--                           pInputText id="serial"-->
<!--                           [(ngModel)]="searchInfo.serialNumber"-->
<!--                           formControlName="serialNumber"-->
<!--                           [pKeyFilter]="vietnamesePattern"-->
<!--                    />-->
<!--                    <label htmlFor="serial">{{ tranService.translate("device.label.serial") }}</label>-->
<!--                </span>-->
<!--            </div>-->
            <div class="col-3">
                <span class="p-float-label">
                    <input class="w-full"
                           pInputText id="msisdn"
                           [(ngModel)]="searchInfo.msisdn"
                           formControlName="msisdn"
                           pKeyFilter="num"
                    />
                    <label htmlFor="msisdn">{{ tranService.translate("device.label.msisdn") }}</label>
                </span>
            </div>
            <div class="col-3" *ngIf="userType == CONSTANTS.USER_TYPE.ADMIN">
                <span class="p-float-label">
                    <vnpt-select
                        [control]="controlComboSelectBusiness"
                        class="w-full"
                        [(value)]="searchInfo.enterpriseUserId"
                        [placeholder]="tranService.translate('device.label.businessName')"
                        objectKey="account"
                        paramKey="name"
                        keyReturn="id"
                        displayPattern="${name}"
                        typeValue="primitive"
                        [isMultiChoice]="false"
                        [paramDefault]="paramSearchBusiness"
                        (onClear)="onSelectedBusiness()"
                        (onSelectItem)="onSelectedBusiness()"
                        [pKeyFilter]="vietnamesePattern"
                    ></vnpt-select>
                </span>
            </div>
            <div class="col-3" *ngIf="userType == CONSTANTS.USER_TYPE.ADMIN || userType == CONSTANTS.USER_TYPE.BUSINESS">
                <span class="p-float-label">
                    <vnpt-select
                        [control]="controlComboSelectIndividual"
                        class="w-full"
                        [(value)]="searchInfo.customerUserId"
                        [placeholder]="tranService.translate('device.label.individualName')"
                        objectKey="account"
                        paramKey="name"
                        keyReturn="id"
                        displayPattern="${name}"
                        typeValue="primitive"
                        [isMultiChoice]="false"
                        [paramDefault]="paramSearchIndividual"
                        (onClear)="onSelectedIndividual()"
                        (onchange)="onSelectedIndividual()"
                        [pKeyFilter]="vietnamesePattern"
                    ></vnpt-select>
                </span>
            </div>
            <div class="col-3">
                <span class="p-float-label">
                    <p-dropdown styleClass="w-full" [showClear]="true"
                                id="type" [autoDisplayFirst]="false"
                                [(ngModel)]="searchInfo.connectionStatus"
                                formControlName="connectionStatus"
                                [options]="listStatus"
                                optionLabel="name"
                                optionValue="value"
                    ></p-dropdown>
                    <label for="type">{{ tranService.translate("device.label.statusConnect") }}</label>
                </span>
            </div>
            <div class="col-3 pb-0">
                <p-button icon="pi pi-search"
                          styleClass="p-button-rounded p-button-secondary p-button-text button-search"
                          type="submit"
                ></p-button>
            </div>
        </div>
    </p-panel>
</form>

<table-vnpt
    [fieldId]="'id'"
    [(selectItems)]="selectItems"
    [columns]="columns"
    [dataSet]="dataSet"
    [options]="optionTable"
    [loadData]="search.bind(this)"
    [pageNumber]="pageNumber"
    [pageSize]="pageSize"
    [sort]="sort"
    [params]="searchInfo"
    [labelTable]="this.tranService.translate('global.menu.listdevice')"
></table-vnpt>

<p-dialog [header]="tranService.translate('device.label.sendCommmad')" [(visible)]="isShowSendCommand"
          [modal]="true" [style]="{ width: '500px', height: 'auto'  }" [draggable]="false" [resizable]="true" (onHide)="closeSendCommand()"
          styleClass="mt-1">
    <div class="col flex flex-row justify-content-between align-items-center pb-3">
        <label class="col-fixed" for="inputCommandId" style="width:180px">{{ tranService.translate("device.label.optionCommand") }}<span
            class="text-red-500">*</span></label>
        <div style="width: calc(100% - 180px)">
            <p-dropdown styleClass="w-full"
                        id="inputCommandId" [autoDisplayFirst]="false"
                        [(ngModel)]="command.commandId"
                        [ngModelOptions]="{standalone: true}"
                        [options]="listOptionCommand"
                        optionLabel="commandName"
                        optionValue="id"
                        [appendTo]="'body'"
                        [placeholder]="tranService.translate('device.input.optionCommand')"
                        (onChange)="onSelectTypeCommand()"
            ></p-dropdown>
        </div>
    </div>
    <form [formGroup]="formData">
    <div *ngFor="let input of inputSchema" class="col flex flex-row justify-content-between align-items-center pb-3">
        <label class="col-fixed" style="width:180px">
            {{ utilService.getLabel(input) }}
            <span class="text-red-500">*</span>
        </label>

        <div style="width: calc(100% - 180px)">
    <span class="p-float-label" *ngIf="input.type == CONSTANTS.COMMAND_VAR_TYPE.NUMBER || input.type == CONSTANTS.COMMAND_VAR_TYPE.STRING">
      <input
          class="w-full"
          pInputText
          [(ngModel)]="formData[input.key]"
          [id]="input.key"
          [pKeyFilter]="input.type === CONSTANTS.COMMAND_VAR_TYPE.NUMBER ? 'num' : normalPattern"
          [formControlName]="input.key"
          required
      />
      <label [for]="input.key">
        {{ tranService.translate(input.key) }}
      </label>
    </span>
    <p-checkbox [(ngModel)]="formData[input.key]" [formControlName]="input.key" [binary]="true" *ngIf="input.type == CONSTANTS.COMMAND_VAR_TYPE.CHECKBOX"/>
            <div class="field grid flex flex-row flex-nowrap">
                <div class="ml-3">
                    <small class="text-red-500 block"
                           *ngIf="formData?.controls[input.key]?.dirty &&
                           formData.controls[input.key]?.errors?.required">{{ tranService.translate("global.message.required") }}</small>
                </div>
            </div>
        </div>
    </div>
    </form>
    <div class="flex flex-row justify-content-center align-items-center">
        <p-button styleClass="mr-2 p-button-secondary" [label]="tranService.translate('global.button.cancel')"
                  (onClick)="isShowSendCommand = false"></p-button>
        <p-button styleClass="p-button-info" [label]="tranService.translate('device.button.sendCommand')"
                  [disabled]="disableSendCommand()"
                  (onClick)="sendCommand()"></p-button>
    </div>
</p-dialog>
