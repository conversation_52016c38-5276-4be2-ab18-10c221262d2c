import { Component, Inject, Injector, Input, OnInit } from "@angular/core";
import { FormBuilder } from "@angular/forms";
import { ComponentBase } from "src/app/component.base";
import { AccountService } from "src/app/service/account/AccountService";
import { CONSTANTS } from "src/app/service/comon/constants";
import { ObservableService } from "src/app/service/comon/observable.service";

@Component({
    selector: "personal-data-protection-policy-form",
    templateUrl: './app.personal.data.protection.policy.form.component.html'
})
export class PersonalDataProtectionFormComponent extends ComponentBase implements OnInit{
    @Input() goToPagePolicy: Function;

    constructor(
        private formBuilder: FormBuilder,
        private accountService: AccountService,
        private injector: Injector) {
            super(injector);
    }

    agreePolicy: boolean = false;
    userAgent: string;
    os: string;
    device: string;
    ngOnInit(): void {
        this.userAgent = navigator.userAgent;
        this.os = this.getOS(this.userAgent);
        this.device = this.getDevice(this.userAgent);
    }
    getOS(userAgent: string): string {
        if (userAgent.indexOf('Win') !== -1) return 'Windows';
        if (userAgent.indexOf('Mac') !== -1) return 'MacOS';
        if (userAgent.indexOf('Linux') !== -1) return 'Linux';
        if (userAgent.indexOf('Android') !== -1) return 'Android';
        if (userAgent.indexOf('like Mac') !== -1) return 'iOS';
        return 'Unknown';
    }

    getDevice(userAgent: string): string {
        if (userAgent.match(/mobile/i)) return 'Mobile';
        if (userAgent.match(/tablet/i)) return 'Tablet';
        if (userAgent.match(/iPad|PlayBook|Silk/i)) return 'Tablet';
        if (userAgent.match(/iPhone|iPod/i)) return 'Mobile';
        if (userAgent.match(/Android/i) && !userAgent.match(/Mobile/i)) return 'Tablet';
        if (userAgent.match(/Macintosh|Windows/i)) return 'Computer';
        return 'Unknown';
    }

    agreePersonalDataProtectionPolicy(){
        let me = this;
        this.messageCommonService.onload();
        let body = {
            os: me.os,
            device: me.device,
        }
        this.accountService.agreePolicy(CONSTANTS.POLICY.PERSONAL_DATA_PROTECTION_POLICY, body, (policy) => {
            let check = false;
            for(let i = 0;i<me.sessionService.confirmPolicyHistory.length;i++){
                if(me.sessionService.confirmPolicyHistory[i].policyId == CONSTANTS.POLICY.PERSONAL_DATA_PROTECTION_POLICY){
                    me.sessionService.confirmPolicyHistory[i] = policy;
                    check = true;
                }
            }
            if(!check){
                me.sessionService.confirmPolicyHistory.push(policy);
            }
            me.sessionService.setData("confirmPolicyHistory",JSON.stringify(me.sessionService.confirmPolicyHistory));
            me.observableService.next(CONSTANTS.OBSERVABLE.KEY_LOAD_CONFIRM_POLICY_HISTORY, {});
            me.agreePolicy = false;
            window.location.href = '/#/sims';
        }, null, ()=>{
            me.messageCommonService.offload();
        })
    }

    goToPageContent(){
        if(this.goToPagePolicy){
            this.goToPagePolicy();
        }
    }
}
