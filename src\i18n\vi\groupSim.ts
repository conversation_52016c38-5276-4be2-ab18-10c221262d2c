export default {
    label:{
        groupKey:"Mã nhóm",
        groupName: "Tên nhóm",
        customer: "<PERSON>h<PERSON><PERSON> hàng",
        contractCode: "<PERSON>ã hợp đồng",
        description: "<PERSON><PERSON> tả",
        time:"<PERSON>h<PERSON><PERSON> gian",
        action:"<PERSON><PERSON> tác",
        buttonAddSim:"<PERSON><PERSON> thuê bao vào nhóm",
        buttonAdd:"Tạo mới",
        buttonSave:"<PERSON><PERSON><PERSON>",
        buttonEdit:"Sửa",
        buttonDelete:"<PERSON>o<PERSON> thuê bao",
        buttonCancel:"Huỷ",
        buttonPlan:"G<PERSON><PERSON> cước",
        buttonPlanRegister:"<PERSON><PERSON><PERSON> ký gói cước",
        buttonPlanChange:"Đổi gói cước",
        buttonPlanCancel:"Huỷ gói cước",
        confirmDelete:"Xác nhận xoá",
        deleteTextGroup:"Bạn có chắc chắn muốn xoá nhóm thuê bao này không?",
        deleteTextSim:"<PERSON><PERSON><PERSON> có chắc chắn muốn xóa thuê bao khỏi  nhóm thuê bao này không?",
        groupScope: "Phạm vi nhóm",
        addByFile:"Thêm bằng file",
        addPhoneByFile: "Thêm SĐT vào nhóm bằng file"
    },
    placeHolder:{
        customer:"Chọn khách hàng",
        contractCode:"Chọn Mã Hợp đồng",
        groupKey:"Nhập mã nhóm",
        groupName:"Nhập tên nhóm",
        description:"Nhập mô tả",
        addSim:"Chọn số thuê bao"
    },
    error:{
        requiredError:"Trường này là bắt buộc",
        lengthError_255:"Trường này không được vượt quá 255 ký tự",
        lengthError_16:"Trường này không được vượt quá 16 ký tự",
        existedError:"Đã tồn tại nhóm thuê bao này",
        characterError_name:"Sai định dạng. Chỉ cho phép (a-z, A-Z, 0-9, . - _, dấu cách, tiếng Viết)",
        characterError_code:"Sai định dạng. Chỉ cho phép (a-z, A-Z, 0-9, - _)"
    },
    breadCrumb:{
        group:"Nhóm thuê bao",
        detail:"Chi tiết nhóm thuê bao",
        create:"Tạo Nhóm thuê bao",
        update:"Sửa Nhóm thuê bao"
    },
    detail:{
        subNumber:"Số thuê bao",
        simNumber:"Số thuê bao",
        status:"Trạng thái",
        planName:"Tên gói cước",
    },
    scope: {
        admin: "Nhóm gồm khách hàng toàn quốc",
        province: "Nhóm gồm khách hàng một tỉnh",
        customer: "Nhóm gồm một khách hàng"
    }
}
