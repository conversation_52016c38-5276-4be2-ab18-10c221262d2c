import {Inject, Injectable} from "@angular/core";
import {HttpService} from "../comon/http.service";

@Injectable()
export class TrafficWalletService {
    private prefixApi: string;
    constructor(@Inject(HttpService) private httpService:HttpService) {
        this.prefixApi = "/wallet";
    }

    //API lấy danh sách combobox cho cảnh báo
    public searchPakageCode(params?:{[key: string]: string}, callback?:Function, errorCallBack?:Function, finallyCallback?:Function){
        this.httpService.get(`${this.prefixApi}/getAll/packCode`, {}, params, callback, errorCallBack, finallyCallback);
    }
    //API check tồn tại cho cảnh báo
    public checkExistedCode(params?:{[key: string]: string}, callback?:Function, errorCallBack?:Function, finallyCallback?:Function){
        this.httpService.get(`${this.prefixApi}/checkExits/packCode`, {}, params, callback, errorCallBack, finallyCallback)
    }

    public searchWallet(body:{[key:string]:any}, callback?:Function, errorCallBack?:Function, finallyCallback?:Function){
        this.httpService.post(`${this.prefixApi}/search`,{}, body,{},callback, errorCallBack, finallyCallback);
    }

    public accuracyWallet(body,callback?: Function, errorCallback?:Function, finallyCallback?: Function){
        this.httpService.post(`${this.prefixApi}/accuracy`, {},body,{}, callback, errorCallback, finallyCallback);
    }

    public getById(body, callback?: Function, errorCallback?:Function, finallyCallback?: Function){
        this.httpService.post(`${this.prefixApi}/detail/bss`,{},body, {}, callback, errorCallback, finallyCallback);
    }

    public getWalletCbb(callback?: Function, errorCallback?:Function, finallyCallback?: Function){
        this.httpService.get(`${this.prefixApi}/combobox/wallet`, {}, {}, callback, errorCallback, finallyCallback);
    }

    public getWalletCbbShare(callback?: Function, errorCallback?:Function, finallyCallback?: Function){
        this.httpService.get(`${this.prefixApi}/combobox/wallet-share`, {}, {}, callback, errorCallback, finallyCallback);
    }

    public searchActivityHistory(params: {
        [key: string]: any
    }, callback?: Function, errorCallBack?: Function, finallyCallback?: Function) {
        this.httpService.get(`${this.prefixApi}/history/list`, {}, params, callback, errorCallBack, finallyCallback);
    }

    public getPackageCbb(params:{[key: string]: string}, callback?:Function, errorCallBack?:Function, finallyCallback?:Function){
        this.httpService.get(`${this.prefixApi}/combobox/package-name`, {}, params, callback, errorCallBack, finallyCallback);
    }
    public searchDetailShare(body:{[key:string]:any}, callback?:Function, errorCallBack?:Function, finallyCallback?:Function){
        this.httpService.post(`${this.prefixApi}/share/list-detail-shared`,{}, body,{},callback, errorCallBack, finallyCallback);
    }

    public getPackageCbbForUser(msisdn: string, callback?:Function, errorCallBack?:Function, finallyCallback?:Function){
        this.httpService.get(`${this.prefixApi}/combobox/share-detail`, {}, {msisdn: msisdn}, callback, errorCallBack, finallyCallback);
    }

    public checkParticipant(body,callback?: Function, errorCallback?:Function, finallyCallback?: Function){
        this.httpService.post(`${this.prefixApi}/check-participant`, {},body,{}, callback, errorCallback, finallyCallback);
    }

    //api lấy danh sách ví chưa tạo cảnh báo chạm ngưỡng
    public search(params:{[key: string]:string},callback?:Function){
        this.httpService.get(this.prefixApi+"/walletToAlert",{}, params,callback)
    }
    public registerWithoutOTP(body, callback?: Function, errorCallback?:Function, finallyCallback?: Function){
        this.httpService.post(`${this.prefixApi}/register-without-otp`,{},body, {}, callback, errorCallback, finallyCallback);
    }
    public unRegisterWithoutOTP(body, callback?: Function, errorCallback?:Function, finallyCallback?: Function){
        this.httpService.post(`${this.prefixApi}/unregister-without-otp`,{},body, {}, callback, errorCallback, finallyCallback);
    }

    public searchAutoGroupWallet(params?:{[key: string]: string}, callback?:Function, errorCallBack?:Function, finallyCallback?:Function){
        this.httpService.get(`${this.prefixApi}/search-auto-group-wallet`, {}, params, callback, errorCallBack, finallyCallback);
    }
    public getListShareWallet(params?:{[key: string]: string}, callback?:Function, errorCallBack?:Function, finallyCallback?:Function){
        this.httpService.get(`${this.prefixApi}/get-list-share-wallet`, {}, params, callback, errorCallBack, finallyCallback);
    }

    public getListPhoneShare(id: number, callback?: Function, errorCallBack?: Function, finallyCallback?: Function){
        this.httpService.get(`${this.prefixApi}/get-list-detail-share/${id}`, {}, {}, callback, errorCallBack, finallyCallback);
    }
}
