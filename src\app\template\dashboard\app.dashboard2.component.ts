import { AfterContentChecked, Component, ElementRef, Injector, OnInit } from "@angular/core";
import { MenuItem } from "primeng/api";
import { ComponentBase } from "src/app/component.base";
import { ConfigChartService } from "src/app/service/charts/ConfigChartService";
import { CONSTANTS } from "src/app/service/comon/constants";
import { DynamicChartController } from "../common-module/charts/dynamic.chart.component";
import { FormBuilder } from "@angular/forms";
import { ComboLazyControl } from "../common-module/combobox-lazyload/combobox.lazyload";

interface PositionConfig {
    // config drag position absolute
    top?: number;
    left?: number;
    widthChart?: number;
    heightChart?: number;
    heightBox?: number;
    zIndex?: number;
    offsetX?: number;
    offsetY?: number;
    pageX?: number;
    pageY?: number;
    //config drag row
    indexBudget?: number,
    indexInBudget?: number,
    width?: number,
    height?: number,
    marginLeft?: number,
    marginRight?: number,
    align?: string
}

interface ChartPolicy{
    id: number | null;
    idChart: any | null;
    status: number | null;
    configLevel: string | null;
    configPosition: string | null;

    chartConfig?: any;
    lastFilter?: any;
    configLevelArray?: {below?: number, above?: number}[];
    configPositionObject?: PositionConfig
}

@Component({
    selector: "app-dashboard",
    templateUrl: "./app.dashboard2.component.html",
})
export class AppDashboard2Component extends ComponentBase implements OnInit,  AfterContentChecked {
    items: MenuItem[];
    home: MenuItem;
    charts: Array<any> = [];
    displayCharts: Array<any> = [];
    chartShows: Array<any> = [];
    modeView: any = CONSTANTS.MODE_VIEW.DETAIL;
    objectModeView = CONSTANTS.MODE_VIEW;
    heightBoxWrapper: number = 1000;
    widthBoxWrapper: number = 1000;
    minX: number = 12;
    minY: number = 12;

    listChartPolicy: Array<ChartPolicy> = [];
    listDynamicChartController: {[key: number|string]:DynamicChartController} = {};

    chartPolicyDragged: ChartPolicy;
    typeDrag: string;
    chartPolicyForcus: ChartPolicy;
    //settingConfig
    isShowEditSetting: boolean = false;

    isShowEditThresholdSetting: boolean = false;
    sliderThreshold: [number, number];
    chartComboboxController: ComboLazyControl;

    constructor(injector: Injector, private configChartService: ConfigChartService, private formBuilder: FormBuilder,
        private eRef: ElementRef) {
        super(injector);
    }

    ngOnInit(): void {
        let me = this;
        this.home = {icon: 'pi pi-home', routerLink: '/'};
        this.items = [{label: 'Dashboard'}];
        this.chartShows = [];
        this.getListChart();
        this.calculateBorderline();
        this.chartComboboxController = new ComboLazyControl();
        this.sliderThreshold = [80,90];
    }

    ngAfterContentChecked(): void {
        this.calculateSize();
    }

    getListChart(){
        let me = this;
        me.messageCommonService.onload();
        this.configChartService.getAll((response)=> {
            me.charts = response;
            me.displayCharts = [...me.charts];
            //get list config
            me.configChartService.getListConfig(me.sessionService.userInfo.id, (response)=> {
                me.listChartPolicy = response.map(el => {
                    return {
                        configLevel: el.threshold,
                        configPosition: el.positionConfig,
                        id: el.id,
                        idChart: el.chartId,
                        status: el.status,
                        lastFilter: el.lastFilter
                    }
                });
                me.fillInfoForChartPolicy();
                me.prepageDataShow();
                me.messageCommonService.offload();
            }, null, (typeFinish) => {
                if(typeFinish == "error"){
                    me.messageCommonService.offload();
                }
            })
        },null, (typeFinally) => {
            if(typeFinally=="error"){
                me.messageCommonService.offload();
            }
        })
    }

    fillInfoForChartPolicy(){
        let me = this;
        this.charts.forEach(chartConfig => {
            let index = me.listChartPolicy.findIndex(el => el.idChart == chartConfig.id);
            if(index != null && index != undefined && index >= 0){
                me.listChartPolicy[index].chartConfig = chartConfig;
            }else{
                me.listChartPolicy.push({
                    id: null,
                    idChart: chartConfig.id,
                    configLevel: null,
                    configPosition: null,
                    status: 1,
                    chartConfig
                })
            }
        })
        this.listChartPolicy = this.listChartPolicy.filter(el => el.chartConfig != null);
        let maxLeft = 12;
        this.listChartPolicy.forEach((chartPolicy, index) => {
            if(chartPolicy.configLevel){
                chartPolicy.configLevelArray = JSON.parse(chartPolicy.configLevel);
            }else{
                chartPolicy.configLevelArray = [{below: 0, above: null}];
            }
            if(chartPolicy.configPosition){
                chartPolicy.configPositionObject = JSON.parse(chartPolicy.configPosition);
            }else{
                chartPolicy.configPositionObject = me.createPositionDefault();
            }
            if (chartPolicy.lastFilter) {
                chartPolicy.lastFilter = JSON.parse(chartPolicy.lastFilter);
            } else {
                chartPolicy.lastFilter = null;
            }
            chartPolicy.configPositionObject.zIndex = index + 1;
            if(!chartPolicy.configPositionObject.widthChart || chartPolicy.configPositionObject.widthChart < 0){
                chartPolicy.configPositionObject.widthChart = 400;
            }
            if(!chartPolicy.configPositionObject.heightChart || chartPolicy.configPositionObject.heightChart < 0){
                chartPolicy.configPositionObject.heightChart = 300;
            }
            if(chartPolicy.configPositionObject.left + chartPolicy.configPositionObject.widthChart > maxLeft && chartPolicy.configPositionObject.left >= 12){
                maxLeft = chartPolicy.configPositionObject.left + chartPolicy.configPositionObject.widthChart;
            }
        });
        this.listChartPolicy.forEach((chartPolicy, index) => {
           if(chartPolicy.configPositionObject.left == 0){
                chartPolicy.configPositionObject.left = maxLeft + 40;
                maxLeft += 80 + chartPolicy.configPositionObject.widthChart;
           }
        });
    }

    createPositionDefault():PositionConfig{
        return {
            top: 0,
            left: 0,
            widthChart: 300,
            heightChart: 400,
            align: 'justify-content-start', //justify-content-start justify-content-end justify-content-center justify-content-between
            height: 300,
            width: 400,
            indexBudget: 0,
            indexInBudget: 0,
            marginLeft: 0,
            marginRight: 0
        }
    }

    prepageDataShow(){
        let me = this;
        this.chartShows = [];
        this.listChartPolicy.forEach(chartPolicy => {
            if(chartPolicy.chartConfig){
                me.listDynamicChartController[chartPolicy.chartConfig.id] = new DynamicChartController();
            }
            if(chartPolicy.status == 1){
                me.chartShows.push(chartPolicy.chartConfig);
            }
        })
        setTimeout(() => {
            Object.keys(me.listDynamicChartController).forEach(chartId => {
                me.listDynamicChartController[chartId].reload(false, true);
            })
        })
    }

    changeChartShow(event){
        let me = this;
        let chartIdShows = (this.chartShows).map(el => el.id);
        this.listChartPolicy.forEach(chartPolicy => {
            if(chartIdShows.includes(chartPolicy.idChart)){
                chartPolicy.status = 1;
                setTimeout(function(){
                    me.listDynamicChartController[chartPolicy.idChart].reloadParam();
                })
            }else{
                chartPolicy.status = 0;
            }
        })
    }

    saveConfig(){
        let me = this;
        me.messageCommonService.onload();
        let dataSend = [];
        this.listChartPolicy.forEach(chartPolicy => {
            chartPolicy.configLevel = JSON.stringify(chartPolicy.configLevelArray);
            delete chartPolicy.configPositionObject.heightBox;
            delete chartPolicy.configPositionObject.zIndex;
            delete chartPolicy.configPositionObject.offsetX;
            delete chartPolicy.configPositionObject.offsetY;
            delete chartPolicy.configPositionObject.pageX;
            delete chartPolicy.configPositionObject.pageY;
            chartPolicy.configPosition = JSON.stringify(chartPolicy.configPositionObject);
            dataSend.push({
                id: chartPolicy.id,
                chartId: chartPolicy.idChart,
                userId: me.sessionService.userInfo.id,
                status: chartPolicy.status,
                threshold: chartPolicy.configLevel,
                positionConfig: chartPolicy.configPosition,
                lastFilter: typeof chartPolicy.lastFilter == 'string' ? chartPolicy.lastFilter : JSON.stringify(chartPolicy.lastFilter).toString(),
            });
        })
        this.configChartService.saveConfigDashboard(dataSend, (response)=> {
            me.messageCommonService.success(me.tranService.translate('global.message.success'))
            me.modeView = CONSTANTS.MODE_VIEW.DETAIL;
            me.messageCommonService.offload();
            window.location.reload();
        }, null, () => me.messageCommonService.offload());
        // console.log(this.listChartPolicy)
    }

    openEdit(){
        this.modeView = CONSTANTS.MODE_VIEW.UPDATE;
    }

    handleOpenSetting(chartId){
        let me = this;
        this.chartPolicyForcus = this.listChartPolicy[this.listChartPolicy.findIndex(el => el.idChart == chartId)];
        let subTypes = JSON.parse(this.chartPolicyForcus.chartConfig.subType)
        let threshold = JSON.parse(this.chartPolicyForcus.configLevel)
        if(threshold?.length > 0 && subTypes.includes(CONSTANTS.CHART_SUB_TYPE.SLIDER_THRESHOLD) && threshold[0]?.below != null && threshold[0]?.above != null){
            me.sliderThreshold[0] = threshold[0].below;
            me.sliderThreshold[1] = threshold[0].above;
        }
        setTimeout(function(){
            if (subTypes.includes(CONSTANTS.CHART_SUB_TYPE.SLIDER_THRESHOLD)){
                    me.isShowEditThresholdSetting = true;
            } else {
                me.isShowEditSetting = true;
            }
        })
    }

    createSliderThreshold() {
        this.chartPolicyForcus.configLevelArray = [{below: this.sliderThreshold[0], above: this.sliderThreshold[1]}]
        this.isShowEditThresholdSetting = false
        this.chartPolicyForcus = null;
    }

    minusSetting(){
        this.chartPolicyForcus.configLevelArray.pop();
        if(this.chartPolicyForcus.configLevelArray.length == 1){
            this.chartPolicyForcus.configLevelArray[0].above = null;
        }
    }

    createSetting(){
        let lastThreshold = this.chartPolicyForcus.configLevelArray[this.chartPolicyForcus.configLevelArray.length - 1];
        if(lastThreshold!=null && lastThreshold.above > 0 && lastThreshold.above > lastThreshold.below){
            this.chartPolicyForcus.configLevelArray.push({
                below: this.chartPolicyForcus.configLevelArray[this.chartPolicyForcus.configLevelArray.length - 1].above
            });
        }
    }

    closeDialog(){
        this.chartPolicyForcus = null;
    }

    onFilterChartname(event){
        let me = this;
        let filter = event.filter || '';
        if(filter.length > 0){
            me.displayCharts = me.charts.filter(chart => me.utilService.convertTextViToEnUpperCase(chart.name).indexOf(me.utilService.convertTextViToEnUpperCase(filter)) >= 0);
            console.log(me.displayCharts);
        }
    }

    //drag drop
    onResize(event){
        this.calculateBorderline();
    }
    calculateSize(){
        let me = this;
        let maxHeight = 100;
        let maxWidth = 100;
        this.listChartPolicy.forEach(chartPolicy => {
            let el: ElementRef = me.eRef.nativeElement.querySelector(`#chart${chartPolicy.idChart}`);
            if(el){
                if(el["offsetTop"] + el["offsetHeight"] > maxHeight){
                    maxHeight = el["offsetTop"] + el["offsetHeight"]
                }
                if(el["offsetLeft"] + el["offsetWidth"] > maxWidth){
                    maxWidth = el["offsetLeft"] + el["offsetWidth"]
                }
            }
        })
        if(this.modeView == CONSTANTS.MODE_VIEW.UPDATE){
            maxHeight += 50;
            maxWidth += 50;
        }
        if(!this.chartPolicyDragged){
            this.heightBoxWrapper = maxHeight;
            this.widthBoxWrapper = maxWidth;
        }else{
            if(maxHeight > me.heightBoxWrapper){
                this.heightBoxWrapper = maxHeight+50;
            }
            if(maxWidth > me.widthBoxWrapper){
                this.widthBoxWrapper = maxWidth+50;
            }
        }
    }
    calculateBorderline(){
        let boxWrapper:Element = this.eRef.nativeElement.querySelector("#boxWrapper");
        this.minX = boxWrapper.getBoundingClientRect().left;
        this.minY = boxWrapper.getBoundingClientRect().top;
    }

    drop(event){
        if(this.modeView != CONSTANTS.MODE_VIEW.DETAIL && this.chartPolicyDragged){
            this.eRef.nativeElement.querySelector(`div#chart${this.chartPolicyDragged.idChart}`).style.opacity = 1;
        };
        this.typeDrag = null;
        this.chartPolicyDragged = null;
    }

    dragEnd(event){
        if(this.modeView != CONSTANTS.MODE_VIEW.DETAIL && this.chartPolicyDragged){
            // this.eRef.nativeElement.querySelector(`div#chart${this.chartPolicyDragged.idChart}`).style.opacity = 1;
        };
    }

    dragStart(event, chartPolicy){
        if(!this.typeDrag){
            if(this.modeView != CONSTANTS.MODE_VIEW.DETAIL){
                this.typeDrag = null;
                this.chartPolicyDragged = chartPolicy;
                this.chartPolicyDragged.configPositionObject.offsetX = event.offsetX;
                this.chartPolicyDragged.configPositionObject.offsetY = event.offsetY;
                this.chartPolicyDragged.configPositionObject.pageX = event.pageX;
                this.chartPolicyDragged.configPositionObject.pageY = event.pageY;
                // this.eRef.nativeElement.querySelector(`div#chart${this.chartPolicyDragged.idChart}`).style.backgroundColor = 'transparent';
            }
        }
    }

    dragXStart(event, chartPolicy){
        if(!this.typeDrag){
            if(this.modeView != CONSTANTS.MODE_VIEW.DETAIL){
                this.chartPolicyDragged = chartPolicy;
                let el: Element = this.eRef.nativeElement.querySelector(`#chart${this.chartPolicyDragged.idChart}`);
                this.chartPolicyDragged.configPositionObject.offsetX = event.offsetX;
                this.chartPolicyDragged.configPositionObject.offsetY = event.offsetY;
                this.chartPolicyDragged.configPositionObject.pageX = el.getBoundingClientRect().left;
                this.chartPolicyDragged.configPositionObject.pageY = el.getBoundingClientRect().top;
                this.typeDrag = 'scale-x';
            }
        }
    }

    dragYStart(event, chartPolicy){
        if(!this.typeDrag){
            if(this.modeView != CONSTANTS.MODE_VIEW.DETAIL){
                this.chartPolicyDragged = chartPolicy;
                let el: Element = this.eRef.nativeElement.querySelector(`#chart${this.chartPolicyDragged.idChart}`);
                this.chartPolicyDragged.configPositionObject.offsetX = event.offsetX;
                this.chartPolicyDragged.configPositionObject.offsetY = event.offsetY;
                this.chartPolicyDragged.configPositionObject.pageX = el.getBoundingClientRect().left;
                this.chartPolicyDragged.configPositionObject.pageY = el.getBoundingClientRect().top;
                this.chartPolicyDragged.configPositionObject.heightBox = el.getBoundingClientRect().height;
                this.typeDrag = 'scale-y';
            }
        }
    }

    dragXYStart(event, chartPolicy){
        if(!this.typeDrag){
            if(this.modeView != CONSTANTS.MODE_VIEW.DETAIL){
                this.chartPolicyDragged = chartPolicy;
                let el: Element = this.eRef.nativeElement.querySelector(`#chart${this.chartPolicyDragged.idChart}`);
                this.chartPolicyDragged.configPositionObject.offsetX = event.offsetX;
                this.chartPolicyDragged.configPositionObject.offsetY = event.offsetY;
                this.chartPolicyDragged.configPositionObject.pageX = el.getBoundingClientRect().left;
                this.chartPolicyDragged.configPositionObject.pageY = el.getBoundingClientRect().top;
                this.chartPolicyDragged.configPositionObject.heightBox = el.getBoundingClientRect().height;
                this.typeDrag = 'scale-xy';
            }
        }
    }

    drag(event){
        if(!this.typeDrag && this.chartPolicyDragged){
            if(!this.typeDrag){
                if(event.offsetX < 0 || event.offsetY < 0) return;
                    let x = event.pageX - this.chartPolicyDragged.configPositionObject.offsetX;
                    let y = event.pageY - this.chartPolicyDragged.configPositionObject.offsetY;
                    let top = y - this.minY;
                    let left = x - this.minX;
                    console.log(x, y, this.minX, this.minY, top, left);
                if(top >= 12 && left >= 12){
                    this.chartPolicyDragged.configPositionObject.top = top;
                    this.chartPolicyDragged.configPositionObject.left = left;
                    return;
                }else if(top >= 12){
                    this.chartPolicyDragged.configPositionObject.top = top;
                }else if(left >= 12){
                    this.chartPolicyDragged.configPositionObject.left = left;
                }
                event.preventDefault();
            }
        }
    }

    dragX(event){
        if(this.chartPolicyDragged){
            this.chartPolicyDragged.configPositionObject.widthChart = event.clientX - this.chartPolicyDragged.configPositionObject.pageX - 40;
        }
    }

    dragY(event){
        if(this.chartPolicyDragged){
            let heightBox = event.clientY - this.chartPolicyDragged.configPositionObject.pageY;
            this.chartPolicyDragged.configPositionObject.heightChart += (heightBox - this.chartPolicyDragged.configPositionObject.heightBox);
            this.chartPolicyDragged.configPositionObject.heightBox = heightBox;
            console.log(event, this.chartPolicyDragged.configPositionObject)
        }
    }

    dragXY(event){
        if(this.chartPolicyDragged){
            this.chartPolicyDragged.configPositionObject.widthChart = event.clientX - this.chartPolicyDragged.configPositionObject.pageX - 40;
            let heightBox = event.clientY - this.chartPolicyDragged.configPositionObject.pageY;
            this.chartPolicyDragged.configPositionObject.heightChart += (heightBox - this.chartPolicyDragged.configPositionObject.heightBox);
            this.chartPolicyDragged.configPositionObject.heightBox = heightBox;
        }
    }
}
