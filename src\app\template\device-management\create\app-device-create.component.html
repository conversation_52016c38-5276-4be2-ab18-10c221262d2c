<form [formGroup]="formCreateDevice" (ngSubmit)="create()">
    <div class="vnpt flex flex-row justify-content-between align-items-center bg-white p-2 border-round">
        <div class="">
            <div class="text-xl font-bold mb-1">{{ tranService.translate("global.titlepage.createDevice") }}</div>
            <p-breadcrumb class="max-w-full col-7" [model]="items" [home]="home"></p-breadcrumb>
        </div>
        <div class="col-5 flex flex-row justify-content-end align-items-center">
            <p-button styleClass="p-button-info mr-2"
                      [disabled]="disableButtonSubmit()"
                      type="submit"
                      [label]="tranService.translate('global.button.save')"
                      icon=""
                      *ngIf="checkAuthen([CONSTANTS.PERMISSIONS.DEVICE.CREATE])"></p-button>
            <p-button styleClass="p-button-secondary p-button-outlined mr-2" (click)="goBack()">
                {{ tranService.translate("global.button.cancel") }}
            </p-button>
        </div>
    </div>
    <div class="mt-1">
        <div class="flex flex-row justify-content-between mt-2 pt-2">
            <div class="flex flex-column" style="width: 49%;">
                <p-card class="flex-1" [header]="tranService.translate('device.label.info')">
                    <!-- name -->
                    <div class="w-full field grid">
                        <label htmlFor="name" class="col-fixed"
                               style="width:180px">{{ tranService.translate("device.label.name") }}<span
                            class="text-red-500">*</span></label>
                        <div class="col">
                <span class="p-float-label">
                    <input pInputText
                           class="w-full"
                           pInputText id="deviceName"
                           [(ngModel)]="deviceInfo.deviceName"
                           formControlName="deviceName"
                           [pattern]="vietnamesePattern"
                           [maxlength]="50"
                           required
                    />
                    <label htmlFor="name">{{ tranService.translate("device.input.name") }}</label>
                </span>
                            <div class="field grid flex flex-row flex-nowrap">
                                <div class="ml-3">
                                    <small class="text-red-500 block"
                                           *ngIf="formCreateDevice.controls.deviceName.dirty && formCreateDevice.controls.deviceName.errors?.required">{{ tranService.translate("global.message.required") }}</small>
                                    <small class="text-red-500 block"
                                           *ngIf="formCreateDevice.controls.deviceName.errors?.pattern">{{ tranService.translate("global.message.formatPhone") }}</small>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="w-full field grid pt-2">
                        <label htmlFor="name" class="col-fixed"
                               style="width:180px">{{ tranService.translate("device.label.type") }}<span
                            class="text-red-500">*</span></label>
                        <div class="col">
                <span class="p-float-label">
                    <span class="relative">
                    <vnpt-select
                        [control]="controlComboSelectType"
                        class="w-full"
                        [(value)]="deviceInfo.typeCode"
                        [placeholder]="tranService.translate('device.input.type')"
                        objectKey="deviceType"
                        paramKey="typeName"
                        keyReturn="typeCode"
                        displayPattern="${typeName}"
                        typeValue="primitive"
                        [isMultiChoice]="false"
                        (onchange)="onSelectedType()"
                        [paramDefault]="paramSearchType"
                        [required]="true"
                        [pattern]="vietnamesePattern"
                    ></vnpt-select>
                </span>
                </span>
                            <div class="field grid flex flex-row flex-nowrap">
                                <div class="ml-3">
                                    <small class="text-red-500 block"
                                           *ngIf="controlComboSelectType.dirty && controlComboSelectType.error?.required">{{ tranService.translate("global.message.required") }}</small>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="w-full field grid pt-2">
                        <label htmlFor="name" class="col-fixed"
                               style="width:180px">{{ tranService.translate("device.label.model") }}<span
                            class="text-red-500">*</span></label>
                        <div class="col">
                            <span class="relative">
                    <vnpt-select
                        [control]="controlComboSelectModel"
                        class="w-full"
                        [(value)]="deviceInfo.modelCode"
                        [placeholder]="tranService.translate('device.input.model')"
                        objectKey="deviceModel"
                        paramKey="modelCode"
                        keyReturn="modelCode"
                        displayPattern="${modelCode}"
                        typeValue="primitive"
                        [isMultiChoice]="false"
                        (onSelectItem)="onSelectedModel()"
                        (onClear)="onSelectedModel()"
                        [paramDefault]="paramSearchModel"
                        [required]="true"
                        [pattern]="vietnamesePattern"
                    ></vnpt-select>

                </span>
                            <div class="field grid flex flex-row flex-nowrap">
                                <div class="ml-3">
                                    <small class="text-red-500 block"
                                           *ngIf="controlComboSelectModel.dirty && controlComboSelectModel.error?.required">{{ tranService.translate("global.message.required") }}</small>
                                </div>
                            </div>
                        </div>
                    </div>
<!--                    <div class="w-full field grid pt-2">-->
<!--                        <label htmlFor="imei" class="col-fixed"-->
<!--                               style="width:180px">{{ tranService.translate("device.label.imei") }}<span-->
<!--                            class="text-red-500"></span></label>-->
<!--                        <div class="col">-->
<!--                <span class="p-float-label">-->
<!--                    <input pInputText-->
<!--                           class="w-full"-->
<!--                           pInputText id="imei"-->
<!--                           [(ngModel)]="deviceInfo.imei"-->
<!--                           formControlName="imei"-->
<!--                    />-->
<!--                    <label htmlFor="imei">{{ tranService.translate("device.input.imei") }}</label>-->
<!--                </span>-->
<!--                        </div>-->
<!--                    </div>-->
                    <div class="w-full field grid pt-2">
                        <label htmlFor="imei" class="col-fixed"
                               style="width:180px">{{ tranService.translate("device.label.imei") }}<span
                            class="text-red-500">*</span></label>
                        <div class="col">
                        <span class="p-float-label">
                            <input pInputText
                                   class="w-full"
                                   pInputText id="imei"
                                   [(ngModel)]="deviceInfo.imei"
                                   formControlName="imei"
                                   [maxlength]="50"
                                   [pattern]="normalPattern"
                                   (ngModelChange)="checkExistsImei()"
                                   [required]="true"
                            />
                            <label htmlFor="imei">{{ tranService.translate("device.input.imei") }}</label>
                        </span>
                            <div class="field grid flex flex-row flex-nowrap">
                                <div class="ml-3">
                                    <small class="text-red-500" *ngIf="formCreateDevice.controls.imei.dirty && formCreateDevice.controls.imei.errors?.required">{{tranService.translate("global.message.required")}}</small>
                                    <small class="text-red-500" *ngIf="formCreateDevice.controls.imei.errors?.pattern">{{tranService.translate("global.message.formatCode")}}</small>
                                    <small class="text-red-500" *ngIf="!formCreateDevice.controls.imei.errors?.pattern && isShowExistsImei">{{tranService.translate("global.message.exists",{type: tranService.translate("device.label.imei").toLowerCase()})}}</small>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="w-full field grid pt-2">
                        <label htmlFor="msisdn" class="col-fixed"
                               style="width:180px">{{ tranService.translate("device.label.msisdn") }}<span
                            class="text-red-500">*</span></label>
                        <div class="col">
                <span class="p-float-label">
                    <input pInputText
                           class="w-full"
                           pInputText id="msisdn"
                           [(ngModel)]="deviceInfo.msisdn"
                           formControlName="msisdn"
                           required
                           pKeyFilter="num"
                           [pattern]="msisdnPattern"
                    />
                    <label htmlFor="msisdn">{{ tranService.translate("device.input.msisdn") }}</label>
                </span>
                            <div class="field grid flex flex-row flex-nowrap">
                                <div class="ml-3">
                                    <small class="text-red-500 block"
                                           *ngIf="formCreateDevice.controls.msisdn.dirty && formCreateDevice.controls.msisdn.errors?.required">{{ tranService.translate("global.message.required") }}</small>
                                    <small class="text-red-500 block"
                                           *ngIf="formCreateDevice.controls.msisdn.errors?.pattern">{{ tranService.translate("global.message.formatPhone") }}</small>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- manufacturer -->
                    <div class="w-full field grid pt-2">
                        <label htmlFor="manufacturer" class="col-fixed"
                               style="width:180px">{{ tranService.translate("device.label.manufacturer") }}</label>
                        <div class="col">
                <span class="p-float-label">
                    <input pInputText
                           class="w-full"
                           pInputText id="manufacturer"
                           [(ngModel)]="deviceInfo.manufacture"
                           formControlName="manufacture"
                           [pattern]="vietnamesePattern"
                           [maxlength]="255"
                    />
                    <label htmlFor="manufacturer">{{ tranService.translate("device.input.manufacturer") }}</label>
                </span>
                        </div>
                    </div>
                    <div class="w-full field grid pt-2">
                        <label htmlFor="description" class="col-fixed"
                               style="width:180px">{{ tranService.translate("device.label.description") }}</label>
                        <div class="col">
                <span class="p-float-label">
                    <textarea pInputText
                              class="w-full"
                              pInputText id="description"
                              [(ngModel)]="deviceInfo.description"
                              formControlName="description"
                              [maxlength]="255"
                    ></textarea>
                    <label htmlFor="description">{{ tranService.translate("device.input.description") }}</label>
                </span>
                        </div>
                    </div>
                </p-card>

                <p-card class="flex-1" styleClass="mt-3" [header]="tranService.translate('device.label.infoMngt')"
                        *ngIf="userType == CONSTANTS.USER_TYPE.ADMIN || userType == CONSTANTS.USER_TYPE.BUSINESS">
                    <div class="w-full field grid pt-2" *ngIf="userType == CONSTANTS.USER_TYPE.ADMIN">
                        <label htmlFor="businessMngt" class="col-fixed"
                               style="width:180px">{{ tranService.translate("device.label.businessMngt") }}<span
                            class="text-red-500">*</span></label>
                        <div class="col">
                            <span class="p-float-label">
                    <vnpt-select
                        [control]="controlComboSelectBusiness"
                        class="w-full"
                        [(value)]="deviceInfo.userEnterpriseId"
                        [placeholder]="tranService.translate('device.input.businessName')"
                        objectKey="account"
                        paramKey="nameOrUsername"
                        keyReturn="id"
                        displayPattern="${name}"
                        typeValue="primitive"
                        [isMultiChoice]="false"
                        [required]="true"
                        [paramDefault]="paramSearchBusiness"
                        (onClear)="onSelectedBusiness()"
                        (onSelectItem)="onSelectedBusiness()"
                        [pattern]="vietnamesePattern"
                    ></vnpt-select>
                </span>
                            <div class="field grid flex flex-row flex-nowrap">
                                <div class="ml-3">
                                    <small class="text-red-500 block"
                                           *ngIf="controlComboSelectBusiness.dirty && controlComboSelectBusiness.error?.required">{{ tranService.translate("global.message.required") }}</small>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="w-full field grid pt-2"
                         *ngIf="userType == CONSTANTS.USER_TYPE.ADMIN || userType == CONSTANTS.USER_TYPE.BUSINESS">
                        <label htmlFor="individualMngt" class="col-fixed"
                               style="width:180px">{{ tranService.translate("device.label.individualMngt") }}<span
                            class="text-red-500"></span></label>
                        <div class="col">
                            <span class="p-float-label">
                    <vnpt-select
                        [control]="controlComboSelectIndividual"
                        class="w-full"
                        [(value)]="deviceInfo.userCustomerId"
                        [placeholder]="tranService.translate('device.label.individualName')"
                        objectKey="account"
                        paramKey="nameOrUsername"
                        keyReturn="id"
                        displayPattern="${name}"
                        typeValue="primitive"
                        [isMultiChoice]="false"
                        [required]="false"
                        [paramDefault]="paramSearchIndividual"
                        (onClear)="onSelectedIndividual()"
                        (onchange)="onSelectedIndividual()"
                        [pattern]="vietnamesePattern"
                    ></vnpt-select>
                </span>
                            <div class="field grid flex flex-row flex-nowrap">
                                <div class="ml-3">
                                    <small class="text-red-500 block"
                                           *ngIf="controlComboSelectIndividual.dirty && controlComboSelectIndividual.error?.required">{{ tranService.translate("global.message.required") }}</small>
                                </div>
                            </div>
                        </div>
                    </div>

                </p-card>
            </div>
            <div style="width: 49%">
                <p-card [header]="tranService.translate('device.label.deviceLocation')">
                    <!--                    <iframe [src]="safeUrl" class="w-full" style="border:0;" allowfullscreen="true" loading="lazy"-->
                    <!--                            height="675"-->
                    <!--                            referrerpolicy="no-referrer-when-downgrade"></iframe>-->
                    <!--                    <div class="w-full field grid pt-2">-->
                    <!--                        <label htmlFor="manufacturer" class="col-fixed"-->
                    <!--                               style="width:180px">{{ tranService.translate("device.label.location") }}</label>-->
                    <!--                        <div class="col">-->
                    <!--                <span class="p-float-label">-->
                    <!--&lt;!&ndash;                    <input pInputText&ndash;&gt;-->
                    <!--&lt;!&ndash;                           class="w-full"&ndash;&gt;-->
                    <!--&lt;!&ndash;                           [(ngModel)]="inputValue"&ndash;&gt;-->
                    <!--&lt;!&ndash;                           (input)="onInputChange(inputValue)"&ndash;&gt;-->
                    <!--&lt;!&ndash;                           (keydown.enter)="search($event)"&ndash;&gt;-->
                    <!--&lt;!&ndash;                    />&ndash;&gt;-->
                    <!--                    <p-autoComplete-->
                    <!--                        styleClass="w-full"-->
                    <!--                        [ngModelOptions]="{standalone: true}"-->
                    <!--                        [(ngModel)]="inputValue"-->
                    <!--                        [suggestions]="results"-->
                    <!--                        (click)="onSelect(inputValue)"-->
                    <!--                        (completeMethod)="onInputChange(inputValue)"-->
                    <!--                        field="name"-->
                    <!--                        inputStyle="w-full"-->
                    <!--                        (keydown.enter)="search($event)"-->
                    <!--                    />-->

                    <!--                    <label htmlFor="location">{{ tranService.translate("device.input.location") }}</label>-->
                    <!--                </span>-->
                    <!--                        </div>-->
                    <!--                    </div>-->
                    <div class="w-full field grid pt-2">
                        <label htmlFor="location" class="col-fixed"
                               style="width:180px">{{ tranService.translate("device.label.location") }}</label>
                        <div class="col">
                <span class="p-float-label">
                    <input pInputText
                           class="w-full"
                           (keydown.enter)="search($event)"
                    />
                    <label htmlFor="location">{{ tranService.translate("device.input.location") }}</label>
                </span>
                        </div>
                    </div>
                    <div id="osm-map" style="height: 566px;"></div>
                </p-card>

            </div>

        </div>

    </div>
</form>
