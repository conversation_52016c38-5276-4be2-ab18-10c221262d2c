import {AfterContentChecked, Component, Inject, Injector, OnInit} from '@angular/core';
import {TranslateService} from "../../../../service/comon/translate.service";
import {AccountService} from "../../../../service/account/AccountService";
import {MessageCommonService} from "../../../../service/comon/message-common.service";
import {FormBuilder} from "@angular/forms";
import {UtilService} from "../../../../service/comon/util.service";
import {ActivatedRoute, Router} from "@angular/router";
import {MenuItem} from "primeng/api";
import {a} from "@fullcalendar/core/internal-common";
import {ColumnInfo, OptionTable} from "../../../common-module/table/table.component";
import {CONSTANTS} from "../../../../service/comon/constants";
import { ComponentBase } from 'src/app/component.base';
import { ReportReceivingGroupService } from 'src/app/service/report-receiving-group/ReportReceivingGroup';

@Component({
  selector: 'report.group-receiving.create',
  templateUrl: './app.group-receiving.create.component.html',
})
export class ReportGroupReceivingCreateComponent extends ComponentBase implements OnInit, AfterContentChecked{
    constructor(@Inject(ReportReceivingGroupService) private reportReceivingGroupService: ReportReceivingGroupService,
                private formBuilder: FormBuilder, private injector: Injector) {
                    super(injector)
    }
    items: MenuItem[];
    home: MenuItem;
    formReceivingGroup : any;
    formMailInput : any;
    receivingGroupInfo: {
        name: string|null,
        description: string|null,
        emails: string|null,
    };
    myEmails: Array<any>|null;
    selectItems: Array<any> = [];
    columns: Array<ColumnInfo>;
    dataSet: {
        content: Array<any>,
        total: number
    };
    optionTable: OptionTable;
    email: {}
    isRGNameExisted: boolean = false;
    isRGEmailExisted = false;

    ngOnInit(): void {
        let me = this;
        this.items = [{ label: this.tranService.translate("global.menu.dynamicreportgroup")},{ label: this.tranService.translate("global.menu.reportGroupReceivingList"), routerLink:"/reports/group-report-dynamic"  }, { label: this.tranService.translate("global.button.create") }];
        this.home = { icon: 'pi pi-home', routerLink: '/' };

        this.receivingGroupInfo = {
            name: null,
            description: null,
            emails: "",
        }
        this.myEmails= []
        this.formReceivingGroup = this.formBuilder.group(this.receivingGroupInfo);
        this.formMailInput = this.formBuilder.group({email: ""});
        this.dataSet = {
            content: [],
            total: 0
        }
        this.selectItems = [];
        this.columns = [
            {
                name: this.tranService.translate("report.receiving.emails"),
                key: "emails",
                size: "80%",
                align: "left",
                isShow: true,
                isSort: false,
            },
        ];
        this.optionTable = {
            hasClearSelected: true,
            hasShowChoose: false,
            hasShowIndex: true,
            hasShowToggleColumn: false,
            action: [
                {
                    icon: "pi pi-trash",
                    tooltip: this.tranService.translate("global.button.registerRatingPlan"),
                    func: function(id, item){
                        me.removeEmail(item)
                    },
                }
            ]
        };
        this.dataSet = {
            content: [],
            total: 0
        }
        this.search();
    }
    ngAfterContentChecked(): void {
    }
    onSubmitCreate(){
        let dataBody = {
            name: this.receivingGroupInfo.name,
            description: this.receivingGroupInfo.description,
            emails: this.receivingGroupInfo.emails,
        }
        this.messageCommonService.onload();
        let me = this;
        this.reportReceivingGroupService.creteReportReceivingGroup(dataBody, (response)=>{
            me.messageCommonService.success(me.tranService.translate("global.message.saveSuccess"));
            me.router.navigate(["/reports/group-report-dynamic"]);
        }, null, ()=>{
            me.messageCommonService.offload();
        })
    }
    closeForm(){
        this.router.navigate(['/reports/group-report-dynamic'])
    }

    addEmail(val){
        let me = this;
        me.dataSet.content.push({emails :val})
        me.myEmails.push(val)
        me.receivingGroupInfo.emails = me.myEmails.join(', ').toString();
        me.formMailInput.reset();
    }
    search(){
        let me = this
        me.dataSet = {
            content: [],
            total: 0
        }
    }
    removeEmail(val){
        let me = this
        me.dataSet.content.splice(me.dataSet.content.indexOf(val), 1)
        me.myEmails.splice(me.myEmails.indexOf(val), 1)
        me.receivingGroupInfo.emails = me.myEmails.join(', ').toString()
    }
    nameChanged(query){
        let me = this
        if(this.receivingGroupInfo.name != null && this.receivingGroupInfo.name != "")
        this.debounceService.set("name",me.reportReceivingGroupService.checkName.bind(me.reportReceivingGroupService),{name:this.formReceivingGroup.value['name']},(response)=>{
            if (response > 0){
                me.isRGNameExisted = true
            }
            else {
                me.isRGNameExisted = false
            }
            // me.isRGNameExisted = response == 1;
        })
        // this.reportReceivingGroupService.checkName({name:me.receivingGroupInfo.name}, (response) =>{
        //     if (response > 0){
        //         me.isRGNameExisted = true
        //     }
        //     else {
        //         me.isRGNameExisted = false
        //     }
        // })
    }

    emailChanged(query){
        let me = this;
        for (let i = 0; i < me.myEmails.length; i++) {
            if (me.myEmails[i] == query){
                this.isRGEmailExisted = true
                return
            }
            else {
                this.isRGEmailExisted = false
            }
        }
    }
}
