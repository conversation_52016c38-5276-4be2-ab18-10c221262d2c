import { AfterContentChecked, Component, Injector, Input, OnInit } from "@angular/core";
import { MenuItem } from "primeng/api";
import { ComponentBase } from "src/app/component.base";
import { GeneralInfo, TabGeneralDynamicReportControl } from "./tab.report.dynamic.general";
import { CONSTANTS } from "src/app/service/comon/constants";
import { CrontabInfo, TabCrontabDynamicReportControl } from "./tab.report.dynamic.crontab";
import { SendInfo, TabSendDynamicReportControl } from "./tab.report.dynamic.send";
import { ReportService } from "src/app/service/report/ReportService";
export class ReportDynamicFormControl {
    reload: Function;
}
@Component({
    selector: "report-dynamic-form",
    templateUrl: "./report.dynamic.form.component.html"
})
export class ReportDynamicFormComponent extends ComponentBase implements OnInit, AfterContentChecked {
    constructor(injector: Injector,
                private reportService: ReportService) {
        super(injector);
    }

    @Input() idReport!: number;
    reportObject: any;

    isShowDialog: boolean = false;
    items: MenuItem[];
    activeItem: MenuItem;
    tabActive: string;
    generalInfo: GeneralInfo;
    controlTabGeneral: TabGeneralDynamicReportControl = new TabGeneralDynamicReportControl();
    crontabInfo: CrontabInfo;
    controlTabCrontab: TabCrontabDynamicReportControl = new TabCrontabDynamicReportControl();
    sendInfo: SendInfo;
    controlTabSend: TabSendDynamicReportControl = new TabSendDynamicReportControl();
    @Input() mode!: number;
    @Input() control!: ReportDynamicFormControl;
    @Input() loadList?: Function;
    
    ngOnInit(): void {
        this.control.reload = this.reload.bind(this);
        this.load();
    }
    load(){
        let me = this;
        this.items = [
            {
                id: "general",
                label: this.tranService.translate("report.label.tabGeneral"),
                command: ()=>{
                    me.tabActive = 'general'
                },
            },
            {
                id: "crontab",
                label: this.tranService.translate("report.label.tabCrontab"),
                command: ()=>{
                    me.tabActive = 'crontab'
                },
                disabled: this.idReport == null
            },
            {
                id: "send",
                label: this.tranService.translate("report.label.tabSend"),
                command: ()=>{
                    me.tabActive = 'send'
                },
                disabled: this.idReport == null
            }
        ]
        this.activeItem = this.items[0];
        this.tabActive = this.activeItem.id;
    }

    reload(param?:string){
        if(param == "truongdx"){
            this.idReport = null;
        }
        let me = this;
        setTimeout(function(){
            me.items = [
                {
                    id: "general",
                    label: me.tranService.translate("report.label.tabGeneral"),
                    command: ()=>{
                        me.tabActive = 'general'
                    },
                },
                {
                    id: "crontab",
                    label: me.tranService.translate("report.label.tabCrontab"),
                    command: ()=>{
                        me.tabActive = 'crontab'
                    },
                    disabled: me.idReport == null
                },
                {
                    id: "send",
                    label: me.tranService.translate("report.label.tabSend"),
                    command: ()=>{
                        me.tabActive = 'send'
                    },
                    disabled: me.idReport == null
                }
            ]
            me.activeItem = me.items[0];
            me.tabActive = me.activeItem.id;
            if(me.mode == CONSTANTS.MODE_VIEW.CREATE){
                me.create();
            }else if(me.mode == CONSTANTS.MODE_VIEW.UPDATE){
                me.update();
            }else{
                me.detail();
            }
        })
    }

    ngAfterContentChecked(): void {
        
    }

    create(){
        if(this.idReport != null){
            this.update();
            return;
        }
        this.generalInfo = {
            id: null,
            name: null,
            description: null,
            enablePreview: CONSTANTS.REPORT_PREVIEW.DISABLE,
            status: CONSTANTS.REPORT_STATUS.ACTIVE,
            reportContents: [],
            filterParams: JSON.stringify([]),
        }
        this.crontabInfo = {
            id: null,
            query: null,
            schema: null,
            timeOnce: null,
            schedule: null,
            scheduleDesc: null
        }
        this.sendInfo = {
            id: null,
            reportConfigId: null,
            emailGroups: null,
            emails: null,
            emailSubject: null,
            schedule: null
        }
        this.controlTabGeneral.reload();
        this.controlTabCrontab.reload();
        this.controlTabSend.reload();
        this.isShowDialog = true;
    }

    update(){
        this.detail();
    }

    detail(){
        let me = this;
        me.messageCommonService.onload();
        this.reportService.getDetailReportDynamic(this.idReport, (response)=>{
            me.reportObject = response;
            me.generalInfo = {
                id: response.id,
                description: response.description,
                enablePreview: response.enablePreview,
                filterParams: response.filterParams,
                name: response.name,
                reportContents: response.reportContents,
                status: response.status
            };
            me.crontabInfo = {
                id: response.id,
                query: response.query,
                schema: response.schema,
                timeOnce: response.timeOnce,
                schedule: response.schedule,
                scheduleDesc: response.scheduleDesc,
            }
            if(response.reportSending){
                me.sendInfo = {
                    id: response.reportSending.id,
                    reportConfigId: response.id,
                    emailGroups: response.reportSending.emailGroups,
                    emails: response.reportSending.emails,
                    emailSubject: response.reportSending.emailSubject,
                    schedule: response.reportSending.schedule,
                }
            }else{
                me.sendInfo = {
                    id: null,
                    reportConfigId: response.id,
                    emailGroups: [],
                    emails: null,
                    emailSubject: null,
                    schedule: null
                }
            }
            this.controlTabGeneral.reload();
            this.controlTabCrontab.reload();
            this.controlTabSend.reload();
            this.isShowDialog = true;
        }, null, ()=>{
            me.messageCommonService.offload();
        })
    }

    close(){
        this.isShowDialog = false;
    }

    saveSuccess(id){
        let me = this;
        this.idReport = id;
        if(this.loadList){
            this.loadList();
        }
        this.reload();
    }

    getHeaderDialog(){
        if(this.mode == CONSTANTS.MODE_VIEW.CREATE){
            return this.tranService.translate('global.button.create');
        }else if(this.mode == CONSTANTS.MODE_VIEW.UPDATE){
            return this.tranService.translate('global.button.edit');
        }else if(this.mode == CONSTANTS.MODE_VIEW.DETAIL){
            return this.tranService.translate('global.button.view');
        }
        return "";
    }
}