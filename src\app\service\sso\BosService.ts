import {Inject, Injectable} from "@angular/core";
import {HttpService} from "../comon/http.service";

@Injectable({ providedIn: 'root'})
export class BosService {
    private prefixApi: string;

    constructor(@Inject(HttpService) private httpService: HttpService) {
        this.prefixApi = "/auth";
    }
    public ssoLogin(body, callback?:Function, errorCallBack?:Function, finallyCallback?:Function){
        this.httpService.postNoHeader(`${this.prefixApi}/sso/token`, {}, body,{}, callback, errorCallBack, finallyCallback);
    }
}
