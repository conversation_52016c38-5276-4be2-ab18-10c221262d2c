import { Component, Injector, Input, OnInit } from "@angular/core";
import { FormBuilder } from "@angular/forms";
import { ComponentBase } from "src/app/component.base";
import { CONSTANTS } from "src/app/service/comon/constants";
import { ReportService } from "src/app/service/report/ReportService";
import { ColumnInputInfo, OptionTableInput, TableInputControl } from "src/app/template/common-module/table/table.input.component";
export interface SendInfo{
    id: number|null;
    reportConfigId: number | null;
    emailSubject: string | null;
    emailGroups: Array<any> | null;
    emails: string | null;
    schedule: string | null;
    time?: Date|null;
    dayInMonth?: Array<number>|null;
    dayInWeek?: Array<number | string>|null;
    month?: Array<number>|null;
    allDayInMonth?: number;
    allDayInWeek?: number;
    allMonth?: number;
    listEmail?: Array<{id: number|null, value: string|null}>
}
export class TabSendDynamicReportControl{
    reload: Function;
}
@Component({
    selector: "tab-report-dynamic-send",
    templateUrl: "./tab.report.dynamic.send.html"
})
export class TabReportDynamicSend extends ComponentBase implements OnInit{
    @Input() sendInfo!: SendInfo;
    @Input() control!: TabSendDynamicReportControl;
    @Input() cancel!: Function;
    @Input() modeView!: number;

    constructor(injector: Injector,
        private formBuilder: FormBuilder,
        private reportService: ReportService) {
        super(injector);
    }
    
    formSendInfo: any;
    maxDateStart: Date = new Date();
    minDateStart: Date = new Date();
    cycles: Array<any>;
    fullDayInMonth = [1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31];
    fullDayInWeek = [
        {value: "MON", name: this.tranService.translate("report.label.monday")},
        {value: "TUE", name: this.tranService.translate("report.label.tuesday")},
        {value: "WED", name: this.tranService.translate("report.label.wednesday")},
        {value: "THU", name: this.tranService.translate("report.label.thursday")},
        {value: "FRI", name: this.tranService.translate("report.label.friday")},
        {value: "SAT", name: this.tranService.translate("report.label.saturday")},
        {value: "SUN", name: this.tranService.translate("report.label.sunday")},
    ];
    fullMonth = [
        {value: 1, name: this.tranService.translate("report.label.january")},
        {value: 2, name: this.tranService.translate("report.label.february")},
        {value: 3, name: this.tranService.translate("report.label.march")},
        {value: 4, name: this.tranService.translate("report.label.april")},
        {value: 5, name: this.tranService.translate("report.label.may")},
        {value: 6, name: this.tranService.translate("report.label.june")},
        {value: 7, name: this.tranService.translate("report.label.july")},
        {value: 8, name: this.tranService.translate("report.label.august")},
        {value: 9, name: this.tranService.translate("report.label.september")},
        {value: 10, name: this.tranService.translate("report.label.october")},
        {value: 11, name: this.tranService.translate("report.label.november")},
        {value: 12, name: this.tranService.translate("report.label.december")},
    ];

    objectMode = CONSTANTS.MODE_VIEW;
    listEmailGroup: Array<any>;
    columnEmailInput: Array<ColumnInputInfo>;
    optionEmailInput: OptionTableInput;
    emailInputControl: TableInputControl = new TableInputControl();

    ngOnInit(): void {
        this.control.reload = this.onload.bind(this);

        this.cycles = [
            {value: 1, name: 1},
            {value: 2, name: 2},
            {value: 3, name: 3},
            {value: 4, name: 4},
            {value: 6, name: 6},
            {value: 8, name: 8},
            {value: 12, name: 12},
        ]

        this.listEmailGroup = [
            {id: 4, name: "nhom 4"},
            {id: 7, name: "nhom 7"},
        ]

        this.columnEmailInput = [
            {
                align: "left",
                key: "value",
                name: "Email",
                size: "500px",
                type: CONSTANTS.PARAMETER_TYPE.STRING,
                allowUpdate: true,
                validate: {
                    required: true,
                    maxLength: 255,
                    // pattern: /^[a-z0-9]+[a-z0-9\-\._]*[a-z0-9]+@([a-z0-9]+[a-z0-9\-\._]*[a-z0-9]+)+(\.[a-z]{2,})$/,
                    pattern: /^[a-z0-9]+[a-z0-9\-\._]*[a-z0-9]+@([a-z0-9]+[a-z0-9\-\._]*[a-z0-9]+)+(\.[a-z]{2,})$/,
                    messageErrorPattern: this.tranService.translate("global.message.invalidEmail"),
                    exists: true
                }
            }
        ]

        this.optionEmailInput = {
            mode: this.modeView
        }

        this.getAllEmailGroup();
    }

    onload(){
        let me = this;
        setTimeout(function(){
            if(me.sendInfo.emailGroups != null && me.sendInfo.emailGroups.length > 0){
                let ids = me.sendInfo.emailGroups.map(el => el.id);
                me.sendInfo.emailGroups = me.listEmailGroup.filter(el => ids.includes(el.id));
            }
            me.sendInfo.allDayInMonth = 1;
            me.sendInfo.allDayInWeek = 1;
            me.sendInfo.allMonth = 1;
            me.sendInfo.dayInMonth = [];
            me.sendInfo.dayInWeek = [];
            me.sendInfo.month = [];
            if(me.sendInfo.emails != null){
                me.sendInfo.listEmail = (me.sendInfo.emails.split(",") || []).filter(el => (el || "").length > 0).map(el => {
                    return {
                        id: null,
                        value: el
                    }
                })
            }else{
                me.sendInfo.listEmail = [];
            }
            if(me.sendInfo.schedule != null && me.sendInfo.schedule.split(" ").length == 6){
                let strValue = me.sendInfo.schedule.split(" ");
                    if(strValue[0] == "0"){
                        me.sendInfo.time = new Date(new Date().getFullYear(), new Date().getMonth(), new Date().getDate(), 0,0,0);
                    }else{
                        me.sendInfo.time = new Date(new Date().getFullYear(), new Date().getMonth(), new Date().getDate(), parseInt(strValue[2]),parseInt(strValue[1]),parseInt(strValue[0]));
                    }
                    if(strValue[3] != "*"){
                        me.sendInfo.dayInMonth = strValue[3].split(",").map(el => parseInt(el));
                        me.sendInfo.allDayInMonth = 0;
                    }
                    if(strValue[4] != "*"){
                        me.sendInfo.month = strValue[4].split(",").map(el => parseInt(el));
                        me.sendInfo.allMonth = 0;
                    }
                    if(strValue[5] != "*"){
                        let valueAccept = me.fullDayInWeek.map(el => el.value);
                        me.sendInfo.dayInWeek = strValue[5].split(",").filter(el => valueAccept.includes(el)).map(el => el);
                        me.sendInfo.allDayInWeek = 0;
                    }
            }else{
                me.sendInfo.time = null;
                me.sendInfo.allDayInMonth = 0;
                me.sendInfo.allDayInWeek = 0;
                me.sendInfo.allMonth = 0;
            }
            setTimeout(function(){
                me.optionEmailInput.mode = me.modeView;
                me.emailInputControl.reset();
            },100)
            let dayInMonth = me.sendInfo.dayInMonth;
            me.sendInfo.dayInMonth = [];
            let dayInWeek = me.sendInfo.dayInWeek;
            me.sendInfo.dayInWeek = [];
            let month = me.sendInfo.month;
            me.sendInfo.month = [];
            me.formSendInfo = me.formBuilder.group(me.sendInfo);
            me.sendInfo.dayInMonth = dayInMonth;
            me.sendInfo.dayInWeek = dayInWeek;
            me.sendInfo.month = month;
            if(me.modeView == CONSTANTS.MODE_VIEW.DETAIL){
                Object.keys(me.sendInfo).forEach(key => {
                    me.formSendInfo.get(key).disable();
                })
            }
        })
    }

    toggleSelect(type){
        if(type == 0){
            if(this.sendInfo.allDayInMonth == 1){
                this.sendInfo.dayInMonth = [...this.fullDayInMonth];
            }else{
                this.sendInfo.dayInMonth = [];
            }
        }else if(type == 1){
            if(this.sendInfo.allDayInWeek == 1){
                this.sendInfo.dayInWeek = this.fullDayInWeek.map(el => el.value);
            }else{
                this.sendInfo.dayInWeek = [];
            }
        }else if(type ==2){
            if(this.sendInfo.allMonth == 1){
                this.sendInfo.month = this.fullMonth.map(el => el.value);
            }else{
                this.sendInfo.month = [];
            }
        }
    }

    onSubmit(){
        let me = this;
        let data: SendInfo = {
            id: this.sendInfo.id,
            reportConfigId: this.sendInfo.reportConfigId,
            emailSubject: this.sendInfo.emailSubject,
            emails: this.sendInfo.listEmail != null ? this.sendInfo.listEmail.map(el => el["value"]).toLocaleString(): null,
            emailGroups: this.sendInfo.emailGroups,
            schedule: this.getCronJob(),
        }
        this.reportService.updateSendingReportDynamic(this.sendInfo.reportConfigId, data, (response)=>{
            me.messageCommonService.success(me.tranService.translate("global.message.saveSuccess"))
            me.sendInfo = response;
            me.onload();
        })
    }

    getCronJob():string{
        let schedule = ""
        if(this.sendInfo.time != null){
            schedule = `${this.sendInfo.time.getSeconds()} ${this.sendInfo.time.getMinutes()} ${this.sendInfo.time.getHours()}`;
        }else{
            schedule = "0 0 0";
        }
        if(this.sendInfo.dayInMonth != null && this.sendInfo.dayInMonth.length > 0 && this.sendInfo.dayInMonth.length < this.fullDayInMonth.length){
            schedule += " " + this.sendInfo.dayInMonth.toLocaleString();
        }else{
            schedule += " *";
        }
        if(this.sendInfo.month != null && this.sendInfo.month.length > 0 && this.sendInfo.month.length < this.fullMonth.length){
            schedule += " " + this.sendInfo.month.toLocaleString();
        }else{
            schedule += " *";
        }
        if(this.sendInfo.dayInWeek != null && this.sendInfo.dayInWeek.length > 0 && this.sendInfo.dayInWeek.length < this.fullDayInWeek.length){
            schedule += " " + this.sendInfo.dayInWeek.toLocaleString();
        }else{
            schedule += " *";
        }
        return schedule;
    }

    sortListEmailGroup(){
        for(let i = 0; i < this.listEmailGroup.length - 1;i++){
            for(let j = i + 1; j < this.listEmailGroup.length; j++){
                let a = this.listEmailGroup[i];
                let b = this.listEmailGroup[j];
                if(a.name.toUpperCase().localeCompare(b.name.toUpperCase()) > 1){
                    this.listEmailGroup[i] = b;
                    this.listEmailGroup[j] = a;
                }
            }
        }
    }

    getAllEmailGroup(){
        let me = this;
        this.reportService.getAllEmailGroup((response)=>{
            me.listEmailGroup = response.sort((a,b)=> a.name.toUpperCase().localeCompare(b.name.toUpperCase()) > 0 ? 1 : -1);
            if(me.sendInfo != null){
                if(me.sendInfo.emailGroups != null && me.sendInfo.emailGroups.length > 0){
                    let ids = me.sendInfo.emailGroups.map(el => el.id);
                    me.sendInfo.emailGroups = me.listEmailGroup.filter(el => ids.includes(el.id));
                }
            }
        })
    }
}