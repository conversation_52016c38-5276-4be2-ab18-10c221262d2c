import { Component, Inject, Injector } from '@angular/core';
import { Router } from '@angular/router';
import { MenuItem } from 'primeng/api';
import { MessageCommonService } from 'src/app/service/comon/message-common.service';
import { TranslateService } from 'src/app/service/comon/translate.service';
import { ColumnInfo, OptionTable } from '../../common-module/table/table.component';
import { CONSTANTS } from 'src/app/service/comon/constants';
import { CustomerService } from 'src/app/service/customer/CustomerService';
import { ComponentBase } from 'src/app/component.base';
import {AbstractControl, FormControl, FormGroup, ValidationErrors, ValidatorFn, Validators} from "@angular/forms";
import {AccountService} from "../../../service/account/AccountService";

interface MenuDropDown{
  name:string,
  value:number
}
@Component({
  selector: 'app-list-customer',
  templateUrl: './list-customer.component.html',
  styleUrls: ['./list-customer.component.scss']
})
export class ListCustomerComponent extends ComponentBase {
    items: MenuItem[];
    home: MenuItem
    searchInfo: {
        id?: number,
        customerCode?: string,
        customerName?: string,
        customerType?: string;
        taxId?: string,
        phone?: string,
        email?: string,
        status?: string,
        loggable?: boolean | null
    };
    columns: Array<ColumnInfo>;
    columnsAccount: Array<ColumnInfo>;
    columsContract: Array<ColumnInfo>;
    dataSet: {
        content: Array<any>,
        total: number
    };
    dataSetAccount: {
        content: Array<any>,
        total: number | null
    }
    dataSetContract: {
        content: Array<any>;
        total: number;
    }
    typeList: MenuDropDown[] = [
        {name: this.tranService.translate("ratingPlan.customerType.personal"), value: CONSTANTS.CUSTOMER_TYPE.PERSONAL},
        {
            name: this.tranService.translate('ratingPlan.customerType.enterprise'),
            value: CONSTANTS.CUSTOMER_TYPE.INTERPRISE
        }
    ]
    statusList: MenuDropDown[] = [
        {name: this.tranService.translate("customer.label.active"), value: CONSTANTS.CUSTOMER_STATUS.ACTIVE},
        {name: this.tranService.translate('customer.label.inActive'), value: CONSTANTS.CUSTOMER_STATUS.INACTIVE}
    ]
    dataStore: Array<any>;
    selectItems: Array<any>;
    selectItemsContract: Array<any>;
    optionTable: OptionTable;
    optionTableContract: OptionTable
    optionTableAccount: OptionTable = {hasClearSelected: true};
    pageNumber: number;
    pageSize: number;
    sort: string;
    pageNumberContract: number;
    pageSizeContract: number;
    sortContract: string;
    contractheader = this.tranService.translate("customer.label.contractHeader");
    isShowContract: boolean = false
    isShowModalDetail: boolean = false;
    allPermissions = CONSTANTS.PERMISSIONS;
    generalHeader: string = this.tranService.translate("customer.label.generalInfo");
    contactHeader: string = this.tranService.translate("customer.label.billingContact")
    paymentHeader: string = this.tranService.translate('customer.label.billingAddress');
    // buttonAdd: string =this.tranService.translate("groupSim.label.buttonAdd");
    isSubmit: boolean = false
    idCus: number;
    customerInfo: any = null;
    listProvince: [];
    isShowListAccount: boolean = false;

    constructor(@Inject(CustomerService) private customerService: CustomerService, private accountService: AccountService, injector: Injector) {
        super(injector)
    }

    customCharacterValidator(): ValidatorFn {
        return (control: AbstractControl): ValidationErrors | null => {
            const value = control.value;
            const isValid = /^[a-zA-Z0-9 \-_\!\#\$\%\&\'\*\+\-\/\=\?\^\_\`\.\{\|\}\~]*$/.test(value);
            return isValid ? null : {'invalidCharacters': {value}};
        };
    }

    regularCharacterValidator(): ValidatorFn {
        return (control: AbstractControl): ValidationErrors | null => {
            const value = control.value;
            const isValid = /^[a-zA-Z0-9 ]*$/.test(value);
            return isValid ? null : {'invalidCharacters': {value}};
        };
    }

    updateCustomerForm = new FormGroup({
        customerCode: new FormControl({value: "", disabled: true}, [Validators.required]),
        taxId: new FormControl({
            value: "",
            disabled: true
        }, [Validators.required, Validators.minLength(2), Validators.maxLength(255), this.regularCharacterValidator()]),
        provinceCode: new FormControl({value: "", disabled: true}),
        customerType: new FormControl({value: "", disabled: true}),
        status: new FormControl({value: "", disabled: true}),
        // Thông tin liên hệ chính
        customerName: new FormControl({
            value: "",
            disabled: true
        }, [Validators.required, Validators.minLength(2), Validators.maxLength(255), this.customCharacterValidator()]),
        phone: new FormControl({value: "", disabled: true}),
        email: new FormControl({value: "", disabled: true}, [Validators.email, Validators.maxLength(255)]),
        birthday: new FormControl({value: "", disabled: true}),
        // Thông tin thanh toán
        billName: new FormControl({
            value: "",
            disabled: true
        }, [Validators.minLength(2), Validators.maxLength(255), this.regularCharacterValidator()]),
        billPhone: new FormControl({value: null, disabled: true}),
        billEmail: new FormControl({value: "", disabled: true}, [Validators.email, Validators.maxLength(255)]),
        billBirthday: new FormControl({value: null, disabled: true}),
        // Địa chỉ
        addrStreet: new FormControl({
            value: "",
            disabled: true
        }, [Validators.minLength(2), Validators.maxLength(255), this.regularCharacterValidator()]),
        addrDist: new FormControl({
            value: "",
            disabled: true
        }, [Validators.minLength(2), Validators.maxLength(255), this.regularCharacterValidator()]),
        addrProvince: new FormControl({
            value: "",
            disabled: true
        }, [Validators.minLength(2), Validators.maxLength(255), this.regularCharacterValidator()]),
        //Ghi chú
        note: new FormControl({
            value: "",
            disabled: true
        }, [Validators.minLength(2), Validators.maxLength(255), this.regularCharacterValidator()])
    })

    ngOnInit() {
        let me = this

        this.items = [{label: this.tranService.translate(`global.menu.customermgmt`)}, {label: this.tranService.translate(`customer.label.listCustomer`)}];

        this.home = {icon: 'pi pi-home', routerLink: '/'};

        this.searchInfo = {};

        this.columns = [{
            name: this.tranService.translate("customer.label.customerCode"),
            key: "customerCode",
            size: "150px",
            align: "left",
            isShow: true,
            isSort: true,
            style: {
                cursor: "pointer",
                color: "var(--mainColorText)"
            },
            funcClick(id, item) {
                me.idCus = id;
                me.getDetail();
                me.isShowModalDetail = true;
            },
        },
            {
                name: this.tranService.translate("customer.label.customerName"),
                key: "customerName",
                size: "300px",
                align: "left",
                isShow: true,
                isSort: true,
                className: "white-space-normal"
            },
            {
                name: this.tranService.translate("customer.label.contact"),
                key: "billName",
                size: "300px",
                align: "left",
                isShow: true,
                isSort: true,
                className: "white-space-normal"
            },
            {
                name: this.tranService.translate("customer.label.type"),
                key: "customerType",
                size: "150px",
                align: "left",
                isShow: true,
                isSort: true,
                funcConvertText: (value) => {
                    if (value == CONSTANTS.CUSTOMER_TYPE.PERSONAL) {
                        return this.tranService.translate("ratingPlan.customerType.personal");
                    } else if (value == CONSTANTS.CUSTOMER_TYPE.INTERPRISE) {
                        return this.tranService.translate("ratingPlan.customerType.enterprise");
                    } else if (value == CONSTANTS.CUSTOMER_TYPE.AGENCY) {
                        return this.tranService.translate("ratingPlan.customerType.agency");
                    }
                    return "";
                },
            },
            {
                name: this.tranService.translate("customer.label.taxCode"),
                key: "taxId",
                size: "150px",
                align: "left",
                isShow: true,
                isSort: true
            },
            {
                name: this.tranService.translate("customer.label.phoneNumber"),
                key: "phone",
                size: "150px",
                align: "left",
                isShow: true,
                isSort: true
            },
            {
                name: this.tranService.translate("customer.label.email"),
                key: "email",
                size: "150px",
                align: "left",
                isShow: true,
                isSort: true
            },
            {
                name: this.tranService.translate("customer.label.status"),
                key: "status",
                size: "150px",
                align: "left",
                isShow: true,
                isSort: true,
                funcGetClassname: (value) => {
                    if (value == CONSTANTS.CUSTOMER_STATUS.CREATE_NEW) {
                        return ['p-1', "border-round", "border-400", "text-color", "inline-block"];
                    } else if (value == CONSTANTS.CUSTOMER_STATUS.ACTIVE) {
                        return ['p-2', "text-green-800", "bg-green-100", "border-round", "inline-block"];
                    } else if (value == CONSTANTS.CUSTOMER_STATUS.INACTIVE) {
                        return ['p-2', 'text-red-700', "bg-red-100", "border-round", "inline-block"];
                    }
                    return [];
                },
                funcConvertText: (value) => {
                    if (value == CONSTANTS.CUSTOMER_STATUS.CREATE_NEW) {
                        return "";
                    } else if (value == CONSTANTS.CUSTOMER_STATUS.ACTIVE) {
                        return me.tranService.translate("customer.label.active");
                    } else if (value == CONSTANTS.CUSTOMER_STATUS.INACTIVE) {
                        return me.tranService.translate("customer.label.inActive");
                    }
                    return "";
                },
                style: {
                    color: "white"
                }
            }];

        this.columsContract = [{
            name: this.tranService.translate("contract.label.contractCode"),
            key: "contractCode",
            size: "150px",
            align: "left",
            isShow: true,
            isSort: false,
        },
            {
                name: this.tranService.translate("contract.label.contractor"),
                key: "customerName",
                size: "150px",
                align: "left",
                isShow: true,
                isSort: false,
            },
            {
                name: this.tranService.translate("contract.label.contractDate"),
                key: "contractDate",
                size: "150px",
                align: "left",
                isShow: true,
                isSort: false,
                funcConvertText: (value) => {
                    return me.utilService.convertLongDateToString(value);
                },
            },
            {
                name: this.tranService.translate("contract.label.centerCode"),
                key: "centerCode",
                size: "150px",
                align: "left",
                isShow: true,
                isSort: false
            },
            {
                name: this.tranService.translate("contract.label.contactPhone"),
                key: "contactPhone",
                size: "150px",
                align: "left",
                isShow: true,
                isSort: false,
            },
            {
                name: this.tranService.translate("contract.label.paymentName"),
                key: "paymentName",
                size: "150px",
                align: "left",
                isShow: true,
                isSort: false
            },
            {
                name: this.tranService.translate("contract.label.paymentAddress"),
                key: "paymentAddress",
                size: "150px",
                align: "left",
                isShow: true,
                isSort: false
            }];

        me.columnsAccount = [
            {
                name: me.tranService.translate("account.label.username"),
                key: "username",
                size: "250px",
                align: "left",
                isShow: true,
                isSort: false,
                style: {
                    cursor: "pointer",
                    color: "var(--mainColorText)"
                },
                funcGetRouting(item) {
                    return [`/accounts/detail/${item.id}`]
                },
            },
            {
                name: me.tranService.translate("account.label.fullname"),
                key: "fullName",
                size: "300px",
                align: "left",
                isShow: true,
                isSort: false,
            },
            {
                name: me.tranService.translate("account.label.email"),
                key: "email",
                size: "300px",
                align: "left",
                isShow: true,
                isSort: false,
            },
        ];

        me.getListProvince();


        this.optionTable = {
            hasClearSelected: false,
            hasShowChoose: false,
            hasShowIndex: true,
            hasShowToggleColumn: false,
            action: [
                {
                    icon: 'pi pi-fw pi-book',
                    tooltip: this.tranService.translate(`customer.label.viewContract`),
                    func: (id: string) => {
                        this.openContractModals(id);
                    },
                    funcAppear: (id: string, item) => {
                        return me.checkAuthen([CONSTANTS.PERMISSIONS.CONTRACT.VIEW_LIST])
                    }
                },
            ]
        }

        this.optionTableContract = {
            hasClearSelected: false,
            hasShowChoose: false,
            hasShowIndex: true,
            hasShowToggleColumn: false,
        };

        this.optionTableAccount = {
            hasClearSelected: true,
            hasShowChoose: false,
            hasShowIndex: false,
            hasShowToggleColumn: false,
            paginator: false
        };

        this.pageNumberContract = 0;
        this.pageSizeContract = 10;
        this.sortContract = "customerCode,asc";
        this.selectItemsContract = [];
        this.dataSetContract = {
            content: [],
            total: 0,
        };

        this.dataSetAccount = {
            content: [],
            total: 0,
        };

        this.pageNumber = 0;
        this.pageSize = 10;
        this.sort = "customerCode,asc"
        this.selectItems = [];
        this.dataSet = {
            content: [],
            total: this.pageNumber
        }
        me.messageCommonService.onload();
        me.customerService.searchCustomers({}, (response) => {
            this.dataSet.content = response.content;
            this.dataSet.total = response.totalElements;
            me.messageCommonService.offload();
        })
    }

    onSearch() {
        this.searchInfo.loggable = true;
        this.search(0, this.pageSize, this.sort, this.searchInfo)
    }

    openContractModals(id) {
        this.isShowContract = true;
        this.customerService.getContractByCustomer(id, (response) => {
            this.dataSetContract.content = response;
            this.dataSetContract.total = response.totalElements;
        })
    }

    search(page, limit, sort, params) {
        let me = this;
        this.pageNumber = page;
        this.pageSize = limit;
        this.sort = sort;
        if (this.searchInfo.customerType == null)
            this.searchInfo.customerType = "";
        if (this.searchInfo.status == null)
            this.searchInfo.status = "";
        let dataParam = {
            ...params,
            page,
            size: limit,
            sort
        }
        me.messageCommonService.onload();
        this.customerService.searchCustomers(dataParam, (response) => {
            this.dataSet.content = response.content.map((item: any) => {
                return item;
            });
            this.dataSet.total = response.totalElements;
        }, null, () => {
            me.messageCommonService.offload();
        })
    }

    searchContract(page, limit, sort, params) {
        let me = this;
        this.pageNumber = page;
        this.pageSize = limit;
        this.sort = sort;
        let dataParam = {
            ...params,
            page,
            size: limit,
            sort
        }
    }

    getDetail() {
        let me = this;
        me.customerService.getCustomerById(me.idCus, (response) => {
            me.customerInfo = response;
            response.phone = response.phone != null ? ((response.phone || "").substring(2)) : null;
            response.billPhone = response.billPhone != null ? ((response.billPhone || "").substring(2)) : null;
            response.birthday = new Date(response.birthday)
            response.billBirthday = new Date(response.billBirthday)
            me.updateCustomerForm.patchValue(response);
        }, null, () => {
            me.messageCommonService.offload();
        });
    }

    openListAccount() {
        let me = this;

        this.customerService.getListAccount(me.idCus, (response) => {
            me.dataSetAccount = {
                content: response,
                total: response ? response.length : 0
            };
            me.isShowListAccount = true;
        })
    }

    getListProvince() {
        this.accountService.getListProvince((response) => {
            this.listProvince = response.map(el => {
                return {
                    ...el,
                    display: `${el.code} - ${el.name}`
                }
            })
        })
    }
}
