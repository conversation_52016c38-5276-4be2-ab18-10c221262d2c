import {Inject, Injectable} from "@angular/core";
import {HttpService} from "../comon/http.service";

@Injectable({providedIn: 'root'})
export class DeviceTypeService {
    private prefixApi: string;

    constructor(@Inject(HttpService) private httpService: HttpService) {
        this.prefixApi = "/device-type";
    }

    public search(params: {
        [key: string]: any
    }, callback?: Function, errorCallBack?: Function, finallyCallback?: Function) {
        this.httpService.get(`${this.prefixApi}/searchDistinct`, {}, params, callback, errorCallBack, finallyCallback);
    }

    // lấy thông tin deviceType
    public getDeviceType(id: number, callback?: Function, errorCallback?: Function, finallyCallback?: Function) {
        this.httpService.get(`${this.prefixApi}/${id}`, {}, {}, callback, errorCallback, finallyCallback);
    }

    // lấy ra 1 bản ghi theo typeCode
    public getById(typeCode, callback?: Function, errorCallback?: Function, finallyCallback?: Function) {
        this.httpService.get(`${this.prefixApi}/search`, {}, {typeCode},
            (res: any) => {
                callback(res?.content?.[0] ?? null);
            }
            , errorCallback, finallyCallback);
    }
}
