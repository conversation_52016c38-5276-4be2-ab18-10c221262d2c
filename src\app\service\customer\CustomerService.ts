import { Inject, Injectable } from "@angular/core";
import { HttpService } from "../comon/http.service";
import {callback} from "chart.js/types/helpers";

@Injectable({
    providedIn: 'root'
})
export class CustomerService {
    private prefixApi: string;
    constructor(@Inject(HttpService) private httpService:HttpService) {
        this.prefixApi = "/customer";
    }

    public searchCustomers(params:{[key: string]:string},callback?:Function, errorCallback?:Function, finallyCallback?: Function){
        this.httpService.get(this.prefixApi+"/search",{}, params,callback, errorCallback, finallyCallback);
    }

    public search(params:{[key: string]:string},callback?:Function){
        this.httpService.get(this.prefixApi+"/search",{}, params,callback)
    }

    public getContractByCustomer(id:number, callback:Function){
        this.httpService.get( "/contract/get-by-customer/"+id, {}, {}, callback );
    }

    public getCustomerById(id:number, callback:Function, errorCallback?:Function, finallyCallback?: Function){
        this.httpService.get(`${this.prefixApi}/${id}`,{},{},callback, errorCallback, finallyCallback);
    }

    public getById(id:number, callback:Function){
        this.httpService.get(`${this.prefixApi}/${id}`,{},{},callback)
    }

    public getByKey(key: string, value: string, callback?: Function, errorCallback?:Function, finallyCallback?: Function){
        this.httpService.get(`${this.prefixApi}/getByKey`,{}, {key, value}, callback, errorCallback, finallyCallback);
    }

    public updateCustomer(id:number, data:any , callback:Function, errorCallback?:Function, finallyCallback?: Function){
        this.httpService.put(`${this.prefixApi}/${id}`,{}, data, {}, callback, errorCallback, finallyCallback);
    }

    public getListAccount(customerId, callback:Function){
        this.httpService.get(`${this.prefixApi}/get-list-account`,{}, {customerId}, callback );
    }

    //Chỉ dùng api này cho phần tài khoản, trả về ds customer của account root (nếu có), nếu không sẽ trả về ds customer chưa gán của tỉnh
    public quickSearchCustomer(params:{[key: string]:string}, body ,callback?:Function, errorCallback?:Function, finallyCallback?: Function ){
        this.httpService.post(`${this.prefixApi}/quick-search`, {}, body, params, callback, errorCallback, finallyCallback)
    }
}
