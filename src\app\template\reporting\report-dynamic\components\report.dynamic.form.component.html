
<style>
    .tab-report-dynamic{
        max-height: calc(100vh - 320px);
        overflow-y: scroll;
    }

    /* Hide scrollbar for Chrome, Safari and Opera */
    .tab-report-dynamic::-webkit-scrollbar {
        display: none;
    }

    /* Hide scrollbar for IE, Edge and Firefox */
    .tab-report-dynamic {
        -ms-overflow-style: none;  /* IE and Edge */
        scrollbar-width: none;  /* Firefox */
    }
</style>
<!-- <p-button styleClass="p-button-info" [label]="tranService.translate('global.button.create')" (click)="create()"></p-button>
<p-button styleClass="p-button-info" [label]="tranService.translate('global.button.edit')" (click)="update()"></p-button>
<p-button styleClass="p-button-info" [label]="tranService.translate('global.button.view')" (click)="detail()"></p-button> -->
<div class="flex justify-content-center dialog-push-group">
    <p-dialog [header]="getHeaderDialog()" [(visible)]="isShowDialog" [modal]="true" [style]="{ width: '900px' }" [draggable]="false" [resizable]="false">
        <div class="w-full field grid p-0 m-0">
            <div class="w-full">
                <p-tabMenu [model]="items" [activeItem]="activeItem"></p-tabMenu>
            </div>
            <div class="mt-2 w-full tab-report-dynamic">
                <tab-report-dynamic-general [saveSuccess]="saveSuccess.bind(this)" [cancel]="close.bind(this)" [generalInfo]="generalInfo" [class]="tabActive == 'general'?'':'hidden'" [control]="controlTabGeneral" [modeView]="mode"></tab-report-dynamic-general>
                <tab-report-dynamic-crontab [cancel]="close.bind(this)" [class]="tabActive == 'crontab'?'':'hidden'" [crontabInfo]="crontabInfo" [control]="controlTabCrontab" [modeView]="mode"></tab-report-dynamic-crontab>
                <tab-report-dynamic-send [cancel]="close.bind(this)" [class]="tabActive == 'send'?'':'hidden'" [sendInfo]="sendInfo" [control]="controlTabSend" [modeView]="mode"></tab-report-dynamic-send>
            </div>
        </div>
    </p-dialog>
</div>