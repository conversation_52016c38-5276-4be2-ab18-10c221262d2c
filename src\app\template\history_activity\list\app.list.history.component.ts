import {Component, Inject, Injector, OnInit} from "@angular/core";
import {MenuItem} from "primeng/api";
import {ColumnInfo, OptionTable} from "../../common-module/table/table.component";
import {CONSTANTS} from "src/app/service/comon/constants";
import {ComponentBase} from "src/app/component.base";
import {AccountService} from "../../../service/account/AccountService";
import {FormBuilder} from "@angular/forms";
import {LogsService} from "../../../service/activity-history/LogsService";
import {CustomerService} from "../../../service/customer/CustomerService";
import {SimService} from "../../../service/sim/SimService";

@Component({
  selector: "history-activity-list",
  templateUrl: './app.list.history.component.html'
})
export class ListHistoryActivityComponent extends ComponentBase implements OnInit {
  items: MenuItem[];
  home: MenuItem
  searchInfo: any
  columns: Array<ColumnInfo>;
  dataSet: {
    content: Array<any>,
    total: number
  };
  selectItems: Array<any>;
  optionTable: OptionTable;
  pageNumber: number;
  pageSize: number;
  sort: string;
  formSearch: any;
  userInfo: any
  userType: any
  actionType: Array<any>
  myColumns: any
  myValues: any
  isShowModal: boolean
  mapMyValue : any

  constructor(
      @Inject(LogsService) private logsService: LogsService,
      @Inject(AccountService) private accountService: AccountService,
      @Inject(CustomerService) private customerService: CustomerService,
      @Inject(SimService) private simService: SimService,
      private formBuilder: FormBuilder,
      private injector: Injector) {
    super(injector);
  }

  ngOnInit() {
    let me = this;
    this.isShowModal = false;
    this.userInfo = this.sessionService.userInfo;
    this.userType = CONSTANTS.USER_TYPE;
    this.myColumns = []
    
    this.mapMyValue = {
      'User': [
        {field: 'fullName', header: me.tranService.translate('account.label.fullname')},
        {field: 'phone', header: me.tranService.translate('account.label.phone')}],
      'Contract': [
        {field: 'customerName', header: me.tranService.translate('customer.label.customerName')},
        {field: 'contactPhone', header: me.tranService.translate('contract.label.contactPhone')},
        {field: 'contactBirthday', header: me.tranService.translate('contract.label.customerBirthday')},
        {field: 'contactAddress', header: me.tranService.translate('contract.label.contactAddress')}
      ],
      'Sim' : [
        {field: 'msisdn', header: me.tranService.translate('sim.label.sothuebao')},
        {field: 'customerName', header: me.tranService.translate('sim.label.khachhang')},
        {field: 'contactPhone', header: me.tranService.translate('sim.label.dienthoailienhe')},
        {field: 'contactAddress', header: me.tranService.translate('contract.label.contactAddress')},
        {field: 'birthday', header: me.tranService.translate('sim.label.customerBirth')}
      ],
      'Customer' : [
        {field: 'customerName', header: me.tranService.translate('sim.label.khachhang')},
        {field: 'customerName', header: me.tranService.translate('customer.label.contact')},
        {field: 'contractorInfo', header: me.tranService.translate('contract.label.contractor')},
        {field: 'phone', header: me.tranService.translate('sim.label.dienthoailienhe')},
      ],
      'Report' : [
        {field: 'name', header: me.tranService.translate('report.label.reportName')},
      ]
    }

    this.myValues = []

    this.actionType = [
      {
        label: this.tranService.translate('logs.actionType.search'),
        value: 'SEARCH'
      }, {
        label: this.tranService.translate('logs.actionType.update'),
        value: 'UPDATE'
      }, {
        label: this.tranService.translate('logs.actionType.create'),
        value: 'CREATE'
      }, {
        label: this.tranService.translate('logs.actionType.delete'),
        value: 'DELETE'
      }
    ]

    this.searchInfo = {
      userName: null,
      action: null
    }
    this.columns = [
      {
        name: this.tranService.translate("logs.label.username"),
        key: "userName",
        size: "150px",
        align: "left",
        isShow: true,
        isSort: true
      },
      {
        name: this.tranService.translate("logs.label.createdDate"),
        key: "requestTime",
        size: "fit-content",
        align: "left",
        isShow: true,
        isSort: true,
        funcConvertText(value) {
          return me.utilService.convertDateTimeToString(new Date(value))
        },
      }, {
        name: this.tranService.translate("logs.label.ip"),
        key: "ipSource",
        size: "fit-content",
        align: "left",
        isShow: true,
        isSort: true
      }, {
        name: this.tranService.translate("logs.label.actionType"),
        key: "action",
        size: "fit-content",
        align: "left",
        isShow: true,
        isSort: true,
        funcGetClassname(value){
          if(value == 'SEARCH'){
            return ['p-2', 'text-teal-800', "bg-teal-100", "border-round","inline-block"];
          }else if(value == 'CREATE'){
            return ['p-2', 'text-green-800', "bg-green-100","border-round","inline-block"];
          }else if(value == 'UPDATE'){
            return ['p-2', 'text-yellow-800', "bg-yellow-100", "border-round","inline-block"];
          }else if(value == 'DELETE'){
            return ['p-2', 'text-red-700', "bg-red-100", "border-round","inline-block"];
          }
          return ""
        },
        funcConvertText(value){
          if(value == 'SEARCH'){
            return me.tranService.translate('logs.actionType.search')
          }else if(value == 'CREATE'){
            return me.tranService.translate('logs.actionType.create')
          }else if(value == 'UPDATE'){
            return me.tranService.translate('logs.actionType.update')
          }else if(value == 'DELETE'){
            return me.tranService.translate('logs.actionType.delete')
          }
          return ""
        }
      }, {
        name: this.tranService.translate("logs.label.module"),
        key: "objectKey",
        size: "fit-content",
        align: "left",
        isShow: true,
        isSort: false,
        funcConvertText(value) {
          if(value == 'User') {
            return me.tranService.translate('logs.objectKey.user');
          }else if(value == 'Customer') {
            return me.tranService.translate('logs.objectKey.customer');
          }else if(value == 'Contract') {
            return me.tranService.translate('logs.objectKey.contract');
          }else if(value == 'Sim') {
            return me.tranService.translate('logs.objectKey.sim');
          }else if(value == 'Report') {
            return me.tranService.translate('logs.objectKey.report');
          }
          return ''
        }
      }, {
        name: this.tranService.translate("logs.label.affectedField"),
        key: null,
        size: "fit-content",
        align: "left",
        isShow: true,
        isSort: true,
        funcConvertText(value, item) {
          if ((item.objectKey == 'Report' && item.action == 'SEARCH') ) return me.tranService.translate('logs.label.detail');
          if ((item.objectKey !== 'Report' && item.action == 'SEARCH') ) return '';
          return me.tranService.translate('logs.label.detail');
        },
        funcClick(id, item) {
          me.myValues = []
         if(item.responseContent != null && item.responseContent !== 'null') {
            if(item.responseContent.startsWith('{')){
              let obj = JSON.parse(item.responseContent);
              if(item.objectKey == 'Customer') {
                me.customerService.getContractByCustomer(obj.id,(dataContract)=>{
                  let contractorInfo = ''
                  for (let i = 0; i < dataContract.length; i++) {
                      contractorInfo += (dataContract[i].contractorInfo + ', ');
                  }
                  obj = {...obj, contractorInfo : contractorInfo.slice(0, -2)}
                  me.myValues.push(obj);
                })
              } else if(item.objectKey == 'Report') {
                me.myValues.push(obj);
              }else {
                me.myValues.push(obj);
              }
            }else {
              let arr = JSON.parse(item.responseContent)
              if(item.objectKey == 'Sim') {
                let customerCodeList = arr.map(e => e.customerCode)
                me.simService.getSimInfoForLog(customerCodeList ,(simDataResp)=> {
                  for(let el of arr) {
                    for(let el1 of simDataResp) {
                      if(el.customerCode == el1.customerCode) {
                        el.contactAddress =  el1.contactAddress
                        el.contactPhone =  el1.contactPhone
                        el.birthday =  me.utilService.convertDateToString(new Date(el1.birthday));
                      }
                    }
                  }
                })
              }
              me.myValues.push(...arr);
            }
          }
          me.myColumns = me.mapMyValue[item.objectKey]
          me.isShowModal = true;
        },
        style: {
          color: "var(--mainColorText)",
          cursor: 'pointer'
        }
      }
    ];

    this.optionTable = {
      hasClearSelected: false,
      hasShowChoose: false,
      hasShowIndex: true,
      hasShowToggleColumn: false,
    }
    this.pageNumber = 0;
    this.pageSize = 10;
    this.sort = "requestTime,desc"
    this.dataSet = {
      content: [],
      total: 0
    }
    this.formSearch = this.formBuilder.group(this.searchInfo);
    this.search(this.pageNumber, this.pageSize, this.sort, this.searchInfo);
  }

  search(page, limit, sort, params) {
    let me = this;
    this.pageNumber = page;
    this.pageSize = limit;
    this.sort = sort;
    let dataParams = {
      page,
      size: limit,
      sort
    }
    Object.keys(params).forEach(key => {
      if (params[key] != null) {
        dataParams[key] = me.searchInfo[key];
      }
    })
    this.dataSet = {
      content: [],
      total: 0
    }
    me.messageCommonService.onload();
    this.logsService.searchLogs(dataParams, (response) => {
      me.dataSet = {
        content: response.content,
        total: response.totalElements
      }
    }, null, () => {
      me.messageCommonService.offload();
    })
  }

  onSubmitSearch() {
    this.pageNumber = 0;
    this.search(this.pageNumber, this.pageSize, this.sort, this.searchInfo);
  }

  loadAccount(params, callback) {
    return this.accountService.getAccountHistory(params, callback)
  }
}
