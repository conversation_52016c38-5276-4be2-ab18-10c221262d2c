export default {
    label: {
        imei: "IMEI/SERIAL",
        address: "Địa chỉ lắp đặt",
        subcriber: "Số điện thoại",
        type: "<PERSON>ại thiết bị",
        msisdn: "SĐT trên thiết bị",
        name: "<PERSON><PERSON><PERSON> thiết bị",
        serial: "Serial",
        individualName: "Tên khách hàng",
        businessName: "Tên doanh nghiệp",
        lastConnected: "Thời gian kết nối gần nhất",
        statusConnect: "Trạng thái kết nối",
        info: "Thông tin thiết bị",
        infoMngt: "Thông tin quản lý",
        infoBusinessMngt: "Thông tin doanh nghiệp quản lý",
        infoIndividualMngt: "Thông tin cá nhân quản lý",
        businessMngt: "<PERSON><PERSON>h nghiệp quản lý",
        individualMngt: "<PERSON><PERSON><PERSON><PERSON> hàng cá nhân sử dụng",
        manufacturer: "<PERSON><PERSON>à sản xuất",
        description: "<PERSON><PERSON> tả",
        model: "Mẫu thiết bị",
        custInfo: "Thông tin khách hàng",
        listCmd: "Danh sách lệnh",
        telemetryData: "Dữ liệu từ xa",
        alert: "Cảnh báo",
        location: "Vị trí",
        taxCode: "Mã số thuế",
        headOffice: "Trụ sở chính",
        representativeName: "Tên người đại diện",
        contactAddress: "Địa chỉ liên hệ",
        email: "Thư điện tử",
        timeSent: "Thời gian gửi",
        timeSentFrom: "Thời gian gửi từ",
        timeSentTo: "Thời gian gửi đến",
        cmdCode: "Mã lệnh",
        cmdName: "Tên",
        cmdType: "Loại lệnh",
        data: "Dữ liệu",
        status: "Trạng thái",
        sampleTime:"Thời gian lấy mẫu",
        consumptionValue: "Giá trị tiêu thụ (m3)",
        pressure: "Áp suất(MPa)",
        batteryVoltage: "Điện áp pin (V)",
        valveStatus: "Trạng thái van",
        gSMSignalStrength: "Cường độ tín hiệu GSM",
        historyAlert: "Lịch sử cảnh báo",
        currentMonthlyFlow: "Lưu lượng tháng hiện tại (m3)",
        estimatedCost: "Chi phí ước tính (VNĐ)",
        sampleValue: "Giá trị tiêu thụ nước (m3)",
        csq: "Cường độ tín hiệu GSM",
        reportTime: "Thời gian gửi báo cáo",
        detailId: "Mã chi tiết bản ghi",
        alertName: "Tên cảnh báo",
        alertType: "Loại cảnh báo",
        alertEmail: "Email nhận cảnh báo",
        alertContent: "Nội dung cảnh báo",
        alertTime: "Thời gian",
        alertStatus: "Trạng thái",
        deviceLocation: "Vị trí thiết bị",
        dashboard: "Dashboard",
        sendCommmad: "Gửi lệnh tới thiết bị",
        optionCommand: "Loại lệnh gửi",
    },
    input: {
        imei: "Nhập IMEI/SERIAL",
        address: "Nhập địa chỉ lắp đặt",
        subcriber: "Nhập số điện thoại",
        type: "Nhập loại thiết bị",
        msisdn: "Nhập SĐT trên thiết bị",
        name: "Nhập tên thiết bị",
        serial: "Nhập serial",
        individualname: "Nhập tên khách hàng",
        businessName: "Nhập tên doanh nghiệp",
        lastConnected: "Nhập thời gian kết nối gần nhất",
        statusConnect: "Nhập trạng thái kết nối",
        info: "Nhập thông tin thiết bị",
        infoMngt: "Nhập thông tin quản lý",
        businessMngt: "Chọn Doanh nghiệp",
        individualMngt: "Chọn khách hàng cá nhân",
        manufacturer: "Nhập nhà sản xuất",
        description: "Nhập mô tả",
        model: "Nhập mẫu thiết bị",
        location: "Nhập để tìm kiếm và lưu vị trí",
        optionCommand: "Chọn loại lệnh muốn gửi tới thiết bị",
    },
    text: {
        messageSuccess: "Lưu thành công",
        textResultImportByFile: "Danh sách đăng ký lỗi đang được tải về",
        wrongFormat: "Tệp phải là excel (xlsx)",
        tooBig: "Tập tin quá lớn",
        columnInvalid: "file không đúng định dạng",
        msisdnEmpty: "Số điện thoại không được để trống",
        msisdnNotExists: "Số điện thoại không tồn tại",
        msisdnAssign: "Số điện thoại đã được gán cho thiết bị khác",
        msisdnInvalid: "Số điện thoại phải là số có đầu 84 (11-12 ký tự)",
        msisdnIsEmptly: "Số điện thoại không được để trống",
        msisdnIsDuplicate: "Số điện thoại trùng trong file",
        imeiIsDuplicate: "IMEI trùng trong file",
        expriredDateInvalid: "Ngày hết hạn phải có định dạng dd/mm/yyyy",
        msisdnNotPermission: "Số điện thoại không tồn tại hoặc không có quyền trên số điện thoại",
        imeiIsExist: "IMEI đã được gán cho thiết bị khác",
        maxRowImport: "File không được quá 1000 dòng",
        imeiLen: "IMEI không hợp lệ. Vui lòng nhập từ 2 đến 64 ký tự không bao gồm ký tự đặc biệt",
        deviceTypeLen: "DEVICE TYPE không hợp lệ. Vui lòng nhập từ 2 đến 64 ký tự không bao gồm ký tự đặc biệt",
        countryLen: "MADEIN không hợp lệ. Vui lòng nhập từ 2 đến 32 ký tự không bao gồm ký tự đặc biệt",
        notFoundLocation: "Không tìm thấy địa điểm",
        exception: "Đã gặp lỗi",
        errorInputShema: "Không lấy được thông tin điều khiển thiết bị",
        sendCommandSucces: "Gửi lệnh thành công",
        oneDevice: "Mỗi tài khoản khách hàng chỉ có thể tạo một thiết bị",
        storedCommand: "Lệnh đã được lưu trữ",
    },
    button: {
      sendCommand: "Gửi lệnh",
    },
    status: {
        registered: "Đăng ký",
        connected: "Đang Kết nối",
        lostConnection: "Mất kết nối",
    },
    statusCmd: {
        archived: "Lưu trữ",
        submitted: "Đã gửi",
        processed: "Đã xử lý",
        rejected: "Từ chối",
        conflict: "Xung đột",
        accepted: "Đã chấp nhận",
    },
}
