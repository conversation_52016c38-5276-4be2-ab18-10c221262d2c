import {Component, Inject, Injector, OnInit} from "@angular/core";
import { FormBuilder } from "@angular/forms";
import { ActivatedRoute, Router } from "@angular/router";
import { MenuItem } from "primeng/api";
import { MessageCommonService } from "src/app/service/comon/message-common.service";
import { TranslateService } from "src/app/service/comon/translate.service";
import { UtilService } from "src/app/service/comon/util.service";
import { PermissionService } from "src/app/service/permission/permission.service";
import { ColumnInfo, OptionTable } from "../common-module/table/table.component";
import {ComponentBase} from "../../component.base";

@Component({
    selector: "app-permission-list",
    templateUrl: "./app.permission.list.component.html"
})
export class PermissionListComponent extends ComponentBase implements OnInit{
    searchInfo: {
        objectKey: string | null,
        permissionName: string | null,
    }
    listObjectKey: Array<any>;
    items: MenuItem[];
    home: MenuItem;
    selectItems: Array<any>;
    columns: Array<ColumnInfo>;
    dataSet: {
        content: Array<any>,
        total: number
    };
    optionTable: OptionTable;
    pageNumber: number;
    pageSize: number;
    sort: string;
    formSearchPermission: any;
    listOriginData: Array<any> = [];
    listFilter: Array<any>;
    constructor(
                @Inject(PermissionService) private permissionService: PermissionService,
                private formBuilder: FormBuilder,
                private injector: Injector)
    {
        super(injector);
    }

    ngOnInit(): void {
        let me = this;
        this.items = [{ label: this.tranService.translate("global.menu.accountmgmt") }, { label: this.tranService.translate("global.menu.listpermission") }];
        this.home = { icon: 'pi pi-home', routerLink: '/' };
        this.searchInfo = {
            objectKey: null,
            permissionName: null,
        }

        this.formSearchPermission = this.formBuilder.group(this.searchInfo);
        this.selectItems = [];
        this.pageNumber = 0;
        this.pageSize = 10;
        this.sort = "objectName,asc",

        this.optionTable = {
            hasClearSelected: true,
            hasShowChoose: false,
            hasShowIndex: true,
            hasShowToggleColumn: false
        }
        this.columns = [
            {
                name: this.tranService.translate("account.label.permission.name"),
                key: "permissionName",
                size: "55%",
                align: "left",
                isShow: true,
                isSort: true
            },
            {
                name: this.tranService.translate("account.label.permission.object"),
                key: "objectName",
                size: "45%",
                align: "left",
                isShow: true,
                isSort: true,
            },
        ]
        this.prepareData();
    }

    onSubmitSearch(){
        this.pageNumber = 0;
        this.search(this.pageNumber, this.pageSize, this.sort, this.searchInfo);
    }

    prepareData(){
        this.listObjectKey = [];
        let me = this;
        this.messageCommonService.onload();
        this.permissionService.search({}, this.handleData.bind(this), null, ()=>{
            me.messageCommonService.offload();
        })
    }

    handleData(response){
        let me = this;
        let listObjKey = [];
        this.listOriginData = response.map(el => {
            if(!listObjKey.includes(el.objectKey)){
                listObjKey.push(el.objectKey);
                this.listObjectKey.push({
                    name: me.tranService.translate(`permission.${el.objectKey}.${el.objectKey}`, null, el.objectKey),
                    value: el.objectKey
                })
            }
            return {
                ...el,
                objectName: me.tranService.translate(`permission.${el.objectKey}.${el.objectKey}`, null, el.objectKey),
                permissionName: me.tranService.translate(`permission.${el.objectKey}.${el.permissionKey}`, null, el.description)
            }
        });
        this.listObjectKey = this.listObjectKey.sort((a,b) => me.convertTextViToEnUpperCase(a.name) > me.convertTextViToEnUpperCase(b.name) ? 1 : -1);
        // console.log(this.listObjectKey);
        this.search(this.pageNumber, this.pageSize, this.sort, this.searchInfo);
    }

    search(page, size, sort, params){
        let me = this;
        this.messageCommonService.onload();
        setTimeout(function(){
            me.messageCommonService.offload();
        }, 500);
        this.pageNumber = page;
        this.pageSize = size;
        this.sort = sort;
        this.listOriginData = this.listOriginData.sort((a, b) =>{
            let fieldSort = me.sort.split(',')[0];
            let direction = me.sort.split(',')[1];
            return a[fieldSort].toUpperCase().localeCompare(b[fieldSort].toUpperCase()) == 1  ? ((direction == 'asc')?1:-1) : ((direction == 'asc')?-1:1)
        });
        this.listFilter = this.listOriginData.filter(el => {
            let okObjectKey = me.searchInfo.objectKey == null || (el.objectKey == me.searchInfo.objectKey);
            let okPermissionName = me.searchInfo.permissionName == null || me.searchInfo.permissionName.trim() == '' || this.checkSearchEnVi(el.permissionName, me.searchInfo.permissionName);
            return okObjectKey && okPermissionName;
        })
        this.dataSet = {
            content: this.listFilter.slice(page*size, page*size+size),
            total: this.listFilter.length
        }
    }
    convertTextViToEnUpperCase(text:string):string{
        text = text.toUpperCase();
        let result = "";
        for(let i = 0;i<text.length;i++){
            if(["A","Á","À","Ã","Ả","Ạ","Ă","Ắ","Ằ","Ẳ","Ẵ","Ặ","Â","Ấ","Ầ","Ẩ","Ẫ","Ậ"].includes(text.charAt(i).toString())){
                result += "A";
            }else if(["E","Ê","È","Ề","É","Ế","Ẻ","Ể","Ẽ","Ễ","Ẹ","Ệ"].includes(text.charAt(i).toString())){
                result += "E"
            }else if(["I","Ì","Í","Ỉ","Ĩ","Ị"].includes(text.charAt(i).toString())){
                result += "I"
            }else if(["O","Ô","Ơ","Ò","Ồ","Ờ","Ó","Ố","Ớ","Ỏ","Ổ","Ở","Õ","Ỗ","Ỡ","Ọ","Ộ","Ợ"].includes(text.charAt(i).toString())){
                result += "O"
            }else if(["U","Ư","Ù","Ừ","Ú","Ứ","Ủ","Ử","Ũ","Ữ","Ụ","Ự"].includes(text.charAt(i).toString())){
                result += "U"
            }else if(["Y","Ỳ","Ý","Ỷ","Ỹ","Ỵ"].includes(text.charAt(i).toString())){
                result += "Y"
            }else if(["D","Đ"].includes(text.charAt(i).toString())){
                result += "D";
            }else{
                result += text.charAt(i).toString();
            }
        }
        return result;
    }
    checkSearchEnVi(text, search){
        search = search.toUpperCase();
        if(this.tranService.lang == 'vi'){
            let searchVi = this.convertTextViToEnUpperCase(search);
            if(searchVi == search){
                return this.convertTextViToEnUpperCase(text).indexOf(searchVi) >= 0;
            }else{
                return text.toUpperCase().indexOf(search) >= 0;
            }
        }else{
            return text.toUpperCase().indexOf(search) >= 0;
        }
    }
}
