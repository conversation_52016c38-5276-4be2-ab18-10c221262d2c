export default {
    label: {
        createdDate : "Created date",
        username : "<PERSON>rname",
        ip : "IP",
        actionType : "Action Type",
        module : "Module",
        affectedField : "The data field is affected",
        viewAccount : "View account",
        detail: "Detail",
    },
    menu : {
        log : 'View activity log'
    },
    actionType: {
        search : "Search",
        create : "Create",
        update : "Update",
        delete : "Delete"
    },
    objectKey: {
        user : "User",
        customer : "Customer",
        contract : "Contract",
        sim : "Subscription",
        report : "Report"
    }
}
