import { Component } from '@angular/core';
import { Router } from '@angular/router';
import { TranslateService } from 'src/app/service/comon/translate.service';
import {MessageCommonService} from "../../../service/comon/message-common.service";

@Component({
    selector: 'app-access',
    templateUrl: './access.component.html',
})
export class AccessComponent {
    constructor(public tranService: TranslateService, private router: Router,
    protected messageCommonService: MessageCommonService
    ) {

    }

    goToHome(){
        // setTimeout(function(){
        //     window.location.hash = "/"
        // })
        let me = this;
        this.messageCommonService.onload();
        setTimeout(()=>{
            localStorage.clear();
            me.messageCommonService.offload();
            window.location.href = "/#/login";
        }, 500)
    }
}
