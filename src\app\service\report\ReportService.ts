import { Inject, Injectable } from "@angular/core";
import { HttpService } from "../comon/http.service";

@Injectable()
export class ReportService {
    private prefixApi: string;
    constructor(@Inject(HttpService) private httpService:HttpService) {
        this.prefixApi = "/report";
    }

    public createGeneralReportDynamic(data, callback, errorCallback?:Function, finallyCallback?: Function){
        this.httpService.post(`${this.prefixApi}`,{}, data, {}, callback, errorCallback, finallyCallback);
    }

    public updateGeneralReportDynamic(id, data, callback, errorCallback?:Function, finallyCallback?: Function){
        this.httpService.put(`${this.prefixApi}/${id}`,{}, data, {}, callback, errorCallback, finallyCallback);
    }

    public updateSummaryReportDynamic(id, data, callback){
        this.httpService.put(`${this.prefixApi}/schedule/${id}`,{}, data, {}, callback);
    }

    public updateSendingReportDynamic(id, data, callback){
        this.httpService.put(`${this.prefixApi}/sending/${id}`,{}, data, {}, callback);
    }

    public getDetailReportDynamic(id, callback, errorCallback?:Function, finallyCallback?: Function){
        this.httpService.get(`${this.prefixApi}/${id}`, {}, {}, callback, errorCallback, finallyCallback);
    }

    public checkExistNameReportDynamic(name, callback){
        this.httpService.get(`${this.prefixApi}/checkExits`, {}, {name}, callback);
    }

    public getAllEmailGroup(callback){
        this.httpService.get(`${this.prefixApi}/email-group/all`, {}, {}, callback);
    }

    public preview(data, callback, errorCallback?:Function, finallyCallback?: Function){
        this.httpService.post(`${this.prefixApi}/preview`, {timeout: 120000}, data, {}, callback,errorCallback, finallyCallback);
    }

    public exportFile(data, callback?:Function, errorCallback?:Function){
        this.httpService.downloadPostForReporting(`${this.prefixApi}/export`, {timeout: 900000}, data, {}, callback, errorCallback);
    }
}
