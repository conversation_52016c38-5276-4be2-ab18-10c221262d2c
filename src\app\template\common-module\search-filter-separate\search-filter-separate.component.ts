import { UtilService } from './../../../service/comon/util.service';
import { AfterContentChecked, Component, EventEmitter, Input, OnInit, Output, SimpleChanges } from '@angular/core';
import { ColumnInfo } from '../table/table.component';
import { TranslateService } from 'src/app/service/comon/translate.service';
import {MessageCommonService} from "../../../service/comon/message-common.service";

export interface SeperateSearchInfo{
  name: string, // tên hiển thị
  key: string, // key gửi api
}

export enum FilterInputType{
  input = 0, // ô nhập liệu
  dropdown = 1, // ô chọn giá trị
  multiselect = 2, // ô chọn nhiều giá trị
  calendar = 3, // ngày
  rangeCalendar = 4 // khoảng ngày
}

export enum SearchType{
  bodySearch = 0, // tìm kiếm kiểu post onedx
  paramSearch = 1 //tìm kiếm params
}

export interface SeperateFilterInfo{
  name: string; // tên hiển thị
  key ?: any; // key gửi xuống
  type: FilterInputType; // loại input
  items?: Array<{name: string, value: string|number}>; // item của input (select, multiselect)
  itemFilter ?: boolean; // ô search của item (select, multiselect)
  unixTime ?:boolean; // gửi giá trị số unix (calendar,rangeCalender)
  unixTimeString?: boolean;// gửi giá trị chuỗi unix (calendar, rangeCalendar)
}

interface FilterInfoDetail extends SeperateFilterInfo{
  mark?:number;
}

export interface Pagination{
  page:number,
  size:number,
  sort:string
}

export function dateToUnixTimestampMilliseconds(date: Date): number {
  // Convert the Date object to Unix timestamp (milliseconds)
  const unixTimestampMilliseconds = date.getTime();
  return unixTimestampMilliseconds;
}

export function dateToStringTimestampMilliseconds(date: Date): string {
    // Convert the Date object to Unix timestamp (milliseconds)
    const unixTimestampMilliseconds = date.getTime().toString();
    return unixTimestampMilliseconds;
}

@Component({
  selector: 'search-filter-separate',
  templateUrl: './search-filter-separate.component.html',
  styleUrls: ['./search-filter-separate.component.scss']
})
export class SearchFilterSeparateComponent implements OnInit, AfterContentChecked{

  @Input() searchList!: Array<SeperateSearchInfo>; //danh sách trong ô tìm kiếm
  @Input() filterList!: Array<SeperateFilterInfo>; //danh sách trong ô bộ lọc
  @Input() searchType : SearchType = SearchType.bodySearch; // kiểu tìm kiếm

  @Output() searchDetail = new EventEmitter<any>(); // trả về json object search

  listSearchItems: Array<string>;
  filterNumber: number;
  usedFilterNumber: number=0;
  selectedFilterList: Array<FilterInfoDetail> = [];
  remainFilterList: Array<FilterInfoDetail> = [];
  filterCount: any[] = [{}];
  inputType: Array<number>;
  isChildFilter :Array<boolean>;


  //ngModel
  searchInputValue : string;
  filterModel: Array<any>;
  dayModel: Array<any>;
  parentFilterModel: Array<any>;

  constructor(public translateService: TranslateService, public utilService : UtilService, protected messageCommonService: MessageCommonService){}

  ngOnInit(): void {
      this.filterNumber = this.filterList?.length;
      this.selectedFilterList = []
      this.remainFilterList = this.filterList
      this.inputType = Array.from({ length: this.filterNumber }, () => -1);
      this.isChildFilter = Array.from({ length: this.filterNumber }, () => false);
      this.filterModel = Array.from({ length: this.filterNumber }, () => null);
      this.dayModel = Array.from({ length: this.filterNumber }, () => []);
      this.parentFilterModel = Array.from({ length: this.filterNumber }, () => []);
      this.listSearchItems = this.searchList?.map(item => item.key);
  }

  ngOnChanges(changes: SimpleChanges): void {
    // Nếu có sự thay đổi trong biến searchList
    if (changes['searchList']) {
      // Khởi tạo lại giá trị cho listSearchItems
      this.initListSearchItems();
    }
  }

  // Hàm khởi tạo giá trị cho listSearchItems
  private initListSearchItems(): void {
    // Kiểm tra nếu searchList không phải undefined và có độ dài lớn hơn 0
    if (this.searchList && this.searchList.length > 0) {
      // Gán giá trị cho listSearchItems từ searchList
      this.listSearchItems = this.searchList.map(item => item.key);
    }
  }


  ngAfterContentChecked(): void {

  }

  filterSearch(){
    this.filterNumber = this.filterNumber + 1;
  }

  listSearchChanged(event){
  }

  searchInputChange(){
  }

  search(){
    const bodySearch = {};
    const setBodySearch = (key, value) => {
        bodySearch[key] = value;
    };

    const setSearchValue = (key, value) => {
        if (value !== null && value !== undefined) {
            setBodySearch(key, value);
        }
    };

    if (this.listSearchItems.length === 0){
        this.messageCommonService.warning(this.translateService.translate("datapool.message.errorSearch"))
        return
    }

    if (this.searchType === SearchType.bodySearch) {
        this.searchList.forEach(item => {
            setBodySearch(item.key, 0);
        });
        if(this.searchInputValue){
          setSearchValue("value", this.searchInputValue.trim());
        } else {
          setSearchValue("value", "");
        }
        if (this.listSearchItems && this.searchInputValue) {
            this.listSearchItems.forEach(item => {
                setBodySearch(item, 1);
            });
        }
    } else {
        if (this.listSearchItems && this.searchInputValue) {
            this.listSearchItems.forEach(item => {
                setBodySearch(item, this.searchInputValue.trim());
            });
        }
    }

    this.selectedFilterList?.forEach(item => {
        const { mark, type, unixTime, unixTimeString } = item;
        const filterValue = this.filterModel[mark];

        if (type === FilterInputType.calendar) {
            this.dayModel[mark] = this.convertCalendarValue(filterValue, unixTime, unixTimeString);
        } else if (type === FilterInputType.rangeCalendar) {
            this.dayModel[mark] = this.convertRangeCalendarValue(filterValue, unixTime, unixTimeString);
        }
    });

    this.selectedFilterList?.forEach(filter => {
        const value = this.filterModel[filter.mark];
        if (filter.mark !== undefined && value !== undefined) {
            if (filter.type === FilterInputType.calendar) {
                setBodySearch(filter.key, this.dayModel[filter.mark]);
            } else if (filter.type === FilterInputType.rangeCalendar) {
                const [start, end] = this.dayModel[filter.mark];
                setSearchValue(filter.key[0], start);
                setSearchValue(filter.key[1], end);
            } else {
                setBodySearch(filter.key, value);
            }
        }
    });
    this.searchDetail.emit(bodySearch)
  }

    convertCalendarValue(value, unixTime, unixTimeString) {
        if (value === null || value === undefined) {
            return null;
        }

        let convertedValue = this.utilService.convertDateToString(value);

        if (unixTime) {
            return dateToUnixTimestampMilliseconds(value);
        } else if (unixTimeString){
            convertedValue = dateToStringTimestampMilliseconds(value);
        }

        return convertedValue;
    }

    convertRangeCalendarValue(value, unixTime, unixTimeString) {
        if (value === null || value === undefined) {
            return [null, null];
        }

        let [start, end] = value.map(date => this.utilService.convertDateToString(date));

        if (unixTime) {
            if (value[0]) {
                start = dateToUnixTimestampMilliseconds(value[0]);
            }
            if (value[1]) {
                end = dateToUnixTimestampMilliseconds(value[1]);
            }
        } else if (unixTimeString) {
            if (value[0]) {
                start = dateToStringTimestampMilliseconds(value[0]);
            }
            if (value[1]) {
                end = dateToStringTimestampMilliseconds(value[1]);
            }
        }

        return [start, end];
    }

  deleteRow(idx){
      // Xóa phần tử tại vị trí idx trong mảng filterCount
      this.filterCount.splice(idx, 1);

      // Cập nhật lại các mảng liên quan khác
      this.selectedFilterList = this.selectedFilterList.filter(item => item.mark !== idx);
      this.filterModel.splice(idx, 1);
      this.inputType.splice(idx, 1);
      this.isChildFilter.splice(idx, 1);
      this.dayModel.splice(idx, 1);
      this.parentFilterModel.splice(idx, 1);

      // Cập nhật lại chỉ số mark trong selectedFilterList
      this.selectedFilterList.forEach((item, index) => {
          if (item.mark > idx) {
              item.mark = item.mark - 1;
          }
      });

      this.usedFilterNumber = this.filterModel.filter(value => value !== null && value !== "").length;
    this.search()
  }

  addFilterItem(){
    this.filterCount.push({})
    this.inputType.push(-1);
  }

  resetFilterItems(){
    this.filterCount = [{}];
    this.selectedFilterList = [];
    this.inputType = Array.from({ length: this.filterNumber }, () => -1);
    this.filterModel = Array.from({ length: this.filterNumber }, () => null);
    this.dayModel = Array.from({ length: this.filterNumber }, () => []);
    this.parentFilterModel = Array.from({ length: this.filterNumber }, () => [])
    this.usedFilterNumber = this.filterModel?.filter(value => value !== null && value !== "").length
    this.search()
  }

  onChangeSelectFilters(event, index){
    if(this.filterModel[index]){
      this.filterModel[index] = null
    }
    let selectedValue = this.filterList.filter(item=> item.key === event)[0];
    let found = false;
    this.selectedFilterList = this.selectedFilterList?.map(item => {
      if (item.mark === index) {
        found = true;
        let newSelectedValue: FilterInfoDetail = { ...selectedValue };
        newSelectedValue.mark = index
        return newSelectedValue;
      } else {
        return item;
      }
    });
    if (!found) {
      let newSelectedValue: FilterInfoDetail = { ...selectedValue };
      newSelectedValue.mark = index
      this.selectedFilterList?.push(newSelectedValue)
    }
    this.inputType[index] = selectedValue.type;
    if(selectedValue.itemFilter){
      this.isChildFilter[index] = selectedValue.itemFilter
    }
  }

  filterOption(index){
    const exception = this.selectedFilterList?.filter(item => item.mark == index)[0];
    let filteredList = this.filterList.filter(filter => !this.selectedFilterList?.some(selectedFilter => selectedFilter.name === filter.name));
    if(exception){
      const excName = exception.name
      let indexForArray = this.filterList.findIndex(item=> item.name == excName)
      filteredList.splice(indexForArray, 0, exception);
    }
    return filteredList
  }

  childOption(idx: number): {name: string, value: string|number}[] {
    const selectedFilter = this.selectedFilterList?.filter(item=>item.mark == idx)[0];
    if (selectedFilter && selectedFilter.items) {
        return selectedFilter.items;
    }
    return [];
  }

  filterChange(index){
    this.usedFilterNumber = this.filterModel.filter(value => {
      // Check for empty array using Array.isArray() and length
      if (Array.isArray(value) && value.length === 0) {
        return false;
      }

      // Check for null and empty string values
      return value !== null && value !== "";
    }).length;
    this.search()
  }

  onTodayCalendar(idx){
    this.usedFilterNumber = this.filterModel.filter(value => {
      // Check for empty array using Array.isArray() and length
      if (Array.isArray(value) && value.length === 0) {
        return false;
      }

      // Check for null and empty string values
      return value !== null && value !== "";
    }).length;
    this.search()
  }

  onClearCalendar(idx){
    this.usedFilterNumber = this.filterModel.filter(value => {
      // Check for empty array using Array.isArray() and length
      if (Array.isArray(value) && value.length === 0) {
        return false;
      }

      // Check for null and empty string values
      return value !== null && value !== "";
    }).length;
    this.search()
  }

  clearValue(idx){
      this.filterModel[idx] = []
      this.usedFilterNumber = this.filterModel.filter(value => {
          // Check for empty array using Array.isArray() and length
          if (Array.isArray(value) && value.length === 0) {
              return false;
          }

          // Check for null and empty string values
          return value !== null && value !== "";
      }).length;
      this.search()
  }

}
