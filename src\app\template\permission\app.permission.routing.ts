import { NgModule } from "@angular/core";
import { RouterModule } from "@angular/router";
import { PermissionListComponent } from "./app.permisstion.list.component";
import DataPage from "../../service/data.page";
import {CONSTANTS} from "../../service/comon/constants";

@NgModule({
    imports: [
        RouterModule.forChild([
            {path: "", component: PermissionListComponent, data: new DataPage("global.menu.listpermission", [CONSTANTS.PERMISSIONS.PERMISSION.VIEW_LIST])}
        ])
    ],
    exports:[
        RouterModule
    ]
})
export class PermissionRoutingModule {}
