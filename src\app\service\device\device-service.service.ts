import {Inject, Injectable} from "@angular/core";
import {HttpService} from "../comon/http.service";

@Injectable()
export class DeviceService {
    private prefixApi: string;

    constructor(@Inject(HttpService) private httpService: HttpService) {
        this.prefixApi = "/device";
    }

    public detailDevice(id: number, callback?: Function, errorCallback?: Function, finallyCallback?: Function) {
        this.httpService.get(`${this.prefixApi}/${id}`, {}, {}, callback, errorCallback, finallyCallback);
    }

    public getById(msisdn: number, callback?: Function, errorCallback?: Function, finallyCallback?: Function) {
        this.httpService.get(`${this.prefixApi}/${msisdn}`, {}, {}, callback, errorCallback, finallyCallback);
    }

    public getTelemetry(params: {
        [key: string]: any
    }, callback?: Function, errorCallback?: Function, finallyCallback?: Function) {
        this.httpService.get(`${this.prefixApi}/getTelemetry`, {}, params, callback, errorCallback, finallyCallback);
    }

    public getByKey(key: string, value: string, callback?: Function, errorCallback?: Function, finallyCallback?: Function) {
        this.httpService.get(`${this.prefixApi}/getByKey`, {}, {key, value}, callback, errorCallback, finallyCallback);
    }

    public createDevice(body, callback?: Function, errorCallback?: Function, finallyCallback?: Function) {
        this.httpService.post(`${this.prefixApi}`, {}, body, {}, callback, errorCallback, finallyCallback);
    }

    public updateDevice(id,body, callback?: Function, errorCallback?: Function, finallyCallback?: Function) {
        this.httpService.put(`${this.prefixApi}/${id}`, {}, body, {}, callback, errorCallback, finallyCallback);
    }

    public search(params: {
        [key: string]: any
    }, callback?: Function, errorCallBack?: Function, finallyCallback?: Function) {
        this.httpService.get(`${this.prefixApi}/search`, {}, params, callback, errorCallBack, finallyCallback);
    }

    public deleleDevice(id: number, callback?: Function, errorCallBack?: Function, finallyCallback?: Function) {
        this.httpService.delete(`${this.prefixApi}/${id}`, {}, {}, callback, errorCallBack, finallyCallback);
    }

    public getLocation(msisdn: number, callback?: Function, errorCallback?: Function, finallyCallback?: Function) {
        this.httpService.get(`/media/getLocation`, {}, {msisdn}, callback, errorCallback, finallyCallback);
    }

    public findCellId(params: {
        [key: string]: any
    }, callback?: Function, errorCallback?: Function, finallyCallback?: Function) {
        this.httpService.get(this.prefixApi + "/getLatLng", {}, params, callback, errorCallback, finallyCallback);
    }

    public findAddress(lat, lon, callback?: Function, errorCallback?: Function, finallyCallback?: Function) {
        this.httpService.findAddress(lat, lon, callback, errorCallback, finallyCallback);
    }
    public getUsage(body, callback?:Function, errorCallBack?:Function, finallyCallback?:Function){
        this.httpService.postNoError(`${this.prefixApi}/getUsageByDeviceId`,{}, body,{}, callback, errorCallBack, finallyCallback);
    }

    public checkExistsImeiDevice(imei: string,callback?:Function, errorCallBack?:Function, finallyCallback?:Function){
        this.httpService.get(this.prefixApi+"/checkImei/" +imei,{},{}, callback,errorCallBack,finallyCallback);
    }
}
