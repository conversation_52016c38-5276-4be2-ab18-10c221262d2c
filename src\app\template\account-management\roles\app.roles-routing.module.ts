import { NgModule } from '@angular/core';
import { RouterModule } from '@angular/router';
import { AppRolesListComponent } from './list/app.roles.list.component';
import {AppRolesCreateComponent} from "./create/app.roles.create.component";
import {AppRolesEditComponent} from "./edit/app.roles.edit.component";
import DataPage from "../../../service/data.page";
import {CONSTANTS} from "../../../service/comon/constants";

@NgModule({
  imports: [
      RouterModule.forChild([
          {path: "", component: AppRolesListComponent, data: new DataPage("global.menu.listroles", [CONSTANTS.PERMISSIONS.ROLE.VIEW_LIST])},
          {path: "create", component: AppRolesCreateComponent, data: new DataPage("global.titlepage.createRole", [CONSTANTS.PERMISSIONS.ROLE.CREATE])},
          {path: "edit/:id", component: AppRolesEditComponent, data: new DataPage("global.menu.editroles", [CONSTANTS.PERMISSIONS.ROLE.UPDATE])},
      ])
  ],
  exports: [RouterModule]
})
export class AppRolesRoutingModule { }
