<div
    class="vnpt flex flex-row justify-content-between align-items-center bg-white p-2 border-round"
>
    <div class="">
        <div class="text-xl font-bold mb-1">{{tranService.translate("customer.label.listCustomer")}}</div>
        <p-breadcrumb class="max-w-full col-7" [model]="items" [home]="home"></p-breadcrumb>
    </div>
    <div>
        <button pButton class="p-button-outlined p-button-secondary" (click)="goToDetailAccount()" *ngIf="getShowViewAccount()">{{tranService.translate("customer.label.viewAccount")}}</button>
    </div>
</div>
<form *ngIf="updateCustomerForm" action="" [formGroup]="updateCustomerForm" (submit)="submitForm()">
    <div class="card my-3">
        <div class="grid">
            <div class="col-3">
                <div class="flex flex-column gap-2">
                    <label htmlFor="customerCode">{{tranService.translate("customer.label.customerCode")}}<span class="text-red-500">*</span></label>
                    <input pInputText formControlName="customerCode" id="customerCode"/>
                </div>
            </div>
            <div class="col-3">
                <div class="flex flex-column gap-2">
                    <label htmlFor="taxCode">{{tranService.translate("customer.label.taxCode")}}</label>
                    <input class="m-0" pInputText formControlName="taxId" id="taxCode"/>
<!--                    <div *ngIf="isTaxIdValid && updateCustomerForm.get('taxId').hasError('required')" class="text-red-500">-->
<!--                        {{this.tranService.translate("customer.error.required")}}-->
<!--                    </div>-->
                    <div *ngIf="isTaxIdValid && (updateCustomerForm.get('taxId').hasError('maxlength')||updateCustomerForm.get('taxId').hasError('minlength'))" class="text-red-500">
                        {{this.tranService.translate("customer.error.length")}}
                    </div>
                    <div *ngIf="isTaxIdValid && updateCustomerForm.get('taxId').hasError('invalidCharacters')" class="text-red-500">
                        {{this.tranService.translate("customer.error.regular")}}
                    </div>
                </div>
            </div>
            <div class="col-3">
                <div class="flex flex-column gap-2">
                    <label htmlFor="provinceCode">{{tranService.translate("customer.label.provinceCode")}}</label>
                    <input pInputText formControlName="provinceCode" id="provinceCode"/>
                </div>
            </div>
            <div class="col-3">
                <div class="flex flex-column gap-2">
                    <label for="type">{{tranService.translate("customer.label.type")}}</label>
                    <p-dropdown styleClass="w-full"
                            id="type" [autoDisplayFirst]="false"
                            formControlName="customerType"
                            [options]="typeList"
                            optionLabel="name"
                            optionValue="value"
                    ></p-dropdown>
                </div>
            </div>
            <div class="col-3">
                <div class="flex flex-column gap-2">
                    <label for="status">{{tranService.translate("customer.label.status")}}</label>
                    <p-dropdown styleClass="w-full" [showClear]="true"
                            id="status" [autoDisplayFirst]="false"
                            formControlName="status"
                            [options]="statusList"
                            optionLabel="name"
                            optionValue="value"
                    ></p-dropdown>
                </div>
            </div>
            <!-- <div class="col-3 flex justify-content-center align-items-center" style="margin-top: 25px;">
                <button pButton class="p-button-outlined p-button-secondary">{{tranService.translate("customer.label.viewAccount")}}</button>
            </div> -->
            <!-- <div class="col-3 flex justify-content-center align-items-center">
                <button pButton>Cập nhật</button>
            </div>
            <div class="col-3 flex justify-content-center align-items-center">
                <button pButton class="p-button-outlined p-button-secondary">Quay lại</button>
            </div> -->
        </div>
    </div>
    <div class="card flex justify-content-center mb-3">
        <div class="grid w-full">
            <div class="col-6">
                <p-panel [header]="generalHeader" [toggleable]="true" styleClass="w-full">
                    <div class="field grid flex flex-row flex-nowrap pb-0 mb-0">
                        <label htmlFor="companyName"  style="min-width: 130px;" class="col-12 mb-2 md:col-2 md:mb-0">{{tranService.translate("customer.label.companyName")}}<span class="text-red-500">*</span></label>
                        <div class="col-12 md:col-10 flex-1 flex">
                            <input pInputText formControlName="customerName" id="companyName" type="text" class="flex-1"/>
                        </div>
                    </div>
                    <div class="field grid flex flex-row flex-nowrap">
                        <div style="min-width: 130px;" class="col-12 mb-2 md:col-2 md:mb-0"></div>
                        <div class="col-12 md:col-10 flex-1 flex">
                            <div *ngIf="isCustomerNameValid && updateCustomerForm.get('customerName').hasError('required')" class="text-red-500">
                                {{this.tranService.translate("customer.error.required")}}
                            </div>
                            <div *ngIf="isCustomerNameValid && (updateCustomerForm.get('customerName').hasError('maxlength')||updateCustomerForm.get('customerName').hasError('minlength'))" class="text-red-500">
                                {{this.tranService.translate("customer.error.length")}}
                            </div>
                            <div *ngIf="isCustomerNameValid && updateCustomerForm.get('customerName').hasError('invalidCharacters')" class="text-red-500">
                                {{this.tranService.translate("customer.error.character")}}
                            </div>
                        </div>
                    </div>
                    <div class="field grid flex flex-row flex-nowrap mb-0">
                        <label htmlFor="phoneNumber"  style="min-width: 130px;" class="col-12 mb-2 md:col-2 md:mb-0">{{tranService.translate("customer.label.phoneNumber")}}</label>
                        <div class="col-12 md:col-10 flex-1 flex">
                            <div class="p-inputgroup flex-1 flex">
                                <span class="p-inputgroup-addon" style="border-radius: 12;">+84</span>
                                <input type="text" pInputText formControlName="phone" style="border-radius: 12;" id="phoneNumber" class="flex-1"/>
                            </div>
                        </div>
                    </div>
                    <div class="field grid flex flex-row flex-nowrap">
                        <div style="min-width: 130px;" class="col-12 mb-2 md:col-2 md:mb-0"></div>
                        <div class="col-12 md:col-10 flex-1 flex">
                            <div *ngIf="updateCustomerForm.get('phone').hasError('pattern')" class="text-red-500">
                                {{this.tranService.translate("global.message.invalidPhone")}}
                            </div>
                        </div>
                    </div>
                    <div class="field grid flex flex-row flex-nowrap mb-0">
                        <label htmlFor="email"  style="min-width: 130px;" class="col-12 mb-2 md:col-2 md:mb-0">{{tranService.translate("customer.label.email")}}</label>
                        <div class="col-12 md:col-10 flex-1 flex">
                            <input pInputText formControlName="email" id="email" type="email" class="flex-1"/>
                        </div>
                    </div>
                    <div class="field grid flex flex-row flex-nowrap">
                        <div style="min-width: 130px;" class="col-12 mb-2 md:col-2 md:mb-0"></div>
                        <div class="col-12 md:col-10 flex-1 flex">
                            <div *ngIf="isEmailValid && updateCustomerForm.get('email').hasError('email')" class="text-red-500">
                                {{this.tranService.translate("global.message.invalidEmail")}}
                            </div>
                            <div *ngIf="isEmailValid && (updateCustomerForm.get('email').hasError('maxlength')||updateCustomerForm.get('email').hasError('minlength'))" class="text-red-500">
                                {{this.tranService.translate("customer.error.length")}}
                            </div>
                            <div *ngIf="isEmailValid && updateCustomerForm.get('email').hasError('invalidCharacters')" class="text-red-500">
                                {{this.tranService.translate("customer.error.regular")}}
                            </div>
                        </div>
                    </div>
                    <div class="field grid flex flex-row flex-nowrap">
                        <label htmlFor="birthday"  style="min-width: 130px;" class="col-12 mb-2 md:col-2 md:mb-0">{{tranService.translate("customer.label.birthday")}}</label>
                        <div class="col-12 md:col-10 flex-1 flex">
                            <p-calendar styleClass="w-full" formControlName="birthday" id="birthday" type="text" class="flex-1"/>
                        </div>
                    </div>
                </p-panel>
            </div>
            <div class="col-6">
                <p-panel [header]="contactHeader" [toggleable]="true" styleClass="w-full">
                    <div class="field grid flex flex-row flex-nowrap mb-0">
                        <label htmlFor="fullName"  style="min-width: 130px;" class="col-12 mb-2 md:col-2 md:mb-0">{{tranService.translate("customer.label.fullName")}}</label>
                        <div class="col-12 md:col-10 flex-1 flex">
                            <input pInputText formControlName="billName" id="fullName" type="text" class="flex-1"/>
                        </div>
                    </div>
                    <div class="field grid flex flex-row flex-nowrap">
                        <div style="min-width: 130px;" class="col-12 mb-2 md:col-2 md:mb-0"></div>
                        <div class="col-12 md:col-10 flex-1 flex">
                            <div *ngIf="isBillNameValid && (updateCustomerForm.get('billName').hasError('maxlength')||updateCustomerForm.get('billName').hasError('minlength'))" class="text-red-500">
                                {{this.tranService.translate("customer.error.length")}}
                            </div>
                            <div *ngIf="isBillNameValid && updateCustomerForm.get('billName').hasError('invalidCharacters')" class="text-red-500">
                                {{this.tranService.translate("customer.error.regular")}}
                            </div>
                        </div>
                    </div>
                    <div class="field grid flex flex-row flex-nowrap mb-0">
                        <label htmlFor="phoneNumber"  style="min-width: 130px;" class="col-12 mb-2 md:col-2 md:mb-0">{{tranService.translate("customer.label.phoneNumber")}}</label>
                        <div class="col-12 md:col-10 flex-1 flex">
                            <div class="p-inputgroup flex-1 flex">
                                <span class="p-inputgroup-addon" style="border-radius: 12;">+84</span>
                                <input type="text" formControlName="billPhone" style="border-radius: 12;" pInputText id="phoneNumber" class="flex-1"/>
                            </div>
                        </div>
                    </div>
                    <div class="field grid flex flex-row flex-nowrap" >
                        <div style="min-width: 130px;" class="col-12 mb-2 md:col-2 md:mb-0"></div>
                        <div class="col-12 md:col-10 flex-1 flex">
                            <div *ngIf="updateCustomerForm.get('billPhone').hasError('pattern')" class="text-red-500">
                                {{this.tranService.translate("global.message.invalidPhone")}}
                            </div>
                        </div>
                    </div>
                    <div class="field grid flex flex-row flex-nowrap mb-0">
                        <label htmlFor="email"  style="min-width: 130px;" class="col-12 mb-2 md:col-2 md:mb-0">{{tranService.translate("customer.label.email")}}</label>
                        <div class="col-12 md:col-10 flex-1 flex">
                            <input pInputText formControlName="billEmail" id="email" type="email" class="flex-1"/>
                        </div>
                    </div>
                    <div class="field grid flex flex-row flex-nowrap">
                        <div style="min-width: 130px;" class="col-12 mb-2 md:col-2 md:mb-0"></div>
                        <div class="col-12 md:col-10 flex-1 flex">
                            <div *ngIf="isBillEmailValid && updateCustomerForm.get('billEmail').hasError('email')" class="text-red-500">
                                {{this.tranService.translate("global.message.invalidEmail")}}
                            </div>
                            <div *ngIf="isBillEmailValid && (updateCustomerForm.get('billEmail').hasError('maxlength')||updateCustomerForm.get('billEmail').hasError('minlength'))" class="text-red-500">
                                {{this.tranService.translate("customer.error.length")}}
                            </div>
                            <div *ngIf="isBillEmailValid && updateCustomerForm.get('billEmail').hasError('invalidCharacters')" class="text-red-500">
                                {{this.tranService.translate("customer.error.regular")}}
                            </div>
                        </div>
                    </div>
                    <div class="field grid flex flex-row flex-nowrap">
                        <label htmlFor="birthday"  style="min-width: 130px;" class="col-12 mb-2 md:col-2 md:mb-0">{{tranService.translate("customer.label.birthday")}}</label>
                        <div class="col-12 md:col-10 flex-1 flex">
                            <p-calendar styleClass="w-full" id="birthday" type="text" formControlName="billBirthday" class="flex-1"/>
                        </div>
                    </div>
                </p-panel>
            </div>
        </div>
    </div>

    <div class="card flex justify-content-center align-items-center flex-column">
    <div class="grid w-full">
        <div class="col-6">
            <p-panel [header]="paymentHeader" [toggleable]="true">
                <div class="field grid flex flex-row flex-nowrap mb-0">
                    <label htmlFor="street"  style="min-width: 130px;" class="col-12 mb-2 md:col-2 md:mb-0">{{tranService.translate("customer.label.street")}}</label>
                    <div class="col-12 md:col-10 flex-1 flex">
                        <input pInputText formControlName="addrStreet" id="street" type="text" class="flex-1"/>
                    </div>
                </div>
                <div class="field grid flex flex-row flex-nowrap">
                    <div style="min-width: 130px;" class="col-12 mb-2 md:col-2 md:mb-0"></div>
                    <div class="col-12 md:col-10 flex-1 flex">
                        <div *ngIf="isAddrStreetValid && (updateCustomerForm.get('addrStreet').hasError('maxlength')||updateCustomerForm.get('addrStreet').hasError('minlength'))" class="text-red-500">
                            {{this.tranService.translate("customer.error.length")}}
                        </div>
                        <div *ngIf="!(updateCustomerForm.get('addrStreet').hasError('maxlength')||updateCustomerForm.get('addrStreet').hasError('minlength')) && updateCustomerForm.get('addrStreet').hasError('invalidCharacters')" class="text-red-500">
                            {{this.tranService.translate("global.message.wrongFormatName")}}
                        </div>
                    </div>
                </div>
                <div class="field grid flex flex-row flex-nowrap mb-0">
                    <label htmlFor="district"  style="min-width: 130px;" class="col-12 mb-2 md:col-2 md:mb-0">{{tranService.translate("customer.label.district")}}</label>
                    <div class="col-12 md:col-10 flex-1 flex">
                        <input pInputText id="district" formControlName="addrDist" type="text" class="flex-1"/>
                    </div>
                </div>
                <div class="field grid flex flex-row flex-nowrap">
                    <div style="min-width: 130px;" class="col-12 mb-2 md:col-2 md:mb-0"></div>
                    <div class="col-12 md:col-10 flex-1 flex">
                        <div *ngIf="isAddrDistValid && (updateCustomerForm.get('addrDist').hasError('maxlength')||updateCustomerForm.get('addrDist').hasError('minlength'))" class="text-red-500">
                            {{this.tranService.translate("customer.error.length")}}
                        </div>
                        <div *ngIf="!(updateCustomerForm.get('addrDist').hasError('maxlength')||updateCustomerForm.get('addrDist').hasError('minlength')) && updateCustomerForm.get('addrDist').hasError('invalidCharacters')" class="text-red-500">
                            {{this.tranService.translate("customer.error.regular")}}
                        </div>
                    </div>
                </div>
                <div class="field grid flex flex-row flex-nowrap mb-0">
                    <label htmlFor="city"  style="min-width: 130px;" class="col-12 mb-2 md:col-2 md:mb-0">{{tranService.translate("customer.label.city")}}</label>
                    <div class="col-12 md:col-10 flex-1 flex">
                        <input pInputText formControlName="addrProvince" id="city" type="text" class="flex-1"/>
                    </div>
                </div>
                <div class="field grid flex flex-row flex-nowrap">
                    <div style="min-width: 130px;" class="col-12 mb-2 md:col-2 md:mb-0"></div>
                    <div class="col-12 md:col-10 flex-1 flex">
                        <div *ngIf="isAddrProvinceValid && (updateCustomerForm.get('addrProvince').hasError('maxlength')||updateCustomerForm.get('addrProvince').hasError('minlength'))" class="text-red-500">
                            {{this.tranService.translate("customer.error.length")}}
                        </div>
                        <div *ngIf="!(updateCustomerForm.get('addrProvince').hasError('maxlength')||updateCustomerForm.get('addrProvince').hasError('minlength')) && updateCustomerForm.get('addrProvince').hasError('invalidCharacters')" class="text-red-500">
                            {{this.tranService.translate("customer.error.regular")}}
                        </div>
                    </div>
                </div>
            </p-panel>
        </div>
        <div class="col-6">
            <p-panel [header]="note" [toggleable]="true">
                <div class="grid flex flex-column flex-nowrap">
                    <div class="p-3 pb-0 flex-1 flex">
                        <textarea id="note" pInputText formControlName="note" type="text" rows="5" class="flex-1"></textarea>
                    </div>
                    <div style="padding-left: 1rem;" *ngIf="isNoteValid && (updateCustomerForm.get('note').hasError('maxlength')||updateCustomerForm.get('note').hasError('minlength'))" class="text-red-500">
                        {{this.tranService.translate("customer.error.length")}}
                    </div>
                    <div style="padding-left: 1rem;" *ngIf="!(updateCustomerForm.get('note').hasError('maxlength')||updateCustomerForm.get('note').hasError('minlength')) && updateCustomerForm.get('note').hasError('invalidCharacters')" class="text-red-500">
                        {{this.tranService.translate("customer.error.note")}}
                    </div>
                </div>
            </p-panel>
        </div>
    </div>
    <div class="flex justify-content-center gap-3">
        <button pButton class="p-button-outlined p-button-secondary" routerLink="/customers">{{tranService.translate("global.button.cancel")}}</button>
        <button pButton class="p-button-info" type="submit" [disabled]="updateCustomerForm.invalid || !updateCustomerForm.dirty">{{tranService.translate("global.button.save")}}</button>
    </div>
    </div>
</form>
