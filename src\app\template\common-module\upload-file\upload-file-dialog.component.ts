import {Component, EventEmitter, Inject, Injector, Input, OnInit, Output} from "@angular/core";
import * as Excel from 'exceljs';
import { saveAs } from 'file-saver';
import * as moment from 'moment';
import {ComponentBase} from "../../../component.base";
import {TranslateService} from "../../../service/comon/translate.service";
import * as XLSX from "xlsx";
import {CONSTANTS, isVinaphoneNumber} from "../../../service/comon/constants";
import {OptionInputFile} from "../input-file/input.file.component";
import {TrafficWalletService} from "../../../service/datapool/TrafficWalletService";
import {ShareManagementService} from "../../../service/datapool/ShareManagementService";

@Component({
    selector: "upload-file-vnpt",
    templateUrl: './upload-file-dialog.component.html'
})

export class UploadFileDialogComponent extends ComponentBase implements OnInit {
    constructor(
        @Inject(TranslateService) public transService: TranslateService, private injector: Injector,
        @Inject(TrafficWalletService) private walletService: TrafficWalletService,
        @Inject(ShareManagementService) private shareService: ShareManagementService,
    ) {
        super(injector);
    }

    @Input() remainDataTotal?: number;
    @Input() shareList?: any;
    @Input() shareListFromFile?: any;
    @Input() isShowDialogImportByFile?: boolean;
    @Input() isShowErrorUpload?: boolean;
    @Input() totalRemain: number;
    @Input() trafficType?: string;

    @Output() onHideImport = new EventEmitter<any>();

    header: Array<string>;
    regexPhone: RegExp;
    regexEmail: RegExp;
    downloadColumns: Array<{header: string, key: string}>;
    maxLength: any;

    errorCol: string;
    name: string;
    email: string;
    phone: string;
    shareTraffic: string;
    autoShare: string;
    maxSize: number;
    excelFileType: string;
    xlsFileType: string;
    phoneCheck: string;
    optionInputFile: OptionInputFile;
    fileObject: any;
    messageErrorUpload: string | null;
    checkPhoneNumberBss: any;
    fileErr: Array<any>;
    fileName: string;

    ngOnInit() {
        this.optionInputFile = {
            type: ['xls', 'xlsx'],
            messageErrorType: this.tranService.translate("global.message.wrongFileExcel"),
            maxSize: 1,
            unit: "MB",
            required: true,
            isShowButtonUpload: true,
            actionUpload: this.uploadFile.bind(this),
            disabled: false
        };
        this.checkPhoneNumberBss = {
            phoneNumber: '',
            isCheck: false,
        };
        this.header = ['Họ tên', 'Email', 'Số điện thoại (bắt buộc)', 'Lưu lượng chia sẻ'];
        this.regexPhone = /^(0)+[0-9]{0,10}$/;
        this.regexEmail = /^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9]{2,}(?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)+$/;
        this.downloadColumns = [
            {header: 'Họ tên', key: 'Họ tên'},
            {header: 'Email', key: 'Email'},
            {header: 'Số điện thoại (bắt buộc)', key: 'Số điện thoại (bắt buộc)'},
            {header: 'Lưu lượng chia sẻ', key: 'Lưu lượng chia sẻ'},
            {header: 'Chia sẻ tự động', key: 'Chia sẻ tự động'},
            {header: 'Nội dung lỗi', key: 'Nội dung lỗi'},
        ];
        this.maxLength = {
            email: 100,
            name: 50,
        };

        this.errorCol= 'Nội dung lỗi';
        this.name = 'Họ tên';
        this.email = 'Email';
        this.phone= 'Số điện thoại (bắt buộc)';
        this.shareTraffic = 'Lưu lượng chia sẻ';
        this.autoShare = 'Chia sẻ tự động';
        this.maxSize = 1048576; // 1MB
        this.excelFileType = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
        this.xlsFileType = 'application/vnd.ms-excel';
        this.fileErr = [];
        this.fileName = "";
    }

    validateFile(file) {
        let me = this;

        return file?.type === me.excelFileType || file?.type === me.xlsFileType;
    }

    minimumTraffic = (trafficType) => {
        let minimum;
        switch (true) {
            case trafficType.toLowerCase().includes('sms'):
                minimum = 10;
                break;
            case trafficType.toLowerCase().includes('thoại'):
                minimum = 50;
                break;
            case trafficType.toLowerCase().includes('data'):
                minimum = 100;
                break;
            default:
                minimum = 100;
        }
        return minimum;
    }

    /** ---------------------------- logic upload file --------------------------------- */
    async uploadFile(file: any) {
        if (!this.validateFile(file)) {
            this.messageCommonService.error('File lỗi');
            return;
        }

        if (file?.size > this.maxSize) {
            this.messageCommonService.error('Dung lượng file không được vượt quá 1MB');
            return;
        }

        try {
            const bufferArr = file;
            this.fileName = file?.name;
            const data = await bufferArr.arrayBuffer();
            const wb = await XLSX.read(data, { type: 'buffer' });
            const sheetName = wb.SheetNames[0];
            const workSheet = wb.Sheets[sheetName];

            const jsonConvertedData = this.convertSheetToJson(workSheet);
            const rowObject = this.getSheetHeader(workSheet);
            const correctHeaderCount = this.countCorrectHeaders(rowObject);
            const range = XLSX.utils.decode_range(workSheet['!ref']);

            if (!this.isColumnCountValid(range, correctHeaderCount)) {
                return;
            }

            const processedData = this.processData(jsonConvertedData);
            if(processedData.errorExceeded){
                this.messageCommonService.error("File vượt quá 1000 dòng");
                this.messageCommonService.offload()
                return;
            }
            this.updateShareList(processedData.validData);
            if (processedData.invalidData.length > 0) {
                this.fileErr = processedData.invalidData;
                this.downloadFileErr();
            }
            this.isShowDialogImportByFile = false;
        } catch (error) {
            this.messageCommonService.error('Không đọc được file tải lên');
            this.messageCommonService.offload()
        }
    }

    convertSheetToJson(workSheet: any) {
        return XLSX.utils.sheet_to_json(workSheet, { defval: null , raw: true}) || [
            {
                [this.email]: null,
                [this.phone]: null,
                [this.name]: null,
                [this.shareTraffic]: null,
                [this.autoShare]: null,
            },
        ];
    }

    getSheetHeader(workSheet: any) {
        return XLSX.utils.sheet_to_json(workSheet, { header: 1, defval: '' });
    }

    countCorrectHeaders(rowObject: Array<any>) {
        return rowObject[0].reduce((count, header) => count + (this.header.includes(header.trim()) ? 1 : 0), 0);
    }

    isColumnCountValid(range: any, correctHeaderCount: number) {
        if (range?.e?.c === 3 && correctHeaderCount !== 4) {
            this.messageCommonService.error('Sai định dạng file mẫu');
            return false;
        }
        if (range?.e?.c < 3 && correctHeaderCount === 3) {
            this.messageCommonService.error('File tải lên thiếu cột');
            return false;
        }
        if (range?.e?.c > 3 && correctHeaderCount === 5) {
            this.messageCommonService.error('File tải lên thừa cột');
            return false;
        }
        if (correctHeaderCount !== 4) {
            this.messageCommonService.error('Sai định dạng file mẫu');
            return false;
        }
        return true;
    }

    processData(jsonConvertedData: Array<any>) {
        const validData = [];
        const invalidData = [];
        const uniquePhoneList = [];
        let remainStorage = this.remainDataTotal;
        let countValidData = jsonConvertedData.length;

        jsonConvertedData.forEach(exportData => {
            const errors = this.validateData(exportData, uniquePhoneList, remainStorage);
            if(this.elemiateNullData(exportData)){
                countValidData--
                return
            }// kiểm tra file import dữ liệu có đúng hay không
            if (errors.length > 0) {
                exportData[this.errorCol] = errors.join(', ');
                invalidData.push(exportData);
            } else {
                validData.push(exportData);
                remainStorage -= exportData[this.shareTraffic] || 0;
            }
        });
        if(countValidData > 1000){
            return {errorExceeded:true, validData: null, invalidData: null}
        }

        return { validData, invalidData };
    }

    validateData(exportData: any, uniquePhoneList: Array<string>, remainStorage: number) {
        const errors = [];

        this.validateEmail(exportData, errors);
        this.validatePhoneNumber(exportData, errors, uniquePhoneList);
        this.validateName(exportData, errors);
        this.validateShareTraffic(exportData, errors, remainStorage, this.trafficType);

        return errors;
    }

    validateEmail(exportData: any, errors: Array<string>) {
        const email = exportData[this.email];
        if (email !== null) {
            if (email.length > this.maxLength.email) {
                errors.push(CONSTANTS.IMPORT_ERROR.emailTooLong);
            } else if (!this.regexEmail.test(<string>email)) {
                errors.push(CONSTANTS.IMPORT_ERROR.wrongFormatEmail);
            }
        }
    }

    validatePhoneNumber(exportData: any, errors: Array<string>, uniquePhoneList: Array<string>) {
        const phoneNumber = exportData[this.phone];
        if (phoneNumber === null) {
            errors.push(CONSTANTS.IMPORT_ERROR.emptyPhoneNumber);
        } else if (!this.regexPhone.test(<string>phoneNumber)) {
            errors.push(CONSTANTS.IMPORT_ERROR.wrongFormatPhone);
        } else {
            this.checkPhoneNumberBss = { phoneNumber, isCheck: false };
            this.phoneCheck = <string>phoneNumber;
            this.checkParticipant();
            if (this.checkPhoneNumberBss.isCheck) {
                errors.push(CONSTANTS.IMPORT_ERROR.notVNPTPhoneNumber);
            }
            if (this.shareList?.find(e => e.phoneReceipt === phoneNumber)) {
                errors.push(CONSTANTS.IMPORT_ERROR.existPhoneNumber);
            }
            if (uniquePhoneList.includes(phoneNumber)) {
                errors.push(CONSTANTS.IMPORT_ERROR.phoneDuplicated);
            } else {
                uniquePhoneList.push(phoneNumber);
            }
        }
    }

    validateName(exportData: any, errors: Array<string>) {
        const name = exportData[this.name];
        if (name?.length > this.maxLength.name) {
            errors.push(CONSTANTS.IMPORT_ERROR.nameTooLong);
        }
        if (!this.utilService.checkValidCharacterVietnamese(name)){
            errors.push(CONSTANTS.IMPORT_ERROR.invalidName);
        }
    }

    validateShareTraffic(exportData: any, errors: Array<string>, remainStorage: number, trafficType?:string) {
        const shareTraffic = exportData[this.shareTraffic];
        const autoType = exportData[this.autoShare]
        if (shareTraffic !== null && shareTraffic !== 0) {
            if (typeof shareTraffic !== 'number') {
                errors.push(CONSTANTS.IMPORT_ERROR.wrongFormatTraffic);
            } else {
                if(trafficType && trafficType == "Gói thoại" && shareTraffic < 50){
                    errors.push(CONSTANTS.IMPORT_ERROR.minimumTrafficMinutes);
                }
                if (trafficType && (trafficType || "").toUpperCase().includes("Gói SMS".toUpperCase()) && shareTraffic < 10){
                    errors.push(CONSTANTS.IMPORT_ERROR.minimumTrafficSMS);
                }
                if (trafficType && trafficType == "Gói Data" && shareTraffic < 100){
                    errors.push(CONSTANTS.IMPORT_ERROR.minimumTrafficData);
                }
                if (shareTraffic > remainStorage) {
                    errors.push(CONSTANTS.IMPORT_ERROR.exceedStorage);
                }
                if(trafficType && (trafficType || "").toUpperCase().includes("Gói SMS".toUpperCase()) && shareTraffic % 5 !== 0){
                    errors.push(CONSTANTS.IMPORT_ERROR.wrongTrafficSMS);
                }
                if(trafficType && trafficType == "Gói Data" && shareTraffic % 100 !== 0){
                    errors.push(CONSTANTS.IMPORT_ERROR.wrongTrafficData);
                }
                if(autoType != "Có" && autoType != "Không"){
                    errors.push(CONSTANTS.IMPORT_ERROR.wrongShareAutoFormat);
                }
            }
        } else {
            errors.push(CONSTANTS.IMPORT_ERROR.minimumTraffic);
        }
    }

    updateShareList(validData: Array<any>) { // Validate danh sách chia sẻ và cập nhật lại trên giao diện màn chia sẻ
        let convertedList = validData.map((s, index) => ({
            phoneReceipt: s[this.phone],
            name: s[this.name],
            email: s[this.email],
            data: s[this.shareTraffic],
            percent: Math.round((s[this.shareTraffic] / this.totalRemain) * 100 * 100)/100,
            locked: false,
            isAuto: s[this.autoShare]
        }))
        this.shareList = [...this.shareList, ...convertedList];
        this.observableService.next(CONSTANTS.OBSERVABLE.UPDATE_SHARE_INFO, this.shareList);
    }

    /** ---------------------------- logic upload file --------------------------------- */

    downloadFileErr = async () => {
        let me = this;
        let maxErrorLength = 0;
        me.fileErr.forEach((item) => {
            if (item[me.errorCol]) {
                maxErrorLength = Math.max(maxErrorLength, item[me.errorCol]?.length + 1);
            }
        });
        const workbook = new Excel.Workbook();
        const worksheet = workbook.addWorksheet('Sheet1');

        worksheet.columns = me.downloadColumns;

        // loop through all of the columns and set the alignment with width.
        worksheet.columns.forEach((column,index) => {
            if (index === 0) column.width = 20; // Chiều rộng cột 1
            else if (index === 1) column.width = 25;
            else if (column.header === me.errorCol && column.header.length < maxErrorLength) column.width = maxErrorLength;
            else column.width = column.header.length + 4;
            column.alignment = { horizontal: 'center' };
        });

        me.fileErr?.forEach((singleData) => {
            const row = worksheet.addRow(singleData);
            row.eachCell((cell) => {
                cell.alignment = { horizontal: 'left' }; // Căn trái cho dữ liệu
            });
        });

        // worksheet?.eachRow({ includeEmpty: false }, (row) => {
        //     // store each cell to currentCell
        //     /* eslint no-underscore-dangle: 0 */
        //     const currentCell = row?._cells;
        //     // loop through currentCell to apply border only for the non-empty cell of excel
        //     currentCell?.forEach((singleCell) => {
        //         // store the cell address i.e. A1, A2, A3, B1, B2, B3, ...
        //         /* eslint no-underscore-dangle: 0 */
        //         const cellAddress = singleCell?._address;
        //         // apply border
        //         worksheet.getCell(cellAddress).border = {
        //             top: { style: 'thin' },
        //             left: { style: 'thin' },
        //             bottom: { style: 'thin' },
        //             right: { style: 'thin' },
        //         };
        //     });
        // });

        const buf = await workbook.xlsx.writeBuffer();
        const spliceFileName = me.fileName?.substring(0, me.fileName?.length - 4);
        const exportFileName = ''.concat(spliceFileName, '_Danh sách lỗi_', moment().format('DDMMYYYYHHMMss'));
        // download the processed file
        saveAs(new Blob([buf]), `${exportFileName}.xlsx`);
        me.messageCommonService.error(me.tranService.translate('datapool.text.downloadErrorMessage'), null, 10000)
    };

    checkParticipant () {
        let me = this;
        let body = {
            phoneNumber: me.phoneCheck?.replace(/^0/, '84')
        }
        // me.messageCommonService.onload();
        // me.walletService.checkParticipant(body, (e) => {
        //     if (e?.error_code === '0' && (e?.result === '02' || e?.result === '11')) {
        //         me.checkPhoneNumberBss = {
        //             ...me.checkPhoneNumberBss,
        //             isCheck: false,
        //         };
        //     } else if (e?.error_code === '0' && e?.result === '0') {
        //         if (isVinaphoneNumber(me.checkPhoneNumberBss?.phoneNumber)) {
        //             me.checkPhoneNumberBss = {
        //                 ...me.checkPhoneNumberBss,
        //                 isCheck: false,
        //             };
        //         } else {
        //             me.checkPhoneNumberBss = {
        //                 ...me.checkPhoneNumberBss,
        //                 isCheck: true,
        //             };
        //         }
        //     } else {
        //         me.checkPhoneNumberBss = {
        //             ...me.checkPhoneNumberBss,
        //             isCheck: true,
        //         };
        //     }
        // }, null, () => {
        //     me.checkPhoneNumberBss = {
        //         ...me.checkPhoneNumberBss,
        //         isCheck: true,
        //     };
        //     me.messageCommonService.offload();
        // })
    }

    clearFileCallback() {
        this.isShowErrorUpload = false;
    }

    onHide(event){
        this.onHideImport.emit(event)
    }

    downloadTemplate() {
        this.shareService.downloadTemplateReceiveInfo();
    }

    elemiateNullData(exportData: any): boolean{
        const shareTraffic = exportData[this.shareTraffic];
        const email = exportData[this.email];
        const phone = exportData[this.phone];
        const name = exportData[this.name];
        if(!email && !phone && !name && !shareTraffic) {
            return true;
        }else{
            return false;
        }
    }
}
