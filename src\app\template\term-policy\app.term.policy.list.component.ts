import { Component, Inject, Injector, OnInit } from "@angular/core";
import { FormBuilder } from "@angular/forms";
import { MenuItem } from "primeng/api";
import { ComponentBase } from "src/app/component.base";

@Component({
    selector: "term-policy-list",
    templateUrl: './app.term.policy.list.component.html'
})
export class TermPolicyListComponent extends ComponentBase implements OnInit{
    constructor(
        private formBuilder: FormBuilder,
        private injector: Injector) {
            super(injector);
    }

    items: MenuItem[];
    home: MenuItem;
    activeIndex: number = 0;

    ngOnInit(): void {
        this.items = [{ label: this.tranService.translate("global.menu.accountmgmt") }, { label: this.tranService.translate("global.menu.termpolicy") },];
        this.home = { icon: 'pi pi-home', routerLink: '/' };
        this.activeIndex = parseInt(this.route.snapshot.queryParams["index"] || 0);
    }
}