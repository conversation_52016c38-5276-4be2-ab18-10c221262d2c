import { NgModule } from "@angular/core";
import { RouterModule } from "@angular/router";
import { AppDeviceListComponent } from "./list/app-device-list.component";
import {AppDeviceDetailComponent} from "./detail/app-device-detail.component";
import {AppDeviceUpdateComponent} from "./edit/app-device-update.component";
import {AppDeviceCreateComponent} from "./create/app-device-create.component";
import DataPage from "../../service/data.page";
import {CONSTANTS} from "../../service/comon/constants";

@NgModule({
    imports: [
        RouterModule.forChild([
            {path: "", component: AppDeviceListComponent, data: new DataPage("global.menu.listdevice", [CONSTANTS.PERMISSIONS.DEVICE.VIEW_LIST])},
            {path: "detail/:id", component: AppDeviceDetailComponent, data: new DataPage("global.titlepage.detailDevice", [CONSTANTS.PERMISSIONS.DEVICE.VIEW_DETAIL]) },
            {path: "edit/:id", component:AppDeviceUpdateComponent, data: new DataPage("global.titlepage.editDevice", [CONSTANTS.PERMISSIONS.DEVICE.UPDATE])},
            {path: "create", component: AppDeviceCreateComponent, data: new DataPage("global.titlepage.createDevice", [CONSTANTS.PERMISSIONS.DEVICE.CREATE])}

        ])
    ],
    exports: [RouterModule]
})
export class DeviceRoutingModule {}
