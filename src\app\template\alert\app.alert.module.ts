import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { CheckboxModule } from 'primeng/checkbox';
import { AppAlertRoutingModule } from './app.alert-routing.module';
import { AppAlertListComponent } from './alert-setting/list/app-alert-list.component';
import {BreadcrumbModule} from "primeng/breadcrumb";
import {FieldsetModule} from "primeng/fieldset";
import {FormsModule, ReactiveFormsModule} from "@angular/forms";
import {InputTextModule} from "primeng/inputtext";
import {ButtonModule} from "primeng/button";
import {CommonVnptModule} from "../common-module/common.module";
import {SplitButtonModule} from "primeng/splitbutton";
import {AutoCompleteModule} from "primeng/autocomplete";
import {CalendarModule} from "primeng/calendar";
import {DropdownModule} from "primeng/dropdown";
import {CardModule} from "primeng/card";
import {DialogModule} from "primeng/dialog";
import {InputTextareaModule} from "primeng/inputtextarea";
import {MultiSelectModule} from "primeng/multiselect";
import {AccountService} from "../../service/account/AccountService";
import { AppAlertCreateComponent } from './alert-setting/create/app-alert-create.component';
import {InputSwitchModule} from "primeng/inputswitch";
import {RadioButtonModule} from "primeng/radiobutton";
import {PanelModule} from "primeng/panel";
import { AppAlertDetailComponent } from './alert-setting/detail/app-alert-detail.component';
import { AppAlertUpdateComponent } from './alert-setting/update/app-alert-update.component';
import {AppAlertsAlertHistoryComponent} from "./alert-history/app-alert-history.component";
import {GroupSimService} from "../../service/group-sim/GroupSimService";
import {SimService} from "../../service/sim/SimService";
import {CustomerService} from "../../service/customer/CustomerService";
import { TrafficWalletService } from 'src/app/service/datapool/TrafficWalletService';
import { TooltipModule } from 'primeng/tooltip';
import {RatingPlanService} from "../../service/rating-plan/RatingPlanService";
import {KeyFilterModule} from "primeng/keyfilter";
import {TabViewModule} from "primeng/tabview";


@NgModule({
    imports: [
        AppAlertRoutingModule,
        CommonModule,
        BreadcrumbModule,
        FieldsetModule,
        FormsModule,
        ReactiveFormsModule,
        InputTextModule,
        ButtonModule,
        CommonVnptModule,
        SplitButtonModule,
        AutoCompleteModule,
        CalendarModule,
        DropdownModule,
        CardModule,
        DialogModule,
        InputTextareaModule,
        MultiSelectModule,
        InputSwitchModule,
        RadioButtonModule,
        PanelModule,
        CheckboxModule,
        TooltipModule,
        KeyFilterModule,
        TabViewModule
    ],
    declarations: [
        AppAlertListComponent,
        AppAlertCreateComponent,
        AppAlertDetailComponent,
        AppAlertUpdateComponent,
        AppAlertsAlertHistoryComponent,
    ],
    providers: [
        AccountService,
        GroupSimService,
        SimService,
        CustomerService,
        TrafficWalletService,
        RatingPlanService
    ]
})
export class AppAlertModule { }

