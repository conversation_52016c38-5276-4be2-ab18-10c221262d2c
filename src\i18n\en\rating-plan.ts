export default {
    label: {
        planName: "Plan Name",
        planCode: "Plan Code",
        status: "Status",
        cycleTimeUnit: "Plan Cycle",
        cycle: "Plan Cycle",
        paidType: "Paid Type",
        customerType: "Customer Type",
        ratingScope: "Plan Scope",
        subscriptionFee: "Subscription Fee (VND)",
        limitDataUsage: "Limited Data (MB)",
        dispatchCode:"Dispatch Code",
        cycleInterval: "Cycle Interval",
        reload: "Reload",
        flat: "Flat",
        flexible: "Flexible",
        limitSmsInside: "Limited inside SMS Fee",
        limitSmsOutside: "Limited outside SMS Fee",
        squeezedSpeed: "Squeeze Speed",
        feeSmsInside: "Fee per Inside SMS",
        feeSmsOutside: "Fee per Outside SMS",
        maximumFee: "Maximum Fee",
        feePerDataUnit: "Fee per Unit",
        province:"Province/City",
        duration:"Duration",
        autoReload:"Auto Reload",
        description:"Description",
        freeData:"Data Free",
        insideSMSFree:"Limited inside SMS Free",
        outsideSMSFree:"Limited outside SMS Free",
        feePerUnit:"Fee per Unit",
        squeezeSpeed:"Squeeze Speed",
        feePerInsideSMS:"Fee per Inside SMS",
        feePerOutsideSMS:"Fee per Outside SMS",
        maxFee:"Maximum Fee",
        assignPlan:"Assign Plan",
        email:"Email",
        username: "Username",
        fullName: "Full Name",
        dataMax: "Dung lượng (MB)",
    },
    placeHolder:{
        planCode:"Enter plan code",
        planeName:"Enter plan name",
        dispatchCode:"Enter dispatch code",
        customerType:"Select customer type",
        description:"Enter description",
        subscriptionFee:"Enter subscription fee",
        subscriptionType:"Select subscription type",
        planScope:"Select plan scope",
        provinceCode:"Select province/city",
        planCycle:"Select plan cycle",
        duration:"Enter duration",
        freeData:"Enter free data",
        insideSMSFree:"Enter inside sms free",
        outsideSMSFree:"Enter outside sms free",
        feePerUnit:"Enter fee per unit",
        squeezeSpeed:"Enter squeeze speed",
        feePerInsideSMS:"Enter fee per inside sms",
        feePerOutsideSMS:"Enter fee per outside sms",
        maxFee:"Enter max fee",
        dataMax: "Capacity",
    },
    text: {
        textRegisterSuccess: "Send registered successfully",
        textChangeSuccess: "Send changed successfully",
        textCancelSuccess: "Send cancelled successfully",
        textResultSuccess: "Send registered successfully for ${success}/${total} subscriptions has status Inactivated, Deactivated",
        textResultFail: "${fail}/${total} subscriptions in the list below did not register successfully because the subscription status is not Inactivated, Deactivated. Please check back!",
        textResultRegisterByFile: "Successfully registered for ${error}/${total} subscribers with valid information. Invalid information in the error list.",
        textResultRegisterGroupSim: "Successfully registered for ${error}/${total} subscribers with valid information. Invalid information in the error list.",
        textDong: "(dong/Month",
        vat: "Included VAT)",
        dayMonth: "(Day/Month)",
    },
    status: {
        create: "Create New",
        pending: "Pending",
        activated: "Activated",
        deactivated: "Deactivated",
    },
    error: {
        requiredError:"This field is required",
        lengthError_255:"This field should have 255 characters or fewer ",
        lengthError_64:"This field should have 64 characters or fewer",
        lengthError_16:"This field should have 16 characters or fewer",
        lengthError_number:"This field should have 10 numeric characters or fewer",
        existedCodeError:"Existed Plan Code",
        existedNameError:"Existed Plan Name",
        characterError_name:"Wrong Format. Only Accept (a-z, A-Z, 0-9, . -_, space, Vietnamese)",
        characterError_code:"Wrong Format. Only Accept (a-z, A-Z, 0-9, -_)",
        onlyPositive:"Not accept \" - \"",
    },
    customerType: {
        enterprise: "Company",
        personal: "Personal",
        agency: "Agency",
    },

    subscriptionType: {
        post: "Postpaid",
        pre: "Prepaid"
    },

    ratingScope: {
        nativeWide: "Nation Wide",
        customer: "Customer",
        province: "Province"
    },

    cycle: {
        day: "Day",
        week: "Week",
        month: "Month",
        year: "Year",
    },
}
