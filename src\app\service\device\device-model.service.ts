import {Inject, Injectable} from "@angular/core";
import {HttpService} from "../comon/http.service";

@Injectable({providedIn: 'root'})
export class DeviceModelService {
    private prefixApi: string;

    constructor(@Inject(HttpService) private httpService: HttpService) {
        this.prefixApi = "/device-type";
    }

    public search(params: {
        [key: string]: any
    }, callback?: Function, errorCallBack?: Function, finallyCallback?: Function) {
        this.httpService.get(`${this.prefixApi}/search`, {}, params, callback, errorCallBack, finallyCallback);
    }

    // lấy ra 1 bản ghi theo modelCode
    public getById(modelCode, callback?: Function, errorCallback?: Function, finallyCallback?: Function) {
        this.httpService.get(`${this.prefixApi}/search`, {}, {modelCode},
            (res: any) => {
                callback(res?.content?.[0] ?? null);
            }
            , errorCallback, finallyCallback);
    }
}
