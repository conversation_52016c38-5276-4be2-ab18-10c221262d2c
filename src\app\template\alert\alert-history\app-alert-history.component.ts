import {AfterContentChecked, Component, Inject, Injector, OnInit} from "@angular/core";
import {MenuItem} from "primeng/api";
import {FormBuilder} from "@angular/forms";
import {ColumnInfo, OptionTable} from "../../common-module/table/table.component";
import {CONSTANTS} from "../../../service/comon/constants";
import {AlertService} from "../../../service/alert/AlertService";
import {ComponentBase} from "../../../component.base";
import {ComboLazyControl} from "../../common-module/combobox-lazyload/combobox.lazyload";

@Component({
    selector: "app-alerts-alert-history",
    templateUrl: "./app-alert-history.component.html"
})
export class AppAlertsAlertHistoryComponent extends ComponentBase implements OnInit, AfterContentChecked {
    items: MenuItem[];
    home: MenuItem;
    optionTable: OptionTable;
    columns: Array<ColumnInfo>;
    formSearchAlertHistory: any;
    searchInfoStandard: any;
    selectItems: Array<{ id: string, [key: string]: any }>;
    searchInfo: {
        deviceId: string | null,
        eventType: number | null,
        fromDate: Date | null,
        toDate: Date | null,
        userId: string | null,
        imei : string | null,
    }
    pageNumber: number;
    pageSize: number;
    sort: string;
    dataSet: {
        content: Array<any>,
        total: number,
    };
    maxDateFrom: Date | number | string | null = new Date();
    minDateTo: Date | number | string | null = null;
    maxDateTo: Date | number | string | null = new Date();
    msisdnPattern = /^\d{0,12}$/;
    eventOptions : Array<any>
    comboSelectCustomerControl: ComboLazyControl = new ComboLazyControl();
    controlComboSelectDevice: ComboLazyControl = new ComboLazyControl();
    constructor(@Inject(AlertService) private alertService: AlertService,
                private formBuilder: FormBuilder,
                injector: Injector) {
        super(injector)
    }

    ngOnInit(): void {
        let me = this
        this.selectItems = [];
        this.home = {icon: 'pi pi-home', routerLink: '/'};
        this.items = [{
            label: this.tranService.translate("global.menu.alerts"),
            routerLink: '/alerts'
        }, {label: this.tranService.translate("global.menu.alerthistory")}];
        this.searchInfoStandard = {
            msisdn: null,
            statusSim: null,
            fromDate: null,
            toDate: null,
            customerId: null,
        }
        this.searchInfo = {
            deviceId: null,
            eventType: null,
            fromDate: null,
            toDate: null,
            userId: null,
            imei : null,
        }

        this.eventOptions = [
            {name:me.tranService.translate("alert.eventType.exceededValue"), value:CONSTANTS.ALERT_EVENT_TYPE.EXCEEDED_VALUE},
            {
                name: this.tranService.translate("alert.eventType.deviceAlert"),
                value: CONSTANTS.ALERT_EVENT_TYPE.DEVICE_ALERT
            },
        ]

        this.formSearchAlertHistory = this.formBuilder.group(this.searchInfo);
        this.optionTable = {
            hasClearSelected: false,
            hasShowChoose: false,
            hasShowIndex: true,
            hasShowToggleColumn: true,
        }
        this.columns = [
            {
                name: this.tranService.translate("device.label.name"),
                key: "deviceName",
                size: "fit-content",
                align: "left",
                isShow: true,
                isSort: true,
            },
            {
                name: this.tranService.translate("device.label.imei"),
                key: "imei",
                size: "fit-content",
                align: "left",
                isShow: true,
                isSort: true,
            },
            {
                name: this.tranService.translate("customer.label.customerName"),
                key: "userCustomerName",
                size: "fit-content",
                align: "left",
                isShow: true,
                isSort: true,
            },
            {
                name: this.tranService.translate("device.label.alertName"),
                key: "alertName",
                size: "150px",
                align: "left",
                isShow: false,
                isSort: true,
            },
            {
                name: this.tranService.translate("device.label.alertType"),
                key: "eventType",
                size: "150px",
                align: "left",
                isShow: true,
                isSort: true,
                funcConvertText(value, item) {
                    const type = me.eventOptions.find(item => item.value === value)
                    return type ? type.name : "";
                }
            },
            {
                name: this.tranService.translate("device.label.alertEmail"),
                key: "alertEmails",
                size: "fit-content",
                align: "left",
                isShow: true,
                isSort: true,
                funcConvertText(value, item) {
                    return (value || "").replace(/,/g, '<br>')
                },
                isSanitizedContent: true,
            },
            {
                name: this.tranService.translate("device.label.alertContent"),
                key: "emailContent",
                size: "250px",
                align: "left",
                isShow: true,
                isSort: true,
                isShowTooltip: true,
                style: {
                    display: 'inline-block',
                    maxWidth: '350px',
                    overflow: 'hidden',
                    textOverflow: 'ellipsis'
                },
                isSanitizedContent: true,
            },
            {
                name: this.tranService.translate("device.label.alertTime"),
                key: "raisedDate",
                size: "150px",
                align: "left",
                isShow: true,
                isSort: true,
            },
        ];
        this.pageNumber = 0;
        this.pageSize = 10;
        this.sort = "";
        this.dataSet = {
            content: [],
            total: 0,
        }
        this.search(this.pageNumber, this.pageSize, this.sort, this.searchInfo);
    }

    onSubmitSearch() {
        let me = this;
        me.pageNumber = 0;
        me.search(this.pageNumber, this.pageSize, this.sort, this.searchInfo);
    }

    search(page, limit, sort, params) {
        let me = this;
        this.pageNumber = page;
        this.pageSize = limit;
        this.sort = sort;
        let dataParams = {
            page,
            size: limit,
            sort
        }
        Object.keys(this.searchInfo).forEach(key => {
            if (this.searchInfo[key] != null) {
                if (key == "fromDate") {
                    dataParams["fromDate"] = me.searchInfo.fromDate.getTime()
                } else if (key == "toDate") {
                    dataParams["toDate"] = me.searchInfo.toDate.getTime()
                } else {
                    dataParams[key] = this.searchInfo[key];
                }
                // if(key == "customerId"){
                //     dataParams['dataPoolTax'] = dataParams['customerId']
                //     dataParams['customerName'] = dataParams['customerId']
                //     delete dataParams['customerId']
                // }
            }
        })
        me.messageCommonService.onload();
        this.alertService.getListAlertHistory(dataParams, (response) => {
            me.dataSet = {
                content: response.content,
                total: response.totalElements
            }
        }, null, () => {
            me.messageCommonService.offload();
        })
    }

    onChangeDateFrom(value) {
        if (value) {
            this.minDateTo = value;
        } else {
            this.minDateTo = null
        }
    }

    onChangeDateTo(value) {
        if (value) {
            this.maxDateFrom = value;
        } else {
            this.maxDateFrom = new Date();
        }
    }

    ngAfterContentChecked(): void {
    }
}
