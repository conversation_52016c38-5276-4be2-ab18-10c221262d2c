import { Component, Inject, Injector, OnInit } from "@angular/core";
import { FormBuilder } from "@angular/forms";
import { ComponentBase } from "src/app/component.base";

@Component({
    selector: "personal-data-protection-policy-content",
    templateUrl: './app.personal.data.protection.policy.content.component.html'
})
export class PersonalDataProtectionContentComponent extends ComponentBase implements OnInit{
    constructor(
        private formBuilder: FormBuilder,
        private injector: Injector) {
            super(injector);
    }

    ngOnInit(): void {
        
    }
}