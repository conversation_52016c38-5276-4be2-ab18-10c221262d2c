<div class="my-3">
<p-card>
    <div class="flex flex-row gap-3 align-items-center">
        <div>
            <span class="p-input-icon-left p-input-icon-right">
                <i class="pi pi-search"></i>
                <input [(ngModel)]="searchInputValue" type="text" id="lefticon" [placeholder]="translateService.translate('global.searchSeperate.placeholder.input')" (input)="searchInputChange()" (keydown.enter)="search()" pInputText>
                <i class="pi pi-cog cursor-pointer" (click)="input.toggle($event)">
                    <p-overlayPanel #input>
                        <ng-template pTemplate="content">
                            <div *ngFor="let item of searchList">
                                <p-checkbox class="mb-1" [(ngModel)]="listSearchItems" [value]="item.key" inputId="item.key" [label]="item.name" (ngModelChange)="listSearchChanged($event)"></p-checkbox>
                            </div>
                        </ng-template>
                    </p-overlayPanel>
                </i>
            </span>
        </div>
        <div>
            <button class="p-button-rounded p-button-outlined" (click)="filter.toggle($event)" pButton>{{translateService.translate('global.text.filter')}} &nbsp; <div class="bg-blue-400 text-0 px-2 text-sm border-round-3xl">{{usedFilterNumber}}</div></button>
            <p-overlayPanel #filter>
                <ng-template pTemplate="content">
                    <div class="p-1 w-full flex flex-column gap-2">
                    <!-- Content -->
                    <div class="text-lg font-bold">{{translateService.translate("global.text.filter")}}</div>
                        <div class="flex gap-2">
                            <button *ngIf="filterCount.length != this.filterList?.length" pButton (click)="addFilterItem()">{{translateService.translate('global.searchSeperate.button.add')}}</button>
                            <button pButton (click)="resetFilterItems()">{{translateService.translate('global.searchSeperate.button.reset')}}</button>
                        </div>
                        <div *ngFor="let i of filterCount; let idx = index" class="flex flex-row gap-2">
                            <vnpt-select class="flex-2" placeholder="Choose Filter"
                                         [(value)]="parentFilterModel[idx]"
                                         [options]="filterOption(idx)"
                                         keyReturn="key"
                                         displayPattern="${name}"
                                         paramKey="name"
                                         [filter]="false"
                                         [placeholder]="translateService.translate('global.searchSeperate.placeholder.dropdownFlter')"
                                         (onchange)="onChangeSelectFilters($event, idx)"
                                         [lazyLoad]="false"
                                         [isMultiChoice]="false"
                                         [showClear]="false"
                            >
                            </vnpt-select>
                            <div class="flex-2">
                                <input [(ngModel)]="filterModel[idx]" *ngIf="inputType[idx] == 0" type="text" pInputText (keydown.enter)="filterChange(idx)" [placeholder]="translateService.translate('global.searchSeperate.placeholder.input')">
                                <vnpt-select
                                             [lazyLoad]="false"
                                             [isMultiChoice]="false"
                                             *ngIf="inputType[idx] == 1"
                                             [(value)]="filterModel[idx]"
                                             [options]="childOption(idx)"
                                             keyReturn="value"
                                             displayPattern="${name}"
                                             [filter]="false"
                                             [placeholder]="translateService.translate('global.searchSeperate.placeholder.dropdown')"
                                             (onchange)="filterChange(idx)"
                                             [showClear]="true"
                                >
                                </vnpt-select>
                                <vnpt-select [isMultiChoice]="true"
                                             [lazyLoad]="false"
                                             *ngIf="inputType[idx] == 2"
                                             [isFilterLocal]="true"
                                             [options]="childOption(idx)"
                                             [(value)]="filterModel[idx]"
                                             paramKey="name"
                                             keyReturn="value"
                                             displayPattern="${name}"
                                             [placeholder]="translateService.translate('global.searchSeperate.placeholder.dropdown')"
                                             (onchange)="filterChange(idx)"
                                             (onClear)="clearValue(idx)"
                                             [showClear]="true">
                                </vnpt-select>
                                <p-calendar *ngIf="inputType[idx] == 3" dateFormat="dd/mm/yy" [showIcon]="true" [iconDisplay]="'input'" [(ngModel)]="filterModel[idx]" [showButtonBar]="true" [placeholder]="translateService.translate('global.searchSeperate.placeholder.calendar')" (keydown.enter)="filterChange(idx)" (onSelect)="filterChange(idx)" (onTodayClick)="onTodayCalendar(idx)" (onClearClick)="onClearCalendar(idx)"></p-calendar>
                                <p-calendar *ngIf="inputType[idx] == 4" dateFormat="dd/mm/yy" selectionMode="range" [showIcon]="true" [(ngModel)]="filterModel[idx]" [iconDisplay]="'input'" [showButtonBar]="true" (onTodayClick)="onTodayCalendar(idx)" (onClearClick)="onClearCalendar(idx)" (keydown.enter)="filterChange(idx)" (onSelect)="filterChange(idx)" [placeholder]="translateService.translate('global.searchSeperate.placeholder.rangeCalendar')"></p-calendar>
                            </div>
                            <button *ngIf="filterCount.length > 1" class="align-self-end p-button-outlined" style="margin-left: auto;" pButton (click)="deleteRow(idx)"><i class="pi pi-trash"></i></button>
                        </div>
                        <!-- <div *ngFor="let i of selectedFilterList; let idx = index">
                            <p-dropdown placeholder="Choose Filter" [options]="filterOption(idx)" optionLabel="name" optionValue="key" (onChange)="onChangeSelectFilters(idx)"></p-dropdown>
                        </div> -->
                    </div>
                </ng-template>
            </p-overlayPanel>
        </div>
    </div>
</p-card>
</div>
