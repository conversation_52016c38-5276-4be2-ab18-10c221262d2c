<form action="" [formGroup]="formAlert">
    <div class="vnpt flex flex-row justify-content-between align-items-center bg-white p-2 border-round">
        <div class="">
            <div class="text-xl font-bold mb-1">{{ tranService.translate("global.titlepage.detailAlarm") }}</div>
            <p-breadcrumb class="max-w-full col-7" [model]="items" [home]="home"></p-breadcrumb>
        </div>
        <div class="col-5 flex flex-row justify-content-end align-items-center">
            <p-button styleClass="p-button-info "
                      *ngIf="checkAuthen([CONSTANTS.PERMISSIONS.ALERT.UPDATE]) && CONSTANTS.ALERT_STATUS.INACTIVE == statusTemp"
                      (onClick)="goUpdate()">{{ tranService.translate("global.button.update") }}
            </p-button>
        </div>
    </div>

    <p-card class="p-4">
        <div class="flex flex-row">
            <h4 class="ml-2 mr-4">{{ tranService.translate("alert.label.info") }}</h4>
        </div>
        <div class="flex flex-row ml-3 grid">
<!--            <h4 class="ml-2 mr-4">{{ tranService.translate("alert.label.info") }}</h4>-->
            <div class="col-4 flex flex-row align-items-center pb-0 pt-3">
                <div>
                    <p-radioButton
                        name="timeSend"
                        value="immediate"
                        [(ngModel)]="alertInfo.timeSend"
                        formControlName="timeSend"
                        disabled
                        [label]="tranService.translate('alert.label.sendAlertImmediate')">
                    </p-radioButton>
                </div>
            </div>

            <div class="col-4 col-offset-2 flex flex-row align-items-center pb-0 pt-3">
                <label for="status" style="width:200px">{{tranService.translate("alert.label.status")}}</label>
                <div style="width: calc(100% - 200px);" class="flex flex-row align-items-center">
                    <span *ngIf="CONSTANTS.ALERT_STATUS.ACTIVE == statusTemp" [class]="['p-2','text-green-800', 'bg-green-100','border-round','inline-block']">{{tranService.translate("alert.status.active")}}</span>
                    <span *ngIf="CONSTANTS.ALERT_STATUS.INACTIVE == statusTemp" [class]="['p-2', 'text-red-700', 'bg-red-100', 'border-round','inline-block']">{{tranService.translate("alert.status.inactive")}}</span>
                    <p-inputSwitch *ngIf="checkAuthen([CONSTANTS.PERMISSIONS.ALERT.CHANGE_STATUS])" pTooltip="{{alertInfo.status == CONSTANTS.ALERT_STATUS.ACTIVE?tranService.translate('alert.label.inactivePopup') : tranService.translate('alert.label.activePopup')}}" tooltipPosition="right" tooltipStyleClass="absolute"
                                   class="ml-4 mt-2" (onChange)="onChangeStatus($event)"
                                   [trueValue]="CONSTANTS.ALERT_STATUS.ACTIVE" [falseValue]="CONSTANTS.ALERT_STATUS.INACTIVE" [(ngModel)]="alertInfo.status" formControlName="status"/>
                </div>
            </div>
        </div>

        <div class="shadow-2 border-round-md m-1 flex p-fluid p-formgrid grid">
            <div class="col-6">
                <!-- ten canh bao -->
                <div class="col-9 flex flex-row justify-content-between align-items-start pb-0">
                    <label htmlFor="name" style="width:200px">{{ tranService.translate("alert.label.name") }}<span
                        class="text-red-500">*</span></label>
                    <div style="width: calc(100% - 200px)" class="relative">
<!--                        <input class="w-full"-->
<!--                               pInputText id="name"-->
<!--                               [(ngModel)]="alertInfo.name"-->
<!--                               formControlName="name"-->
<!--                               [required]="true"-->
<!--                               [maxLength]="255"-->
<!--                               readonly-->
<!--                               [placeholder]="tranService.translate('alert.text.inputName')"-->
<!--                        />-->
                        <span>{{alertInfo.name}}</span>
                    </div>
                </div>
                <div class="col-9 flex flex-row justify-content-between align-items-start pb-0">
                    <label htmlFor="name" style="width:200px">{{ tranService.translate("device.label.model") }}<span
                        class="text-red-500">*</span></label>
                    <div style="width: calc(100% - 200px)" class="relative">

                        <span>{{alertInfo.model}}</span>
                    </div>
                </div>
                <!-- muc do -->
                <div class="col-9 flex flex-row justify-content-between align-items-start pb-0 pt-3">
                    <label for="severity" style="width:200px">{{ tranService.translate("alert.label.level") }}<span
                        class="text-red-500">*</span></label>
                    <div style="width: calc(100% - 200px)">
<!--                        <p-dropdown styleClass="w-full"-->
<!--                                    id="severity" [autoDisplayFirst]="false"-->
<!--                                    [(ngModel)]="alertInfo.severity"-->
<!--                                    [required]="true"-->
<!--                                    formControlName="severity"-->
<!--                                    [options]="severityOptions"-->
<!--                                    [readonly]="true"-->
<!--                                    optionLabel="name"-->
<!--                                    optionValue="value"-->
<!--                                    [placeholder]="tranService.translate('alert.text.inputlevel')"-->
<!--                        ></p-dropdown>-->
                        <span>{{getSeverity(alertInfo.severity)}}</span>
                    </div>
                </div>
            </div>
            <div class="col-6">
                <div class="col-9 flex flex-row justify-content-between align-items-start pb-0">
                    <label htmlFor="name" style="width:200px">{{ tranService.translate("device.label.type") }}<span
                        class="text-red-500">*</span></label>
                    <div style="width: calc(100% - 200px)" class="relative">
                        <span>{{alertInfo.deviceType}}</span>
                    </div>
                </div>

                <!-- loai -->
                <div class="col-9 flex flex-row justify-content-between align-items-start pb-0">
                    <label for="ruleCategory" style="width:200px">{{ tranService.translate("alert.label.rule") }}<span
                        class="text-red-500">*</span></label>
                    <div style="width: calc(100% - 200px)">
<!--                        <p-dropdown styleClass="w-full"-->
<!--                                    id="ruleCategory" [autoDisplayFirst]="false"-->
<!--                                    [(ngModel)]="alertInfo.eventType"-->
<!--                                    [required]="true"-->
<!--                                    formControlName="eventType"-->
<!--                                    [options]="eventOptions"-->
<!--                                    optionLabel="name"-->
<!--                                    optionValue="value"-->
<!--                                    [readonly]="true"-->
<!--                                    [placeholder]="tranService.translate('alert.text.rule')"-->
<!--                        ></p-dropdown>-->
                        <span>{{getEventType(alertInfo.eventType)}}</span>
                    </div>
                </div>
                <div class="col-9 flex flex-row justify-content-center align-items-start pb-2">
                    <label htmlFor="description"
                           style="width:200px">{{ tranService.translate("alert.label.description") }}</label>
                    <div style="width: calc(100% - 200px)">
<!--                    <textarea pInputText-->
<!--                              class="col-8"-->
<!--                              pInputText id="description"-->
<!--                              [(ngModel)]="alertInfo.description"-->
<!--                              formControlName="description"-->
<!--                              name="description"-->
<!--                              [readonly]="true"-->
<!--                              [disabled]="true"-->
<!--                    ></textarea>-->
                        <span [innerHTML]="getContent(alertInfo.description)"></span>
                    </div>
                </div>
            </div>
        </div>


        <div *ngIf="inputSchema.length > 0">
            <h4 class="ml-2">{{ tranService.translate("alert.label.alertInfoConfig") }}</h4>
            <div class="shadow-2 border-round-md m-1 flex p-fluid p-formgrid grid">

                <div *ngFor="let input of inputSchema"
                     class="justify-content-between align-items-center pb-2 col-6">
                    <div
                        *ngIf="alertInfo.eventType == CONSTANTS.ALERT_EVENT_TYPE.EXCEEDED_VALUE">
                        <div class="col-9 flex flex-row justify-content-between align-items-center pb-0">

                            <label
                                style="width:200px">{{ utilService.getLabel(input) }}<span
                                class="text-red-500"></span></label>
                            <div style="width: calc(100% - 200px)" class="relative">
                                <span>{{getValueFormControl(input.key)}}</span>
                            </div>
                        </div>
                    </div>
                    <div *ngIf="alertInfo.eventType == CONSTANTS.ALERT_EVENT_TYPE.DEVICE_ALERT">
                        <div class="col-9 flex flex-row justify-content-between align-items-center pb-2">
                            <span>{{utilService.getLabel(input)}}</span>
                            <p-inputSwitch
                                class="mb-1"
                                [id]="utilService.getKey(input.key)"
                                [formControlName]="utilService.getKey(input.key)"
                                [disabled]="true"
                            >
                            </p-inputSwitch>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <h4 class="ml-2">{{ tranService.translate("alert.label.filterInforApplied") }}</h4>
        <div class="p-3 pt-0 shadow-2 border-round-md m-1 flex p-fluid p-formgrid grid">
            <div class="col-6" *ngIf="userType == CONSTANTS.USER_TYPE.ADMIN">
                <div class="col-9 flex flex-row justify-content-between align-items-start pb-0">
                    <label htmlFor="name" style="width:200px">{{ tranService.translate("alert.label.business") }}<span
                        class="text-red-500"></span></label>
                    <div style="width: calc(100% - 200px)" class="relative">
<!--                        <vnpt-select-->
<!--                            [control]="controlComboSelectBusiness"-->
<!--                            class="w-full"-->
<!--                            [(value)]="alertInfo.userEnterpriseId"-->
<!--                            [placeholder]="tranService.translate('device.input.businessName')"-->
<!--                            objectKey="account"-->
<!--                            paramKey="name"-->
<!--                            keyReturn="id"-->
<!--                            displayPattern="${name}"-->
<!--                            typeValue="primitive"-->
<!--                            [isMultiChoice]="false"-->
<!--                            [paramDefault]="paramSearchBusiness"-->
<!--                            (onClear)="onSelectedBusiness()"-->
<!--                            (onSelectItem)="onSelectedBusiness()"-->
<!--                            [pKeyFilter]="vietnamesePattern"-->
<!--                            [disabled]="true"-->
<!--                        ></vnpt-select>-->
                        <span>{{alertInfo.businessName}}</span>
                    </div>
                </div>
            </div>
            <div class="col-6"
                 *ngIf="userType == CONSTANTS.USER_TYPE.ADMIN || userType == CONSTANTS.USER_TYPE.BUSINESS">
                <div class="col-9 flex flex-row justify-content-between align-items-start pb-0">
                    <label htmlFor="name" style="width:200px">{{ tranService.translate("alert.label.individual") }}<span
                        class="text-red-500"></span></label>
                    <div style="width: calc(100% - 200px)" class="relative">
                        <!--                        <vnpt-select-->
                        <!--                            [control]="controlComboSelectIndividual"-->
                        <!--                            class="w-full"-->
                        <!--                            [(value)]="alertInfo.userCustomerId"-->
                        <!--                            [placeholder]="tranService.translate('device.label.individualName')"-->
                        <!--                            objectKey="account"-->
                        <!--                            paramKey="name"-->
                        <!--                            keyReturn="id"-->
                        <!--                            displayPattern="${name}"-->
                        <!--                            typeValue="primitive"-->
                        <!--                            [isMultiChoice]="false"-->
                        <!--                            [paramDefault]="paramSearchIndividual"-->
                        <!--                            (onClear)="onSelectedIndividual()"-->
                        <!--                            [disabled]="true"-->
                        <!--                            (onchange)="onSelectedIndividual()"-->
                        <!--                            [pKeyFilter]="vietnamesePattern"-->
                        <!--                        ></vnpt-select>-->
                        <span>{{alertInfo.individualName}}</span>
                    </div>
                </div>
            </div>
            <div class="col-6">
                <div class="col-9 flex flex-row justify-content-between align-items-start pb-0">
                    <label htmlFor="name" style="width:200px">{{ tranService.translate("permission.Device.Device") }}
                        <span
                            class="text-red-500"></span></label>
                    <div style="width: calc(100% - 200px)" class="relative">
                        <!--                        <vnpt-select-->
                        <!--                            [control]="controlComboSelectDevice"-->
                        <!--                            class="w-full"-->
                        <!--                            [(value)]="alertInfo.deviceId"-->
                        <!--                            [placeholder]="tranService.translate('permission.Device.Device')"-->
                        <!--                            objectKey="device"-->
                        <!--                            paramKey="deviceName"-->
                        <!--                            keyReturn="id"-->
                        <!--                            displayPattern="${deviceName}"-->
                        <!--                            typeValue="primitive"-->
                        <!--                            [isMultiChoice]="false"-->
                        <!--                            [disabled]="true"-->
                        <!--                            [pKeyFilter]="vietnamesePattern"-->
                        <!--                        ></vnpt-select>-->
                        <span>{{alertInfo.deviceName}}</span>
                    </div>
                </div>
            </div>
        </div>


        <div class="ml-2 my-4 flex flex-row justify-content-start align-items-center gap-3">
            <h4 class="ml-2 mr-2">{{ tranService.translate("alert.text.labelAlert") }}<span
                class="text-red-500">*</span></h4>
            <div>
                <p-multiSelect styleClass="w-full"
                               id="sendingMethod"
                               [(ngModel)]="alertInfo.sendingMethod"
                               [required]="true"
                               formControlName="sendingMethod"
                               [options]="optionSend"
                               [readonly]="true"
                               [placeholder]="tranService.translate('alert.text.actionType')"
                               (onChange)="changeTypeSendAlert()"
                ></p-multiSelect>
            </div>
        </div>


        <div class="p-3 pt-0 shadow-2 border-round-md m-1  " *ngIf="alertInfo.sendingMethod.length > 0">
            <p-tabView class="w-full">
                <p-tabPanel *ngIf="alertInfo.sendingMethod.includes('Email')"
                            [header]="tranService.translate('alert.label.sendMail')">
                    <div class="flex flex-column w-full">
                        <div class="flex w-full justify-content-center align-items-center">
                            <!-- email -->
                            <div class="col">
                                <div class="col-9 flex flex-row justify-content-center pb-0">
                                    <label class="col-fixed" htmlFor="emailList"
                                           style="width:200px; height: fit-content;">{{ tranService.translate("alert.label.emails") }}
                                        <span class="text-red-500">*</span></label>
                                    <div style="width: calc(100% - 200px)" class="align-content-center">
<!--                            <textarea class="w-full" style="resize: none;"-->
<!--                                      rows="3"-->
<!--                                      [autoResize]="false"-->
<!--                                      pInputTextarea id="emailList"-->
<!--                                      [(ngModel)]="alertInfo.emailList"-->
<!--                                      formControlName="emailList"-->
<!--                                      [readonly]="true"-->
<!--                                      [placeholder]="tranService.translate('alert.text.inputemails')"-->
<!--                                      pattern="^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}(?:, ?[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})*$"-->
<!--                                      [required]="true"-->
<!--                            ></textarea>-->
                                        <span>{{alertInfo.emailList}}</span>
                                    </div>
                                </div>

                            </div>
                        </div>

                        <div class="col">
                            <!-- noi dung email -->
                            <div class="col-9 flex flex-row justify-content-center pb-0">
                                <label class="col-fixed" htmlFor="emailContent"
                                       style="width:200px; height: fit-content;">{{ tranService.translate("alert.label.contentEmail") }}
                                    <span class="text-red-500">*</span></label>
                                <div style="width: calc(100% - 200px);" class="align-content-center">
<!--                            <textarea class="w-full" style="resize: none;"-->
<!--                                      rows="5"-->
<!--                                      [autoResize]="false"-->
<!--                                      pInputTextarea id="emailContent"-->
<!--                                      [(ngModel)]="alertInfo.emailContent"-->
<!--                                      formControlName="emailContent"-->
<!--                                      [maxlength]="255"-->
<!--                                      [readonly]="true"-->
<!--                                      [placeholder]="tranService.translate('alert.text.inputcontentEmail')"-->
<!--                                      [required]="true"-->
<!--                            ></textarea>-->
                                    <span [innerHTML]="getContent(alertInfo.emailContent)"></span>
                                </div>
                            </div>
                        </div>
                    </div>
                </p-tabPanel>
                <p-tabPanel *ngIf="alertInfo.sendingMethod.includes('SMS')"
                            [header]="tranService.translate('alert.label.sendSMS')">
                    <div class="flex flex-column w-full">
                        <!--                <div class="flex col-3 justify-content-center align-items-center">-->
                        <!--                    <div class="field-radiobutton">-->
                        <!--                        <p-radioButton-->
                        <!--                            name="sendingMethod"-->
                        <!--                            value="email"-->
                        <!--                            [(ngModel)]="alertInfo.sendingMethod"-->
                        <!--                            formControlName="sendingMethod"-->
                        <!--                            [label]="tranService.translate('alert.label.sendMail')">-->
                        <!--                        </p-radioButton>-->
                        <!--                    </div>-->
                        <!--                </div>-->
                        <div class="flex w-full justify-content-center align-items-center">
                            <!-- SMS -->
                            <div class="col">
                                <div class="col-9 flex flex-row justify-content-center pb-0">
                                    <label class="col-fixed" htmlFor="msisdnsNotify"
                                           style="width:200px; height: fit-content;">{{ tranService.translate("alert.label.sms") }}
                                        <span class="text-red-500">*</span></label>
                                    <div style="width: calc(100% - 200px)" class="align-content-center">
<!--                            <textarea class="w-full" style="resize: none;"-->
<!--                                      rows="1"-->
<!--                                      [autoResize]="false"-->
<!--                                      pInputTextarea id="msisdnsNotify"-->
<!--                                      [readonly]="true"-->
<!--                                      [(ngModel)]="alertInfo.msisdnsNotify"-->
<!--                                      formControlName="msisdnsNotify"-->
<!--                                      [placeholder]="tranService.translate('alert.text.inputsms')"-->
<!--                                      pattern="^(?:0|84)\d{9,10}(?:, ?(?:0|84)\d{9,10})*$"-->
<!--                                      [required]="true"-->
<!--                            ></textarea>-->
                                        <span>{{alertInfo.msisdnsNotify}}</span>
                                    </div>
                                </div>

                            </div>
                        </div>

                        <div class="col">
                            <!-- noi dung sms -->
                            <div class="col-9 flex flex-row justify-content-center pb-0">
                                <label class="col-fixed" htmlFor="smsContent"
                                       style="width:200px; height: fit-content;">{{ tranService.translate("alert.label.contentSms") }}
                                    <span class="text-red-500">*</span></label>
                                <div style="width: calc(100% - 200px);" class="align-content-center">
<!--                            <textarea class="w-full" style="resize: none;"-->
<!--                                      rows="5"-->
<!--                                      [autoResize]="false"-->
<!--                                      pInputTextarea id="smsContent"-->
<!--                                      [(ngModel)]="alertInfo.smsContent"-->
<!--                                      formControlName="smsContent"-->
<!--                                      [readonly]="true"-->
<!--                                      [maxlength]="255"-->
<!--                                      [placeholder]="tranService.translate('alert.text.inputcontentEmail')"-->
<!--                                      [required]="true"-->
<!--                            ></textarea>-->
                                    <span [innerHTML]="getContent(alertInfo.smsContent)"></span>
                                </div>
                            </div>
                        </div>
                    </div>
                </p-tabPanel>
                <p-tabPanel *ngIf="alertInfo.sendingMethod.includes('Zalo')"
                            [header]="tranService.translate('alert.label.sendZalo')">
                    <div class="flex flex-column w-full">
                        <div class="flex w-full justify-content-center align-items-center">
                            <!-- SMS -->
                            <div class="col">
                                <div class="col-9 flex flex-row justify-content-center pb-0">
                                    <label class="col-fixed" htmlFor="zaloNotify"
                                           style="width:200px; height: fit-content;">{{ tranService.translate("alert.label.zalo") }}
                                        <span class="text-red-500">*</span></label>
                                    <div style="width: calc(100% - 200px)" class="align-content-center">
<!--                            <textarea class="w-full" style="resize: none;"-->
<!--                                      rows="1"-->
<!--                                      [autoResize]="false"-->
<!--                                      pInputTextarea id="zaloNotify"-->
<!--                                      [(ngModel)]="alertInfo.zaloNotify"-->
<!--                                      [readonly]="true"-->
<!--                                      formControlName="zaloNotify"-->
<!--                                      [placeholder]="tranService.translate('alert.text.inputZalo')"-->
<!--                                      pattern="^(?:0|84)\d{9,10}(?:, ?(?:0|84)\d{9,10})*$"-->
<!--                                      [required]="true"-->
<!--                            ></textarea>-->
                                        <span>{{alertInfo.zaloNotify}}</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col">
                            <!-- noi dung sms -->
                            <div class="col-9 flex flex-row justify-content-center pb-0">
                                <label class="col-fixed" htmlFor="smsContent"
                                       style="width:200px; height: fit-content;">{{ tranService.translate("alert.label.contentZalo") }}
                                    <span class="text-red-500">*</span></label>
                                <div style="width: calc(100% - 200px);" class="align-content-center">
<!--                            <textarea class="w-full" style="resize: none;"-->
<!--                                      rows="5"-->
<!--                                      [autoResize]="false"-->
<!--                                      [readonly]="true"-->
<!--                                      pInputTextarea id="zaloContent"-->
<!--                                      [(ngModel)]="alertInfo.zaloContent"-->
<!--                                      formControlName="zaloContent"-->
<!--                                      [maxlength]="255"-->
<!--                                      [placeholder]="tranService.translate('alert.text.inputcontentEmail')"-->
<!--                                      [required]="true"-->
<!--                            ></textarea>-->
                                    <span [innerHTML]="getContent(alertInfo.zaloContent)"></span>
                                </div>
                            </div>
                        </div>
                    </div>
                </p-tabPanel>
            </p-tabView>

        </div>
    </p-card>
</form>
