import { NgModule } from "@angular/core";
import { RouterModule } from "@angular/router";
import DataPage from "../../service/data.page";
import {BosSsoComponent} from "./bos-sso.component";

@NgModule({
    imports:[
        RouterModule.forChild([
            {path: "", component: BosSsoComponent, data: new DataPage("login.label.signIn")},
        ])
    ],
    exports: [RouterModule]
})
export class BosRoutingModule {}
