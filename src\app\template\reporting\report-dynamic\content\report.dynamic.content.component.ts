import { Component, Injector, OnInit } from "@angular/core";
import { MenuItem } from "primeng/api";
import { ComponentBase } from "src/app/component.base";
import { ReportService } from "src/app/service/report/ReportService";
import { ParameterInfo, TableReportInfo } from "../components/tab.report.dynamic.general";
import { FormBuilder } from "@angular/forms";
import { CONSTANTS } from "src/app/service/comon/constants";
import { ColumnInfo, OptionTable } from "src/app/template/common-module/table/table.component";
import { dA } from "@fullcalendar/core/internal-common";
import { ComboLazyControl } from "src/app/template/common-module/combobox-lazyload/combobox.lazyload";
import {TimeoutError} from "rxjs";
import {HttpErrorResponse} from "@angular/common/http";

@Component({
    selector: "report-dynamic-content",
    templateUrl: "./report.dynamic.content.component.html"
})
export class ReportDynamicContentComponent extends ComponentBase implements OnInit {
    constructor(injector: Injector, private formBuilder: FormBuilder,
                private reportService: ReportService) {
        super(injector);
    }

    reportDetail: any;
    idReport: number;
    items: MenuItem[];
    home: MenuItem;

    listTable: Array<TableReportInfo> = [];
    listParameters: Array<ParameterInfo> = [];

    formSearch: any;
    searchInfo: any;

    paramTypes = CONSTANTS.PARAMETER_TYPE;
    dateTypes = CONSTANTS.DATE_TYPE;

    menuTable: MenuItem[] = [];
    defaultTableActive: MenuItem = null;
    activeTable: any;
    tables: Array<any> = [];
    mapTable: {
        [key: string | number]: {
            selectItems: Array<any>,
            columns: ColumnInfo[],
            dataSet: {
                content: Array<any>,
                total: number
            },
            optionTable: OptionTable,
            loadData(page, size, sort, params):void,
            pageNumber: number,
            pageSize: number,
            sort: string,
            params: any,
            dataOrigin: Array<any>
        }
    } ={};

    ngOnInit(): void {
        this.idReport = parseInt(this.route.snapshot.paramMap.get("id"));
        this.getReportDetail();
    }

    getReportDetail(){
        let me = this;
        this.reportService.getDetailReportDynamic(this.idReport, (response)=>{
            me.reportDetail = response;
            setTimeout(function(){
                me.loadPage();
            })
        })
    }

    loadPage(){
        this.items = [{ label: this.tranService.translate("global.menu.report")},
                        { label: this.tranService.translate("global.menu.dynamicreport"),routerLink: '/reports/report-dynamic'},
                        { label: this.tranService.translate("permission.RptContent.RptContent"),routerLink: '/reports/report-dynamic/report-content'},
                        { label: this.reportDetail.name}
                    ];
        this.home = { icon: 'pi pi-home', routerLink: '/' };
        this.loadListParams();
        this.loadTables();
    }

    loadListParams(){
        let me = this;
        if(this.reportDetail.filterParams){
            this.listParameters = JSON.parse(this.reportDetail.filterParams);
            this.listParameters.forEach(el => {
                if(el.prType == this.paramTypes.LIST_NUMBER || el.prType == this.paramTypes.LIST_STRING || (el.prType == this.paramTypes.STRING && el.isAutoComplete == true)){
                    el["control"] = new ComboLazyControl();
                }
            })
            this.searchInfo = {};
            this.listParameters.forEach(el => {
                me.searchInfo[el.prKey] = null;
            })
            this.formSearch = this.formBuilder.group(this.searchInfo);
        }
    }

    loadTables(){
        let me = this;
        if(this.reportDetail.reportContents){
            this.reportDetail.reportContents.forEach(el => {
                me.menuTable.push({
                    id: el.id,
                    label: el.tableName,
                    command: ()=>{
                        me.activeTable = el.id;
                    }
                })
                let columnKeys = el.columnQueryResult.split(",");
                let columnDisplays = el.columnDisplay.split(",");
                let columns = [];
                columnKeys.forEach((el, index)=>{
                    columns.push({
                        key: el,
                        name: columnDisplays[index],
                    })
                })
                me.mapTable[el.id] = {
                    selectItems: [],
                    columns: columns.map(function(el): ColumnInfo {
                        let object: ColumnInfo = {
                            key: el.key,
                            name: el.name,
                            align: "left",
                            isShow: true,
                            isSort: false,
                            size: `calc((100% - 100px) / ${columns.length})`,
                        }
                        return object;
                    }),
                    dataSet: {
                        content: [],
                        total: 0
                    },
                    dataOrigin: [],
                    loadData(page, size, sort, params) {
                        me.loadDataTable(el.id, page, size, sort, params);
                    },
                    optionTable: {
                        hasClearSelected: true,
                        action: null,
                        hasShowChoose: false,
                        hasShowIndex: false,
                        hasShowJumpPage: true,
                        hasShowToggleColumn: false,
                        paginator: true
                    },
                    pageNumber: 0,
                    pageSize: 10,
                    params: {},
                    sort: `${columns[0].key},asc`
                }
            })
            this.defaultTableActive = this.menuTable[0];
            this.activeTable = this.defaultTableActive.id;
            this.tables = [...this.reportDetail.reportContents];
        }
    }

    prepareData(){
        let data = {
            id: this.reportDetail.id,
            customerCodes: null,
            paramsValue: []
        };
        this.listParameters.forEach(el => {
            if(el.prType == CONSTANTS.PARAMETER_TYPE.DATE){
                data.paramsValue.push({
                    prKey: el.prKey,
                    value: this.searchInfo[el.prKey] ? this.utilService.convertDateTimeToString(this.searchInfo[el.prKey]) : null
                })
            }else if(el.prType == CONSTANTS.PARAMETER_TYPE.TIMESTAMP){
                data.paramsValue.push({
                    prKey: el.prKey,
                    value: this.searchInfo[el.prKey] ? this.searchInfo[el.prKey].getTime() : null
                })
            }else if(el.prType == CONSTANTS.PARAMETER_TYPE.NUMBER){
                data.paramsValue.push({
                    prKey: el.prKey,
                    value: this.searchInfo[el.prKey] ? this.searchInfo[el.prKey] * 1 : null
                })
            }else if(el.prType == CONSTANTS.PARAMETER_TYPE.LIST_NUMBER || el.prType == CONSTANTS.PARAMETER_TYPE.LIST_STRING){
                if(el.isMultiChoice){
                    if(this.searchInfo[el.prKey]==null||this.searchInfo[el.prKey].length == 0){
                        data.paramsValue.push({
                            prKey: el.prKey,
                            value: null
                        })
                    }else{
                        data.paramsValue.push({
                            prKey: el.prKey,
                            value: this.searchInfo[el.prKey] ? this.searchInfo[el.prKey] : null
                        })
                    }
                }else{
                    data.paramsValue.push({
                        prKey: el.prKey,
                        value: this.searchInfo[el.prKey] ? [this.searchInfo[el.prKey]] : null
                    })
                }
            }else{
                data.paramsValue.push({
                    prKey: el.prKey,
                    value: this.searchInfo[el.prKey] ? this.searchInfo[el.prKey] : null
                })
            }
        })
        return data;
    }

    getParamDefault(param): { [key: string]: string } {
        const parsed = this.parseKeyValuePairs(param.queryParam);

        // Khởi tạo đối tượng trống để lưu các cặp key-value
        const result: { [key: string]: string } = {};

        if (param.isAutoComplete) {
            // Sử dụng vòng lặp để thêm nhiều cặp key-value vào đối tượng
            for (let i = 0; i < parsed.keys.length; i++) {
                result[parsed.keys[i]] = parsed.values[i] || null;
            }
        }

        return result;
    }

    parseKeyValuePairs(input: string): { keys: string[], values: string[] } {
        // Khởi tạo đối tượng để lưu các cặp key-value
        const keyValueMap: { [key: string]: string[] } = {};

        // Tách chuỗi thành các cặp key-value
        const pairs = input.split('&');

        // Xử lý từng cặp
        for (const pair of pairs) {
            const [key, value] = pair.split('=');

            // Xử lý giá trị bắt đầu bằng $
            let actualValue: string;
            if (value.startsWith('$')) {
                actualValue = this.getValueData(value.substring(1)); // Loại bỏ dấu $ và gọi hàm
            } else {
                // Xử lý giá trị không bắt đầu bằng $
                actualValue = value.replace(/^"|"$/g, ''); // Loại bỏ dấu ngoặc kép ở đầu và cuối nếu có
            }

            // Thêm giá trị vào đối tượng keyValueMap
            if (key in keyValueMap) {
                keyValueMap[key].push(actualValue);
            } else {
                keyValueMap[key] = [actualValue];
            }
        }

        // Tạo mảng keys và values từ đối tượng keyValueMap
        const keys: string[] = [];
        const values: string[] = [];

        for (const key in keyValueMap) {
            keys.push(key);
            values.push(keyValueMap[key].join(',')); // Nối các giá trị với dấu phẩy
        }

        // console.log(keys, values);

        // Trả về cả mảng keys và values
        return { keys, values };
    }

    getValueData(key){
        let returnValue
        if(this.getTypeKey(key) == CONSTANTS.PARAMETER_TYPE.DATE){
            returnValue = this.searchInfo[key] ? this.utilService.convertDateTimeToString(this.searchInfo[key]) : null
        }else if(this.getTypeKey(key) == CONSTANTS.PARAMETER_TYPE.TIMESTAMP){
            returnValue = this.searchInfo[key] ? this.searchInfo[key].getTime() : null
        }else if(this.getTypeKey(key) == CONSTANTS.PARAMETER_TYPE.NUMBER){
            returnValue = this.searchInfo[key] ? this.searchInfo[key] * 1 : null
        }else if(this.getTypeKey(key) == CONSTANTS.PARAMETER_TYPE.LIST_NUMBER || this.getTypeKey(key) == CONSTANTS.PARAMETER_TYPE.LIST_STRING){
            returnValue = this.searchInfo[key] ? this.searchInfo[key] : null
        }else{
            returnValue = this.searchInfo[key] ? this.searchInfo[key] : null
        }
        return returnValue
    }

    getTypeKey(key){
        const value = this.listParameters.find(data => data.prKey === key)
        return value ? value.prType :null
    }

    onSubmitSearch(){
        if(!this.reportDetail.enablePreview) return;
        if(!this.checkValidForm()) return;
        let me = this;
        let data = this.prepareData();
        this.messageCommonService.onload();
        this.reportService.preview(data, (response)=>{
            if(response.reportContents){
                response.reportContents.forEach(tableContent => {
                    me.mapTable[tableContent.reportContentID].dataOrigin = tableContent.data.filter(el => Object.keys(el).length > 0);
                    me.loadDataTable(tableContent.reportContentID, 0, me.mapTable[tableContent.reportContentID].pageSize, me.mapTable[tableContent.reportContentID].sort, {})
                })
            }
            // console.log(me.mapTable)
        }, null, ()=>{
            me.messageCommonService.offload();
        })
    }

    loadDataTable(tableId, page, size, sort, param){
        this.mapTable[tableId].pageNumber = page;
        this.mapTable[tableId].pageSize = size;
        this.mapTable[tableId].sort = sort;
        this.mapTable[tableId].params = param;
        let dataSet = {
            content: this.mapTable[tableId].dataOrigin.slice(page * size, page*size + size),
            total: this.mapTable[tableId].dataOrigin.length
        }
        this.mapTable[tableId].dataSet = dataSet;
    }

    export(){
        if(!this.checkValidForm()) return;
        let data = this.prepareData();
        this.messageCommonService.onload();
        this.reportService.exportFile(data,()=>{}, (error)=>{
            console.log(error);
            let me = this;
            if(error.error instanceof Blob){
                error.error.text().then(function(resultText){
                    let bodyError = JSON.parse(resultText).error;
                    if(bodyError.errorCode == "error.report.excel.limit") {
                        me.messageCommonService.error(me.tranService.translate("report.text.errorExportLimit"))
                    }else{
                        me.messageCommonService.error(me.tranService.translate("global.message.error"))
                    }
                })
            }else{
                console.log(error)
            }
        });
    }

    checkValidForm(){
        if(!this.formSearch) return false;
        if(this.formSearch.invalid) return false;
        if((this.listParameters || []).length > 0){
            for(let i = 0; i < this.listParameters.length;i++){
                let el = this.listParameters[i];
                if(el["control"]){
                    if(el["control"].invalid){
                        return false;
                    }
                }
            }
        }
        return true;
    }

    preventCharacter(event){
        if(event.ctrlKey || event.altKey || event.shiftKey){
            return;
        }
        if(event.keyCode == 8 || event.keyCode == 13 || event.keyCode == 46 || event.keyCode == 37 || event.keyCode == 39){
            return;
        }
        if(event.keyCode < 48 || event.keyCode > 57){
            event.preventDefault();
        }
    }

    checkIsNumberOrNull(key){
        let me = this;
        if(this.searchInfo[key] == null) return;
        if(isNaN(this.searchInfo[key])){
            setTimeout(function(){
                me.searchInfo[key] = null;
            })
        }
    }
}
