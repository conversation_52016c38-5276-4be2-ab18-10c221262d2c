import { CommonModule } from "@angular/common";
import { NgModule } from "@angular/core";
import { ReportRoutingModule } from "./app.report.routing.module";
import { BreadcrumbModule } from "primeng/breadcrumb";
import { FieldsetModule } from "primeng/fieldset";
import { FormsModule, ReactiveFormsModule } from "@angular/forms";
import { InputTextModule } from "primeng/inputtext";
import { ButtonModule } from "primeng/button";
import { CommonVnptModule } from "../common-module/common.module";
import { SplitButtonModule } from "primeng/splitbutton";
import { DropdownModule } from "primeng/dropdown";
import { AutoCompleteModule } from "primeng/autocomplete";
import { CalendarModule } from "primeng/calendar";
import { DialogModule } from "primeng/dialog";
import { CardModule } from "primeng/card";
import { SplitterModule } from "primeng/splitter";
import { ToggleButtonModule } from "primeng/togglebutton";
import { RadioButtonModule } from "primeng/radiobutton";
import { MultiSelectModule } from "primeng/multiselect";
import { InputTextareaModule } from "primeng/inputtextarea";
import { InputNumberModule } from 'primeng/inputnumber';
import { TabReportDynamicCrontab } from "./report-dynamic/components/tab.report.dynamic.crontab";
import { TabReportDynamicGeneral } from "./report-dynamic/components/tab.report.dynamic.general";
import { TabReportDynamicSend } from "./report-dynamic/components/tab.report.dynamic.send";
import { TabMenuModule } from 'primeng/tabmenu';
import { CheckboxModule } from 'primeng/checkbox';
import { DividerModule } from 'primeng/divider';
import { ReportReceivingGroupComponent } from "./report-receiving-group/report.receiving.group.component";
import { ReportGroupReceivingEditComponent } from "./report-receiving-group/edit/app.group-receiving.edit.component";
import { ReportGroupReceivingDetailComponent } from "./report-receiving-group/detail/app.group-receiving.detail.component";
import { ReportGroupReceivingCreateComponent } from "./report-receiving-group/create/app.group-receiving.create.component";
import { AccountService } from "src/app/service/account/AccountService";
import { ReportReceivingGroupService } from "src/app/service/report-receiving-group/ReportReceivingGroup";
import { ReportService } from "src/app/service/report/ReportService";
import { ReportDynamicFormComponent } from "./report-dynamic/components/report.dynamic.form.component";
import {ReportDynamicListComponent} from "./report-dynamic/list/report.dynamic.list.component";
import { ReportDynamicContentComponent } from "./report-dynamic/content/report.dynamic.content.component";
import { RatingPlanService } from "src/app/service/rating-plan/RatingPlanService";
import { SimService } from "src/app/service/sim/SimService";
import { ReportDynamicListContentComponent } from "./report-dynamic/list-content/report.dynamic.list.content";
import {PanelModule} from "primeng/panel";

@NgModule({
    imports: [
        CommonModule,
        ReportRoutingModule,
        BreadcrumbModule,
        FieldsetModule,
        FormsModule,
        ReactiveFormsModule,
        InputTextModule,
        ButtonModule,
        CommonVnptModule,
        SplitButtonModule,
        DropdownModule,
        AutoCompleteModule,
        CalendarModule,
        DialogModule,
        CardModule,
        SplitterModule,
        ToggleButtonModule,
        RadioButtonModule,
        MultiSelectModule,
        InputTextareaModule,
        TabMenuModule,
        CheckboxModule,
        DividerModule,
        InputNumberModule,
        PanelModule
    ],
    declarations: [
        ReportDynamicListComponent,
        TabReportDynamicCrontab,
        TabReportDynamicGeneral,
        TabReportDynamicSend,
        ReportReceivingGroupComponent,
        ReportGroupReceivingEditComponent,
        ReportGroupReceivingDetailComponent,
        ReportGroupReceivingCreateComponent,
        ReportDynamicFormComponent,
        ReportDynamicContentComponent,
        ReportDynamicListContentComponent
    ],
    exports: [

    ],
    providers: [
        ReportReceivingGroupService,
        AccountService, ReportService, RatingPlanService, SimService
    ]
})
export class ReportModule {}
