export default {
    label: {
        planName: "Tên gói cước",
        planCode: "Mã gói cước",
        status: "Trạng thái",
        cycleTimeUnit: "Đơn vị chu kỳ",
        cycle: "<PERSON> kỳ",
        paidType: "<PERSON><PERSON><PERSON> thức thanh toán",
        customerType: "Loại khách hàng",
        ratingScope: "Phạm vi gói cước",
        subscriptionFee: "Giá gói cước (VND)",
        limitDataUsage: "Dung lượng miễn phí (MB)",
        dispatchCode:"Mã công văn",
        cycleInterval: "Số lượng",
        reload: "Tự động gia hạn",
        flat: "Định mức",
        flexible: "Vượt định mức",
        limitSmsInside: "Số lượng tin nhắn nội mạng miễn phí ",
        limitSmsOutside: "Số lượng tin nhắn ngoại mạng miễn phí ",
        squeezedSpeed: "<PERSON><PERSON> băng thông",
        feeSmsInside: "Phí trên 1 SMS nội mạng",
        feeSmsOutside: "Phí trên 1 SMS ngoại mạng",
        maximumFee: "Ngưỡng cước nóng ",
        feePerDataUnit: "Phí trên một đơn vị data ",
        province:"Tỉnh/Thành phố",
        duration:"Số lượng",
        autoReload:"Tự động gia hạn",
        description:"Mô tả",
        freeData:"Dung lượng miễn phí",
        insideSMSFree:"Số lượng tin nhắn nội mạng miễn phí",
        outsideSMSFree:"Số lượng tin nhắn ngoại mạng miễn phí",
        feePerUnit:"Phí trên một đơn vị data",
        squeezeSpeed:"Hạ băng thông",
        feePerInsideSMS:"Phí trên 1 SMS nội mạng",
        feePerOutsideSMS:"Phí trên 1 SMS ngoại mạng",
        maxFee:"Ngưỡng cước nóng",
        assignPlan:"Gán gói cước",
        email:"Thư điện tử",
        username: "Tên đăng nhập",
        fullName: "Họ và tên",
        dataMax: "Dung lượng (MB)",
    },
    placeHolder:{
        planCode:"Nhập mã gói cước",
        planeName:"Nhập tên gói cước",
        dispatchCode:"Nhập mã công văn",
        customerType:"Chọn loại khách hàng",
        description:"Nhập mô tả",
        subscriptionFee:"Nhập giá gói cước",
        subscriptionType:"Chọn loại gói cước",
        planScope:"Chọn phạm vi",
        provinceCode:"Chọn Tỉnh/Thành phố",
        planCycle:"Chọn chu kỳ",
        duration:"Nhập số lượng",
        freeData:"Nhập dung lượng miễn phí",
        insideSMSFree:"Nhập số lượng tin nhắn nội mạng miễn phí",
        outsideSMSFree:"Nhập số lượng tin nhắn ngoại mạng miễn phí",
        feePerUnit:"Nhập phí trên một đơn vị data",
        squeezeSpeed:"Nhập mức hạ băng thông",
        feePerInsideSMS:"Nhập phí trên 1 SMS nội mạng",
        feePerOutsideSMS:"Nhập phí trên 1 SMS ngoại mạng",
        maxFee:"Nhập ngưỡng cước nóng",
        dataMax: "Dung lượng",
    },
    text: {
        textRegisterSuccess: "Gửi đăng ký thành công",
        textChangeSuccess: "Gửi yêu cầu đổi thành công",
        textCancelSuccess: "Gửi yêu cầu hủy thành công",
        textResultSuccess: "Gửi đăng ký thành công cho ${success}/${total} thuê bao có trạng thái Khóa 1 chiều, Khóa 2 chiều",
        textResultFail: "${fail}/${total} thuê bao ở danh sách bên dưới không đăng ký thành công do trạng thái thuê bao không phải là Khóa 1 chiều, Khóa 2 chiều. Hãy kiểm tra lại!",
        textResultRegisterByFile: "Đã đăng ký thành công cho ${error}/${total} thuê bao có thông tin hợp lệ. Các thông tin không hợp lệ ở danh sách lỗi.",
        textResultRegisterGroupSim: "Đã đăng ký thành công cho ${error}/${total} thuê bao có thông tin hợp lệ. Các thông tin không hợp lệ ở danh sách lỗi.",
        textDong: "(đồng/tháng",
        vat: "đã gồm VAT)",
        dayMonth: "(ngày/tháng)",
    },
    status: {
        create: "Tạo mới",
        pending: "Chờ duyệt",
        activated: "Kích hoạt",
        deactivated: "Tạm ngưng"
    },
    error: {
        requiredError:"Trường này là bắt buộc",
        lengthError_255:"Trường này không được vượt quá 255 ký tự",
        lengthError_64:"Trường này không được vượt quá 64 ký tự",
        lengthError_16:"Trường này không được vượt quá 16 ký tự",
        lengthError_number:"Trường này không được vượt quá 10 ký tự số",
        existedCodeError:"Đã tồn tại mã gói cước này",
        existedNameError:"Đã tồn tại tên gói cước này",
        characterError:"Sai định dạng. Chỉ cho phép (a-z, A-Z, 0-9, . - _, dấu cách, tiếng Viết)",
        characterError_code:"Sai định dạng. Chỉ cho phép (a-z, A-Z, 0-9, - _)",
        onlyPositive:"Không chấp nhận ký tự \" - \"",
    },
    customerType: {
        enterprise: "Doanh nghiệp",
        personal: "Cá nhân",
        agency: "Đại lý",
    },

    subscriptionType: {
        post: "Trả sau",
        pre: "Trả trước"
    },

    ratingScope: {
        nativeWide: "Toàn quốc",
        customer: "Khách hàng",
        province: "Tỉnh/Thành phố"
    },

    cycle: {
        day: "Ngày",
        week: "Tuần",
        month: "Tháng",
        year: "Năm",
    },
}
