import { Component, Inject, Injector, OnInit } from "@angular/core";
import { FormBuilder } from "@angular/forms";
import { MenuItem } from "primeng/api";
import { ComponentBase } from "src/app/component.base";
import { AccountService } from "src/app/service/account/AccountService";
import { CONSTANTS } from "src/app/service/comon/constants";
import { ObservableService } from "src/app/service/comon/observable.service";
import {b} from "@fullcalendar/core/internal-common";

@Component({
    selector: "term-policy-history",
    templateUrl: './app.term.policy.history.component.html'
})
export class TermPolicyHistoryComponent extends ComponentBase implements OnInit{
    constructor(
        private formBuilder: FormBuilder,
        private accountService: AccountService,
        private injector: Injector) {
            super(injector);
    }

    items: MenuItem[];
    home: MenuItem;
    formPersonalDataProtectionPolicy: any = null;
    isShowDisagreePersonalDataProtectionPolicy: boolean;
    rejectPolicy: boolean;
    dataHistory: {
        personalDataProtectionPolicy?: {
            username: string;
            fullname: string;
            email: string;
            deviceType?: string;
            os?: string;
            ip?: string;
            time: number;
        }
    }

    ngOnInit(): void {
        this.items = [{ label: this.tranService.translate("global.menu.accountmgmt") }, { label: this.tranService.translate("global.menu.termpolicyhistory") },];
        this.home = { icon: 'pi pi-home', routerLink: '/' };
        this.getData();
    }

    getData(): void {
        this.initData();
    }

    initData(): void {
        let me = this;
        this.dataHistory = {

        }
        this.isShowDisagreePersonalDataProtectionPolicy = false;
        this.rejectPolicy = false;
        this.sessionService.confirmPolicyHistory.forEach(policy => {
            if(policy.policyId == CONSTANTS.POLICY.PERSONAL_DATA_PROTECTION_POLICY && policy.status == CONSTANTS.POLICY_STATUS.AGREE){
                let info = JSON.parse(policy.info);
                me.dataHistory["personalDataProtectionPolicy"] = {
                    username: me.sessionService.userInfo.username,
                    fullname: me.sessionService.userInfo.fullName,
                    email: me.sessionService.userInfo.email,
                    deviceType: info.deviceType,
                    os: info.os,
                    ip: info.ip,
                    time: info.time
                }
            }
        })
        if(this.dataHistory.personalDataProtectionPolicy){
            this.formPersonalDataProtectionPolicy = this.formBuilder.group(this.dataHistory.personalDataProtectionPolicy)
        }else{
            this.formPersonalDataProtectionPolicy = null;
        }
    }

    confirmDisagreePersonalDataProtectionPolicy(){
        this.isShowDisagreePersonalDataProtectionPolicy = true;
        // this.messageCommonService.confirm(this.tranService.translate("title"), "Nếu bạn không đồng ý với Điều khoản và Chính sách sẽ không được tiếp tục sử dụng dịch vụ này.", {
        //     ok: this.disagreePersonalDataProtectionPolicy.bind(this)
        // })
    }

    disagreePersonalDataProtectionPolicy(){
        let me = this;
        this.messageCommonService.onload();
        this.accountService.disagreePolicy(CONSTANTS.POLICY.PERSONAL_DATA_PROTECTION_POLICY, (policy)=>{
            for(let i = 0;i < me.sessionService.confirmPolicyHistory.length;i++){
                if(me.sessionService.confirmPolicyHistory[i].policyId == policy.policyId){
                    me.sessionService.confirmPolicyHistory[i] = policy;
                }
            }
            let listPolicyChecked = JSON.parse(localStorage.getItem('listPolicyChecked') || "[]");
            localStorage.setItem('listPolicyChecked', JSON.stringify(listPolicyChecked.filter(el => el != CONSTANTS.POLICY.PERSONAL_DATA_PROTECTION_POLICY)))
            me.sessionService.setData("confirmPolicyHistory",JSON.stringify(me.sessionService.confirmPolicyHistory));
            me.initData();
            window.location.href = "/#/policies/history/";
        }, null, ()=>{
            me.messageCommonService.offload();
        })
    }
}
