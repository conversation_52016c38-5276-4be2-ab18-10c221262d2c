import { Inject, Injectable } from "@angular/core";
import { HttpService } from "../comon/http.service";

@Injectable()
export class SimService{
    private prefixApi: string;
    constructor(@Inject(HttpService) private httpService:HttpService) {
        this.prefixApi = "/sim-mgmt";
    }

    public demo(url:string, params:{[key: string]: string}, callback?:Function, errorCallBack?:Function, finallyCallback?:Function){
        this.httpService.get(url,{}, params,callback, errorCallBack, finallyCallback);
    }

    public search(params:{[key:string]:any}, callback?:Function, errorCallBack?:Function, finallyCallback?:Function){
        this.httpService.get(`${this.prefixApi}/search`,{timeout: 180000}, params,callback, errorCallBack, finallyCallback);
    }

    public quickSearch(params:{[key:string]:any}, callback?:Function, errorCallBack?:Function, finallyCallback?:Function){
        this.httpService.get(`${this.prefixApi}/quickSearch`,{timeout: 900000}, params,callback, errorCallBack, finallyCallback);
    }

    public searchNotInGroup(params:{[key:string]:any}, callback?:Function){
        this.httpService.get(`${this.prefixApi}/get-sim-not-in-group`,{}, params,callback);
    }

    public getById(msisdn: string, callback?:Function, errorCallBack?:Function, finallyCallback?:Function){
        this.httpService.get(`${this.prefixApi}/${msisdn}`,{},{}, callback, errorCallBack, finallyCallback);
    }

    public getDetailPlanSim(msisdn: string, callback?:Function, errorCallBack?:Function, finallyCallback?:Function){
        this.httpService.get(`${this.prefixApi}/detail-plan/${msisdn}`, {}, {}, callback, errorCallBack, finallyCallback);
    }

    public getByKey(key: string, value: string, callback?: Function, errorCallback?:Function, finallyCallback?: Function){
        this.httpService.get(`${this.prefixApi}/getByKey`,{}, {key, value}, callback, errorCallback, finallyCallback);
    }

    public getDetailContract(contractCode: string, callback?:Function, errorCallBack?:Function, finallyCallback?:Function){
        this.httpService.get(`${this.prefixApi}/detail-contract/${contractCode}`, {timeout: 120000}, {}, callback, errorCallBack, finallyCallback);
    }

    public getDetailStatus(msisdn: string, callback,errorCallBack?:Function, finallyCallback?:Function){
        this.httpService.get(`${this.prefixApi}/detail-status/${msisdn}`, {}, {}, callback, errorCallBack, finallyCallback);
    }

    public pushSimToGroup(sims: Array<number>, group:{id: number|null, groupKey?:string,name?:string,customer?:string,description?:string|null}, callback:Function, errorCallBack?:Function, finallyCallback?:Function){
        this.httpService.post(`${this.prefixApi}/add-sim-to-group`,{},{sims, group}, {},callback,errorCallBack,finallyCallback);
    }

    public removeSIMFromGroup(msisdnList: Array<string>, groupId:number,callback:Function){
        this.httpService.post(`/group-sim/remove-sim-group`,{},{msisdnList, groupId},{},callback)
    }

    public exportSim(params: any){
        this.httpService.download(`${this.prefixApi}/export-sim`, {timeout: 900000},params);
    }

    public exportSimSelected(data: any){
        this.httpService.downloadPost(`${this.prefixApi}/export-sim`, {},data,{});
    }

    public exportExels(params: any){
        this.httpService.download(`${this.prefixApi}/export-sim-excel`, {timeout: 600000},params);
    }

    public exportExelSelected(data: any){
        this.httpService.downloadPost(`${this.prefixApi}/export-sim-excel`, {timeout: 600000},data,{});
    }

    public getSimByContractCode(params:any, callback){
        this.httpService.get(this.prefixApi+"/get-by-contract",{timeout: 180000},params, callback)
    }

    public getListByCustomerCode(query:{[key:string]:any}, callback?:Function, errorCallBack?:Function, finallyCallback?:Function){
        this.httpService.get(`${this.prefixApi}/get-by-customer-code`,{}, query,callback, errorCallBack, finallyCallback);
    }

    public deleteListSim(ids:Array<String>, callback?:Function, errorCallBack?:Function, finallyCallback?:Function){
        this.httpService.post(`${this.prefixApi}/delete-sim`,{},{ids},{},callback, errorCallBack, finallyCallback);
    }
    // get connection status
    public getConnectionStatus(body, callback?:Function, errorCallBack?:Function, finallyCallback?:Function){
        this.httpService.post('/media/getStatus',{}, body,{}, callback, errorCallBack, finallyCallback);
    }

    // get data used
    public getDataUsed(body, callback?:Function, errorCallBack?:Function, finallyCallback?:Function){
        this.httpService.postNoError(`${this.prefixApi}/get-data-used`,{}, body,{}, callback, errorCallBack, finallyCallback);
    }

    public getSimInfoForLog(list: Array<any>, callback:Function, errorCallBack?:Function, finallyCallback?:Function){
        this.httpService.post(`${this.prefixApi}/get-sim-info-for-log`,{},list,{},callback)
    }

    public loadDropdown(params:{[key:string]:any}, callback?:Function, errorCallBack?:Function, finallyCallback?:Function){
        this.httpService.get(`${this.prefixApi}/dropdown`,{timeout: 180000}, params,callback, errorCallBack, finallyCallback);
    }
}
