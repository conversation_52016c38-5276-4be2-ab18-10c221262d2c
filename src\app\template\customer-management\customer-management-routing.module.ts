import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { ListCustomerComponent } from './list-customer/list-customer.component';
import { DetailCustomerComponent } from './detail-customer/detail-customer.component';
import { UpdateCustomerComponent } from './update-customer/update-customer.component';
import DataPage from 'src/app/service/data.page';
import { CONSTANTS } from 'src/app/service/comon/constants';

const routes: Routes = [
  {path: "", component: ListCustomerComponent, data: new DataPage("global.menu.listcustomer", [CONSTANTS.PERMISSIONS.CUSTOMER.VIEW_LIST])},
  {path: "detail/:id", component: DetailCustomerComponent,data: new DataPage("global.titlepage.detailCustomer",[CONSTANTS.PERMISSIONS.CUSTOMER.VIEW_DETAIL])},
  {path: "update/:id", component: UpdateCustomerComponent,data: new DataPage("global.titlepage.editCustomer",[CONSTANTS.PERMISSIONS.CUSTOMER.UPDATE])},
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class CustomerManagementRoutingModule { }
