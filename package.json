{"name": "sakai-ng", "version": "16.0.0", "license": "PrimeNG Commercial", "scripts": {"ng": "ng", "start": "ng serve --port 9090 --host 0.0.0.0", "build": "ng build", "test": "ng test", "lint": "ng lint", "e2e": "ng e2e"}, "private": true, "dependencies": {"@angular/animations": "^16.1.0", "@angular/cdk": "^16.1.0", "@angular/common": "^16.1.0", "@angular/compiler": "^16.1.0", "@angular/core": "^16.1.0", "@angular/forms": "^16.1.0", "@angular/platform-browser": "^16.1.0", "@angular/platform-browser-dynamic": "^16.1.0", "@angular/router": "^16.1.0", "@fullcalendar/angular": "^6.0.3", "@fullcalendar/core": "^6.0.3", "@fullcalendar/daygrid": "^6.0.3", "@fullcalendar/interaction": "^6.0.3", "@fullcalendar/timegrid": "^6.0.3", "@types/grecaptcha": "^2.0.35", "chart.js": "^3.3.2", "chartjs-plugin-datalabels": "^2.2.0", "exceljs": "^4.4.0", "leaflet": "^1.9.4", "moment": "^2.30.1", "ng-recaptcha": "^12.0.0", "primeflex": "^3.3.1", "primeicons": "6.0.1", "primeng": "16.0.2", "prismjs": "^1.29.0", "quill": "^1.3.7", "rxjs": "~7.8.0", "suneditor": "^2.46.1", "tslib": "^2.3.0", "xlsx": "^0.18.5", "xlsx-js-style": "^1.2.0", "zone.js": "~0.13.0"}, "devDependencies": {"@angular-devkit/build-angular": "^16.1.5", "@angular/cli": "~16.1.5", "@angular/compiler-cli": "^16.1.0", "@types/file-saver": "^2.0.7", "@types/jasmine": "~4.3.0", "file-saver": "^2.0.5", "install": "^0.13.0", "jasmine-core": "~4.6.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "typescript": "~5.1.3"}}