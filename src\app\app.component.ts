import { Component, OnInit } from '@angular/core';
import { PrimeNGConfig } from 'primeng/api';
import {TranslateService} from "./service/comon/translate.service";

@Component({
    selector: 'app-root',
    templateUrl: './app.component.html'
})
export class AppComponent implements OnInit {

    constructor(private primengConfig: PrimeNGConfig, public tranService: TranslateService) { }

    ngOnInit() {
        this.primengConfig.ripple = true;
    }
}
