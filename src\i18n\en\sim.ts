export default {
    label: {
        sothuebao: "Subscription Number",
        sosim: "SIM Number",
        imsi: "IMSI",
        maapn: "APN Code",
        goicuoc: "Rating Plan",
        mahopdong: "Contract Code",
        nguoilamhopdong: "Contractor",
        dientho<PERSON><PERSON><PERSON>: "Contact Phone",
        trangthaisim: "Status",
        nhomsim: "Group Subcriber",
        ma<PERSON><PERSON><PERSON>: "Customer Code",
        khachhang: "Customer",
        kha<PERSON><PERSON><PERSON><PERSON><PERSON>: "Customer / Tax Code",
        ngaylamhopdongtu: "Date From",
        ngaylamhopdongden: "Date To",
        simid: "SIM ID",
        dungluong: "Data Usage (MB)",
        tengoicuoc: "Plan Name",
        trangtha<PERSON>t<PERSON>i : "Connection status",
        ngaykichhoat : "Activate date",
        ngaylamhopdong: "Contract Date",
        matrungtam: "Center Code",
        diachilienhe: "Contract Address",
        paymentName: "Payment Name",
        paymentAddress: "Payment Address",
        routeCode: "Route Code",
        customerBirth: "Customer Birthday",
        customerCode: "Customer Code",
        iccid: "ICCID",
        description: "Description",
        groupName: "Group Name",
        groupKey: "Subcriber Group Code",
        pointAccess: "Point Access",
        imeiDevice: "IMEI Device",
        dataUseMax: "Limit Data Usage",
        dataUseInMonth: "Data Usage In Month",
        dataUse: "Data Usage",
        typeConnection: "Connection Type",
        staticIp: "Static IP",
        dynamicIp: "Dynamic IP",
        rangeIp: "Range IP",
        apnStatus: "APN Status",
        vpnchannelname: "3G-VPN Channel Name",
        pdpcp: "PDPCP",
        epsprofileid: "EPSProfileID",
        iptype: "IP Type",
        ip: "IP",
        note: "Note",
        overData: "Over Data",
        dataUseOnRatingPlan: "DataUsed On RatingPlan",
        dataRemainOnRatingPlan: "Data remain On RatingPlan",
        smsIntra: "Remaining intra-network messages on Rating Plan",
        smsInter: "Remaining inter-network messages on Rating Plan",
        chargesIncurred: "Charges Incurred",
        smsUnit: "SMS",
        deleteSim : "Delete Sim",
        startDate : "Start Date",
        serviceType : "Service Type",
        simType : "Sim Type",
        dataPoolSubCode: "Wallet Code",
        dataPoolEmail: "Recipient Email",
        dataPoolPhoneActive: "Recipient phone number",
        quickSearch: "Quick Search",
        statusDetach: "Detached to network",
        statusNotAttach: "Attached to network, no data session created, unavaiable paging",
        statusAttach: "Attached to network, no data session created, avaiable paging",
        statusNotConnect: "Attached to network, data session created, unavaiable paging",
        statusConnect: "Attached to network, data session created, avaiable paging",
        statusNetwork: "Network determined not reachable"
    },
    text:{
        selectCustomer: "Select Customer",
        selectGroupSim: "Select Group Subcriber",
        selectRatingPlan: "Select Plan",
        inputGroupName: "Input Group Name",
        inputGroupKey: "Input Group Key",
        inputDescription: "Input Description",
        detailSim: "Subcriber Detail",
        simInfo: "Subcriber Information",
        simStatusInfo: "Status Information of subcriber service",
        customerInfo: "Customer Information",
        ratingPlanInfo: "Rating Plan Information",
        contractInfo: "Contract Information",
        apnInfo: "APN Information",
        pushSim: "Push Group Subcriber",
        sameProvince: "Require Subcribers is same province",
        sameCustomer: "Require Subcribers is same customer",
        deleteSim : "Are you sure you want to delete the sim?"
    },
    status: {
        all: "All",
        inventory: "Inventory",
        ready: "Ready",
        activationReady: "Activation Ready",
        activated: "Activated",
        inactivated: "Inactivated",
        deactivated: "Deactivated",
        purged: "Purged",
        processing: "Procesing",
        processingChangePlan: "Procesing Change Plan",
        processingRegisterPlan: "Procesing Register Plan",
        waitingCancelPlan: "Waiting Cancel Plan",
        service:{
            data: "Data",
            callReceived: "Incoming Call",
            callSent: "Outcoming Call",
            callWorld: "International Direct Dialling",
            smsReceived: "SMS Mobile Terminated",
            smsSent: "SMS Mobile Originated"
        }
    },
    serviceType :{
        prepaid : "Prepay",
        postpaid : "Postpaid"
    },
    type: {
        unknown: "Unknown",
        esim: "eSIM",
        sim: "SIM Normal"
    }

}
