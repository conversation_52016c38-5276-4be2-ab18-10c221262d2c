import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import {AppAlertListComponent} from "./alert-setting/list/app-alert-list.component";
import {AppAlertCreateComponent} from "./alert-setting/create/app-alert-create.component";
import {AppAlertDetailComponent} from "./alert-setting/detail/app-alert-detail.component";
import {AppAlertUpdateComponent} from "./alert-setting/update/app-alert-update.component";
import {AppAlertsAlertHistoryComponent} from "./alert-history/app-alert-history.component";
import DataPage from "../../service/data.page";
import {CONSTANTS} from "../../service/comon/constants";

@NgModule({
    imports:[
        RouterModule.forChild([
            {path: "", component: AppAlertListComponent, data: new DataPage("global.menu.alertList", [CONSTANTS.PERMISSIONS.ALERT.VIEW_LIST])},
            {path: "create", component: AppAlertCreateComponent, data: new DataPage("global.titlepage.createAlarm", [CONSTANTS.PERMISSIONS.ALERT.CREATE])},
            {path: "detail/:id", component: AppAlertDetailComponent, data: new DataPage("global.titlepage.detailAlarm", [CONSTANTS.PERMISSIONS.ALERT.VIEW_DETAIL])},
            {path: "update/:id", component: AppAlertUpdateComponent, data: new DataPage("global.titlepage.editAlarm", [CONSTANTS.PERMISSIONS.ALERT.UPDATE])},
            {path: "history", component: AppAlertsAlertHistoryComponent, data: new DataPage("global.titlepage.listAlertHistory", [CONSTANTS.PERMISSIONS.ALERT_HISTORY.VIEW_LIST])}
        ])
    ],
    exports: [RouterModule]
})
export class AppAlertRoutingModule { }
