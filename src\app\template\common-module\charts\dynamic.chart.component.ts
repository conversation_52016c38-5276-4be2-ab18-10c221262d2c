import { AfterContentChecked, Component, Injector, Input, OnInit, ViewChild } from "@angular/core";
import { ComponentBase } from "src/app/component.base";
import { ParameterInfo } from "../../reporting/report-dynamic/components/tab.report.dynamic.general";
import { CONSTANTS } from "src/app/service/comon/constants";
import { ComboLazyControl } from "../combobox-lazyload/combobox.lazyload";
import { FormBuilder } from "@angular/forms";
import { Chart, ChartData } from "chart.js";
import ChartJsPluginDataLabels from 'chartjs-plugin-datalabels'
import { commonChart } from "./common-chart";
import { UIChart } from "primeng/chart";
import { ConfigChartService } from "src/app/service/charts/ConfigChartService";
import {DomSanitizer} from "@angular/platform-browser";

Chart.register(ChartJsPluginDataLabels);

export class DynamicChartController{
    reload: Function = (isPreview: boolean, isLoadData: boolean = true) => {

    };
    reloadParam: Function = () => {

    }
}

export class PositionInfo {
    top: number;
    left: number;
    width: number;
    height: number;
}

@Component({
    selector: 'dynamic-chart-vnpt',
    templateUrl: './dynamic.chart.component.html'
})
export class  DynamicChartComponent extends ComponentBase implements OnInit, AfterContentChecked {
    @Input() control: DynamicChartController;
    @Input() width!: number;
    @Input() height!: number;
    @Input() handleOpenSetting!: Function;
    @Input() handleOpenSizing!: Function;
    @Input() isShowButtonExtra!: boolean;
    @Input() mode: number = CONSTANTS.MODE_VIEW.DETAIL;
    @Input() chartConfig: {
        id: number|null,
        name: string|null,
        type: string|null,
        subType: string|null,
        query: string|null,//bigText
        typeQuery: string|null,
        datasetConfig: string|null,//bigText
        optionConfig: string|null,//bigText
        filterParams: string|null//bigText
        description: string|null,

        //calculate,
        subTypes?: Array<any>|null;
        filters?: Array<any>|null;
        datasetConfigs?: Array<any>|null;
        optionConfigEntity?: any|null;
    }
    @Input() chartPolicy?: any;

    chartConfigParser: {
        id: number|null,
        name: string|null,
        type: string|null,
        subType: string|null,
        query: string|null,//bigText
        typeQuery: string|null,
        datasetConfig: string|null,//bigText
        optionConfig: string|null,//bigText
        filterParams: string|null//bigText

        //calculate,
        subTypes?: Array<any>|null;
        filters?: Array<any>|null;
        datasetConfigs?: Array<any>|null;
        optionConfigEntity?: any|null;
    }

    isLoadingChart = true
    notChangeFilterParam: boolean = true;
    isFirstLoad: boolean = true;

    @ViewChild("chart") chart: UIChart;

    listParameters: Array<ParameterInfo> = [];

    formSearch: any;
    searchInfo: any;
    searchInfoOld: any = {};

    paramTypes = CONSTANTS.PARAMETER_TYPE;
    dateTypes = CONSTANTS.DATE_TYPE;

    datasets: any;
    options: any;

    dataTest:any = {
        labels: ['A','B','C'],
        datasets: [
            {
                data: [300, 50, 100],
                backgroundColor: [
                    "#FF6384",
                    "#36A2EB",
                    "#FFCE56"
                ],
                hoverBackgroundColor: [
                    "#FF6384",
                    "#36A2EB",
                    "#FFCE56"
                ]
            }
        ]
    };

    optionTest: any = {
        plugins: {
            legend: {
                labels: {
                    color: '#495057'
                }
            }
        }
    }

    typeTest:string = "pie"

    objectMode = CONSTANTS.MODE_VIEW;

    isValidChart: boolean = true;
    isErrorQuery: boolean = false;

    keyCycle = "cyclee";
    keyYear = "yea";
    valueSelectYear = 3;
    valueSelectMonth = 1;
    configSearchYear: any;
    indexOfConfigSearchYear: number = -1;
    numberElementPerRow: number = 1;
    inputRecentlyDate: {
        inputDateFrom: Date| null,
        inputDateTo: Date| null,
        minDateFrom: Date|null,
        maxDateFrom: Date| null,
        minDateTo: Date| null,
        maxDateTo: Date| null,
    }
    formInputRecentlyDate: any;
    constructor(injector: Injector, private formBuilder: FormBuilder, private configChartService: ConfigChartService, public sanitizer: DomSanitizer) {
        super(injector);
    }
    htmlContentDescription : any
    ngOnInit(): void {
        let me = this;
        if(this.control){
            this.control.reload = this.onload.bind(this);
            this.control.reloadParam = this.reonload.bind(this);
        }
        this.htmlContentDescription = this.sanitizer.bypassSecurityTrustHtml(this.chartConfig.description)
    }

    ngAfterContentChecked(): void {
        let me = this;
        if(!this.searchInfo) return;
        if(JSON.stringify(this.searchInfo) != JSON.stringify(this.searchInfoOld)){
            me.notChangeFilterParam = false;
            this.searchInfoOld = {...this.searchInfo};
            if(!this.listParameters) return;
            let provinceName = this.listParameters.find(e => e.prDisplayName.toUpperCase() === "THÀNH PHỐ");
            if (provinceName) {
                this.listParameters = this.listParameters.filter(item => item.prDisplayName.toUpperCase() !== provinceName.prDisplayName.toUpperCase());
                this.listParameters.unshift(provinceName);
            }
            Object.keys(this.searchInfo).forEach(key => {
                if(key == me.keyCycle){
                    if(me.searchInfo[key] == me.valueSelectYear || me.searchInfo[key] == null){
                        me.listParameters.splice(me.indexOfConfigSearchYear, 1);
                    }else{
                        if(me.listParameters.length == me.indexOfConfigSearchYear || me.listParameters[me.indexOfConfigSearchYear].prKey != this.keyYear){
                            me.listParameters.splice(me.indexOfConfigSearchYear, 0, me.configSearchYear);
                        }
                    }
                }
            })
        }
        if(this.width >= 1200){
            this.numberElementPerRow = 5;
        }else if(this.width >= 900){
            this.numberElementPerRow = 4;
        }else if(this.width >= 600){
            this.numberElementPerRow = 3;
        }else if(this.width >= 300){
            this.numberElementPerRow = 2;
        }else{
            this.numberElementPerRow = 1;
        }
    }

    onload(isPreview: boolean, isLoadData: boolean = true){
        if(this.chartConfig){
            this.convertChartConfig();
            this.loadListParams();
            this.options = this.getOptionChart();
            this.loadDataSet(isPreview, isLoadData);
        }
    }

    reonload(){
        this.convertChartConfig();
        this.loadListParams();
    }

    loadListParams(){
        let me = this;
        if(this.chartConfig.filterParams){
            this.listParameters = JSON.parse(this.chartConfig.filterParams);
            this.listParameters.forEach((el, index) => {
                if(el.prType == this.paramTypes.LIST_NUMBER || el.prType == this.paramTypes.LIST_STRING || (el.prType == this.paramTypes.STRING && el.isAutoComplete == true)){
                    el["control"] = new ComboLazyControl();
                }

                if(el.prKey == me.keyYear){
                    me.configSearchYear = el;
                    me.indexOfConfigSearchYear = index;
                }
            });
            let provinceName = this.listParameters.find(e => e.prDisplayName.toUpperCase() === "THÀNH PHỐ");
            if (provinceName) {
                this.listParameters = this.listParameters.filter(item => item.prDisplayName.toUpperCase() !== provinceName.prDisplayName.toUpperCase());
                this.listParameters.unshift(provinceName);
            }
            this.searchInfo = {};
            const lastFilter = typeof me.chartPolicy.lastFilter === 'string' ? JSON.parse(me.chartPolicy.lastFilter) : me.chartPolicy.lastFilter;
            this.listParameters.forEach(el => {
                if(el.prKey == me.keyCycle){
                    me.searchInfo[el.prKey] = this.valueSelectMonth;
                } else {
                    me.searchInfo[el.prKey] = null;
                }
            })
            if(this.keyCycle in this.searchInfo && this.keyYear in this.searchInfo){
                this.searchInfo[this.keyYear] = new Date().getFullYear();
            }
            this.formSearch = this.formBuilder.group(this.searchInfo);
            this.inputRecentlyDate = {
                inputDateFrom: null,
                inputDateTo: null,
                minDateFrom: null,
                maxDateFrom: new Date(),
                minDateTo: null,
                maxDateTo: new Date(),
            }
            this.formInputRecentlyDate = this.formBuilder.group(this.inputRecentlyDate)
            this.listParameters.forEach(el => {
                if (lastFilter) {
                    if (el.prType == 2 && lastFilter[el.prKey]) {
                        if (el.isAutoComplete == true) { // mặc định ngày tháng hiện tại, không cho sửa
                            me.searchInfo[el.prKey] = new Date();
                        } else {
                            me.searchInfo[el.prKey] = new Date(lastFilter[el.prKey]);
                        }
                    }
                    else if ((el.prType == CONSTANTS.PARAMETER_TYPE.RECENTLY_DATE_FROM || el.prType == CONSTANTS.PARAMETER_TYPE.RECENTLY_DATE_TO) && lastFilter[el.prKey]) {
                        if (el.prType == CONSTANTS.PARAMETER_TYPE.RECENTLY_DATE_FROM) {
                            // console.log(lastFilter[el.prKey])
                            me.searchInfo[el.prKey] = lastFilter[el.prKey];
                            me.inputRecentlyDate.inputDateFrom = this.getDateFromRecentlyDateString(lastFilter[el.prKey])
                            me.onChangeRecentlyDateFrom(me.inputRecentlyDate.inputDateFrom, el.prKey, false)
                        } else if (el.prType == CONSTANTS.PARAMETER_TYPE.RECENTLY_DATE_TO) {
                            // console.log(lastFilter[el.prKey])
                            me.searchInfo[el.prKey] = lastFilter[el.prKey];
                            me.inputRecentlyDate.inputDateTo = this.getDateFromRecentlyDateString(lastFilter[el.prKey])
                            me.onChangeRecentlyDateTo(me.inputRecentlyDate.inputDateTo, el.prKey, false)
                        }
                    }
                    else if (Array.isArray(lastFilter[el.prKey])) {
                        me.searchInfo[el.prKey] = lastFilter[el.prKey];
                    } else {
                        me.searchInfo[el.prKey] = lastFilter[el.prKey];
                    }
                } else {
                    if (el.prType == CONSTANTS.PARAMETER_TYPE.RECENTLY_DATE_FROM) {
                        me.inputRecentlyDate.inputDateFrom = this.getDateFromRecentlyDateString(el.prValue)
                        me.onChangeRecentlyDateFrom(me.inputRecentlyDate.inputDateFrom, el.prKey, false)
                    } else if (el.prType == CONSTANTS.PARAMETER_TYPE.RECENTLY_DATE_TO) {
                        me.inputRecentlyDate.inputDateTo = this.getDateFromRecentlyDateString(el.prValue)
                        me.onChangeRecentlyDateTo(me.inputRecentlyDate.inputDateTo, el.prKey, false)
                    } else if (el.prType == CONSTANTS.PARAMETER_TYPE.DATE && el.isAutoComplete == true) {
                        me.searchInfo[el.prKey] = new Date();
                    }
                }
            })
            // if(this.indexOfConfigSearchYear >= 0){
            //     this.listParameters.splice(me.indexOfConfigSearchYear, 1);
            // }
        }
    }

    checkValidForm(){
        // if(!this.formSearch) return false;
        // if(this.formSearch.invalid) return false;
        // if((this.listParameters || []).length > 0){
        //     for(let i = 0; i < this.listParameters.length;i++){
        //         let el = this.listParameters[i];
        //         if(el["control"]){
        //             if(el["control"].invalid){
        //                 return false;
        //             }
        //         }
        //     }
        // }
        return true;
    }

    prepareData(){
        let me = this;
        let data = {
            id: this.chartConfig.id,
            customerCodes: null,
            paramsValue: []
        };
        let listParameters = [...this.listParameters];
        if(this.indexOfConfigSearchYear >= 0){
            if(me.listParameters.length == me.indexOfConfigSearchYear || me.listParameters[me.indexOfConfigSearchYear].prKey != this.keyYear){
                listParameters.splice(me.indexOfConfigSearchYear, 0, me.configSearchYear);
            }
        }
        listParameters.forEach(el => {
            if(el.prType == CONSTANTS.PARAMETER_TYPE.DATE){
                data.paramsValue.push({
                    prType: el.prType,
                    prKey: el.prKey,
                    value: this.searchInfo[el.prKey] ? this.utilService.convertDateTimeToString(this.searchInfo[el.prKey]) : null
                })
            }else if(el.prType == CONSTANTS.PARAMETER_TYPE.TIMESTAMP){
                data.paramsValue.push({
                    prType: el.prType,
                    prKey: el.prKey,
                    value: this.searchInfo[el.prKey] ? this.searchInfo[el.prKey].getTime() : null
                })
            }else if(el.prType == CONSTANTS.PARAMETER_TYPE.NUMBER){
                data.paramsValue.push({
                    prType: el.prType,
                    prKey: el.prKey,
                    value: this.searchInfo[el.prKey] ? this.searchInfo[el.prKey] * 1 : null
                })
            }else if(el.prType == CONSTANTS.PARAMETER_TYPE.LIST_NUMBER || el.prType == CONSTANTS.PARAMETER_TYPE.LIST_STRING){
                if(el.isMultiChoice){
                    data.paramsValue.push({
                        prType: el.prType,
                        prKey: el.prKey,
                        value: this.searchInfo[el.prKey] ? this.searchInfo[el.prKey].flat() : null
                    })
                } else {
                    data.paramsValue.push({
                        prType: el.prType,
                        prKey: el.prKey,
                        value: this.searchInfo[el.prKey] ? [this.searchInfo[el.prKey]] : null
                    })
                }
            } else if (el.prType == CONSTANTS.PARAMETER_TYPE.RECENTLY_DATE_FROM) {
                data.paramsValue.push({
                    prType: el.prType,
                    prKey: el.prKey,
                    value: this.searchInfo[el.prKey]
                })
            } else if (el.prType == CONSTANTS.PARAMETER_TYPE.RECENTLY_DATE_TO) {
                data.paramsValue.push({
                    prType: el.prType,
                    prKey: el.prKey,
                    value: this.searchInfo[el.prKey]
                })
            }else{
                data.paramsValue.push({
                    prType: el.prType,
                    prKey: el.prKey,
                    value: this.searchInfo[el.prKey] ? this.searchInfo[el.prKey] : null
                })
            }
        })
        // if(me.sessionService.userInfo.id != 1){
        //     data.paramsValue.forEach(param => {
        //         if(param.prKey == 'userId'){
        //             if((param.value || '') == ''){
        //                 param.value = [me.sessionService.userInfo.id];
        //             }
        //         }
        //     })
        // }
        return data;
    }

    preventCharacter(event){
        if(event.ctrlKey || event.altKey || event.shiftKey){
            return;
        }
        if(event.keyCode == 8 || event.keyCode == 13 || event.keyCode == 46 || event.keyCode == 37 || event.keyCode == 39){
            return;
        }
        if(event.keyCode < 48 || event.keyCode > 57){
            event.preventDefault();
        }
    }

    onSubmitSearch(){
        let me = this;
        let dataSend = [];
        me.chartPolicy.lastFilter = JSON.stringify(this.searchInfo);
        dataSend.push({
            id: me.chartPolicy.id,
            chartId: me.chartPolicy.idChart,
            userId: me.sessionService.userInfo.id,
            status: me.chartPolicy.status,
            threshold: me.chartPolicy.configLevel,
            positionConfig: me.chartPolicy.configPosition,
            lastFilter: me.chartPolicy.lastFilter
        });
        me.configChartService.saveConfigDashboard(dataSend, (response)=> {
            me.messageCommonService.success(me.tranService.translate('global.message.success'))
            me.mode = CONSTANTS.MODE_VIEW.DETAIL;
            me.messageCommonService.offload();
        }, null, () => me.messageCommonService.offload());
        me.searchDataDashboard();
        me.notChangeFilterParam = true;
    }

    searchDataDashboard() {
        if(!this.checkValidForm()) return;
        let me = this;
        let data = this.prepareData();
        if(this.chartConfig.id == 0){
            console.log(data);
            return;
        };
        //get data va tranfer data
        this.isLoadingChart = true
        this.configChartService.getContent(data, (response) => {
            me.transDataset(response);
            me.isErrorQuery = false;
        }, (e) => {
            if (e.error.error.errorCode === 'error.report.query') {
                me.isErrorQuery = true;
            }
        })
    }

    refresh(){
        this.loadListParams();
        this.searchDataDashboard();
    }

    checkIsNumberOrNull(key){
        let me = this;
        if(this.searchInfo[key] == null) return;
        if(isNaN(this.searchInfo[key])){
            setTimeout(function(){
                me.searchInfo[key] = null;
            })
        }
    }

    getStyleChart(){
        return {
            backgroundColor: "white",
            padding: "20px",
            borderRadius: "4px",
            width: this.width+"px",
            boxSizing: "content-box"
        };
    }

    convertChartConfig(){
        if(this.chartConfig.subType){
            this.chartConfig.subTypes = JSON.parse(this.chartConfig.subType);
        }else{
            this.chartConfig.subTypes = [];
        }
        if(this.chartConfig.filterParams){
            this.chartConfig.filters = JSON.parse(this.chartConfig.filterParams);
        }else{
            this.chartConfig.filters = [];
        }
        if(this.chartConfig.datasetConfig){
            this.chartConfig.datasetConfigs = JSON.parse(this.chartConfig.datasetConfig);
        }else{
            this.chartConfig.datasetConfigs = [];
        }
        if(this.chartConfig.optionConfig){
            this.chartConfig.optionConfigEntity = JSON.parse(this.chartConfig.optionConfig);
        }else{
            this.chartConfig.optionConfigEntity = {
                tooltip: {},
                legend: {},
                title: {},
                subTitle: {},
                scales: {
                    x: {},
                    y: {}
                }
            }
        }
        if(!this.chartConfig.optionConfigEntity['boxvalue']){
            this.chartConfig.optionConfigEntity['boxvalue'] = {
                isShowBoxValue: false
            }
        }
        this.chartConfigParser = {...this.chartConfig};
    }


    createDataFake(){
        let data =  {};
        let labels = ['label1', 'label2', 'label3', 'label4', 'label5'];
        this.chartConfig.datasetConfigs.forEach(config => {
            let dataOfDataset = [];
            labels.forEach(label => {
                dataOfDataset.push({
                    [config.keyDataset]: config.datasetName,
                    [config.keyLabel]: label,
                    [config.keyValue]: Math.round(Math.random() * 10000)
                })
            });
            if(config.query){
                data[config.datasetName] = dataOfDataset;
            }else{
                if(data["default"]){
                    data["default"].push(...dataOfDataset);
                }else{
                    data["default"] = dataOfDataset;
                }
            }
        })
        return data;
    }

    loadDataSet(isPreview, isLoadData){
        if(isPreview){
            let data = this.createDataFake();
            this.transDataset(data);
        }else{
            if(isLoadData){
                this.searchDataDashboard();
            }else{
                this.chart.reinit();
                this.isLoadingChart = false
            }
        }
    }

    transDataset(data){
        let me = this;
        let isStacked = this.chartConfigParser.subTypes.includes(CONSTANTS.CHART_SUB_TYPE.GROUP_BAR) || this.chartConfigParser.subTypes.includes(CONSTANTS.CHART_SUB_TYPE.STACKED_BAR)
        let isSliderThreshold = this.chartConfigParser.subTypes.includes(CONSTANTS.CHART_SUB_TYPE.SLIDER_THRESHOLD)
        // let threshold = [80, 90]
        // if (isSliderThreshold) {
        //     let configLevel = typeof me.chartPolicy.configLevel === 'string' ? JSON.parse(me.chartPolicy.configLevel) : me.chartPolicy.configLevel;
        //     if (configLevel && configLevel.length > 0) {
        //         threshold[0] = Number(configLevel[0].below)
        //         threshold[1] = Number(configLevel[0].above)
        //     }
        // }
        this.datasets = {
            labels: [],
            datasets: [],
            maxValues: [],
        }
        let listNameConfig = this.chartConfigParser.datasetConfigs.map(el => el.datasetName);
        let configDatasets = this.chartConfigParser.datasetConfigs;
        let listDataset = [];
        let datasetObjects = [];
        // let labelValuePairs = [];
        if(!data){
            this.isValidChart = false;
            return;
        }
        //get list labels
        Object.keys(data).forEach(key => {
            if(listNameConfig.includes(key)){
                let config = configDatasets.filter(el => el.datasetName === key)[0];
                data[key].forEach(item => {
                    if(me.chartConfig.type == CONSTANTS.CHART_TYPE.PIE || me.chartConfig.type == CONSTANTS.CHART_TYPE.DOUGHNUT){
                        if(key == "default"){
                            if(!listDataset.includes(item[config.keyDataset])){
                                listDataset.push(item[config.keyDataset]);
                            }
                            if(!me.datasets.labels.includes(item[config.keyLabel])){
                                me.datasets.labels.push(item[config.keyLabel])
                                // labelValuePairs.push({ label: item[config.keyLabel], value: item[config.keyValue] });
                            }
                        }
                    }else{
                        if(!listDataset.includes(item[config.keyDataset])){
                            listDataset.push(item[config.keyDataset]);
                        }
                        if(!me.datasets.labels.includes(item[config.keyLabel])){
                            me.datasets.labels.push(item[config.keyLabel])
                            // labelValuePairs.push({ label: item[config.keyLabel], value: item[config.keyValue] });
                        }
                    }
                })
            }
        })
        // const isDate = (str: string) => {
        //     const datePattern = /^\d{2}\/\d{2}\/\d{4}$/;
        //     return datePattern.test(str);
        // };
        //Tự sắp xếp ở truy vấn
        // this.datasets.labels.sort((a,b) => a > b ? 1 : -1);

        // Sắp xếp dựa trên:
        // 1. Nếu LABEL là ngày, sắp xếp theo ngày.
        // 2. Nếu không phải ngày, sắp xếp theo VALUE giảm dần.
        // labelValuePairs.sort((a, b) => {
        //     if (isDate(a.label) && isDate(b.label)) {
        //         // Sắp xếp theo ngày nếu cả hai LABEL đều là ngày
        //         const dateA = new Date(a.label.split("/").reverse().join("/"));
        //         const dateB = new Date(b.label.split("/").reverse().join("/"));
        //         return dateA.getTime() - dateB.getTime();
        //     } else if (!isDate(a.label) && !isDate(b.label)) {
        //         // Sắp xếp theo VALUE nếu không phải là ngày
        //         const valueA = Number(a.value);
        //         const valueB = Number(b.value);
        //         return valueB - valueA; // Sắp xếp giảm dần theo VALUE
        //     } else {
        //         // Nếu một bên là ngày và một bên không phải là ngày, ưu tiên ngày lên trước
        //         return isDate(a.label) ? -1 : 1;
        //     }
        // });
        // // Lấy lại danh sách các LABEL sau khi đã sắp xếp
        // this.datasets.labels = labelValuePairs.map(pair => pair.label);
        if(this.datasets.labels.length == 0){
            this.isValidChart = false;
            return;
        }else {
            this.isValidChart = true;
            this.datasets.labels.forEach(label => {
                if(label == null || label == undefined || label == ""){
                    me.isValidChart = false;
                }
            })
        };
        //uu tien config ko phai default
        if(this.chartConfigParser.type != CONSTANTS.CHART_TYPE.PIE && this.chartConfigParser.type != CONSTANTS.CHART_TYPE.DOUGHNUT){
            configDatasets.forEach(config  => {
                if(config.datasetName != "default"){
                    let listDatasetOfConfig = [];
                    if((config.query || "").length > 0){
                        data[config.datasetName].forEach(el => {
                            if(!listDatasetOfConfig.includes(el[config.keyDataset])){
                                listDatasetOfConfig.push(el[config.keyDataset]);
                            }
                        })
                    }else{
                        if(!listDatasetOfConfig.includes(config.datasetName)){
                            listDatasetOfConfig.push(config.datasetName);
                        }
                    }
                    listDatasetOfConfig.forEach((datasetOfConfig, index) => {
                        if(listDataset.includes(datasetOfConfig)){
                            listDataset.splice(listDataset.findIndex(dsn => datasetOfConfig == dsn), 1);
                            let arrObjectValue = {};
                            // let arrObjectMaxValue = {};
                            let arrObjectRValue = {};
                            me.datasets.labels.forEach(label => {
                                arrObjectValue[label] = 0;
                                // arrObjectMaxValue[label] = 0;
                                arrObjectRValue[label] = {x: 0, y: 0, r: 0};
                            })
                            if((config.query || "").length > 0){
                                data[config.datasetName].forEach(item => {
                                    if(item[config.keyDataset] == datasetOfConfig){
                                        arrObjectValue[item[config.keyLabel]] = item[config.keyValue];
                                        // arrObjectMaxValue[item[config.keyLabel]] = item[config.keyMaxValue];
                                        arrObjectRValue[item[config.keyLabel]] = {
                                            x: item[config.keyValueX],
                                            y: item[config.keyValueY],
                                            r: item[config.keyValueR],
                                        }
                                    }
                                });
                            }else{
                                data["default"].forEach(item => {
                                    if(item[config.keyDataset] == datasetOfConfig){
                                        arrObjectValue[item[config.keyLabel]] = item[config.keyValue];
                                        // arrObjectMaxValue[item[config.keyLabel]] = item[config.keyMaxValue];
                                        arrObjectRValue[item[config.keyLabel]] = {
                                            x: item[config.keyValueX],
                                            y: item[config.keyValueY],
                                            r: item[config.keyValueR],
                                        }
                                    }
                                });
                            }
                            let arrValue = [];
                            let arrMaxValue = [];
                            let arrRValue = [];
                            // let arrColor = [];
                            me.datasets.labels.forEach(label => {
                                arrValue.push(arrObjectValue[label]);
                                arrRValue.push(arrObjectRValue[label]);
                                // arrMaxValue.push(arrObjectMaxValue[label]);
                            })
                            if (isSliderThreshold) {
                                index = this.getIndexColorForThreshold(datasetOfConfig)
                            }
                            this.pushDataset(config, datasetOfConfig, arrValue, arrRValue, index, isStacked, datasetObjects)
                        }
                    })
                }
            })
        }

        //quet config default
        configDatasets.forEach(config  => {
            if(config.datasetName == "default"){
                // console.log("config.keyMaxValue default")
                // console.log(config.keyMaxValue)
                let listDatasetOfConfig = [];
                data[config.datasetName].forEach(el => {
                    if(!listDatasetOfConfig.includes(el[config.keyDataset])){
                        listDatasetOfConfig.push(el[config.keyDataset]);
                    }
                })
                listDatasetOfConfig.forEach((datasetOfConfig, index) => {
                    if(listDataset.includes(datasetOfConfig)){
                        listDataset.splice(listDataset.findIndex(dsn => datasetOfConfig == dsn), 1);
                        let arrObjectValue = {};
                        let arrObjectRValue = {};
                        // let arrObjectMaxValue = {};
                        me.datasets.labels.forEach(label => {
                            arrObjectValue[label] = 0;
                            // arrObjectMaxValue[label] = 0;
                            arrObjectRValue[label] = {x: 0, y: 0, r: 0};
                        })
                        if((config.query || "").length > 0){
                            data[config.datasetName].forEach(item => {
                                if(item[config.keyDataset] == datasetOfConfig){
                                    arrObjectValue[item[config.keyLabel]] = item[config.keyValue];
                                    arrObjectRValue[item[config.keyLabel]] = {
                                        x: item[config.keyValueX],
                                        y: item[config.keyValueY],
                                        r: item[config.keyValueR],
                                    }
                                }
                            });
                        }else{
                            data["default"].forEach(item => {
                                if(item[config.keyDataset] == datasetOfConfig){
                                    arrObjectValue[item[config.keyLabel]] = item[config.keyValue];
                                    // arrObjectMaxValue[item[config.keyLabel]] = item[config.keyMaxValue];
                                    arrObjectRValue[item[config.keyLabel]] = {
                                        x: item[config.keyValueX],
                                        y: item[config.keyValueY],
                                        r: item[config.keyValueR],
                                    }
                                }
                            });
                        }
                        let arrValue = [];
                        let arrRValue = [];
                        let arrMaxValue = [];
                        // let arrColor =[]
                        me.datasets.labels.forEach(label => {
                            arrValue.push(arrObjectValue[label]);
                            arrRValue.push(arrObjectRValue[label]);
                            // arrMaxValue.push(arrObjectMaxValue[label]);
                        })
                        if (isSliderThreshold) {
                            index = this.getIndexColorForThreshold(datasetOfConfig)
                        }
                        this.pushDataset(config, datasetOfConfig, arrValue, arrRValue, index, isStacked, datasetObjects)
                    }
                })
            }
        })

        //sap xep dataset theo type
        let chartTypeOrder = [CONSTANTS.CHART_TYPE.LINE, CONSTANTS.CHART_TYPE.SCATTER,
                                CONSTANTS.CHART_TYPE.BUBBLE,CONSTANTS.CHART_TYPE.BAR,
                                CONSTANTS.CHART_TYPE.PIE, CONSTANTS.CHART_TYPE.DOUGHNUT,
                                CONSTANTS.CHART_TYPE.POLAR, CONSTANTS.CHART_TYPE.RADAR];
        let arrSort = [];
        chartTypeOrder.forEach(chartType => {
            arrSort = [...arrSort,...datasetObjects.filter(el => el.type == chartType)];
        })
        this.datasets.datasets = [...arrSort];
        if (this.chart) {
            this.chart.reinit();
        }
        this.isLoadingChart = false
    }

    pushDataset(config, datasetOfConfig, arrValue, arrRValue, index, isStacked, datasetObjects){
        if (config.type == CONSTANTS.CHART_TYPE.BAR) {
            datasetObjects.push(commonChart.genDatasetBarChart(datasetOfConfig, arrValue, config.typeAnimation, config.xAxisID,
                config.yAxisID, isStacked ? config.stack : null, config.backgroundColors.length > 0 ? config.backgroundColors[index % config.backgroundColors.length] : null,
                config.barPercentage, config.barThickness, 50, config.base, config.borderColors.length > 0 ? config.borderColors[index % config.borderColors.length] : null,
                config.borderRadius, config.borderWidth, config.categoryPercentage, config.hoverBackgroundColors.length > 0 ? config.hoverBackgroundColors[index % config.hoverBackgroundColors.length] : null,
                config.hoverBorderColors.length > 0 ? config.hoverBorderColors[index % config.hoverBorderColors.length] : null, config.hoverBorderWidth));

        }else if(config.type == CONSTANTS.CHART_TYPE.LINE){
            datasetObjects.push(commonChart.genDatasetLineChart(datasetOfConfig, arrValue, config.typeAnimation, config.backgroundColors.length > 0 ? config.backgroundColors[index % config.backgroundColors.length] : null,
                config.borderColors.length > 0 ? config.borderColors[index % config.borderColors.length]: null, config.borderCapStyle, config.borderJoinStyle, config.fill,
                config.tension, config.isLineDash, config.hoverBackgroundColors.length > 0 ? config.hoverBackgroundColors[index % config.hoverBackgroundColors.length] : null,
                config.hoverBorderColors.length > 0 ? config.hoverBorderColors[index % config.hoverBorderColors.length]: null, config.hoverBorderWidth,
                (config.pointColors || []).length > 0 ? (config.pointColors || [])[index % (config.pointColors || []).length]: null,
                config.pointBorderColors.length > 0 ? config.pointBorderColors[index % config.pointBorderColors.length]: null, config.pointBorderWidth, config.xAxisID, config.yAxisID));
        }else if(config.type == CONSTANTS.CHART_TYPE.PIE){
            datasetObjects.push(commonChart.genDatasetPieChart(arrValue, config.typeAnimation, config.backgroundColors,
                config.borderColors, config.borderWidth,
                config.hoverBackgroundColors,
                config.hoverBorderColors, config.hoverBorderWidth, config.weight));
        }else if(config.type == CONSTANTS.CHART_TYPE.DOUGHNUT){
            datasetObjects.push(commonChart.genDatasetDoughnutChart(arrValue, config.typeAnimation, config.backgroundColors,
                config.borderColors, config.borderWidth,
                config.hoverBackgroundColors,
                config.hoverBorderColors, config.hoverBorderWidth, config.weight));
        }else if(config.type == CONSTANTS.CHART_TYPE.POLAR){
            datasetObjects.push(commonChart.genDatasetPolarAreaChart(arrValue, config.typeAnimation, config.backgroundColors.length > 0 ? config.backgroundColors[index % config.backgroundColors.length]: null,
                config.borderColors.length > 0 ? config.borderColors[index % config.borderColors.length]: null, config.borderWidth,
                config.hoverBackgroundColors.length > 0 ? config.hoverBackgroundColors[index % config.hoverBackgroundColors.length]: null,
                config.hoverBorderColors.length > 0 ? config.hoverBorderColors[index % config.hoverBorderColors.length] : null, config.hoverBorderWidth, config.weight));
        }else if(config.type == CONSTANTS.CHART_TYPE.RADAR){
            datasetObjects.push(commonChart.genDatasetRadarChart(datasetOfConfig, arrValue, config.typeAnimation, config.backgroundColors.length > 0 ? config.backgroundColors[index % config.backgroundColors.length] : null,
                config.borderColors.length > 0 ? config.borderColors[index % config.borderColors.length]: null, config.borderCapStyle, config.borderJoinStyle, config.fill,
                config.tension, config.isLineDash, config.hoverBackgroundColors.length > 0 ? config.hoverBackgroundColors[index % config.hoverBackgroundColors.length] : null,
                config.hoverBorderColors.length > 0 ? config.hoverBorderColors[index % config.hoverBorderColors.length]: null, config.hoverBorderWidth,
                (config.pointColors || []).length > 0 ? (config.pointColors || [])[index % (config.pointColors || []).length]: null,
                config.pointBorderColors.length > 0 ? config.pointBorderColors[index % config.pointBorderColors.length]: null, config.pointBorderWidth, config.borderWidth));
        }else if(config.type == CONSTANTS.CHART_TYPE.SCATTER){
            datasetObjects.push(commonChart.genDatasetScatterChart(datasetOfConfig, arrRValue, config.typeAnimation, config.backgroundColors.length > 0 ? config.backgroundColors[index % config.backgroundColors.length]: null,
                config.borderColors.length > 0 ? config.borderColors[index % config.borderColors.length] : null, config.borderWidth,
                config.hoverBackgroundColors.length > 0 ? config.hoverBackgroundColors[index % config.hoverBackgroundColors.length]: null,
                config.hoverBorderColors.length > 0 ? config.hoverBorderColors[index % config.hoverBorderColors.length]: null, config.hoverBorderWidth, config.isLineDash, config.fill, config.tension,
                config.borderCapStyle, config.borderJoinStyle, (config.pointColors || []).length > 0 ? (config.pointColors || [])[index % (config.pointColors || []).lenth]: null,
                config.pointBorderColors.length > 0 ? config.pointBorderColors[index % config.pointBorderColors.length]: null, config.pointBorderWidth));
        }else if(config.type == CONSTANTS.CHART_TYPE.BUBBLE){
            datasetObjects.push(commonChart.genDatasetBubbleChart(datasetOfConfig, arrRValue, config.backgroundColors.length > 0 ? config.backgroundColors[index % config.backgroundColors.length]: null,
                config.borderColors.length > 0 ? config.borderColors[index % config.borderColors.length] : null, config.borderWidth,
                config.hoverBackgroundColors.length > 0 ? config.hoverBackgroundColors[index % config.hoverBackgroundColors.length]: null,
                config.hoverBorderColors.length > 0 ? config.hoverBorderColors[index % config.hoverBorderColors.length]: null, config.hoverBorderWidth, config.pointStyle))
        }
    }

    getOptionChart(){
        let isStacked = this.chartConfig.subTypes.includes(CONSTANTS.CHART_SUB_TYPE.GROUP_BAR) || this.chartConfig.subTypes.includes(CONSTANTS.CHART_SUB_TYPE.STACKED_BAR);
        let chartNotScale = [CONSTANTS.CHART_TYPE.DOUGHNUT, CONSTANTS.CHART_TYPE.PIE, CONSTANTS.CHART_TYPE.POLAR, CONSTANTS.CHART_TYPE.RADAR];
        let scales = null;
        if(!chartNotScale.includes(this.chartConfig.type)){
            let configX = this.chartConfig.optionConfigEntity.scales.x;
            scales = {
                x: commonChart.genOptionScaleX(isStacked, configX.beginAtZero, configX.position, configX.tickColor, configX.rotation, configX.drawOnChartArea,
                    configX.colorGrid, configX.borderGridColor, configX.isDash, configX.titleText, configX.titleAlign, configX.titleDisplay, configX.titleColor,configX.titleFont)
            }
            Object.keys(this.chartConfig.optionConfigEntity.scales).forEach(key => {
                if(key != 'x'){
                    let configY = this.chartConfig.optionConfigEntity.scales[key];
                    scales[key] = commonChart.genOptionScaleY(isStacked, configY.beginAtZero, configY.position, configY.tickColor, configY.rotation, configY.drawOnChartArea,
                        configY.colorGrid, configY.borderGridColor, configY.isDash, configY.titleText, configY.titleAlign, configY.titleDisplay, configY.titleColor,configY.titleFont)
                }
            })
        }
        let configTooltip = this.chartConfig.optionConfigEntity.tooltip;
        let configLegend = this.chartConfig.optionConfigEntity.legend;
        let configTitle = this.chartConfig.optionConfigEntity.title;
        let configSubTitle = this.chartConfig.optionConfigEntity.subTitle;
        let configBoxValue = this.chartConfig.optionConfigEntity.boxvalue;
        return commonChart.genOptions(
            this.chartConfig.subTypes.includes(CONSTANTS.CHART_SUB_TYPE.HORIZONTAL_BAR),
            commonChart.genOptionTooltip(configTooltip.mode, configTooltip.intersect, configTooltip.backgroundColor, configTooltip.bodyColor, configTooltip.bodyAlign,
                configTooltip.bodyFont, configTooltip.borderColor, configTooltip.borderWidth, configTooltip.bodySpacing, configTooltip.boxHeight, configTooltip.boxWidth,
                configTooltip.radius, configTooltip.caretSize, configTooltip.titleAlign, configTooltip.titleColor, configTooltip.titleFont, configTooltip.footerColor,
                configTooltip.patternTitle, configTooltip.patternLabel, configTooltip.patternFooter),
            commonChart.genOptionLegend(configLegend.titleColor, configLegend.titleDisplay, configLegend.titleFont, configLegend.titleText, configLegend.align,
                configLegend.display, configLegend.labelColor, configLegend.labelFont, configLegend.boxWidth, configLegend.boxHeight, configLegend.position, configLegend.patternBody),
            commonChart.genOptionsTitleAndSubTitle(configTitle.align, configTitle.color, configTitle.display, configTitle.font, configTitle.position, configTitle.text),
            commonChart.genOptionsTitleAndSubTitle(configSubTitle.align, configSubTitle.color, configSubTitle.display, configSubTitle.font, configSubTitle.position, configSubTitle.text),
            scales ? scales : null,
            commonChart.getOptionBoxValue(configBoxValue.isShowBoxValue)
        )
    }

    openSetting(){
        if(this.handleOpenSetting){
            this.handleOpenSetting(this.chartConfig.id);
        }
    }

    openSizing(){
        if(this.handleOpenSizing){
            this.handleOpenSizing(this.chartConfig.id);
        }
    }

    checkDisabledBtn() {
        let me = this;
        let disable = true;
        let notDisable = false;
        if(JSON.stringify(this.searchInfo) != JSON.stringify(this.searchInfoOld)){
            this.notChangeFilterParam = false;
        }

        let isValidRequired = true
        me.listParameters.forEach( parameter => {
            if (parameter.required) {
                if ((parameter.prType == CONSTANTS.PARAMETER_TYPE.LIST_NUMBER || parameter.prType == CONSTANTS.PARAMETER_TYPE.LIST_STRING || parameter.prType == CONSTANTS.PARAMETER_TYPE.STRING)
                    && (me.searchInfo[parameter.prKey] == null || me.searchInfo[parameter.prKey].length == 0)
                ) {
                    isValidRequired = false;
                } else if (me.searchInfo[parameter.prKey] == null) {
                    isValidRequired = false;
                }
            }
        })

        if (isValidRequired && !this.notChangeFilterParam) {
            return notDisable
        } else {
            return disable
        }
    }

    // Kiểm tra xem có thay đổi filter search để lưu hay không
    changeSelect(value, param?) {
        if (value) {
            this.notChangeFilterParam = false;
            this.checkDisabledBtn();
        }
        // console.log(param, this.listParameters.some(item => item.prKey === "userId"))
        this.listParameters.forEach(item=> {
                if (this.listParameters.some(item => item.prKey === "userId") && this.listParameters.some(item => item.prKey === "province_code") && param.prKey ==="province_code" && item.prKey === "userId") {
                    item.paramDefault = {provinceCode: value}
                }
            }
        )
        // console.log(this.listParameters)
    }

    onDateChange(event: any) {
        if (!event && !this.isFirstLoad) {
            this.notChangeFilterParam = false;
        }
        this.checkDisabledBtn();
    }

    ngOnDestroy(): void {
        this.notChangeFilterParam = true;
    }
    countDiffDays (inputString): number   {
        const match = inputString.match(/-?\d+/);
        if (match) {
            const number = parseInt(match[0], 10);
            return number;
        } else {
            console.log("No number found in the string.");
            return 0;
        }
    }

    getDateFromRecentlyDateString(inputString: string): Date | null {
        let me = this;
        const match = inputString.match(/@(BeginToday|EndToday)([-+]?\d+)/);
        if (match) {
            let offset = me.countDiffDays(inputString)
            const today = new Date();
            const type = match[1]; // "BeginToday" hoặc "EndToday"

            today.setDate(today.getDate() + offset);

            if (type === "BeginToday") {
                // Nếu là @BeginToday, trả về thời gian đầu ngày (00:00:00)
                today.setHours(0, 0, 0, 0);
            } else if (type === "EndToday") {
                // Nếu là @EndToday, trả về thời gian cuối ngày (23:59:59)
                today.setHours(23, 59, 59, 999);
            }
            return today;
        }

        return null;
    }
    convertDateToRecentlyDateFrom(event: Date) {

        const today = new Date();

        today.setHours(0, 0, 0, 0);
        const selectedDate = new Date(event);
        selectedDate.setHours(0, 0, 0, 0);


        const diffTime = selectedDate.getTime() - today.getTime();
        const diffDays = Math.round(diffTime / (1000 * 60 * 60 * 24));

        let formattedDate = '';
        // console.log(diffDays)
        if (diffDays === 0) {
            formattedDate = '@BeginToday-0';
        } else if (diffDays > 0) {
            formattedDate = `@BeginToday` + `+` + diffDays;
        } else {
            formattedDate = `@BeginToday` + diffDays;
        }
        return  formattedDate;
    }

    convertDateToRecentlyDateTo(event: Date) {

        const today = new Date();
        today.setHours(0, 0, 0, 0);

        const selectedDate = new Date(event);
        selectedDate.setHours(0, 0, 0, 0);
        const diffTime = selectedDate.getTime() - today.getTime();
        const diffDays = Math.round(diffTime / (1000 * 60 * 60 * 24));

        // console.log(diffDays)

        let formattedDate = '';
        if (diffDays === 0) {
            formattedDate = '@EndToday-0';
        } else if (diffDays > 0) {
            formattedDate = `@EndToday` + `+` + diffDays;
        } else {
            formattedDate = `@EndToday` + diffDays;
        }

        return formattedDate;
    }

    onChangeRecentlyDateFrom(value, prKey, onChange) {
        if (onChange) {
            this.notChangeFilterParam = false;
            this.checkDisabledBtn();
        }
        // console.log("this.inputRecentlyDate.inputDateFrom")
        // console.log(this.inputRecentlyDate.inputDateFrom)
        if (value) {
            this.inputRecentlyDate.inputDateFrom = value;
            this.inputRecentlyDate.minDateTo = value;
            const maxDateTo = new Date(value);
            maxDateTo.setDate(maxDateTo.getDate() + CONSTANTS.MAX_DATE_SELECTION_RANGE);
            if (maxDateTo > new Date()) {
                this.inputRecentlyDate.maxDateTo = new Date()
            } else {
                this.inputRecentlyDate.maxDateTo = maxDateTo;
            }
            this.searchInfo[prKey] = this.convertDateToRecentlyDateFrom(value)
        } else {
            this.inputRecentlyDate.minDateTo = null;
            this.inputRecentlyDate.maxDateTo = new Date();
            this.searchInfo[prKey] = null;
        }
    }

    onChangeRecentlyDateTo(value, prKey, onChange) {
        if (onChange) {
            this.notChangeFilterParam = false;
            this.checkDisabledBtn();
        }
        if (value) {
            this.inputRecentlyDate.inputDateTo = value;
            this.inputRecentlyDate.maxDateFrom = value;
            const minDateFrom = new Date(value);
            minDateFrom.setDate(minDateFrom.getDate() - CONSTANTS.MAX_DATE_SELECTION_RANGE);
            this.inputRecentlyDate.minDateFrom = minDateFrom;
            this.searchInfo[prKey] = this.convertDateToRecentlyDateTo(value)
        } else {
            this.inputRecentlyDate.maxDateFrom = new Date();
            this.inputRecentlyDate.minDateFrom = null;
            this.searchInfo[prKey] = null;
        }
    }
    onClearRecentlyDateTo(prKey) {
        this.inputRecentlyDate.inputDateTo = null;
        this.onChangeRecentlyDateTo(null, prKey, null)
    }
    onClearRecentlyDateFrom(prKey) {
        this.inputRecentlyDate.inputDateFrom = null;
        this.onChangeRecentlyDateFrom(null, prKey, null)
    }
    getParamDefault(param): { [key: string]: string } {
        const keys: string[] = [];
        const values: string[] = [];
        let parsed = {keys, values}

        if (param.queryParam) {
            parsed = this.parseKeyValuePairs(param.queryParam);
        }
        // Khởi tạo đối tượng trống để lưu các cặp key-value
        const result: { [key: string]: string } = {};

        if (param.queryInfo && param.queryInfo.displayPattern) {
            const matches = [...param.queryInfo.displayPattern.matchAll(/\$\{([^}]+)\}/g)];
            const resultList = matches.map(match => match[1]);

            parsed.keys.push("sort")
            parsed.values.push(resultList[0] + ",asc")

            if(param.queryInfo.output) {
                resultList.push(param.queryInfo.output)
            }
            parsed.keys.push("selectFields")
            parsed.values.push(resultList.toString())
        }


        if (param.isAutoComplete) {
            // Sử dụng vòng lặp để thêm nhiều cặp key-value vào đối tượng
            for (let i = 0; i < parsed.keys.length; i++) {
                result[parsed.keys[i]] = parsed.values[i] || null;
            }
        }

        return result;
    }

    parseKeyValuePairs(input: string): { keys: string[], values: string[] } {
        // Khởi tạo đối tượng để lưu các cặp key-value
        const keyValueMap: { [key: string]: string[] } = {};

        // Tách chuỗi thành các cặp key-value
        const pairs = input.split('&');

        // Xử lý từng cặp
        for (const pair of pairs) {
            const [key, value] = pair.split('=');

            // Xử lý giá trị bắt đầu bằng $
            let actualValue: string;
            if (value.startsWith('$')) {
                actualValue = this.getValueData(value.substring(1)); // Loại bỏ dấu $ và gọi hàm
            } else {
                // Xử lý giá trị không bắt đầu bằng $
                actualValue = value.replace(/^"|"$/g, ''); // Loại bỏ dấu ngoặc kép ở đầu và cuối nếu có
            }

            // Thêm giá trị vào đối tượng keyValueMap
            if (key in keyValueMap) {
                keyValueMap[key].push(actualValue);
            } else {
                keyValueMap[key] = [actualValue];
            }
        }

        // Tạo mảng keys và values từ đối tượng keyValueMap
        const keys: string[] = [];
        const values: string[] = [];

        for (const key in keyValueMap) {
            keys.push(key);
            values.push(keyValueMap[key].join(',')); // Nối các giá trị với dấu phẩy
        }

        // console.log(keys, values);

        // Trả về cả mảng keys và values
        return { keys, values };
    }

    getValueData(key){
        let returnValue
        if(this.getTypeKey(key) == CONSTANTS.PARAMETER_TYPE.DATE){
            returnValue = this.searchInfo[key] ? this.utilService.convertDateTimeToString(this.searchInfo[key]) : null
        }else if(this.getTypeKey(key) == CONSTANTS.PARAMETER_TYPE.TIMESTAMP){
            returnValue = this.searchInfo[key] ? this.searchInfo[key].getTime() : null
        }else if(this.getTypeKey(key) == CONSTANTS.PARAMETER_TYPE.NUMBER){
            returnValue = this.searchInfo[key] ? this.searchInfo[key] * 1 : null
        }else if(this.getTypeKey(key) == CONSTANTS.PARAMETER_TYPE.LIST_NUMBER || this.getTypeKey(key) == CONSTANTS.PARAMETER_TYPE.LIST_STRING){
            returnValue = this.searchInfo[key] ? this.searchInfo[key] : null
        }else{
            returnValue = this.searchInfo[key] ? this.searchInfo[key] : null
        }
        return returnValue
    }

    getTypeKey(key){
        const value = this.listParameters.find(data => data.prKey === key)
        return value ? value.prType :null
    }

    // getColorForThreshold(value, maxValue, threshold: number[],  config) {
    //     let defaultBgColorOk = '#33CC66'
    //     let defaultBgColorWarning = '#FFFF33'
    //     let defaultBgColorAlert = '#DD0000'
    //     if (config.backgroundColors.length >= 3) {
    //         defaultBgColorAlert = config.backgroundColors[2];
    //         defaultBgColorWarning = config.backgroundColors[1];
    //         defaultBgColorOk = config.backgroundColors[0];
    //     }
    //     let colorBg
    //
    //     if (maxValue == 0) {
    //         colorBg = defaultBgColorAlert;
    //     } else {
    //         if (value / maxValue < threshold[0] / 100) {
    //
    //             colorBg = defaultBgColorOk
    //             // console.log(value)
    //             //     console.log(value + "/" + maxValue + "=" + (value / maxValue));
    //             // console.log(threshold[0] / 100 + "-" + threshold[1] / 100);
    //             // console.log("green")
    //         } else if (threshold[0] / 100 <= value / maxValue && value / maxValue <= threshold[1] / 100) {
    //             colorBg = defaultBgColorWarning
    //
    //                 // console.log(value + "/" + value + "=" + (value / maxValue));
    //             // console.log(threshold[0] / 100 + "-" + threshold[1] / 100);
    //             // console.log(value)
    //             // console.log("yellow")
    //         } else {
    //             colorBg = defaultBgColorAlert
    //                 // console.log(value + "/" + value + "=" + (value / maxValue));
    //             // console.log(threshold[0] / 100 + "-" + threshold[1] / 100);
    //             // console.log(value)
    //             // console.log("red")
    //         }
    //     }
    //     return colorBg
    // }

    /**
     * Trả về index để khớp thông tin cấu hình của biểu đồ ngưỡng
     * @param datasetOfConfig = "An toàn || Xem xét || cảnh báo"
     */
    getIndexColorForThreshold(datasetOfConfig) {
        let index = 2;
        if (datasetOfConfig && datasetOfConfig.toUpperCase() == 'An Toàn'.toUpperCase()) {
            index = 0
        } else if (datasetOfConfig && datasetOfConfig.toUpperCase() == 'Xem xét'.toUpperCase()) {
            index = 1
        } else {
            index = 2;
        }
        return index;
    }
}
