import {Inject, Injectable} from "@angular/core";
import {HttpService} from "../comon/http.service";

@Injectable()
export class GroupSubWalletService {
    private prefixApi: string;
    constructor(@Inject(HttpService) private httpService:HttpService) {
        this.prefixApi = "/group-sub-wallet";
    }

    public search(params?:{[key: string]: string}, callback?:Function, errorCallBack?:Function, finallyCallback?:Function){
        this.httpService.get(`${this.prefixApi}/search`, {}, params, callback, errorCallBack, finallyCallback)
    }

    public create(headers:{[key:string]:any}, data: any,params?:{[key:string]:any}, callback?:Function, errorCallback?:Function, finallyCallback?:Function){
        this.httpService.post(`${this.prefixApi}/create`,headers,data,params,callback,errorCallback,finallyCallback)
    }

    public addPhoneInGroup(headers:{[key:string]:any}, data: any,params?:{[key:string]:any}, callback?:Function, errorCallback?:Function, finallyCallback?:Function){
        this.httpService.post(`${this.prefixApi}/add-phone-in-group`,headers,data,params,callback,errorCallback,finallyCallback)
    }

    public update(id,body,callback?: Function, errorCallback?:Function, finallyCallback?: Function){
        this.httpService.put(`${this.prefixApi}/update/${id}`, {},body,{}, callback, errorCallback, finallyCallback);
    }

    public getDetail(id: number, callback?: Function, errorCallback?:Function, finallyCallback?: Function){
        this.httpService.get(`${this.prefixApi}/${id}`,{}, {}, callback, errorCallback, finallyCallback);
    }

    public delete(id: number, callback?:Function, errorCallBack?:Function, finallyCallback?:Function){
        this.httpService.delete(`${this.prefixApi}/${id}`, {}, {}, callback, errorCallBack, finallyCallback);
    }

    public deleteSubInGroup(id: number, callback?:Function, errorCallBack?:Function, finallyCallback?:Function){
        this.httpService.delete(`${this.prefixApi}/delete-sub-in-group/${id}`, {}, {}, callback, errorCallBack, finallyCallback);
    }

    public searchNotInGroup(params:{[key:string]:any}, callback?:Function, errorCallback?:Function, finallyCallback?: Function){
        this.httpService.get(`${this.prefixApi}/get-sim-not-in-group`,{}, params,callback, errorCallback, finallyCallback);
    }

    public searchInGroup(idGroup: number, params:{[key:string]:any}, callback?:Function, errorCallback?:Function, finallyCallback?: Function){
        this.httpService.get(`${this.prefixApi}/get-sim-in-group/${idGroup}`,{}, params,callback, errorCallback, finallyCallback);
    }

    public deleteMany(ids: number[], callback?: Function, errorCallBack?: Function, finallyCallback?: Function){
        this.httpService.post(`${this.prefixApi}/delete-many-sub`,{},{ids},{},callback, errorCallBack, finallyCallback);
    }

    public getAllGroup(callback?: Function, errorCallback?:Function, finallyCallback?: Function){
        this.httpService.get(`${this.prefixApi}/get-all`,{}, {}, callback, errorCallback, finallyCallback);
    }

    public checkPhoneBelongGroup(body,callback?: Function, errorCallback?:Function, finallyCallback?: Function){
        this.httpService.post(`${this.prefixApi}/check-phone`, {},body,{}, callback, errorCallback, finallyCallback);
    }
    public checkExistGroupCode(params, callback?:Function, errorCallback?:Function, finallyCallback?: Function){
        this.httpService.get(`${this.prefixApi}/check-group-code`,{}, params,callback, errorCallback, finallyCallback);
    }
    public uploadFile(objectFile,body, callback:Function,errorCallback?:Function, finallyCallback?: Function){
        this.httpService.uploadContainBody(`${this.prefixApi}/import/share_info`, objectFile, body,{}, {}, callback, errorCallback, finallyCallback);
    }
    public downloadTemplate(){
        this.httpService.downloadLocal(`/assets/data/Mẫu_file_thông_tin_người_chia_sẻ.xlsx`, "Mẫu_file_thông_tin_người_chia_sẻ.xlsx");
    }
}
