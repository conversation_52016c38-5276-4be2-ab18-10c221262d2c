import { CommonModule } from "@angular/common";
import { NgModule } from "@angular/core";
import { CommonVnptModule } from "../common-module/common.module";
import { TermPolicyListComponent } from "./app.term.policy.list.component";
import { TermPolicyHistoryComponent } from "./app.term.policy.history.component";
import { PersonalDataProtectionFormComponent } from "./personal-data-protection-policy/app.personal.data.protection.policy.form.component";
import { PersonalDataProtectionContentComponent } from "./personal-data-protection-policy/app.personal.data.protection.policy.content.component";
import { AccountService } from "src/app/service/account/AccountService";
import { FormsModule, ReactiveFormsModule } from "@angular/forms";
import { TermPolicyRoutingModule } from "./app.term.policy.routing";

import { TabViewModule } from 'primeng/tabview';
import { BreadcrumbModule } from "primeng/breadcrumb";
import { PanelModule } from "primeng/panel";
import { InputTextModule } from "primeng/inputtext";
import { ButtonModule } from "primeng/button";
import { TermPolicyConfirmComponent } from "./app.term.policy.confirm.component";
import { DialogModule } from "primeng/dialog";
import { ConfirmDialogModule } from "primeng/confirmdialog";
import { ConfirmationService } from "primeng/api";
import { CheckboxModule } from "primeng/checkbox";

@NgModule({
    imports: [
        TermPolicyRoutingModule,
        CommonModule,
        CommonVnptModule,
        FormsModule,
        ReactiveFormsModule,
        TabViewModule,
        BreadcrumbModule,
        PanelModule,
        InputTextModule,
        ButtonModule,
        DialogModule,
        ConfirmDialogModule,
        CheckboxModule
    ],
    declarations: [
        TermPolicyListComponent,
        TermPolicyHistoryComponent,
        PersonalDataProtectionFormComponent,
        PersonalDataProtectionContentComponent,
        TermPolicyConfirmComponent
    ],
    exports: [
        PersonalDataProtectionFormComponent,
        TermPolicyConfirmComponent
    ],
    providers: [
        AccountService, ConfirmationService
    ]
})
export class TermPolicyModule {};