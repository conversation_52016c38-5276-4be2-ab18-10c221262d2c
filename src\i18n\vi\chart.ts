export default {
    titlePage: "Biểu đồ",
    label: {
        generalInfo: "Thông tin chung",
        chartConfig: "Cấu hình tùy chọn",
        dataConfig: "Cấu hình dữ liệu",
        filterConfig: "Cấu hình tìm kiếm",
        chartName: "Tên biểu đồ",
        chartType: "Loại biểu đồ",
        chartSubType: "Kiểu biểu đồ mở rộng",
        query: "Câu truy vấn",
        typeQuery: "Kiểu truy vấn",
        keyLabel: "Trường lấy nhãn dữ liệu",
        keyValue: "Trường lấy giá trị",
        keyDataset: "Trường lấy mẫu dữ liệu",
        datasetColor: "<PERSON><PERSON><PERSON> cho mẫu dữ liệu",
        datasetName: "Tên mẫu dữ liệu",
        threshold: "Ngưỡng",
        thresholdConfig: "<PERSON><PERSON>u hình ngưỡng",
        width: "Chiều dài",
        height: "Chiều rộng",
        marginLeft: "Dãn lề trái",
        marginRight: "Dãn lề phải",
        align: "<PERSON>ă<PERSON> lề",
        apply: "Áp dụng",
        sizing: "Cấu hình kích thước",
        left: "Trái",
        right: "Phải",
        center: "Giữa",
        justify: "Đều hai bên",
        description: "Mô tả",
        thresholdConfigSlider: "Cấu hình ngưỡng trượt",
        keyMaxValue: "Trường giới hạn dữ liệu"
    },
    type: {
        bar: "Biểu đồ cột",
        bubble: "Biểu đồ bong bóng",
        combo: "Biểu đồ kết hợp",
        doughnut: "Biểu đồ bánh",
        line: "Biểu đồ đường",
        pie: "Biểu đồ quạt",
        polar: "Biểu đồ tọa cực",
        radar: "Biểu đồ mạng nhện",
        scatter: "Biểu đồ phân tán"
    },
    subType: {
        horizontalBar: "Biểu đồ cột ngang",
        verticalBar: "Biểu đồ cột dọc",
        stackedBar: "Biểu đồ cột xếp chồng",
        groupBar: "Biểu đồ cột nhóm",
        multiAxis: "Biểu đồ đa chiều",
        threshold: "Biểu đồ cấu hình ngưỡng",
        sliderThreshold: "Biểu đồ cấu hình ngưỡng trượt",
    }
}
