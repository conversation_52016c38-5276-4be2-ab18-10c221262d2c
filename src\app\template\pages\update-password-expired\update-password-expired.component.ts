import {Component, Injector, Input, OnInit} from "@angular/core";
import {AccountService} from "../../../service/account/AccountService";
import {MessageCommonService} from "../../../service/comon/message-common.service";
import {Router} from "@angular/router";
import {TranslateService} from "../../../service/comon/translate.service";
import {Subscription} from "rxjs";
import {CONSTANTS} from "../../../service/comon/constants";
import {ObservableService} from "../../../service/comon/observable.service";
import {FormBuilder} from "@angular/forms";

@Component({
    selector: 'update-password-expired-popup',
    templateUrl: "./update-password-expired.component.html"
})

export class FormUpdatePasswordExpired implements OnInit {

    constructor(
        private router: Router,
        public accountService: AccountService,
        public messageCommonService: MessageCommonService,
        public tranService: TranslateService,
        private observableService: ObservableService,
        private formBuilder: FormBuilder
    ) {}

    @Input() token: string;

    formChangePass: any;
    isShowOldPass: boolean = false;
    isValidOldPass: boolean;
    items: any;
    home: any;
    changePassInfo: {
        oldPassword: string,
        newPassword: string,
        confirmPassword: string
    };
    isShowNewPass: boolean = false;
    isShowRepeatPass: boolean = false;
    isShowPopupUpdatePassword: boolean = false;
    subConfirmExpiredPassword: Subscription;


    ngOnInit(): void {
        this.subConfirmExpiredPassword = this.observableService.subscribe(CONSTANTS.OBSERVABLE.KEY_EXPIRED_PASSWORD, {
            next: this.loadData.bind(this)
        });

        this.isValidOldPass = true
        this.changePassInfo = {
            oldPassword: "",
            newPassword: "",
            confirmPassword: ""
        }
        this.formChangePass = this.formBuilder.group(this.changePassInfo);
        this.items = [
            {label: this.tranService.translate("global.menu.account")},
            {label: this.tranService.translate("global.menu.detailAccount"), routerLink:"/profile"},
            {label: this.tranService.translate("global.menu.changePass")}
        ];
        this.home = {icon: 'pi pi-home', routerLink: '/'};
    }

    loadData(data): void {
        this.changePassInfo = {
            oldPassword: "",
            newPassword: "",
            confirmPassword: ""
        }
        if (data?.isExpiredPassword) {
            this.isShowPopupUpdatePassword = true;
        }
    }


    submitChangePass(oldPass: string) {
        this.messageCommonService.onload();
        let tokenChangePass = localStorage.getItem("tokenUpdatePass");
        let me = this;
        let header = {
            Authorization: "Bearer " + tokenChangePass,
        }
        this.accountService.changePassword(header,this.changePassInfo,(response) => {
            me.messageCommonService.success(me.tranService.translate("global.message.saveSuccess"));
            this.router.navigate(["/login"])
        }, null, ()=>{
            me.messageCommonService.offload();
        })
        this.isShowPopupUpdatePassword = false;
    }

    ngOnDestroy(): void {
        this.subConfirmExpiredPassword.unsubscribe();
    }

    onHideUpdate(){
        let me = this;
        me.router.navigate(["/login"]);
    }

}
