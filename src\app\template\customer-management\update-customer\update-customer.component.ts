import { Component, Inject, On<PERSON><PERSON>roy, inject, Injector, AfterContentChecked } from '@angular/core';
import { AbstractControl, FormControl, FormGroup, ValidationErrors, ValidatorFn, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { MenuItem } from 'primeng/api';
import { Subscription } from 'rxjs';
import { ComponentBase } from 'src/app/component.base';
import { CONSTANTS } from 'src/app/service/comon/constants';
import { MessageCommonService } from 'src/app/service/comon/message-common.service';
import { TranslateService } from 'src/app/service/comon/translate.service';
import { UtilService } from 'src/app/service/comon/util.service';
import { CustomerService } from 'src/app/service/customer/CustomerService';

@Component({
  selector: 'app-update-customer',
  templateUrl: './update-customer.component.html',
  styleUrls: ['./update-customer.component.scss']
})
export class UpdateCustomerComponent extends ComponentBase implements OnD<PERSON>roy, AfterContentChecked{
  idForEdit:number;
  initialData:any;
  customerInfo: any = null;

  isCustomerCodeValid : boolean = false
  isTaxIdValid : boolean = false
  isProvinceCodeValid : boolean = false
  isStatusValid : boolean = false
  isCustomerNameValid : boolean = false
  isEmailValid : boolean = false
  isBillNameValid : boolean = false
  isBillEmailValid : boolean = false
  isAddrStreetValid : boolean = false;
  isAddrDistValid : boolean = false
  isAddrProvinceValid : boolean = false
  isNoteValid : boolean = false

  items: MenuItem[]=[{ label: this.tranService.translate(`global.menu.customermgmt`), routerLink:'../../' }, { label: this.tranService.translate(`global.button.edit`),}];
  home: MenuItem={ icon: 'pi pi-home', routerLink: '/' };
  typeList:any = [
    {name:this.tranService.translate("ratingPlan.customerType.personal"), value: CONSTANTS.CUSTOMER_TYPE.PERSONAL},
    {name:this.tranService.translate('ratingPlan.customerType.enterprise'), value: CONSTANTS.CUSTOMER_TYPE.INTERPRISE},
    {name:this.tranService.translate('ratingPlan.customerType.agency'), value: CONSTANTS.CUSTOMER_TYPE.AGENCY}
  ]

  statusList:any= [
    {name:this.tranService.translate("customer.label.active"), value:CONSTANTS.CUSTOMER_STATUS.ACTIVE},
    {name:this.tranService.translate('customer.label.inActive'), value:CONSTANTS.CUSTOMER_STATUS.INACTIVE}
  ]

  generalHeader: string = this.tranService.translate("customer.label.generalInfo");
  contactHeader: string = this.tranService.translate("customer.label.billingContact")
  paymentHeader: string = this.tranService.translate('customer.label.billingAddress');
  note: string = this.tranService.translate("customer.label.note")
  constructor(@Inject(CustomerService) private customerService: CustomerService,injector: Injector) {
    super(injector)
  }


  reformatDate(dateStr: string): string {
    const parts = dateStr.split('/');
    const day = parts[0].length === 1 ? `0${parts[0]}` : parts[0];
    const month = parts[1].length === 1 ? `0${parts[1]}` : parts[1];
    return `${parts[2]}-${month}-${day}`;
  }


  customCharacterValidator(): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
      const value = control.value;
      const isValid = /^[a-zA-Z0-9 \-_\!\#\$\%\&\'\*\+\-\/\=\?\^\_\`\.\{\|\}\~\u00C0-\u1EF9]*$/.test(value);
      return isValid ? null : { 'invalidCharacters': { value } };
    };
  }
  regularCharacterValidator(): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
      const value = control.value;
      const isValid = /^[a-zA-Z0-9 \-_~\u00C0-\u1EF9]*$/.test(value);
      return isValid ? null : { 'invalidCharacters': { value } };
    };
  }
  noteValidator(): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
        const value = control.value;
        const isValid = /^[a-zA-Z0-9!#\$%&'\*\+\-\/=\?\^_`\.,\(\)\{\|\}~: \u00C0-\u1EF9]*$/.test(value);
        return isValid ? null : { 'invalidCharacters': { value } };
    };
  }

  addressCharacterValidator(): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
      const value = control.value;
      const isValid = /^[a-zA-Z0-9 \-,_~\u00C0-\u1EF9]*$/.test(value);
      return isValid ? null : { 'invalidCharacters': { value } };
    };
  }

  updateCustomerForm:FormGroup
  subCustomerCode: Subscription;
  subTaxId: Subscription;
  subProvinceCode: Subscription;
  subStatus: Subscription;
  subCustomerName: Subscription;
  subEmail: Subscription;
  subBillName: Subscription;
  subBillEmail: Subscription;
  subAddrStreet: Subscription;
  subAddrDist: Subscription;
  subAddrProvince: Subscription;
  subNote: Subscription;
  ngOnInit(){
      if (!this.checkAuthen([CONSTANTS.PERMISSIONS.CUSTOMER.UPDATE])) {window.location.hash = "/access";}

      let me = this
    me.idForEdit = Number(this.route.snapshot.params["id"]);
    // console.log(this.idForEdit);
    this.customerService.getCustomerById(me.idForEdit, (response)=>{
      me.customerInfo = response;
      response.phone = response.phone != null ? ((response.phone || "").substring(2)) : null;
      response.billPhone = response.billPhone != null ? ((response.billPhone || "").substring(2)): null;
      response.birthday = new Date(response.birthday)
      response.billBirthday = new Date(response.billBirthday)
      this.initialData=response;
      this.updateCustomerForm = this.initUpdateForm();
      this.updateCustomerForm.patchValue(this.initialData);
      this.checkErrorForm();
    })
  }

  ngAfterContentChecked(): void {
    // console.log(this.updateCustomerForm);
  }

  checkErrorForm(){
    this.subCustomerCode = this.updateCustomerForm.get('customerCode').statusChanges.subscribe(() => {
      const errors = this.updateCustomerForm.get('customerCode').errors;
      if (errors) {
        this.isCustomerCodeValid= true;
      }else{
        this.isCustomerCodeValid= false;
      }
    });

    this.subTaxId = this.updateCustomerForm.get('taxId').statusChanges.subscribe(() => {
      const errors = this.updateCustomerForm.get('taxId').errors;
      if (errors) {
        this.isTaxIdValid= true;
      }else{
        this.isTaxIdValid= false;
      }
    });

    this.subProvinceCode = this.updateCustomerForm.get('provinceCode').statusChanges.subscribe(() => {
      const errors = this.updateCustomerForm.get('provinceCode').errors;
      if (errors) {
        this.isProvinceCodeValid= true;
      }else{
        this.isProvinceCodeValid= false;
      }
    });

    this.subStatus = this.updateCustomerForm.get('status').statusChanges.subscribe(() => {
      const errors = this.updateCustomerForm.get('status').errors;
      if (errors) {
        this.isStatusValid= true;
      }else{
        this.isStatusValid= false;
      }
    });

    this.subCustomerName = this.updateCustomerForm.get('customerName').statusChanges.subscribe(() => {
      const errors = this.updateCustomerForm.get('customerName').errors;
      if (errors) {
        this.isCustomerNameValid= true;
      }else{
        this.isCustomerNameValid= false;
      }
    });

    this.subEmail = this.updateCustomerForm.get('email').statusChanges.subscribe(() => {
      const errors = this.updateCustomerForm.get('email').errors;
      if (errors) {
        this.isEmailValid= true;
      }else{
        this.isEmailValid= false;
      }
    });

    this.subBillName = this.updateCustomerForm.get('billName').statusChanges.subscribe(() => {
      const errors = this.updateCustomerForm.get('billName').errors;
      if (errors) {
        this.isBillNameValid= true;
      }else{
        this.isBillNameValid= false;
      }
    });

    this.subBillEmail = this.updateCustomerForm.get('billEmail').statusChanges.subscribe(() => {
      const errors = this.updateCustomerForm.get('billEmail').errors;
      if (errors) {
        this.isBillEmailValid= true;
      }else{
        this.isBillEmailValid= false;
      }
    });

    this.subAddrStreet = this.updateCustomerForm.get('addrStreet').statusChanges.subscribe(() => {
      const errors = this.updateCustomerForm.get('addrStreet').errors;
      if (errors) {
        this.isAddrStreetValid= true;
      }else{
        this.isAddrStreetValid= false;
      }
    });

    this.subAddrDist = this.updateCustomerForm.get('addrDist').statusChanges.subscribe(() => {
      const errors = this.updateCustomerForm.get('addrDist').errors;
      if (errors) {
        this.isAddrDistValid= true;
      }else{
        this.isAddrDistValid= false;
      }
    });

    this.subAddrProvince = this.updateCustomerForm.get('addrProvince').statusChanges.subscribe(() => {
      const errors = this.updateCustomerForm.get('addrProvince').errors;
      if (errors) {
        this.isAddrProvinceValid= true;
      }else{
        this.isAddrProvinceValid= false;
      }
    });

    this.subNote = this.updateCustomerForm.get('note').statusChanges.subscribe(() => {
      const errors = this.updateCustomerForm.get('note').errors;
      if (errors) {
        this.isNoteValid= true;
      }else{
        this.isNoteValid= false;
      }
    });
  }

  initUpdateForm(): FormGroup{
    return new FormGroup({
      customerCode : new FormControl({value:"",disabled:true}, [Validators.required]),
      taxId : new FormControl("", [Validators.minLength(2), Validators.maxLength(255), this.regularCharacterValidator()]),
      provinceCode : new FormControl({value:"", disabled:true}),
      customerType: new FormControl(),
      status : new FormControl({value:"", disabled:true}),
      // Thông tin liên hệ chính
      customerName : new FormControl("",[Validators.required, Validators.minLength(2), Validators.maxLength(255), this.customCharacterValidator()]),
      phone : new FormControl("", [Validators.pattern("^[1-9][0-9]{8,9}$")]),
      email : new FormControl("", [Validators.email, Validators.maxLength(255)]),
      birthday : new FormControl(),
      // Thông tin thanh toán
      billName : new FormControl("", [Validators.minLength(2), Validators.maxLength(255), this.regularCharacterValidator()]),
      billPhone : new FormControl("",[Validators.pattern("^[1-9][0-9]{8,9}$")]),
      billEmail : new FormControl("", [Validators.email, Validators.maxLength(255)]),
      billBirthday : new FormControl(),
      // Địa chỉ
      addrStreet : new FormControl("", [Validators.minLength(2), Validators.maxLength(255), this.addressCharacterValidator()]),
      addrDist : new FormControl("", [Validators.minLength(2), Validators.maxLength(255), this.regularCharacterValidator()]),
      addrProvince : new FormControl("", [Validators.minLength(2), Validators.maxLength(255), this.regularCharacterValidator()]),
      //Ghi chú
      note : new FormControl("", [Validators.minLength(2), Validators.maxLength(255), this.noteValidator()])
    })
  }

  submitForm(){
      let me = this;
    // console.log(this.updateCustomerForm.value)
    if(this.updateCustomerForm.valid){
        me.messageCommonService.onload();
      // console.log(this.updateCustomerForm.value)
      let data = {...this.updateCustomerForm.value};
      data.birthday = this.reformatDate(this.utilService.convertDateToString(data.birthday));
      data.billBirthday = this.reformatDate(this.utilService.convertDateToString(data.billBirthday));
      data.phone = "84"+data.phone;
      data.billPhone = "84"+data.billPhone;
      // console.log(data)
      this.customerService.updateCustomer(this.idForEdit, data, ()=>{
          me.router.navigate(['/customers'])
          me.messageCommonService.success(me.tranService.translate("global.message.saveSuccess"))
      }, null, ()=>{
        me.messageCommonService.offload();
      })
    }
  }

  ngOnDestroy(): void {
      this.subCustomerCode.unsubscribe();
      this.subTaxId.unsubscribe();
      this.subProvinceCode.unsubscribe();
      this.subStatus.unsubscribe();
      this.subCustomerName.unsubscribe();
      this.subEmail.unsubscribe();
      this.subBillName.unsubscribe();
      this.subBillEmail.unsubscribe();
      this.subAddrStreet.unsubscribe();
      this.subAddrDist.unsubscribe();
      this.subAddrProvince.unsubscribe();
      this.subNote.unsubscribe();
  }

  getShowViewAccount(){
    if(this.customerInfo != null && this.customerInfo.userId != null){
      return true;
    }
    return false;
  }

  goToDetailAccount(){
    if(this.customerInfo != null && this.customerInfo.userId != null){
      this.router.navigate(["/accounts/detail/"+this.customerInfo.userId])
    }
  }
}
