import {AfterContentChecked, Component, Inject, Injector, OnInit} from '@angular/core';
import {AccountService} from "../../../../service/account/AccountService";
import {FormBuilder, FormControl} from "@angular/forms";
import {MenuItem} from "primeng/api";
import {CONSTANTS} from "../../../../service/comon/constants";
import {ComponentBase} from "../../../../component.base";
import {AlertService} from "../../../../service/alert/AlertService";
import {ComboLazyControl} from 'src/app/template/common-module/combobox-lazyload/combobox.lazyload';
import {DeviceTypeService} from "../../../../service/device/device-type.service";
import {DeviceModelService} from "../../../../service/device/device-model.service";
import {ru} from "suneditor/src/lang";


@Component({
    selector: 'app-alert-update',
    templateUrl: './app-alert-update.component.html',
})
export class AppAlertUpdateComponent extends ComponentBase implements OnInit, AfterContentChecked {
    constructor(
        @Inject(AccountService) private accountService: AccountService,
        @Inject(DeviceTypeService) private deviceTypeService: DeviceTypeService,
        @Inject(DeviceModelService) private deviceModelService: DeviceModelService,
        private formBuilder: FormBuilder,
        @Inject(AlertService) private alertService: AlertService,
        private injector: Injector) {
        super(injector);
    }

    items: MenuItem[];
    home: MenuItem;
    formAlert: any;
    alertInfo: {
        name: string | null,
        description: string | null,
        severity: string | null,
        emailList: string | null,
        emailContent: string | null,
        msisdnsNotify: string | null,
        smsContent: string | null,
        zaloNotify: string | null,
        zaloContent: string | null,
        eventType: number | null,
        deviceType: string | null,
        model: string | null,
        userCustomerId: number | null,
        userEnterpriseId: number | null,
        deviceId: number | null,
        sendingMethod: Array<any> | null,
        timeSend: string | null,
        deviceTypeId: number | null,
        status: number | null,
    };
    userType: number;
    wallet: any;
    severityOptions: Array<any>;
    eventOptions: Array<any>;
    repeat: boolean = false;
    userInfo: any;
    readonly CONSTANTS = CONSTANTS;
    paramSearchCustomer = {};
    controlComboSelectDevice: ComboLazyControl = new ComboLazyControl();
    controlComboSelectType: ComboLazyControl = new ComboLazyControl();
    controlComboSelectModel: ComboLazyControl = new ComboLazyControl()
    controlComboSelectIndividual: ComboLazyControl = new ComboLazyControl()
    controlComboSelectBusiness: ComboLazyControl = new ComboLazyControl()
    paramSearchType: {
        modelCode: any;
    }
    paramSearchModel: {
        typeCode: any;
    }
    paramSearchBusiness: {
        type: 2,
        sort: 'id,asc'
        customerId: number | -1,
        deviceTypeId: number | -1,
    }
    paramSearchIndividual: {
        type: 3,
        sort: 'id,asc'
        managerId: number | -1,
        deviceTypeId: number | -1,
    }
    paramSearchDevice: {
        enterpriseUserId: number | null,
        customerUserId: number | null,
        sort: "",
        typeCode: string | null,
        modelCode: string | null,
    }
    paramSearchUserCreated: {}
    normalPattern = /^[a-zA-Z0-9 ]*$/;
    vietnamesePattern = /^[^~`!@#\$%\^&*\(\)=\+\[\]\{\}\|\\,<>\/?]*$/;
    numberWithOneDot: RegExp = /[0-9\.]/;
    inputSchema: Array<any>
    optionSend = ['Email', 'SMS', 'Zalo'];
    alertId = this.route.snapshot.paramMap.get("id");

    ngOnInit(): void {
        let me = this;
        this.paramSearchType = {
            modelCode: "",
        }
        this.paramSearchModel = {
            typeCode: "",
        }
        this.paramSearchBusiness = {
            type: 2,
            sort: 'id,asc',
            customerId: -1,
            deviceTypeId: -1,
        }
        this.paramSearchIndividual = {
            type: 3,
            sort: 'id,asc',
            managerId: -1,
            deviceTypeId: -1,
        }
        this.paramSearchUserCreated = {
            sort: 'id,asc',
        }
        this.paramSearchDevice = {
            enterpriseUserId: null,
            customerUserId: null,
            sort: "",
            typeCode: null,
            modelCode: null,
        }
        this.inputSchema = [];
        if (this.sessionService.userInfo.type != CONSTANTS.USER_TYPE.ADMIN) {
            this.paramSearchCustomer = {
                provinceCode: this.sessionService.userInfo.provinceCode
            }
        }
        this.userType = this.sessionService.userInfo.type;
        this.items = [{
            label: this.tranService.translate("global.menu.alertList"),
            routerLink: "/alerts"
        }, {label: this.tranService.translate("global.titlepage.editAlarm")}];
        this.home = {icon: 'pi pi-home', routerLink: '/'};
        this.alertInfo = {
            name: null,
            description: null,
            severity: null,
            emailList: null,
            emailContent: me.tranService.translate('alert.text.inputcontentEmail'),
            msisdnsNotify: null,
            smsContent: me.tranService.translate('alert.text.inputcontentEmail'),
            eventType: null,
            deviceType: null,
            model: null,
            deviceId: null,
            userEnterpriseId: null,
            userCustomerId: null,
            sendingMethod: [],
            timeSend: "immediate",
            zaloNotify: null,
            zaloContent: me.tranService.translate('alert.text.inputcontentEmail'),
            deviceTypeId: null,
            status: null,
        }
        me.formAlert = me.formBuilder.group(this.alertInfo);
        this.severityOptions = [
            {name: this.tranService.translate("alert.severity.critical"), value: 0},
            {name: this.tranService.translate("alert.severity.major"), value: 1},
            {name: this.tranService.translate("alert.severity.minor"), value: 2},
            {name: this.tranService.translate("alert.severity.info"), value: 3}
        ]
        this.eventOptions = [
            {
                name: this.tranService.translate("alert.eventType.exceededValue"),
                value: CONSTANTS.ALERT_EVENT_TYPE.EXCEEDED_VALUE
            },
            {
                name: this.tranService.translate("alert.eventType.deviceAlert"),
                value: CONSTANTS.ALERT_EVENT_TYPE.DEVICE_ALERT
            },
        ];
        this.userInfo = this.sessionService.userInfo;
        this.getDetail();
    }

    getDetail() {
        let me = this;
        let alertId = this.route.snapshot.paramMap.get("id");
        me.messageCommonService.onload()
        this.alertService.getById(parseInt(alertId), (response) => {
            me.alertInfo = {...response};
            me.alertInfo.timeSend = "immediate"
            if (response.sendingMethod) {
                me.alertInfo.sendingMethod = response.sendingMethod.split(',');
            } else {
                me.alertInfo.sendingMethod = []
            }
            // console.log(me.alertInfo)
            // me.formAlert = me.formBuilder.group(this.alertInfo);
            let rule = JSON.parse(response.rule);
            me.getDetailDeviceType(rule)
        }, null, () => {
            me.messageCommonService.offload();
        })
    }
    updateParams(notChange?) {
        let me = this;
        me.updateDeviceType()
        me.onSelectedType(notChange);
        me.onSelectedModel(notChange);
        me.onSelectedBusiness();
        me.onSelectedIndividual(notChange);
    }

    addFormControl() {
        let me = this;
        if (this.inputSchema.length > 0) {
            me.inputSchema.forEach(input => {
                me.formAlert.addControl(me.utilService.getKey(input.key), new FormControl())
            })
        }
    }

    filValFormControl(rule) {
        let me = this;
        if (this.inputSchema.length > 0) {
            me.inputSchema.forEach(input => {
                const found = rule.find(rule => me.utilService.getKey(rule.key) === me.utilService.getKey(input.key));
                // console.log(found)
                if (me.alertInfo.eventType == CONSTANTS.ALERT_EVENT_TYPE.EXCEEDED_VALUE) {
                    me.formAlert.addControl(me.utilService.getKey(input.key), new FormControl(found?.value))
                } else if (me.alertInfo.eventType == CONSTANTS.ALERT_EVENT_TYPE.DEVICE_ALERT) {
                    me.formAlert.addControl(me.utilService.getKey(input.key), new FormControl(Boolean(found?.value)))
                }

            })
        }
    }

    removeFormControl() {
        let me = this;
        if (this.inputSchema.length > 0) {
            me.inputSchema.forEach(input => {
                me.formAlert.removeControl(me.utilService.getKey(input.key))
            })
        }
    }

    onSelectedType(notChange?) {
        let me = this;

        if (!notChange) {
            me.alertInfo.deviceTypeId = null;
            me.alertInfo.userEnterpriseId = null;
            me.alertInfo.userCustomerId = null;
            me.alertInfo.deviceId = null;
        }

        me.paramSearchModel.typeCode = me.alertInfo.deviceType ? me.alertInfo.deviceType : "";
        if (me.alertInfo.deviceType == null || me.alertInfo.deviceType == undefined || me.alertInfo.deviceType == '') {
            me.inputSchema = []
        } else if (me.alertInfo.deviceType && me.alertInfo.model) {
            me.removeFormControl();
            me.getSchema();
        }
        me.paramSearchDevice.typeCode = me.alertInfo.deviceType ? me.alertInfo.deviceType : "";
    }

    onSelectedModel(notChange?) {
        let me = this;

        if (!notChange) {
            me.alertInfo.deviceTypeId = null;
            me.alertInfo.userEnterpriseId = null;
            me.alertInfo.userCustomerId = null;
            me.alertInfo.deviceId = null;
        }
        me.paramSearchType.modelCode = me.alertInfo.model ? me.alertInfo.model : "";
        if (me.alertInfo.model == null || me.alertInfo.model == undefined || me.alertInfo.model == '') {
            me.inputSchema = []
        } else if (me.alertInfo.deviceType && me.alertInfo.model) {
            me.removeFormControl();
            me.getSchema();
        }
        me.paramSearchDevice.modelCode = me.alertInfo.model ? me.alertInfo.model : "";
    }

    onSelectedBusiness() {
        let me = this;
        if (me.paramSearchIndividual.managerId != me.alertInfo.userEnterpriseId) {
            me.paramSearchIndividual.managerId = me.alertInfo.userEnterpriseId;
        }

        if (me.paramSearchDevice.enterpriseUserId != me.alertInfo.userEnterpriseId) {
            me.paramSearchDevice.enterpriseUserId = me.alertInfo.userEnterpriseId;
        }
    }

    onSelectedIndividual(notChange?) {
        let me = this;
        if (me.paramSearchBusiness.customerId != me.alertInfo.userCustomerId) {
            me.paramSearchBusiness.customerId = me.alertInfo.userCustomerId;
        }

        if (me.paramSearchDevice.customerUserId != me.alertInfo.userCustomerId) {
            me.paramSearchDevice.customerUserId = me.alertInfo.userCustomerId;
        }
        if (!notChange) {
            me.alertInfo.deviceId = null;
        }
    }

    onClearBusiness() {
        let me = this;
        me.alertInfo.userEnterpriseId = null;
        me.paramSearchIndividual.managerId = -1;
        me.paramSearchDevice.enterpriseUserId = -1;

        // me.alertInfo.userCustomerId = null;
        // me.paramSearchBusiness.customerId = -1;
        // me.paramSearchDevice.customerUserId = -1;

        // me.alertInfo.deviceId = null;
    }

    onClearIndividual() {
        let me = this;

        me.alertInfo.userCustomerId = null;
        me.paramSearchBusiness.customerId = -1;
        me.paramSearchDevice.customerUserId = -1;

        me.alertInfo.deviceId = null;
    }

    getDetailDeviceType(rule) {
        let me = this;
        me.deviceTypeService.getDeviceType(me.alertInfo.deviceTypeId, resp => {
            try {
                me.alertInfo.deviceType = resp.typeCode;
                me.alertInfo.model = resp.modelCode
                me.updateParams(true)
                let config = JSON.parse(resp.telemetryConfigSchema)
                if (config && config.length > 0) {
                    me.filterInputSchema(config)
                    me.filValFormControl(rule)
                } else {
                    me.inputSchema = []
                    me.alertInfo.deviceTypeId = null;
                }
                // if (me.inputSchema.length == 0) {
                //     me.messageCommonService.warning(me.tranService.translate('alert.text.noSchema'))
                // }
            } catch (e) {
                console.log(e)
                me.inputSchema = []
                me.alertInfo.deviceTypeId = null;
                // me.messageCommonService.warning(me.tranService.translate('alert.text.noSchema'))
            }
        })
    }

    getSchema() {
        let me = this;
        let dataBody = {
            typeCode: me.alertInfo.deviceType,
            modelCode: me.alertInfo.model,
        }
        me.messageCommonService.onload()
        me.deviceModelService.search(dataBody, (response) => {
            if (response.content.length > 0) {
                let detaiDeviceType = response.content[0];
                me.alertInfo.deviceTypeId = Number(detaiDeviceType.id);
                me.updateDeviceType();
                me.deviceTypeService.getDeviceType(me.alertInfo.deviceTypeId, resp => {
                    try {
                        let config = JSON.parse(resp.telemetryConfigSchema)
                        if (config && config.length > 0) {
                            me.filterInputSchema(config)
                            me.addFormControl()
                        } else {
                            me.inputSchema = []
                            me.alertInfo.deviceTypeId = null;
                        }
                        // if (me.inputSchema.length == 0) {
                        //     me.messageCommonService.warning(me.tranService.translate('alert.text.noSchema'))
                        // }
                    } catch (e) {
                        console.log(e)
                        me.inputSchema = []
                        me.alertInfo.deviceTypeId = null;
                        // me.messageCommonService.warning(me.tranService.translate('alert.text.noSchema'))
                    }
                })
            } else {
                me.inputSchema = []
                me.alertInfo.deviceTypeId = null;
                me.messageCommonService.warning(me.tranService.translate('alert.text.noSchema'))
            }
        }, null, () => {
            me.messageCommonService.offload();
        })
    }

    ngAfterContentChecked() {

    }

    onSubmitUpdate() {
        let me = this;
        let dataBody = {}
        // Object.keys(this.formAlert.controls).forEach(key => {
        //     dataBody[key] = this.formAlert.get(key).value;
        //   });
        Object.keys(this.alertInfo).forEach(key => {
            if (this.alertInfo[key] != null) {
                dataBody[key] = this.alertInfo[key];
            }
        })
        dataBody['sendingMethod'] = dataBody['sendingMethod'].toString();
        let rule = []
        // let check = false;
        me.inputSchema.forEach(el => {
            let object: {}
            // if (me.formAlert.get(el.key).value != null) check = true;
            if (me.alertInfo.eventType == CONSTANTS.ALERT_EVENT_TYPE.EXCEEDED_VALUE) {
                object = {
                    key: me.utilService.getKey(el.key),
                    value: parseFloat(me.formAlert.get(me.utilService.getKey(el.key))?.value) || null,
                }
            } else if (me.alertInfo.eventType == CONSTANTS.ALERT_EVENT_TYPE.DEVICE_ALERT){
                object = {
                    key: CONSTANTS.ALERT.PREFIX_KEY_DEVICE_ALARM + me.utilService.getKey(el.key),
                    value: me.formAlert.get(me.utilService.getKey(el.key))?.value || false,
                }
            }
            if (object['value']) {
                rule.push(object);
            }
        })
        if (!me.hasAtLeastOneInputFilled()) {
            me.messageCommonService.warning(me.tranService.translate("alert.message.requiredValue"))
        }else {
            dataBody['rule'] = JSON.stringify(rule);
            // console.log(dataBody)

            me.alertService.updateAlert(me.alertId, dataBody, (response) => {
                me.messageCommonService.success(me.tranService.translate("global.message.saveSuccess"));
                me.router.navigate(['/alerts']);
            }, null, () => {
                me.messageCommonService.offload();
            })
        }


    }

    checkExistEmailList() {
        if (this.alertInfo.emailList == null || this.alertInfo.emailList == null ||
            this.alertInfo.emailList == '' || this.formAlert.controls.emailList.errors?.pattern) {
            return false;
        }
        const arr = this.alertInfo.emailList.split(',')
        let duplicate = false;
        const set = new Set();
        for (const el of arr) {
            if (!set.has(el)) {
                set.add(el)
            } else {
                duplicate = true;
            }
        }
        return duplicate;
    }

    checkExistSmsList() {
        if (this.alertInfo.msisdnsNotify == null || this.alertInfo.msisdnsNotify == null ||
            this.alertInfo.msisdnsNotify == '' || this.formAlert.controls.msisdnsNotify.errors?.pattern) {
            return false;
        }
        const arr = this.alertInfo.msisdnsNotify.split(',')
        let duplicate = false;
        const set = new Set();
        for (const el of arr) {
            if (!set.has(el)) {
                set.add(el)
            } else {
                duplicate = true;
            }
        }
        return duplicate;
    }

    checkLimitEmail() {
        if (this.alertInfo.emailList == null || this.alertInfo.emailList == null ||
            this.alertInfo.emailList == '' || this.formAlert.controls.emailList.errors?.pattern) {
            return false;
        }
        const arr = this.alertInfo.emailList.split(',')
        if (arr.length > CONSTANTS.ALERT_LIMIT.EMAIL) {
            return true;
        } else {
            return false;
        }
    }

    checkLimitSms() {
        if (this.alertInfo.msisdnsNotify == null || this.alertInfo.msisdnsNotify == null ||
            this.alertInfo.msisdnsNotify == '' || this.formAlert.controls.msisdnsNotify.errors?.pattern) {
            return false;
        }
        const arr = this.alertInfo.msisdnsNotify.split(',')
        if (arr.length > CONSTANTS.ALERT_LIMIT.SMS) {
            return true;
        } else {
            return false;
        }
    }

    checkDisableSave() {
        let me = this;
        // console.log(me.formAlert)
        const invalidControlsAlert = Object.keys(this.formAlert.controls)
            .filter(controlName => this.formAlert.controls[controlName].invalid);
        // console.log("Invalid fields in formAlert: ", invalidControlsAlert);
        if (this.formAlert.invalid
            || me.alertInfo.deviceTypeId == null
            || this.controlComboSelectBusiness.invalid
            || this.controlComboSelectIndividual.invalid
            || this.controlComboSelectModel.invalid
            || this.controlComboSelectType.invalid
            || this.controlComboSelectDevice.invalid
            || this.inputSchema.length == 0
            || this.alertInfo.sendingMethod.length == 0
            || me.alertInfo.sendingMethod.includes('Email') && (this.checkExistEmailList() || this.checkLimitEmail())
            || me.alertInfo.sendingMethod.includes('SMS') && (this.checkExistSmsList() || this.checkLimitSms())
            || me.alertInfo.sendingMethod.includes('Zalo') && (this.checkExistZaloList() || this.checkLimitZalo())
            // || !me.hasAtLeastOneInputFilled()
            // || (me.alertInfo.userEnterpriseId == null && me.alertInfo.userCustomerId == null && me.alertInfo.deviceId == null)
        ) {
            return true;
        } else {
            return false;
        }
    }

    hasAtLeastOneInputFilled(): boolean {
        let me = this;
        return this.inputSchema.some(input => {
            const value = this.formAlert.get(me.utilService.getKey(input.key))?.value;
            if (input.type === CONSTANTS.COMMAND_VAR_TYPE.CHECKBOX) {
                return value === true;
            } else {
                return value !== null && value !== '';
            }
        });
    }

    checkExistZaloList() {
        if (this.alertInfo.zaloNotify == null || this.alertInfo.zaloNotify == null ||
            this.alertInfo.zaloNotify == '' || this.formAlert.controls.zaloNotify.errors?.pattern) {
            return false;
        }
        const arr = this.alertInfo.zaloNotify.split(',')
        let duplicate = false;
        const set = new Set();
        for (const el of arr) {
            if (!set.has(el)) {
                set.add(el)
            } else {
                duplicate = true;
            }
        }
        return duplicate;
    }

    checkLimitZalo() {
        if (this.alertInfo.zaloNotify == null || this.alertInfo.zaloNotify == null ||
            this.alertInfo.zaloNotify == '' || this.formAlert.controls.zaloNotify.errors?.pattern) {
            return false;
        }
        const arr = this.alertInfo.zaloNotify.split(',')
        if (arr.length > CONSTANTS.ALERT_LIMIT.ZALO) {
            return true;
        } else {
            return false;
        }
    }

    changeTypeSendAlert() {
        let me = this;
        const sendingMap = {
            Email: ['emailList', 'emailContent'],
            SMS: ['msisdnsNotify', 'smsContent'],
            Zalo: ['zaloNotify', 'zaloContent']
        };

        // Bước 1: Tập hợp tất cả control có thể có
        const allControls = Object.values(sendingMap).flat();

        // Bước 2: Duyệt qua từng loại gửi (Email, SMS, Zalo)
        Object.entries(sendingMap).forEach(([type, fields]) => {
            if (me.alertInfo.sendingMethod.includes(type)) {
                // Nếu có trong sendingMethod → thêm control nếu chưa có
                fields.forEach(field => {
                    if (!me.formAlert.contains(field)) {
                        me.formAlert.addControl(field, new FormControl());
                    }
                });
            } else {
                // Nếu KHÔNG có trong sendingMethod → xóa control nếu đang có
                fields.forEach(field => {
                    if (me.formAlert.contains(field)) {
                        me.formAlert.removeControl(field);
                    }
                });
            }
        });
    }

    goBack() {
        window.history.back();
    }
    updateDeviceType() {
        let me = this;
        me.paramSearchIndividual.deviceTypeId = me.alertInfo.deviceTypeId ? me.alertInfo.deviceTypeId : null;
        me.paramSearchBusiness.deviceTypeId = me.alertInfo.deviceTypeId ? me.alertInfo.deviceTypeId : null;
    }

    onClearDeivceTypeOrModel() {
        let me = this;
        me.alertInfo.deviceTypeId = null;
        me.alertInfo.userEnterpriseId = null;
        me.alertInfo.userCustomerId = null;
        me.alertInfo.deviceId = null;
        me.updateParams();
    }

    preventMultipleDot(event: KeyboardEvent) {
        const input = event.target as HTMLInputElement;
        if (event.key === '.' && input.value.includes('.')) {
            event.preventDefault(); // Chặn dấu chấm thứ 2
        }
    }
    onInputChange(event: any, type): void {
        if (type == null || type == CONSTANTS.COMMAND_VAR_TYPE.NUMBER) {
            let input: string = event.target.value;

            // Chỉ giữ số và dấu phẩy
            input = input.replace(/[^0-9,]/g, '');

            // Giữ lại dấu phẩy đầu tiên nếu có
            const firstCommaIndex = input.indexOf(',');
            if (firstCommaIndex !== -1) {
                const before = input.substring(0, firstCommaIndex + 1);
                const after = input.substring(firstCommaIndex + 1).replace(/,/g, '');
                input = before + after;
            }

            // Cập nhật giá trị mới cho form control
            const control = this.formAlert.get(event.target.id);
            if (control) {
                control.setValue(input, {emitEvent: false}); // không gây loop
            }

            // Cập nhật lại vào input nếu cần (nếu form không phản chiếu ngay)
            event.target.value = input;
        }
    }
    filterInputSchema(config) {
        let me = this;
        if (me.alertInfo.eventType == CONSTANTS.ALERT_EVENT_TYPE.EXCEEDED_VALUE) {
            me.inputSchema = config.filter((item) => item.isShowAlert);
        } else if (me.alertInfo.eventType == CONSTANTS.ALERT_EVENT_TYPE.DEVICE_ALERT){
            me.inputSchema = config.filter((item) => item.isAlert);
        }
    }
    changeEventType() {
        let me = this;
        if (me.alertInfo.deviceType && me.alertInfo.model) {
            me.removeFormControl();
            me.getSchema();
        }
    }
}
