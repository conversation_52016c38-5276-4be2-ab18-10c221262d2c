import {Inject, Injectable} from "@angular/core";
import {HttpService} from "../comon/http.service";

@Injectable({
    providedIn: 'root'
})
export class AlertService {
    private prefixApi: string;

    constructor(@Inject(HttpService) private httpService: HttpService) {
        this.prefixApi = "/alerts";
    }

    public getListAlertReceivingGroup(params: {
        [key: string]: any
    }, callback?: Function, errorCallBack?: Function, finallyCallback?: Function) {
        this.httpService.get(`${this.prefixApi}/listAlertReceivingGroup`, {}, params, callback, errorCallBack, finallyCallback);
    }

    public deleleAlertReceivingGroup(id: number, callback?: Function, errorCallBack?: Function, finallyCallback?: Function) {
        this.httpService.delete(`${this.prefixApi}/deleteAlertReceivingGroup/${id}`, {}, {}, callback, errorCallBack, finallyCallback);
    }

    public deleleListAlertReceivingGroup(listId: Array<string>, callback?: Function, errorCallBack?: Function, finallyCallback?: Function) {
        this.httpService.delete(`${this.prefixApi}/deleteAlertReceivingGroup/${listId}`, {}, {}, callback, errorCallBack, finallyCallback);
    }

    public getListAlertHistory(params: {
        [key: string]: any
    }, callback?: Function, errorCallBack?: Function, finallyCallback?: Function) {
        this.httpService.get(`${this.prefixApi}/history`, {}, params, callback, errorCallBack, finallyCallback);
    }

    public search(params: any, callback: Function, errorCallBack?: Function, finallyCallback?: Function){
        this.httpService.get(`${this.prefixApi}/search`, {}, params,callback, errorCallBack, finallyCallback);
    }

    public getAllReceivingGroup(params: any, callback?: Function, errorCallBack?: Function, finallyCallback?: Function){
        this.httpService.get(`/receiving-group/all`, {}, params,callback, errorCallBack, finallyCallback);
    }

    public createAlert(body,callback?: Function, errorCallback?:Function, finallyCallback?: Function){
        this.httpService.post(`${this.prefixApi}`, {},body,{}, callback, errorCallback, finallyCallback);
    }

    public getById(id: number, callback?: Function, errorCallback?:Function, finallyCallback?: Function){
        this.httpService.get(`${this.prefixApi}/${id}`,{}, {}, callback, errorCallback, finallyCallback);
    }
    public deleteById(id:number, callback?:Function, errorCallBack?:Function, finallyCallback?:Function){
        this.httpService.delete(`${this.prefixApi}/${id}`,{}, {},callback, errorCallBack, finallyCallback);
    }

    public changeStatus(body,callback?: Function, errorCallback?:Function, finallyCallback?: Function){
        this.httpService.post(`${this.prefixApi}/status`, {},body,{}, callback, errorCallback, finallyCallback);
    }
    public updateAlert(id,body,callback?: Function, errorCallback?:Function, finallyCallback?: Function){
        this.httpService.put(`${this.prefixApi}/${id}`, {},body,{}, callback, errorCallback, finallyCallback);
    }
    public checkName(query:{[key:string]:any}, callback?:Function, errorCallBack?:Function, finallyCallback?:Function){
        this.httpService.get(`${this.prefixApi}/check-exist`,{}, query,callback, errorCallBack, finallyCallback);
    }
    public createAlertWalletExpiry(body,callback?: Function, errorCallback?:Function, finallyCallback?: Function){
        this.httpService.post(`${this.prefixApi}/createAlertWalletExpiry`, {},body,{}, callback, errorCallback, finallyCallback);
    }
    public createAlertWalletThreshold(body,callback?: Function, errorCallback?:Function, finallyCallback?: Function){
        this.httpService.post(`${this.prefixApi}/createAlertWalletThreshold`, {},body,{}, callback, errorCallback, finallyCallback);
    }
    public updateAlertWalletExpiry(id,body,callback?: Function, errorCallback?:Function, finallyCallback?: Function){
        this.httpService.put(`${this.prefixApi}/updateAlertWalletExpiry/${id}`, {},body,{}, callback, errorCallback, finallyCallback);
    }
    public updateAlertWalletThreshold(id,body,callback?: Function, errorCallback?:Function, finallyCallback?: Function){
        this.httpService.put(`${this.prefixApi}/updateAlertWalletThreshold/${id}`, {},body,{}, callback, errorCallback, finallyCallback);
    }
}
