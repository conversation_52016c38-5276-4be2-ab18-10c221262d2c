import { Inject, Injectable } from "@angular/core";
import { HttpService } from "../comon/http.service";

@Injectable()
export class GroupSimService {
    private prefixApi: string;
    constructor(@Inject(HttpService) private httpService:HttpService) {
        this.prefixApi = "/group-sim";
    }

    public getAllSumaryGroupSim(params,callback: Function, errorCallBack?: Function, finallyCallback?: Function){
        this.httpService.get(`${this.prefixApi}/get-list-group-sim`,{},params,callback,errorCallBack,finallyCallback);
    }

    public searchSimGroup(headers:{[key:string]:any},params:{[key: string]: any},callback?:Function, errorCallBack?:Function, finallyCallback?:Function){
        this.httpService.get(this.prefixApi+"/search",headers,params, callback,errorCallBack,finallyCallback);
    }

    public search(params:{[key: string]: any},callback?:Function){
        this.httpService.get(this.prefixApi+"/search",{},params, callback);
    }

    public createSimGroup(headers:{[key:string]:any}, data: any,params?:{[key:string]:any}, callback?:Function, errorCallback?:Function, finallyCallback?:Function){
        this.httpService.post(this.prefixApi,headers,data,params,callback,errorCallback,finallyCallback)
    }

    public getSimGroupById(pathVariable:string, headers:{[key:string]:any},params:{[key: string]: string},callback?:Function, errorCallBack?:Function, finallyCallback?:Function){
        this.httpService.get(this.prefixApi+"/"+pathVariable,headers,params,callback,errorCallBack,finallyCallback)
    }

    public getById(id:any,callback?:Function){
        this.httpService.get(this.prefixApi+"/"+id,{},{},callback)
    }

    public getByKey(key: string, value: string, callback?: Function, errorCallback?:Function, finallyCallback?: Function){
        this.httpService.get(`${this.prefixApi}/getByKey`,{}, {key, value}, callback, errorCallback, finallyCallback);
    }

    public updateSimGroup(pathVariable:string, headers:{[key:string]:any},data:any, params:{[key:string]:any}, callback?:Function, errorCallBack?:Function, finallyCallback?:Function){
        this.httpService.put(this.prefixApi+"/"+pathVariable, headers,data, params, callback, errorCallBack, finallyCallback)
    }

    public deleteSimGroup(pathVariable:string, headers:{[key:string]:any},params:{[key: string]: string},callback?:Function, errorCallBack?:Function, finallyCallback?:Function){
        this.httpService.delete(this.prefixApi+"/"+pathVariable, headers, params, callback, errorCallBack, finallyCallback)
    }

    public getListDetailGroupSim(pathVariable:string,headers:{[key:string]:any},params:{[key: string]: string},callback:Function){
        this.httpService.get(this.prefixApi+"/"+pathVariable+"/sims",headers,params,callback);
    }

    public groupkeyCheckExisted(headers:{[key:string]:any},params:{[key: string]: string},callback:Function){
        this.httpService.get(this.prefixApi+"/checkExist",headers,params,callback)
    }
    public getListByCustomerCode(query:{[key:string]:any}, callback?:Function, errorCallBack?:Function, finallyCallback?:Function){
        this.httpService.get(`${this.prefixApi}/get-by-customer-code`,{}, query,callback, errorCallBack, finallyCallback);
    }
}
