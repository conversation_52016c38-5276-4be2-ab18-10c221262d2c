import {NgModule} from "@angular/core";
import {RouterModule} from "@angular/router";
import DataPage from "../../service/data.page";
import {ListHistoryActivityComponent} from "./list/app.list.history.component";
import {CONSTANTS} from "../../service/comon/constants";


@NgModule({
  imports: [
    RouterModule.forChild([
      {path: 'list', component: ListHistoryActivityComponent, data: new DataPage("logs.menu.log",[CONSTANTS.PERMISSIONS.LOG.VIEW_LIST])},
    ]),
  ],
  exports: [RouterModule],
})
export class AppHistoryRouting {
}
