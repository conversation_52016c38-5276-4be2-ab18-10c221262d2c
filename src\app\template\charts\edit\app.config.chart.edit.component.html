<div class="vnpt flex flex-row justify-content-between align-items-center bg-white p-2 border-round">
    <div class="">
        <div class="text-xl font-bold mb-1">{{tranService.translate("global.menu.chartList")}}</div>
        <p-breadcrumb class="max-w-full col-7" [model]="items" [home]="home"></p-breadcrumb>
    </div>
    <p-button styleClass="p-button-info"
                    [label]="tranService.translate('global.button.preview')"
                    icon=""
                    (click)="openPreview()"></p-button>
</div>
<p-card styleClass="mt-3">
    <div>
        <!-- generalConfig -->
        <p-panel [toggleable]="true" [header]="tranService.translate('chart.label.generalInfo')" *ngIf="generalConfigForm">
            <form [formGroup]="generalConfigForm">
                <div class="flex flex-row justify-content-between">
                    <div style="width: 49%;">
                        <!-- chartName -->
                        <div class="w-full field grid">
                            <label htmlFor="chartName" class="col-fixed" style="width:180px">{{tranService.translate("chart.label.chartName")}}<span class="text-red-500">*</span></label>
                            <div class="col">
                                <input class="w-full"
                                        pInputText id="chartName"
                                        [(ngModel)]="dynamicConfig.name"
                                        formControlName="chartName"
                                        [required]="true"
                                        [maxLength]="255"
                                        pattern="^[a-zA-Z0-9 _\-\u00C0-\u1EF9]+$"
                                        [placeholder]="tranService.translate('chart.label.chartName')"
                                        (ngModelChange)="checkExistChart()"
                                />
                            </div>
                        </div>
                        <!-- error chartName -->
                        <div class="w-full field grid text-error-field">
                            <label htmlFor="chartName" class="col-fixed" style="width:180px"></label>
                            <div class="col">
                                <small class="text-red-500" *ngIf="generalConfigForm.controls.chartName.dirty && generalConfigForm.controls.chartName.errors?.required">{{tranService.translate("global.message.required")}}</small>
                                <small class="text-red-500" *ngIf="generalConfigForm.controls.chartName.errors?.maxLength">{{tranService.translate("global.message.maxLength",{len:255})}}</small>
                                <small class="text-red-500" *ngIf="generalConfigForm.controls.chartName.errors?.pattern">{{tranService.translate("global.message.formatContainVN")}}</small>
                                <small class="text-red-500" *ngIf="isChartExisted">{{tranService.translate("global.message.exists",{type: tranService.translate("chart.label.chartName").toLowerCase()})}}</small>
                            </div>
                        </div>

                        <!-- description -->
                        <div class="w-full field grid">
                            <label htmlFor="description" class="col-fixed" style="width:180px;height: fit-content;">{{tranService.translate("chart.label.description")}}</label>
                            <div class="col">
                                <textarea class="w-full"
                                        pInputText id="description"
                                        [(ngModel)]="dynamicConfig.description"
                                        formControlName="description"
                                        rows="10"
                                        [placeholder]="tranService.translate('chart.label.description')"
                                        maxlength="500"
                                ></textarea>
                            </div>
                        </div>
                    </div>
                    <div style="width: 49%;">
                        <!-- chartType -->
                        <div class="w-full field grid">
                            <label htmlFor="chartType" class="col-fixed" style="width:180px">{{tranService.translate("chart.label.chartType")}}<span class="text-red-500">*</span></label>
                            <div class="col">
                                <p-dropdown styleClass="w-full"
                                        [showClear]="false"
                                        id="chartType" [autoDisplayFirst]="false"
                                        [(ngModel)]="dynamicConfig.type"
                                        [required]="true"
                                        formControlName="chartType"
                                        [options]="typeCharts"
                                        optionLabel="name"
                                        optionValue="value"
                                        [placeholder]="tranService.translate('chart.label.chartType')"
                                        (ngModelChange)="changeChartType()"
                                ></p-dropdown>
                            </div>
                        </div>
                        <!-- error chartType -->
                        <div class="w-full field grid text-error-field">
                            <label htmlFor="chartName" class="col-fixed" style="width:180px"></label>
                            <div class="col">
                                <small class="text-red-500" *ngIf="generalConfigForm.controls.chartType.dirty && generalConfigForm.controls.chartType.errors?.required">{{tranService.translate("global.message.required")}}</small>
                            </div>
                        </div>
                        <!-- chartSubType -->
                        <div class="w-full field grid">
                            <label htmlFor="chartSubType" class="col-fixed" style="width:180px">{{tranService.translate("chart.label.chartSubType")}}</label>
                            <div class="col">
                                <p-multiSelect styleClass="w-full"
                                        [showClear]="true"
                                        id="chartSubType"
                                        [(ngModel)]="dynamicConfig.subTypes"
                                        formControlName="chartSubType"
                                        [options]="subTypeCharts"
                                        optionLabel="name"
                                        optionValue="value"
                                        [placeholder]="tranService.translate('chart.label.chartSubType')"
                                        (ngModelChange)="changeChartSubType()"
                                ></p-multiSelect>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </p-panel>
        <!-- dataConfig -->
        <p-panel [toggleable]="true" [header]="tranService.translate('chart.label.dataConfig')" styleClass="mt-3">
            <!-- query -->
            <div class="w-full field grid">
                <label htmlFor="query" class="col-fixed" style="width:180px;height: fit-content;">{{tranService.translate("chart.label.query")}}</label>
                <div class="col">
                    <textarea class="w-full"
                            pInputText id="query"
                            [(ngModel)]="dynamicConfig.query"
                            rows="10"
                            [placeholder]="tranService.translate('chart.label.query')"
                    ></textarea>
                </div>
            </div>
            <!-- typeQuery -->
            <div class="w-full field grid">
                <label htmlFor="query" class="col-fixed" style="width:180px">{{tranService.translate("chart.label.typeQuery")}}</label>
                <div class="col">
                    <p-dropdown styleClass="w-full"
                            [showClear]="false"
                            id="typeQuery" [autoDisplayFirst]="false"
                            [(ngModel)]="dynamicConfig.typeQuery"
                            [options]="typeQueries"
                            optionLabel="name"
                            optionValue="value"
                            [placeholder]="tranService.translate('chart.label.typeQuery')"
                    ></p-dropdown>
                </div>
            </div>
            <!-- schema -->
            <div class="w-full field grid">
                <label for="schema" class="col-fixed" style="width:180px">{{tranService.translate("report.label.schema")}}</label>
                <div class="col">
                    <p-dropdown styleClass="w-full"
                            [showClear]="false"
                            id="schema" [autoDisplayFirst]="false"
                            [(ngModel)]="dynamicConfig.schema"
                            [options]="schemas"
                            optionLabel="name"
                            optionValue="value"
                            [placeholder]="tranService.translate('report.text.selectSchema')"
                    ></p-dropdown>
                </div>
            </div>
            <span style="vertical-align: 8px;">Danh sách mẫu dữ liệu &nbsp;</span>
            <p-button [label]="tranService.translate('global.button.add2')"
                            styleClass="p-button-info ml-2 w-auto"
                            [disabled]="!dynamicConfig.type"
                            (click)="prepareDataConfig()"
            ></p-button>
            <table-vnpt
                [fieldId]="'id'"
                [columns]="dataConfigTableColumns"
                [dataSet]="dataConfigContentTable"
                [options]="dataConfigTableOption"
                scrollHeight="300px"
            ></table-vnpt>
        </p-panel>
        <!-- chartConfig -->
        <p-panel [toggleable]="true" [header]="tranService.translate('chart.label.chartConfig')" styleClass="mt-3">
            <p-tabView>
                <p-tabPanel header="Tooltip" *ngIf="tooltipConfigForm">
                    <form [formGroup]="tooltipConfigForm">
                        <div class="flex flex-row justify-content-between">
                            <div style="width: 33%;">
                                <!-- mode -->
                                <div class="w-full field grid">
                                    <label htmlFor="query" class="col-fixed" style="width:150px">Mode Tooltip</label>
                                    <div class="col">
                                        <p-dropdown styleClass="w-full"
                                                [showClear]="false"
                                                id="modeTootip" [autoDisplayFirst]="false"
                                                [(ngModel)]="dynamicConfig.optionConfigEntity.tooltip.mode"
                                                formControlName="mode"
                                                [options]="tooltipModes"
                                                optionLabel="name"
                                                optionValue="value"
                                                [placeholder]="'Mode Tooltip'"
                                        ></p-dropdown>
                                    </div>
                                </div>
                                <!-- intersect -->
                                <div class="w-full field grid">
                                    <label htmlFor="query" class="col-fixed" style="width:150px">Intersect</label>
                                    <div class="col">
                                        <p-checkbox [(ngModel)]="dynamicConfig.optionConfigEntity.tooltip.intersect" [binary]="true" formControlName="intersect" inputId="intersect"></p-checkbox>
                                    </div>
                                </div>
                                <!-- backgroundColor -->
                                <div class="w-full field grid">
                                    <label htmlFor="query" class="col-fixed" style="width:150px">Background Color</label>
                                    <div class="col flex flex-row jutify-content-start align-items-center">
                                        <div *ngIf="dynamicConfig.optionConfigEntity.tooltip.backgroundColor"
                                            [style]="{backgroundColor: dynamicConfig.optionConfigEntity.tooltip.backgroundColor, width: '24px', height: '24px'}" class="mr-2"
                                            [pTooltip]="tranService.translate('global.button.view')"
                                            (click)="openChooseColorBox(null, null, 'dynamicConfig.optionConfigEntity.tooltip.backgroundColor')"
                                        ></div>
                                        <p-button [label]="tranService.translate('global.button.add2')" *ngIf="!dynamicConfig.optionConfigEntity.tooltip.backgroundColor"
                                            styleClass="p-button-secondary p-button-outlined ml-2 w-12"
                                            (click)="openChooseColorBox(null, null, 'dynamicConfig.optionConfigEntity.tooltip.backgroundColor')"></p-button>
                                        <p-button [label]="tranService.translate('global.button.delete')" *ngIf="dynamicConfig.optionConfigEntity.tooltip.backgroundColor"
                                            styleClass="p-button-secondary p-button-outlined ml-2 w-12"
                                            (click)="colorConfig.color = null;saveColor()"></p-button>
                                    </div>
                                </div>
                                <!-- bodyColor -->
                                <div class="w-full field grid">
                                    <label htmlFor="query" class="col-fixed" style="width:150px">Body Color</label>
                                    <div class="col flex flex-row jutify-content-start align-items-center">
                                        <div *ngIf="dynamicConfig.optionConfigEntity.tooltip.bodyColor"
                                            [style]="{backgroundColor: dynamicConfig.optionConfigEntity.tooltip.bodyColor, width: '24px', height: '24px'}" class="mr-2"
                                            [pTooltip]="tranService.translate('global.button.view')"
                                            (click)="openChooseColorBox(null, null, 'dynamicConfig.optionConfigEntity.tooltip.bodyColor')"
                                        ></div>
                                        <p-button [label]="tranService.translate('global.button.add2')" *ngIf="!dynamicConfig.optionConfigEntity.tooltip.bodyColor"
                                            styleClass="p-button-secondary p-button-outlined ml-2 w-12"
                                            (click)="openChooseColorBox(null, null, 'dynamicConfig.optionConfigEntity.tooltip.bodyColor')"></p-button>
                                        <p-button [label]="tranService.translate('global.button.delete')" *ngIf="dynamicConfig.optionConfigEntity.tooltip.bodyColor"
                                            styleClass="p-button-secondary p-button-outlined ml-2 w-12"
                                            (click)="colorConfig.color = null;saveColor()"></p-button>
                                    </div>
                                </div>
                                <!-- bodyAlign -->
                                <div class="w-full field grid">
                                    <label htmlFor="query" class="col-fixed" style="width:150px">Body Align</label>
                                    <div class="col">
                                        <p-dropdown styleClass="w-full"
                                                [showClear]="false"
                                                id="bodyAlign" [autoDisplayFirst]="false"
                                                [(ngModel)]="dynamicConfig.optionConfigEntity.tooltip.bodyAlign"
                                                formControlName="bodyAlign"
                                                [options]="titleAligns"
                                                optionLabel="name"
                                                optionValue="value"
                                                [placeholder]="'Body Align'"
                                        ></p-dropdown>
                                    </div>
                                </div>
                                <!-- bodyFont -->
                                <div class="w-full field grid">
                                    <label htmlFor="query" class="col-fixed" style="width:150px">Body Font</label>
                                    <div class="col flex flex-row jutify-content-start align-items-center">
                                        <div *ngIf="dynamicConfig.optionConfigEntity.tooltip.bodyFont"
                                            [style]="{fontSize: dynamicConfig.optionConfigEntity.tooltip.bodyFont.size + 'px',
                                                            fontFamily: dynamicConfig.optionConfigEntity.tooltip.bodyFont.family,
                                                            lineHeight: dynamicConfig.optionConfigEntity.tooltip.bodyFont.lineHeight,
                                                            fontStyle: dynamicConfig.optionConfigEntity.tooltip.bodyFont.style,
                                                            fontWeight: dynamicConfig.optionConfigEntity.tooltip.bodyFont.weight
                                                        }" class="mr-2"
                                            (click)="openConfigFontBox('dynamicConfig.optionConfigEntity.tooltip.bodyFont')"
                                        >Example</div>
                                        <p-button [label]="tranService.translate('global.button.add2')" *ngIf="!dynamicConfig.optionConfigEntity.tooltip.bodyFont"
                                            styleClass="p-button-secondary p-button-outlined ml-2 w-12"
                                            (click)="openConfigFontBox('dynamicConfig.optionConfigEntity.tooltip.bodyFont')"></p-button>
                                        <p-button [label]="tranService.translate('global.button.delete')" *ngIf="dynamicConfig.optionConfigEntity.tooltip.bodyFont"
                                            styleClass="p-button-secondary p-button-outlined ml-2 w-12"
                                            (click)="dynamicConfig.optionConfigEntity.tooltip.bodyFont = null"></p-button>
                                    </div>
                                </div>
                                <!-- borderColor -->
                                <div class="w-full field grid">
                                    <label htmlFor="query" class="col-fixed" style="width:150px">Border Color</label>
                                    <div class="col flex flex-row jutify-content-start align-items-center">
                                        <div *ngIf="dynamicConfig.optionConfigEntity.tooltip.borderColor"
                                            [style]="{backgroundColor: dynamicConfig.optionConfigEntity.tooltip.borderColor, width: '24px', height: '24px'}" class="mr-2"
                                            [pTooltip]="tranService.translate('global.button.view')"
                                            (click)="openChooseColorBox(null, null, 'dynamicConfig.optionConfigEntity.tooltip.borderColor')"
                                        ></div>
                                        <p-button [label]="tranService.translate('global.button.add2')" *ngIf="!dynamicConfig.optionConfigEntity.tooltip.borderColor"
                                            styleClass="p-button-secondary p-button-outlined ml-2 w-12"
                                            (click)="openChooseColorBox(null, null, 'dynamicConfig.optionConfigEntity.tooltip.borderColor')"></p-button>
                                        <p-button [label]="tranService.translate('global.button.delete')" *ngIf="dynamicConfig.optionConfigEntity.tooltip.borderColor"
                                            styleClass="p-button-secondary p-button-outlined ml-2 w-12"
                                            (click)="colorConfig.color = null;saveColor()"></p-button>
                                    </div>
                                </div>
                            </div>
                            <div style="width: 33%;">
                                <!-- border width -->
                                <div class="w-full field grid">
                                    <label htmlFor="borderWidth" class="col-fixed" style="width:150px">Border Width</label>
                                    <div class="col flex flex-row justify-content-start align-items-center" style="max-width:calc(100% - 150px)">
                                        <p-inputNumber class="flex-1"  [(ngModel)]="dynamicConfig.optionConfigEntity.tooltip.borderWidth" inputId="minmax" mode="decimal" [min]="0"
                                            formControlName="borderWidth" suffix=" px"
                                        > </p-inputNumber>
                                    </div>
                                </div>
                                <!-- bodySpacing -->
                                <div class="w-full field grid">
                                    <label htmlFor="bodySpacing" class="col-fixed" style="width:150px">Body Spacing</label>
                                    <div class="col flex flex-row justify-content-start align-items-center" style="max-width:calc(100% - 150px)">
                                        <p-inputNumber class="flex-1"  [(ngModel)]="dynamicConfig.optionConfigEntity.tooltip.bodySpacing" inputId="minmax" mode="decimal" [min]="0"
                                            formControlName="bodySpacing" suffix=" px"
                                        > </p-inputNumber>
                                    </div>
                                </div>
                                <!-- boxHeight -->
                                <div class="w-full field grid">
                                    <label htmlFor="boxHeight" class="col-fixed" style="width:150px">Box Height</label>
                                    <div class="col flex flex-row justify-content-start align-items-center" style="max-width:calc(100% - 150px)">
                                        <p-inputNumber class="flex-1"  [(ngModel)]="dynamicConfig.optionConfigEntity.tooltip.boxHeight" inputId="minmax" mode="decimal" [min]="0"
                                            formControlName="boxHeight" suffix=" px"
                                        > </p-inputNumber>
                                    </div>
                                </div>
                                <!-- boxWidth -->
                                <div class="w-full field grid">
                                    <label htmlFor="boxWidth" class="col-fixed" style="width:150px">Box Width</label>
                                    <div class="col flex flex-row justify-content-start align-items-center" style="max-width:calc(100% - 150px)">
                                        <p-inputNumber class="flex-1"  [(ngModel)]="dynamicConfig.optionConfigEntity.tooltip.boxWidth" inputId="minmax" mode="decimal" [min]="0"
                                            formControlName="boxWidth" suffix=" px"
                                        > </p-inputNumber>
                                    </div>
                                </div>
                                <!-- radius -->
                                <div class="w-full field grid">
                                    <label htmlFor="radius" class="col-fixed" style="width:150px">Radius</label>
                                    <div class="col flex flex-row justify-content-start align-items-center" style="max-width:calc(100% - 150px)">
                                        <p-inputNumber class="flex-1"  [(ngModel)]="dynamicConfig.optionConfigEntity.tooltip.radius" inputId="minmax" mode="decimal" [min]="0"
                                            formControlName="radius" suffix=" px"
                                        > </p-inputNumber>
                                    </div>
                                </div>
                                <!-- caretSize -->
                                <div class="w-full field grid">
                                    <label htmlFor="caretSize" class="col-fixed" style="width:150px">CaretSize</label>
                                    <div class="col flex flex-row justify-content-start align-items-center" style="max-width:calc(100% - 150px)">
                                        <p-inputNumber class="flex-1"  [(ngModel)]="dynamicConfig.optionConfigEntity.tooltip.caretSize" inputId="minmax" mode="decimal" [min]="0"
                                            formControlName="caretSize" suffix=" px"
                                        > </p-inputNumber>
                                    </div>
                                </div>
                                <!-- titleAlign -->
                                <div class="w-full field grid">
                                    <label htmlFor="titleAlign" class="col-fixed" style="width:150px">Title Align</label>
                                    <div class="col">
                                        <p-dropdown styleClass="w-full"
                                                [showClear]="false"
                                                id="titleAlign" [autoDisplayFirst]="false"
                                                [(ngModel)]="dynamicConfig.optionConfigEntity.tooltip.titleAlign"
                                                formControlName="titleAlign"
                                                [options]="titleAligns"
                                                optionLabel="name"
                                                optionValue="value"
                                                [placeholder]="'Title Align'"
                                        ></p-dropdown>
                                    </div>
                                </div>
                            </div>
                            <div style="width: 33%;">
                                <!-- titleFont -->
                                <div class="w-full field grid">
                                    <label htmlFor="titleFont" class="col-fixed" style="width:150px">Title Font</label>
                                    <div class="col flex flex-row jutify-content-start align-items-center">
                                        <div *ngIf="dynamicConfig.optionConfigEntity.tooltip.titleFont"
                                            [style]="{fontSize: dynamicConfig.optionConfigEntity.tooltip.titleFont.size + 'px',
                                                            fontFamily: dynamicConfig.optionConfigEntity.tooltip.titleFont.family,
                                                            lineHeight: dynamicConfig.optionConfigEntity.tooltip.titleFont.lineHeight,
                                                            fontStyle: dynamicConfig.optionConfigEntity.tooltip.titleFont.style,
                                                            fontWeight: dynamicConfig.optionConfigEntity.tooltip.titleFont.weight
                                                        }" class="mr-2"
                                            (click)="openConfigFontBox('dynamicConfig.optionConfigEntity.tooltip.titleFont')"
                                        >Example</div>
                                        <p-button [label]="tranService.translate('global.button.add2')" *ngIf="!dynamicConfig.optionConfigEntity.tooltip.titleFont"
                                            styleClass="p-button-secondary p-button-outlined ml-2 w-12"
                                            (click)="openConfigFontBox('dynamicConfig.optionConfigEntity.tooltip.titleFont')"></p-button>
                                        <p-button [label]="tranService.translate('global.button.delete')" *ngIf="dynamicConfig.optionConfigEntity.tooltip.titleFont"
                                            styleClass="p-button-secondary p-button-outlined ml-2 w-12"
                                            (click)="dynamicConfig.optionConfigEntity.tooltip.titleFont = null"></p-button>
                                    </div>
                                </div>
                                <!-- titleColor -->
                                <div class="w-full field grid">
                                    <label htmlFor="query" class="col-fixed" style="width:150px">Title Color</label>
                                    <div class="col flex flex-row jutify-content-start align-items-center">
                                        <div *ngIf="dynamicConfig.optionConfigEntity.tooltip.titleColor"
                                            [style]="{backgroundColor: dynamicConfig.optionConfigEntity.tooltip.titleColor, width: '24px', height: '24px'}" class="mr-2"
                                            [pTooltip]="tranService.translate('global.button.view')"
                                            (click)="openChooseColorBox(null, null, 'dynamicConfig.optionConfigEntity.tooltip.titleColor')"
                                        ></div>
                                        <p-button [label]="tranService.translate('global.button.add2')" *ngIf="!dynamicConfig.optionConfigEntity.tooltip.titleColor"
                                            styleClass="p-button-secondary p-button-outlined ml-2 w-12"
                                            (click)="openChooseColorBox(null, null, 'dynamicConfig.optionConfigEntity.tooltip.titleColor')"></p-button>
                                        <p-button [label]="tranService.translate('global.button.delete')" *ngIf="dynamicConfig.optionConfigEntity.tooltip.titleColor"
                                            styleClass="p-button-secondary p-button-outlined ml-2 w-12"
                                            (click)="colorConfig.color = null;saveColor()"></p-button>
                                    </div>
                                </div>
                                <!-- footerColor -->
                                <div class="w-full field grid">
                                    <label htmlFor="query" class="col-fixed" style="width:150px">Footer Color</label>
                                    <div class="col flex flex-row jutify-content-start align-items-center">
                                        <div *ngIf="dynamicConfig.optionConfigEntity.tooltip.footerColor"
                                            [style]="{backgroundColor: dynamicConfig.optionConfigEntity.tooltip.footerColor, width: '24px', height: '24px'}" class="mr-2"
                                            [pTooltip]="tranService.translate('global.button.view')"
                                            (click)="openChooseColorBox(null, null, 'dynamicConfig.optionConfigEntity.tooltip.footerColor')"
                                        ></div>
                                        <p-button [label]="tranService.translate('global.button.add2')" *ngIf="!dynamicConfig.optionConfigEntity.tooltip.footerColor"
                                            styleClass="p-button-secondary p-button-outlined ml-2 w-12"
                                            (click)="openChooseColorBox(null, null, 'dynamicConfig.optionConfigEntity.tooltip.footerColor')"></p-button>
                                        <p-button [label]="tranService.translate('global.button.delete')" *ngIf="dynamicConfig.optionConfigEntity.tooltip.footerColor"
                                            styleClass="p-button-secondary p-button-outlined ml-2 w-12"
                                            (click)="colorConfig.color = null;saveColor()"></p-button>
                                    </div>
                                </div>
                                <!-- patternTitle -->
                                <div class="w-full field grid">
                                    <label htmlFor="titleAlign" class="col-fixed" style="width:150px; height: fit-content;">Pattern Title</label>
                                    <div class="col">
                                        <input class="w-full" type="text" pInputText [(ngModel)]="dynamicConfig.optionConfigEntity.tooltip.patternTitle" formControlName="patternTitle"/>
                                        <div class="flex flex-row justify-content-start align-items-center mt-2">
                                            <p-dropdown styleClass="w-full"
                                                [showClear]="false"
                                                id="titleAlign" [autoDisplayFirst]="false"
                                                [(ngModel)]="calculatorFunctionTitle"
                                                [options]="actionTooltipCalculator"
                                                formControlName="calculatorFunctionTitle"
                                                optionLabel="name"
                                                optionValue="value"
                                                [placeholder]="'Calculator Function'" class="w-8"
                                            ></p-dropdown>
                                            <p-button [label]="tranService.translate('global.button.add2')"
                                                        styleClass="p-button-secondary p-button-outlined ml-2"
                                                            (click)="dynamicConfig.optionConfigEntity.tooltip.patternTitle = (dynamicConfig.optionConfigEntity.tooltip.patternTitle || '') + (calculatorFunctionTitle || '')"></p-button>
                                        </div>
                                    </div>
                                </div>
                                <!-- patternLabel -->
                                <div class="w-full field grid">
                                    <label htmlFor="titleAlign" class="col-fixed" style="width:150px; height: fit-content;">Pattern Label</label>
                                    <div class="col">
                                        <input class="w-full" type="text" pInputText [(ngModel)]="dynamicConfig.optionConfigEntity.tooltip.patternLabel" formControlName="patternLabel"/>
                                        <div class="flex flex-row justify-content-start align-items-center mt-2">
                                            <p-dropdown styleClass="w-full"
                                                [showClear]="false"
                                                id="titleAlign" [autoDisplayFirst]="false"
                                                [(ngModel)]="calculatorFunctionLabel"
                                                [options]="actionTooltipCalculator"
                                                formControlName="calculatorFunctionLabel"
                                                optionLabel="name"
                                                optionValue="value"
                                                [placeholder]="'Calculator Function'" class="w-8"
                                            ></p-dropdown>
                                            <p-button [label]="tranService.translate('global.button.add2')"
                                                        styleClass="p-button-secondary p-button-outlined ml-2"
                                                            (click)="dynamicConfig.optionConfigEntity.tooltip.patternLabel = (dynamicConfig.optionConfigEntity.tooltip.patternLabel || '') + (calculatorFunctionLabel || '')"></p-button>
                                        </div>
                                    </div>
                                </div>
                                <!-- patternFooter -->
                                <div class="w-full field grid">
                                    <label htmlFor="titleAlign" class="col-fixed" style="width:150px; height: fit-content;">Pattern Footer</label>
                                    <div class="col">
                                        <input class="w-full" type="text" pInputText [(ngModel)]="dynamicConfig.optionConfigEntity.tooltip.patternFooter" formControlName="patternFooter"/>
                                        <div class="flex flex-row justify-content-start align-items-center mt-2">
                                            <p-dropdown styleClass="w-full"
                                                [showClear]="false"
                                                id="titleAlign" [autoDisplayFirst]="false"
                                                [(ngModel)]="calculatorFunctionFooter"
                                                [options]="actionTooltipCalculator"
                                                formControlName="calculatorFunctionFooter"
                                                optionLabel="name"
                                                optionValue="value"
                                                [placeholder]="'Calculator Function'" class="w-8"
                                            ></p-dropdown>
                                            <p-button [label]="tranService.translate('global.button.add2')"
                                                        styleClass="p-button-secondary p-button-outlined ml-2"
                                                            (click)="dynamicConfig.optionConfigEntity.tooltip.patternFooter = (dynamicConfig.optionConfigEntity.tooltip.patternFooter || '') + (calculatorFunctionFooter || '')"></p-button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </p-tabPanel>
                <p-tabPanel header="Legend" *ngIf="legendConfigForm">
                    <form [formGroup]="legendConfigForm">
                        <div class="flex flex-row justify-content-between">
                            <div style="width: 49%;">
                                <!-- titleDisplay -->
                                <div class="w-full field grid">
                                    <label htmlFor="query" class="col-fixed" style="width:150px">Title Display</label>
                                    <div class="col">
                                        <p-checkbox [(ngModel)]="dynamicConfig.optionConfigEntity.legend.titleDisplay" [binary]="true" formControlName="titleDisplay" inputId="titleDisplay"></p-checkbox>
                                    </div>
                                </div>
                                <!-- titleFont -->
                                <div class="w-full field grid">
                                    <label htmlFor="query" class="col-fixed" style="width:150px">Title Font</label>
                                    <div class="col flex flex-row jutify-content-start align-items-center">
                                        <div *ngIf="dynamicConfig.optionConfigEntity.legend.titleFont"
                                            [style]="{fontSize: dynamicConfig.optionConfigEntity.legend.titleFont.size + 'px',
                                                            fontFamily: dynamicConfig.optionConfigEntity.legend.titleFont.family,
                                                            lineHeight: dynamicConfig.optionConfigEntity.legend.titleFont.lineHeight,
                                                            fontStyle: dynamicConfig.optionConfigEntity.legend.titleFont.style,
                                                            fontWeight: dynamicConfig.optionConfigEntity.legend.titleFont.weight
                                                        }" class="mr-2"
                                            (click)="openConfigFontBox('dynamicConfig.optionConfigEntity.legend.titleFont')"
                                        >Example</div>
                                        <p-button [label]="tranService.translate('global.button.add2')" *ngIf="!dynamicConfig.optionConfigEntity.legend.titleFont"
                                            styleClass="p-button-secondary p-button-outlined ml-2 w-12"
                                            (click)="openConfigFontBox('dynamicConfig.optionConfigEntity.legend.titleFont')"></p-button>
                                        <p-button [label]="tranService.translate('global.button.delete')" *ngIf="dynamicConfig.optionConfigEntity.legend.titleFont"
                                            styleClass="p-button-secondary p-button-outlined ml-2 w-12"
                                            (click)="dynamicConfig.optionConfigEntity.legend.titleFont = null"></p-button>
                                    </div>
                                </div>
                                <!-- titleColor -->
                                <div class="w-full field grid">
                                    <label htmlFor="query" class="col-fixed" style="width:150px">Title Color</label>
                                    <div class="col flex flex-row jutify-content-start align-items-center">
                                        <div *ngIf="dynamicConfig.optionConfigEntity.legend.titleColor"
                                            [style]="{backgroundColor: dynamicConfig.optionConfigEntity.legend.titleColor, width: '24px', height: '24px'}" class="mr-2"
                                            [pTooltip]="tranService.translate('global.button.view')"
                                            (click)="openChooseColorBox(null, null, 'dynamicConfig.optionConfigEntity.legend.titleColor')"
                                        ></div>
                                        <p-button [label]="tranService.translate('global.button.add2')" *ngIf="!dynamicConfig.optionConfigEntity.legend.titleColor"
                                            styleClass="p-button-secondary p-button-outlined ml-2 w-12"
                                            (click)="openChooseColorBox(null, null, 'dynamicConfig.optionConfigEntity.legend.titleColor')"></p-button>
                                        <p-button [label]="tranService.translate('global.button.delete')" *ngIf="dynamicConfig.optionConfigEntity.legend.titleColor"
                                            styleClass="p-button-secondary p-button-outlined ml-2 w-12"
                                            (click)="colorConfig.color = null;saveColor()"></p-button>
                                    </div>
                                </div>
                                <!-- titleText -->
                                <div class="w-full field grid">
                                    <label htmlFor="titleText" class="col-fixed" style="width:150px;">Title Text</label>
                                    <div class="col">
                                        <input class="w-full" type="text" pInputText [(ngModel)]="dynamicConfig.optionConfigEntity.legend.titleText" formControlName="titleText"
                                        placeholder="Title Text"/>
                                    </div>
                                </div>
                                <!-- align -->
                                <div class="w-full field grid">
                                    <label htmlFor="align" class="col-fixed" style="width:150px;">Align</label>
                                    <div class="col">
                                        <p-dropdown styleClass="w-full"
                                            [showClear]="false"
                                            id="titleAlign" [autoDisplayFirst]="false"
                                            [(ngModel)]="dynamicConfig.optionConfigEntity.legend.align"
                                            [options]="aligns"
                                            formControlName="align"
                                            optionLabel="name"
                                            optionValue="value"
                                            [placeholder]="'Align'"
                                        ></p-dropdown>
                                    </div>
                                </div>
                                <!-- pattern body -->
                                <div class="w-full field grid">
                                    <label htmlFor="titleAlign" class="col-fixed" style="width:150px; height: fit-content;">Pattern Body</label>
                                    <div class="col">
                                        <input class="w-full" type="text" pInputText [(ngModel)]="dynamicConfig.optionConfigEntity.legend.patternBody" formControlName="patternBody"/>
                                        <div class="flex flex-row justify-content-start align-items-center mt-2">
                                            <p-dropdown styleClass="w-full"
                                                [showClear]="false"
                                                id="titleAlign" [autoDisplayFirst]="false"
                                                [(ngModel)]="calculatorFunctionLegendBody"
                                                [options]="actionLegendCalculator"
                                                formControlName="calculatorFunctionLegendBody"
                                                optionLabel="name"
                                                optionValue="value"
                                                [placeholder]="'Calculator Function'" class="w-8"
                                            ></p-dropdown>
                                            <p-button [label]="tranService.translate('global.button.add2')"
                                                        styleClass="p-button-secondary p-button-outlined ml-2"
                                                            (click)="dynamicConfig.optionConfigEntity.legend.patternBody = (dynamicConfig.optionConfigEntity.legend.patternBody || '') + (calculatorFunctionLegendBody || '')"></p-button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div style="width: 49%;">
                                <!-- display -->
                                <div class="w-full field grid">
                                    <label htmlFor="query" class="col-fixed" style="width:150px">Display</label>
                                    <div class="col">
                                        <p-checkbox [(ngModel)]="dynamicConfig.optionConfigEntity.legend.display" [binary]="true" formControlName="display" inputId="display"></p-checkbox>
                                    </div>
                                </div>
                                <!-- labelFont -->
                                <div class="w-full field grid">
                                    <label htmlFor="labelFont" class="col-fixed" style="width:150px">Label Font</label>
                                    <div class="col flex flex-row jutify-content-start align-items-center">
                                        <div *ngIf="dynamicConfig.optionConfigEntity.legend.labelFont"
                                            [style]="{fontSize: dynamicConfig.optionConfigEntity.legend.labelFont.size + 'px',
                                                            fontFamily: dynamicConfig.optionConfigEntity.legend.labelFont.family,
                                                            lineHeight: dynamicConfig.optionConfigEntity.legend.labelFont.lineHeight,
                                                            fontStyle: dynamicConfig.optionConfigEntity.legend.labelFont.style,
                                                            fontWeight: dynamicConfig.optionConfigEntity.legend.labelFont.weight
                                                        }" class="mr-2"
                                            (click)="openConfigFontBox('dynamicConfig.optionConfigEntity.legend.labelFont')"
                                        >Example</div>
                                        <p-button [label]="tranService.translate('global.button.add2')" *ngIf="!dynamicConfig.optionConfigEntity.legend.labelFont"
                                            styleClass="p-button-secondary p-button-outlined ml-2 w-12"
                                            (click)="openConfigFontBox('dynamicConfig.optionConfigEntity.legend.labelFont')"></p-button>
                                        <p-button [label]="tranService.translate('global.button.delete')" *ngIf="dynamicConfig.optionConfigEntity.legend.labelFont"
                                            styleClass="p-button-secondary p-button-outlined ml-2 w-12"
                                            (click)="dynamicConfig.optionConfigEntity.legend.labelFont = null"></p-button>
                                    </div>
                                </div>
                                <!-- labelColor -->
                                <div class="w-full field grid">
                                    <label htmlFor="labelColor" class="col-fixed" style="width:150px">Label Color</label>
                                    <div class="col flex flex-row jutify-content-start align-items-center">
                                        <div *ngIf="dynamicConfig.optionConfigEntity.legend.labelColor"
                                            [style]="{backgroundColor: dynamicConfig.optionConfigEntity.legend.labelColor, width: '24px', height: '24px'}" class="mr-2"
                                            [pTooltip]="tranService.translate('global.button.view')"
                                            (click)="openChooseColorBox(null, null, 'dynamicConfig.optionConfigEntity.legend.labelColor')"
                                        ></div>
                                        <p-button [label]="tranService.translate('global.button.add2')" *ngIf="!dynamicConfig.optionConfigEntity.legend.labelColor"
                                            styleClass="p-button-secondary p-button-outlined ml-2 w-12"
                                            (click)="openChooseColorBox(null, null, 'dynamicConfig.optionConfigEntity.legend.labelColor')"></p-button>
                                        <p-button [label]="tranService.translate('global.button.delete')" *ngIf="dynamicConfig.optionConfigEntity.legend.labelColor"
                                            styleClass="p-button-secondary p-button-outlined ml-2 w-12"
                                            (click)="colorConfig.color = null;saveColor()"></p-button>
                                    </div>
                                </div>
                                <!-- boxHeight -->
                                <div class="w-full field grid">
                                    <label htmlFor="boxHeight" class="col-fixed" style="width:150px">Box Height</label>
                                    <div class="col flex flex-row justify-content-start align-items-center" style="max-width:calc(100% - 150px)">
                                        <p-inputNumber class="flex-1"  [(ngModel)]="dynamicConfig.optionConfigEntity.legend.boxHeight" inputId="minmax" mode="decimal" [min]="0"
                                            formControlName="boxHeight" suffix=" px"
                                        > </p-inputNumber>
                                    </div>
                                </div>
                                <!-- boxWidth -->
                                <div class="w-full field grid">
                                    <label htmlFor="boxWidth" class="col-fixed" style="width:150px">Box Width</label>
                                    <div class="col flex flex-row justify-content-start align-items-center" style="max-width:calc(100% - 150px)">
                                        <p-inputNumber class="flex-1"  [(ngModel)]="dynamicConfig.optionConfigEntity.legend.boxWidth" inputId="minmax" mode="decimal" [min]="0"
                                            formControlName="boxWidth" suffix=" px"
                                        > </p-inputNumber>
                                    </div>
                                </div>
                                <!-- position -->
                                <div class="w-full field grid">
                                    <label htmlFor="position" class="col-fixed" style="width:150px;">Position</label>
                                    <div class="col">
                                        <p-dropdown styleClass="w-full"
                                            [showClear]="false"
                                            id="position" [autoDisplayFirst]="false"
                                            [(ngModel)]="dynamicConfig.optionConfigEntity.legend.position"
                                            [options]="layoutPositions"
                                            formControlName="position"
                                            optionLabel="name"
                                            optionValue="value"
                                            [placeholder]="'Position'"
                                        ></p-dropdown>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </p-tabPanel>
                <p-tabPanel header="Title/SubTitle" *ngIf="titleConfigForm && subTitleConfigForm">
                    <div class="flex flex-row justify-content-between">
                        <div style="width: 49%;">
                            <p-divider styleClass="flex" [align]="'center'"><b>TITLE</b></p-divider>
                            <form [formGroup]="titleConfigForm">
                                <!-- display -->
                                <div class="w-full field grid">
                                    <label htmlFor="query" class="col-fixed" style="width:150px">Display</label>
                                    <div class="col">
                                        <p-checkbox [(ngModel)]="dynamicConfig.optionConfigEntity.title.display" [binary]="true" formControlName="display" inputId="display"></p-checkbox>
                                    </div>
                                </div>
                                <!-- font -->
                                <div class="w-full field grid">
                                    <label htmlFor="font" class="col-fixed" style="width:150px">Font</label>
                                    <div class="col flex flex-row jutify-content-start align-items-center">
                                        <div *ngIf="dynamicConfig.optionConfigEntity.title.font"
                                            [style]="{fontSize: dynamicConfig.optionConfigEntity.title.font.size + 'px',
                                                            fontFamily: dynamicConfig.optionConfigEntity.title.font.family,
                                                            lineHeight: dynamicConfig.optionConfigEntity.title.font.lineHeight,
                                                            fontStyle: dynamicConfig.optionConfigEntity.title.font.style,
                                                            fontWeight: dynamicConfig.optionConfigEntity.title.font.weight
                                                        }" class="mr-2"
                                            (click)="openConfigFontBox('dynamicConfig.optionConfigEntity.title.font')"
                                        >Example</div>
                                        <p-button [label]="tranService.translate('global.button.add2')" *ngIf="!dynamicConfig.optionConfigEntity.title.font"
                                            styleClass="p-button-secondary p-button-outlined ml-2 w-12"
                                            (click)="openConfigFontBox('dynamicConfig.optionConfigEntity.title.font')"></p-button>
                                        <p-button [label]="tranService.translate('global.button.delete')" *ngIf="dynamicConfig.optionConfigEntity.title.font"
                                            styleClass="p-button-secondary p-button-outlined ml-2 w-12"
                                            (click)="dynamicConfig.optionConfigEntity.title.font = null"></p-button>
                                    </div>
                                </div>
                                <!-- color -->
                                <div class="w-full field grid">
                                    <label htmlFor="color" class="col-fixed" style="width:150px">Color</label>
                                    <div class="col flex flex-row jutify-content-start align-items-center">
                                        <div *ngIf="dynamicConfig.optionConfigEntity.title.color"
                                            [style]="{backgroundColor: dynamicConfig.optionConfigEntity.title.color, width: '24px', height: '24px'}" class="mr-2"
                                            [pTooltip]="tranService.translate('global.button.view')"
                                            (click)="openChooseColorBox(null, null, 'dynamicConfig.optionConfigEntity.title.color')"
                                        ></div>
                                        <p-button [label]="tranService.translate('global.button.add2')" *ngIf="!dynamicConfig.optionConfigEntity.title.color"
                                            styleClass="p-button-secondary p-button-outlined ml-2 w-12"
                                            (click)="openChooseColorBox(null, null, 'dynamicConfig.optionConfigEntity.title.color')"></p-button>
                                        <p-button [label]="tranService.translate('global.button.delete')" *ngIf="dynamicConfig.optionConfigEntity.title.color"
                                            styleClass="p-button-secondary p-button-outlined ml-2 w-12"
                                            (click)="colorConfig.color = null;saveColor()"></p-button>
                                    </div>
                                </div>
                                <!-- text -->
                                <div class="w-full field grid">
                                    <label htmlFor="text" class="col-fixed" style="width:150px;">Text</label>
                                    <div class="col">
                                        <input class="w-full" type="text" pInputText [(ngModel)]="dynamicConfig.optionConfigEntity.title.text" formControlName="text"
                                        placeholder="Text"/>
                                    </div>
                                </div>
                                <!-- position -->
                                <div class="w-full field grid">
                                    <label htmlFor="position" class="col-fixed" style="width:150px;">Position</label>
                                    <div class="col">
                                        <p-dropdown styleClass="w-full"
                                            [showClear]="false"
                                            id="position" [autoDisplayFirst]="false"
                                            [(ngModel)]="dynamicConfig.optionConfigEntity.title.position"
                                            [options]="positionTexts"
                                            formControlName="position"
                                            optionLabel="name"
                                            optionValue="value"
                                            [placeholder]="'Position'"
                                        ></p-dropdown>
                                    </div>
                                </div>
                                <!-- align -->
                                <div class="w-full field grid">
                                    <label htmlFor="align" class="col-fixed" style="width:150px;">Align</label>
                                    <div class="col">
                                        <p-dropdown styleClass="w-full"
                                            [showClear]="false"
                                            id="align" [autoDisplayFirst]="false"
                                            [(ngModel)]="dynamicConfig.optionConfigEntity.title.align"
                                            [options]="aligns"
                                            formControlName="align"
                                            optionLabel="name"
                                            optionValue="value"
                                            [placeholder]="'Align'"
                                        ></p-dropdown>
                                    </div>
                                </div>
                            </form>
                        </div>

                        <div style="width: 49%;">
                            <p-divider styleClass="flex" [align]="'center'"><b>SUB-TITLE</b></p-divider>
                            <form [formGroup]="subTitleConfigForm">
                                <!-- display -->
                                <div class="w-full field grid">
                                    <label htmlFor="query" class="col-fixed" style="width:150px">Display</label>
                                    <div class="col">
                                        <p-checkbox [(ngModel)]="dynamicConfig.optionConfigEntity.subTitle.display" [binary]="true" formControlName="display" inputId="display"></p-checkbox>
                                    </div>
                                </div>
                                <!-- font -->
                                <div class="w-full field grid">
                                    <label htmlFor="font" class="col-fixed" style="width:150px">Font</label>
                                    <div class="col flex flex-row jutify-content-start align-items-center">
                                        <div *ngIf="dynamicConfig.optionConfigEntity.subTitle.font"
                                            [style]="{fontSize: dynamicConfig.optionConfigEntity.subTitle.font.size + 'px',
                                                            fontFamily: dynamicConfig.optionConfigEntity.subTitle.font.family,
                                                            lineHeight: dynamicConfig.optionConfigEntity.subTitle.font.lineHeight,
                                                            fontStyle: dynamicConfig.optionConfigEntity.subTitle.font.style,
                                                            fontWeight: dynamicConfig.optionConfigEntity.subTitle.font.weight
                                                        }" class="mr-2"
                                            (click)="openConfigFontBox('dynamicConfig.optionConfigEntity.subTitle.font')"
                                        >Example</div>
                                        <p-button [label]="tranService.translate('global.button.add2')" *ngIf="!dynamicConfig.optionConfigEntity.subTitle.font"
                                            styleClass="p-button-secondary p-button-outlined ml-2 w-12"
                                            (click)="openConfigFontBox('dynamicConfig.optionConfigEntity.subTitle.font')"></p-button>
                                        <p-button [label]="tranService.translate('global.button.delete')" *ngIf="dynamicConfig.optionConfigEntity.subTitle.font"
                                            styleClass="p-button-secondary p-button-outlined ml-2 w-12"
                                            (click)="dynamicConfig.optionConfigEntity.subTitle.font = null"></p-button>
                                    </div>
                                </div>
                                <!-- color -->
                                <div class="w-full field grid">
                                    <label htmlFor="color" class="col-fixed" style="width:150px">Color</label>
                                    <div class="col flex flex-row jutify-content-start align-items-center">
                                        <div *ngIf="dynamicConfig.optionConfigEntity.subTitle.color"
                                            [style]="{backgroundColor: dynamicConfig.optionConfigEntity.subTitle.color, width: '24px', height: '24px'}" class="mr-2"
                                            [pTooltip]="tranService.translate('global.button.view')"
                                            (click)="openChooseColorBox(null, null, 'dynamicConfig.optionConfigEntity.subTitle.color')"
                                        ></div>
                                        <p-button [label]="tranService.translate('global.button.add2')" *ngIf="!dynamicConfig.optionConfigEntity.subTitle.color"
                                            styleClass="p-button-secondary p-button-outlined ml-2 w-12"
                                            (click)="openChooseColorBox(null, null, 'dynamicConfig.optionConfigEntity.subTitle.color')"></p-button>
                                        <p-button [label]="tranService.translate('global.button.delete')" *ngIf="dynamicConfig.optionConfigEntity.subTitle.color"
                                            styleClass="p-button-secondary p-button-outlined ml-2 w-12"
                                            (click)="colorConfig.color = null;saveColor()"></p-button>
                                    </div>
                                </div>
                                <!-- text -->
                                <div class="w-full field grid">
                                    <label htmlFor="text" class="col-fixed" style="width:150px;">Text</label>
                                    <div class="col">
                                        <input class="w-full" type="text" pInputText [(ngModel)]="dynamicConfig.optionConfigEntity.subTitle.text" formControlName="text"
                                        placeholder="Text"/>
                                    </div>
                                </div>
                                <!-- position -->
                                <div class="w-full field grid">
                                    <label htmlFor="position" class="col-fixed" style="width:150px;">Position</label>
                                    <div class="col">
                                        <p-dropdown styleClass="w-full"
                                            [showClear]="false"
                                            id="position" [autoDisplayFirst]="false"
                                            [(ngModel)]="dynamicConfig.optionConfigEntity.subTitle.position"
                                            [options]="positionTexts"
                                            formControlName="position"
                                            optionLabel="name"
                                            optionValue="value"
                                            [placeholder]="'Position'"
                                        ></p-dropdown>
                                    </div>
                                </div>
                                <!-- align -->
                                <div class="w-full field grid">
                                    <label htmlFor="align" class="col-fixed" style="width:150px;">Align</label>
                                    <div class="col">
                                        <p-dropdown styleClass="w-full"
                                            [showClear]="false"
                                            id="align" [autoDisplayFirst]="false"
                                            [(ngModel)]="dynamicConfig.optionConfigEntity.subTitle.align"
                                            [options]="aligns"
                                            formControlName="align"
                                            optionLabel="name"
                                            optionValue="value"
                                            [placeholder]="'Align'"
                                        ></p-dropdown>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </p-tabPanel>
                <p-tabPanel header="Scales">
                    <div class="flex flex-row justify-content-start align-items-center">
                        <p-dropdown styleClass="w-full"
                            [showClear]="false"
                            id="position" [autoDisplayFirst]="false"
                            [(ngModel)]="scaleKey"
                            [options]="listScale"
                            optionLabel="name"
                            optionValue="value"
                            [placeholder]="'Scales'"
                            (ngModelChange)="changeScaleKey()"
                        ></p-dropdown>
                        <p-button [label]="tranService.translate('global.button.add2')" *ngIf="(dynamicConfig.subTypes || []).includes('multiAxis')" styleClass="p-button-info ml-2" (click)="createScaleYConfigDefault(true)"></p-button>
                    </div>
                    <div class="mt-2" *ngIf="scaleConfigForm && scaleKey">
                        <form [formGroup]="scaleConfigForm">
                            <div class="flex flex-row justify-content-between">
                                <div style="width: 33%;">
                                    <!-- stacked -->
                                    <div class="w-full field grid">
                                        <label htmlFor="stacked" class="col-fixed" style="width:150px">Stacked</label>
                                        <div class="col">
                                            <p-checkbox [(ngModel)]="scaleConfig.stacked" [binary]="true" formControlName="stacked" inputId="stacked"></p-checkbox>
                                        </div>
                                    </div>
                                    <!-- beginAtZero -->
                                    <div class="w-full field grid">
                                        <label htmlFor="beginAtZero" class="col-fixed" style="width:150px">Begin At Zero</label>
                                        <div class="col">
                                            <p-checkbox [(ngModel)]="scaleConfig.beginAtZero" [binary]="true" formControlName="beginAtZero" inputId="beginAtZero"></p-checkbox>
                                        </div>
                                    </div>
                                    <!-- position -->
                                    <div class="w-full field grid">
                                        <label htmlFor="position" class="col-fixed" style="width:150px;">Position</label>
                                        <div class="col">
                                            <p-dropdown styleClass="w-full"
                                                [showClear]="false"
                                                id="position" [autoDisplayFirst]="false"
                                                [(ngModel)]="scaleConfig.position"
                                                [options]="positionTexts"
                                                formControlName="position"
                                                optionLabel="name"
                                                optionValue="value"
                                                [placeholder]="'Position'"
                                            ></p-dropdown>
                                        </div>
                                    </div>
                                    <!-- tickColor -->
                                    <div class="w-full field grid">
                                        <label htmlFor="tickColor" class="col-fixed" style="width:150px">Tick Color</label>
                                        <div class="col flex flex-row jutify-content-start align-items-center">
                                            <div *ngIf="scaleConfig.tickColor"
                                                [style]="{backgroundColor:scaleConfig.tickColor, width: '24px', height: '24px'}" class="mr-2"
                                                [pTooltip]="tranService.translate('global.button.view')"
                                                (click)="openChooseColorBox(null, null, 'scaleConfig.tickColor')"
                                            ></div>
                                            <p-button [label]="tranService.translate('global.button.add2')" *ngIf="!scaleConfig.tickColor"
                                                styleClass="p-button-secondary p-button-outlined ml-2 w-12"
                                                (click)="openChooseColorBox(null, null, 'scaleConfig.tickColor')"></p-button>
                                            <p-button [label]="tranService.translate('global.button.delete')" *ngIf="scaleConfig.tickColor"
                                                styleClass="p-button-secondary p-button-outlined ml-2 w-12"
                                                (click)="colorConfig.color = null;saveColor()"></p-button>
                                        </div>
                                    </div>
                                    <!-- rotation -->
                                    <div class="w-full field grid">
                                        <label htmlFor="rotation" class="col-fixed" style="width:150px">Rotation</label>
                                        <div class="col flex flex-row justify-content-start align-items-center" style="max-width:calc(100% - 150px)">
                                            <p-inputNumber class="flex-1"  [(ngModel)]="scaleConfig.rotation" inputId="minmax" mode="decimal" [min]="0"
                                                formControlName="rotation"
                                            > </p-inputNumber>
                                        </div>
                                    </div>
                                </div>
                                <div style="width: 33%;">
                                    <!-- drawOnChartArea -->
                                    <div class="w-full field grid">
                                        <label htmlFor="drawOnChartArea" class="col-fixed" style="width:150px">Draw On Chart Area</label>
                                        <div class="col">
                                            <p-checkbox [(ngModel)]="scaleConfig.drawOnChartArea" [binary]="true" formControlName="drawOnChartArea" inputId="drawOnChartArea"></p-checkbox>
                                        </div>
                                    </div>
                                    <!-- colorGrid -->
                                    <div class="w-full field grid">
                                        <label htmlFor="colorGrid" class="col-fixed" style="width:150px">Color Grid</label>
                                        <div class="col flex flex-row jutify-content-start align-items-center">
                                            <div *ngIf="scaleConfig.colorGrid"
                                                [style]="{backgroundColor:scaleConfig.colorGrid, width: '24px', height: '24px'}" class="mr-2"
                                                [pTooltip]="tranService.translate('global.button.view')"
                                                (click)="openChooseColorBox(null, null, 'scaleConfig.colorGrid')"
                                            ></div>
                                            <p-button [label]="tranService.translate('global.button.add2')" *ngIf="!scaleConfig.colorGrid"
                                                styleClass="p-button-secondary p-button-outlined ml-2 w-12"
                                                (click)="openChooseColorBox(null, null, 'scaleConfig.colorGrid')"></p-button>
                                            <p-button [label]="tranService.translate('global.button.delete')" *ngIf="scaleConfig.colorGrid"
                                                styleClass="p-button-secondary p-button-outlined ml-2 w-12"
                                                (click)="colorConfig.color = null;saveColor()"></p-button>
                                        </div>
                                    </div>
                                    <!-- borderGridColor -->
                                    <div class="w-full field grid">
                                        <label htmlFor="borderGridColor" class="col-fixed" style="width:150px">Border Grid Color</label>
                                        <div class="col flex flex-row jutify-content-start align-items-center">
                                            <div *ngIf="scaleConfig.borderGridColor"
                                                [style]="{backgroundColor:scaleConfig.borderGridColor, width: '24px', height: '24px'}" class="mr-2"
                                                [pTooltip]="tranService.translate('global.button.view')"
                                                (click)="openChooseColorBox(null, null, 'scaleConfig.borderGridColor')"
                                            ></div>
                                            <p-button [label]="tranService.translate('global.button.add2')" *ngIf="!scaleConfig.borderGridColor"
                                                styleClass="p-button-secondary p-button-outlined ml-2 w-12"
                                                (click)="openChooseColorBox(null, null, 'scaleConfig.borderGridColor')"></p-button>
                                            <p-button [label]="tranService.translate('global.button.delete')" *ngIf="scaleConfig.borderGridColor"
                                                styleClass="p-button-secondary p-button-outlined ml-2 w-12"
                                                (click)="colorConfig.color = null;saveColor()"></p-button>
                                        </div>
                                    </div>
                                    <!-- isDash -->
                                    <div class="w-full field grid">
                                        <label htmlFor="isDash" class="col-fixed" style="width:150px">Grid Dash</label>
                                        <div class="col">
                                            <p-checkbox [(ngModel)]="scaleConfig.isDash" [binary]="true" formControlName="isDash" inputId="isDash"></p-checkbox>
                                        </div>
                                    </div>
                                    <!-- titleText -->
                                    <div class="w-full field grid">
                                        <label htmlFor="titleText" class="col-fixed" style="width:150px;">Title Text</label>
                                        <div class="col">
                                            <input class="w-full" type="text" pInputText [(ngModel)]="scaleConfig.titleText" formControlName="titleText"
                                            placeholder="Title Text"/>
                                        </div>
                                    </div>
                                </div>
                                <div style="width: 33%;">
                                    <!-- titleAlign -->
                                    <div class="w-full field grid">
                                        <label htmlFor="titleAlign" class="col-fixed" style="width:150px;">Title Align</label>
                                        <div class="col">
                                            <p-dropdown styleClass="w-full"
                                                [showClear]="false"
                                                id="titleAlign" [autoDisplayFirst]="false"
                                                [(ngModel)]="scaleConfig.titleAlign"
                                                [options]="aligns"
                                                formControlName="titleAlign"
                                                optionLabel="name"
                                                optionValue="value"
                                                [placeholder]="'Title Align'"
                                            ></p-dropdown>
                                        </div>
                                    </div>
                                    <!-- titleDisplay -->
                                    <div class="w-full field grid">
                                        <label htmlFor="titleDisplay" class="col-fixed" style="width:150px">Title Display</label>
                                        <div class="col">
                                            <p-checkbox [(ngModel)]="scaleConfig.titleDisplay" [binary]="true" formControlName="titleDisplay" inputId="titleDisplay"></p-checkbox>
                                        </div>
                                    </div>
                                    <!-- titleColor -->
                                    <div class="w-full field grid">
                                        <label htmlFor="titleColor" class="col-fixed" style="width:150px">Title Color</label>
                                        <div class="col flex flex-row jutify-content-start align-items-center">
                                            <div *ngIf="scaleConfig.titleColor"
                                                [style]="{backgroundColor:scaleConfig.titleColor, width: '24px', height: '24px'}" class="mr-2"
                                                [pTooltip]="tranService.translate('global.button.view')"
                                                (click)="openChooseColorBox(null, null, 'scaleConfig.titleColor')"
                                            ></div>
                                            <p-button [label]="tranService.translate('global.button.add2')" *ngIf="!scaleConfig.titleColor"
                                                styleClass="p-button-secondary p-button-outlined ml-2 w-12"
                                                (click)="openChooseColorBox(null, null, 'scaleConfig.titleColor')"></p-button>
                                            <p-button [label]="tranService.translate('global.button.delete')" *ngIf="scaleConfig.titleColor"
                                                styleClass="p-button-secondary p-button-outlined ml-2 w-12"
                                                (click)="colorConfig.color = null;saveColor()"></p-button>
                                        </div>
                                    </div>
                                    <!-- titleFont -->
                                    <div class="w-full field grid">
                                        <label htmlFor="titleFont" class="col-fixed" style="width:150px">Title Font</label>
                                        <div class="col flex flex-row jutify-content-start align-items-center">
                                            <div *ngIf="scaleConfig.titleFont"
                                                [style]="{fontSize: scaleConfig.titleFont.size + 'px',
                                                                fontFamily: scaleConfig.titleFont.family,
                                                                lineHeight: scaleConfig.titleFont.lineHeight,
                                                                fontStyle: scaleConfig.titleFont.style,
                                                                fontWeight: scaleConfig.titleFont.weight
                                                            }" class="mr-2"
                                                (click)="openConfigFontBox('scaleConfig.titleFont')"
                                            >Example</div>
                                            <p-button [label]="tranService.translate('global.button.add2')" *ngIf="!scaleConfig.titleFont"
                                                styleClass="p-button-secondary p-button-outlined ml-2 w-12"
                                                (click)="openConfigFontBox('scaleConfig.titleFont')"></p-button>
                                            <p-button [label]="tranService.translate('global.button.delete')" *ngIf="scaleConfig.titleFont"
                                                styleClass="p-button-secondary p-button-outlined ml-2 w-12"
                                                (click)="scaleConfig.titleFont = null"></p-button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="flex flex-row justify-content-center">
                                <p-button *ngIf="scaleKey != 'x' && scaleKey != 'y'" [label]="tranService.translate('global.button.delete')" styleClass="p-button-secondary" (click)="deleteScale()"></p-button>
                                <p-button [label]="tranService.translate('global.button.save')" styleClass="p-button-info ml-2" (click)="saveScale()"></p-button>
                            </div>
                        </form>
                    </div>
                </p-tabPanel>
                <p-tabPanel header="Box Value" *ngIf="dynamicConfig.optionConfigEntity.boxvalue">
                    <div class="mt-2">
                        <div class="flex flex-row justify-content-between">
                            <div style="width: 33%;">
                                <!-- stacked -->
                                <div class="w-full field grid">
                                    <label htmlFor="stacked" class="col-fixed" style="width:150px">Show Box Value</label>
                                    <div class="col">
                                        <p-checkbox [(ngModel)]="dynamicConfig.optionConfigEntity.boxvalue.isShowBoxValue" [binary]="true" inputId="stacked"></p-checkbox>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </p-tabPanel>
            </p-tabView>
        </p-panel>
        <p-panel [toggleable]="true" [header]="tranService.translate('chart.label.filterConfig')" styleClass="mt-3">
            <div>
                <div class="flex flex-row justify-content-between align-items-center">
                    <div><b>{{tranService.translate("report.label.paramList")}}</b></div>
                    <div *ngIf="modeView != objectMode.DETAIL" class="text-cyan-500 cursor-pointer" (click)="openCreateParameter()"><u>{{tranService.translate("report.button.addParam")}}</u></div>
                </div>
                <table-vnpt
                    [fieldId]="'id'"
                    [columns]="paramColumns"
                    [dataSet]="dataParams"
                    [options]="optionTableListParam"
                    scrollHeight="300px"
                    isRowDraggable="true"
                ></table-vnpt>
            </div>
        </p-panel>
    </div>
    <p-divider type="solid"></p-divider>
    <div class="flex flex-row justify-content-center align-items-center">
        <p-button [label]="tranService.translate('global.button.cancel')" styleClass="p-button-secondary p-button-outlined mr-2" (click)="closeForm()"></p-button>
        <p-button [label]="tranService.translate('global.button.save')" styleClass="p-button-info" (click)="save()"
        ></p-button>
    </div>
</p-card>

<!-- parameter -->
<div class="flex justify-content-center dialog-vnpt" *ngIf="formParameter">
    <p-dialog [header]="getHeaderParameter()" [(visible)]="isShowDialogParameter" [modal]="true" [style]="{ width: '700px',top: 0 }" [draggable]="false" [resizable]="false">
        <div class="w-full field grid p-0 m-0">
            <form [formGroup]="formParameter" class="w-full">
                <!-- paramKey -->
                <div class="w-full field grid">
                    <label htmlFor="prKey" class="col-fixed" style="width:180px">{{tranService.translate("report.label.paramKey")}}<span class="text-red-500">*</span></label>
                    <div class="col" style="max-width: 400px;">
                        <input class="w-full"
                                pInputText id="prKey"
                                [(ngModel)]="parameterInfo.prKey"
                                formControlName="prKey"
                                [required]="true"
                                [maxLength]="255"
                                pattern="^[a-zA-Z0-9_]*$"
                                [placeholder]="tranService.translate('report.text.inputParamKey')"
                                (ngModelChange)="checkExistParamKey()"
                        />
                    </div>
                </div>
                <!-- error prKey -->
                <div class="w-full field grid text-error-field">
                    <label htmlFor="prKey" class="col-fixed" style="width:180px"></label>
                    <div class="col">
                        <small class="text-red-500" *ngIf="formParameter.controls.prKey.dirty && formParameter.controls.prKey.errors?.required">{{tranService.translate("global.message.required")}}</small>
                        <small class="text-red-500" *ngIf="formParameter.controls.prKey.errors?.maxLength">{{tranService.translate("global.message.maxLength",{len:255})}}</small>
                        <small class="text-red-500" *ngIf="formParameter.controls.prKey.errors?.pattern">{{tranService.translate("global.message.formatCodeNotSub")}}</small>
                        <small class="text-red-500" *ngIf="isParamKeyExisted">{{tranService.translate("global.message.exists",{type: tranService.translate("report.label.paramKey").toLowerCase()})}}</small>
                    </div>
                </div>
                <!-- prDisplayName -->
                <div class="w-full field grid">
                    <label htmlFor="prDisplayName" class="col-fixed" style="width:180px;">{{tranService.translate("report.label.paramDisplay")}}<span class="text-red-500">*</span></label>
                    <div class="col" style="max-width: 400px;">
                        <input class="w-full"
                                pInputText id="prDisplayName"
                                [(ngModel)]="parameterInfo.prDisplayName"
                                formControlName="prDisplayName"
                                [required]="true"
                                [maxLength]="255"
                                pattern="^[^~`!@#\$%\^&*\(\)=\+\[\]\{\}\|\\,<>\/?]*$"
                                [placeholder]="tranService.translate('report.text.inputDisplayName')"
                                (ngModelChange)="checkExistParamDisplay()"
                        />
                    </div>
                </div>
                <!-- error prDisplayName -->
                <div class="w-full field grid text-error-field">
                    <label htmlFor="name" class="col-fixed" style="width:180px"></label>
                    <div class="col">
                        <small class="text-red-500" *ngIf="formParameter.controls.prDisplayName.dirty && formParameter.controls.prDisplayName.errors?.required">{{tranService.translate("global.message.required")}}</small>
                        <small class="text-red-500" *ngIf="formParameter.controls.prDisplayName.errors?.maxLength">{{tranService.translate("global.message.maxLength",{len:255})}}</small>
                        <small class="text-red-500" *ngIf="formParameter.controls.prDisplayName.errors?.pattern">{{tranService.translate("global.message.formatContainVN")}}</small>
                        <small class="text-red-500" *ngIf="isParamDisplayExisted">{{tranService.translate("global.message.exists",{type: tranService.translate("report.label.paramDisplay").toLowerCase()})}}</small>
                    </div>
                </div>
                <div class="w-full flex flex-row justify-content-between align-items-center">
                <!-- required -->
                <div class="w-6 field grid">
                    <label for="required" class="col-fixed" style="width:180px">{{tranService.translate("report.label.required")}}</label>
                    <div class="col" style="max-width: 400px;">
                        <p-checkbox
                            binary="true"
                            [trueValue]="true"
                            [falseValue]="false"
                            name="required"
                            [(ngModel)]="parameterInfo.required"
                            formControlName="required"
                        ></p-checkbox>
                    </div>
                </div>
                <div class="w-6 field grid" [class]="parameterInfo.prType == objectType.DATE ? ' ' : 'hidden'">
                    <label class="col-fixed" style="width:180px">{{tranService.translate("report.label.showDateDefault")}}</label>
                    <div class="col" style="max-width: 400px;">
                        <p-checkbox
                            binary="true"
                            [trueValue]="true"
                            [falseValue]="false"
                            name="isAutoComplete"
                            [(ngModel)]="parameterInfo.isAutoComplete"
                            formControlName="isAutoComplete" (ngModelChange)="changeIsAutoComplete()"
                        ></p-checkbox>
                    </div>
                </div>
                </div>
                <!-- prType -->
                <div class="w-full field grid">
                    <label for="prType" class="col-fixed" style="width:180px">{{tranService.translate("report.label.paramType")}}<span class="text-red-500">*</span></label>
                    <div class="col" style="max-width: 400px;">
                        <p-dropdown styleClass="w-full" showClear="true"
                                id="prType" [autoDisplayFirst]="false"
                                [(ngModel)]="parameterInfo.prType"
                                formControlName="prType"
                                [options]="parameterTypes"
                                optionLabel="name"
                                optionValue="value"
                                [required]="true"
                                [placeholder]="tranService.translate('report.text.selectParamType')"
                                (ngModelChange)="changeParamType()"
                        ></p-dropdown>
                    </div>
                </div>
                <!-- error prType -->
                <div class="w-full field grid text-error-field">
                    <label htmlFor="name" class="col-fixed" style="width:180px"></label>
                    <div class="col">
                        <small class="text-red-500" *ngIf="formParameter.controls.prType.dirty && formParameter.controls.prType.errors?.required">{{tranService.translate("global.message.required")}}</small>
                    </div>
                </div>
                <!-- date type -->
                <div class="w-full field grid" [class]="parameterInfo.prType == objectType.DATE || parameterInfo.prType == objectType.TIMESTAMP ? '' : 'hidden'">
                    <label for="dateType" class="col-fixed" style="width:180px">{{tranService.translate("report.label.dateType")}}<span class="text-red-500">*</span></label>
                    <div class="col" style="max-width: 400px;">
                        <p-dropdown styleClass="w-full" showClear="true"
                                id="dateType" [autoDisplayFirst]="false"
                                [(ngModel)]="parameterInfo.dateType"
                                formControlName="dateType"
                                [options]="dateTypes"
                                optionLabel="name"
                                optionValue="value"
                                [required]="parameterInfo.prType == objectType.DATE || parameterInfo.prType == objectType.TIMESTAMP"
                                [placeholder]="tranService.translate('report.text.selectDateType')"
                        ></p-dropdown>
                    </div>
                </div>
                <!-- error datetype -->
                <div class="w-full field grid text-error-field">
                    <label htmlFor="dateType" class="col-fixed" style="width:180px"></label>
                    <div class="col">
                        <small class="text-red-500" *ngIf="formParameter.controls.dateType.dirty && formParameter.controls.dateType.errors?.required">{{tranService.translate("global.message.required")}}</small>
                    </div>
                </div>

                <!-- recently date from-->
                <form [formGroup]="formInputRecentlyDate">
                    <div class="w-full field grid"
                         [class]="parameterInfo.prType == objectType.RECENTLY_DATE_FROM ? '' : 'hidden'">
                        <label for="dateType" class="col-fixed"
                               style="width:180px">{{ tranService.translate("report.label.recentlyDateFrom") }}<span
                            class="text-red-500">*</span></label>
                        <div class="col" style="max-width: 400px;">
                            <p-calendar styleClass="w-full" showClear="true"
                                        *ngIf="parameterInfo.prType == objectType.RECENTLY_DATE_FROM"
                                        id="recentlyDateFrom"
                                        formControlName="inputDateFrom"
                                        optionLabel="name"
                                        optionValue="value"
                                        [required]="parameterInfo.prType == objectType.RECENTLY_DATE_FROM"
                                        (ngModelChange)="onDateRecentlyDateFrom($event)"
                                        [(ngModel)]="inputRecentlyDate.inputDateFrom"
                                        [maxDate]="inputRecentlyDate.maxDateFrom"
                                        [minDate]="inputRecentlyDate.minDateFrom"
                                        (onSelect)="onChangeRecentlyDateFrom(inputRecentlyDate.inputDateFrom)"
                                        (onInput)="onChangeRecentlyDateFrom(inputRecentlyDate.inputDateFrom)"
                                        [placeholder]="tranService.translate('report.text.recentlyDateFrom')"
                                        dateFormat="dd/mm/yy"
                            ></p-calendar>
                        </div>
                    </div>
                    <div class="w-full field grid text-error-field"
                         *ngIf="parameterInfo.prType == objectType.RECENTLY_DATE_FROM ||  parameterInfo.prType == objectType.RECENTLY_DATE_TO">
                        <label htmlFor="prKey" class="col-fixed" style="width:180px"></label>
                        <div class="col">
                            <small class="text-red-500"
                                   *ngIf="formInputRecentlyDate.controls.inputDateFrom.dirty && formInputRecentlyDate.controls.inputDateFrom.errors?.required">{{ tranService.translate("global.message.required") }}</small>
                        </div>
                    </div>

                    <!-- recently date to-->
                    <div class="w-full field grid"
                         [class]="parameterInfo.prType == objectType.RECENTLY_DATE_TO ? '' : 'hidden'">
                        <label for="dateType" class="col-fixed"
                               style="width:180px">{{ tranService.translate("report.label.recentlyDateTo") }}<span
                            class="text-red-500">*</span></label>
                        <div class="col" style="max-width: 400px;">
                            <p-calendar styleClass="w-full" showClear="true"
                                        *ngIf="parameterInfo.prType == objectType.RECENTLY_DATE_TO"
                                        id="recentlyDateTo"
                                        [(ngModel)]="inputRecentlyDate.inputDateTo"
                                        formControlName="inputDateTo"
                                        optionLabel="name"
                                        optionValue="value"
                                        [minDate]="inputRecentlyDate.minDateTo"
                                        [maxDate]="inputRecentlyDate.maxDateTo"
                                        (onSelect)="onChangeRecentlyDateTo(inputRecentlyDate.inputDateTo)"
                                        (onInput)="onChangeRecentlyDateTo(inputRecentlyDate.inputDateTo)"
                                        (ngModelChange)="onDateRecentlyDateTo($event)"
                                        [required]="parameterInfo.prType == objectType.RECENTLY_DATE_TO"
                                        [placeholder]="tranService.translate('report.text.recentlyDateTo')"
                                        dateFormat="dd/mm/yy"
                            ></p-calendar>
                        </div>
                    </div>
                    <div class="w-full field grid text-error-field"
                         *ngIf="parameterInfo.prType == objectType.RECENTLY_DATE_FROM ||  parameterInfo.prType == objectType.RECENTLY_DATE_TO">
                        <label htmlFor="prKey" class="col-fixed" style="width:180px"></label>
                        <div class="col">
                            <small class="text-red-500"
                                   *ngIf="formInputRecentlyDate.controls.inputDateTo.dirty && formInputRecentlyDate.controls.inputDateTo.errors?.required">{{ tranService.translate("global.message.required") }}</small>
                        </div>
                    </div>
                </form>

                <!-- is autocomplete, is multichoice -->
                <div class="w-full flex flex-row justify-content-between align-items-center" [class]="parameterInfo.prType == objectType.LIST_NUMBER || parameterInfo.prType == objectType.STRING || parameterInfo.prType == objectType.LIST_STRING ? '' : 'hidden'">
                    <div class="w-6 field grid" [class]="parameterInfo.prType == objectType.STRING || parameterInfo.prType == objectType.LIST_STRING ? '' : ''">
                        <label for="isAutoComplete" class="col-fixed" style="width:180px">{{tranService.translate("report.label.isAutoComplete")}}</label>
                        <div class="col" style="max-width: 400px;">
                            <p-checkbox
                                binary="true"
                                [trueValue]="true"
                                [falseValue]="false"
                                name="isAutoComplete"
                                [(ngModel)]="parameterInfo.isAutoComplete"
                                formControlName="isAutoComplete" (ngModelChange)="changeIsAutoComplete()"
                                ></p-checkbox>
                        </div>
                    </div>
                    <div class="w-6 field grid" [class]="parameterInfo.prType == objectType.LIST_NUMBER || parameterInfo.prType == objectType.LIST_STRING ? '' : 'hidden'">
                        <label for="isMultiChoice" class="col-fixed" style="width:180px">{{tranService.translate("report.label.isMultiChoice")}}</label>
                        <div class="col" style="max-width: 400px;">
                            <p-checkbox
                                name="isMultiChoice"
                                binary="true"
                                [trueValue]="true"
                                [falseValue]="false"
                                [(ngModel)]="parameterInfo.isMultiChoice"
                                formControlName="isMultiChoice"></p-checkbox>
                        </div>
                    </div>
                </div>
                <!-- objectkey -->
                <div class="w-full field grid" [class]="parameterInfo.isAutoComplete == true && parameterInfo.prType != objectType.DATE ? '' : 'hidden'">
                    <label htmlFor="objectKey" class="col-fixed" style="width:180px">{{tranService.translate("report.label.objectKey")}}<span class="text-red-500">*</span></label>
                    <div class="col" style="max-width: 400px;">
                        <p-dropdown styleClass="w-full"
                            id="objectKey" [autoDisplayFirst]="false"
                            [(ngModel)]="parameterInfo.objectKey"
                            formControlName="objectKey"
                            [options]="listObjectKey"
                            optionLabel="display"
                            optionValue="value"
                            [required]="parameterInfo.isAutoComplete == true && parameterInfo.prType != objectType.DATE  "
                            [placeholder]="tranService.translate('report.text.inputObjectKey')"
                    ></p-dropdown>
                    </div>
                </div>
                <!-- error object key -->
                <div class="w-full field grid text-error-field">
                    <label htmlFor="prKey" class="col-fixed" style="width:180px"></label>
                    <div class="col">
                        <small class="text-red-500" *ngIf="formParameter.controls.objectKey.dirty && formParameter.controls.objectKey.errors?.required">{{tranService.translate("global.message.required")}}</small>
                        <small class="text-red-500" *ngIf="formParameter.controls.objectKey.errors?.maxLength">{{tranService.translate("global.message.maxLength",{len:16})}}</small>
                        <small class="text-red-500" *ngIf="formParameter.controls.objectKey.errors?.pattern">{{tranService.translate("global.message.formatCode")}}</small>
                    </div>
                </div>
                <!-- input -->
                <div class="w-full field grid" [class]="parameterInfo.isAutoComplete == true && parameterInfo.prType != objectType.DATE  ? '' : 'hidden'">
                    <label htmlFor="input" class="col-fixed" style="width:180px">{{tranService.translate("report.label.input")}}<span class="text-red-500">*</span></label>
                    <div class="col" style="max-width: 400px;">
                        <input class="w-full"
                                pInputText id="input"
                                [(ngModel)]="parameterInfo.input"
                                formControlName="input"
                                [required]="parameterInfo.isAutoComplete == true && parameterInfo.prType != objectType.DATE"
                                [maxLength]="16"
                                pattern="^[a-zA-Z0-9\-_]*$"
                                [placeholder]="tranService.translate('report.text.inputInput')"
                        />
                    </div>
                </div>
                <!-- error input -->
                <div class="w-full field grid text-error-field">
                    <label htmlFor="prKey" class="col-fixed" style="width:180px"></label>
                    <div class="col">
                        <small class="text-red-500" *ngIf="formParameter.controls.input.dirty && formParameter.controls.input.errors?.required">{{tranService.translate("global.message.required")}}</small>
                        <small class="text-red-500" *ngIf="formParameter.controls.input.errors?.maxLength">{{tranService.translate("global.message.maxLength",{len:16})}}</small>
                        <small class="text-red-500" *ngIf="formParameter.controls.input.errors?.pattern">{{tranService.translate("global.message.formatCode")}}</small>
                    </div>
                </div>
                <!-- output -->
                <div class="w-full field grid" [class]="parameterInfo.isAutoComplete == true && parameterInfo.prType != objectType.DATE  ? '' : 'hidden'">
                    <label htmlFor="output" class="col-fixed" style="width:180px">{{tranService.translate("report.label.output")}}<span class="text-red-500">*</span></label>
                    <div class="col" style="max-width: 400px;">
                        <input class="w-full"
                                pInputText id="output"
                                [(ngModel)]="parameterInfo.output"
                                formControlName="output"
                                [required]="parameterInfo.isAutoComplete == true  && parameterInfo.prType != objectType.DATE  "
                                [maxLength]="16"
                                pattern="^[a-zA-Z0-9\-_]*$"
                                [placeholder]="tranService.translate('report.text.inputOutput')"
                        />
                    </div>
                </div>
                <!-- error output -->
                <div class="w-full field grid text-error-field">
                    <label htmlFor="prKey" class="col-fixed" style="width:180px"></label>
                    <div class="col">
                        <small class="text-red-500" *ngIf="formParameter.controls.output.dirty && formParameter.controls.output.errors?.required">{{tranService.translate("global.message.required")}}</small>
                        <small class="text-red-500" *ngIf="formParameter.controls.output.errors?.maxLength">{{tranService.translate("global.message.maxLength",{len:16})}}</small>
                        <small class="text-red-500" *ngIf="formParameter.controls.output.errors?.pattern">{{tranService.translate("global.message.formatCode")}}</small>
                    </div>
                </div>
                <!-- displaypattern -->
                <div class="w-full field grid" [class]="parameterInfo.isAutoComplete == true && parameterInfo.prType != objectType.DATE  ? '' : 'hidden'">
                    <label htmlFor="displayPattern" class="col-fixed" style="width:180px">{{tranService.translate("report.label.displayPattern")}}<span class="text-red-500">*</span></label>
                    <div class="col" style="max-width: 400px;">
                        <input class="w-full"
                                pInputText id="displayPattern"
                                [(ngModel)]="parameterInfo.displayPattern"
                                formControlName="displayPattern"
                                [required]="parameterInfo.isAutoComplete == true && parameterInfo.prType != objectType.DATE"
                                [maxLength]="255"
                                placeholder="${var1} - ${var2}"
                        />
                        <!-- [placeholder]="tranService.translate('report.text.inputDisplayPattern')" -->
                    </div>
                </div>
                <!-- error displaypattern -->
                <div class="w-full field grid text-error-field">
                    <label htmlFor="prKey" class="col-fixed" style="width:180px"></label>
                    <div class="col">
                        <small class="text-red-500" *ngIf="formParameter.controls.displayPattern.dirty && formParameter.controls.displayPattern.errors?.required">{{tranService.translate("global.message.required")}}</small>
                        <small class="text-red-500" *ngIf="formParameter.controls.displayPattern.errors?.maxLength">{{tranService.translate("global.message.maxLength",{len:255})}}</small>
                        <small class="text-red-500" *ngIf="formParameter.controls.displayPattern.errors?.pattern">{{tranService.translate("global.message.formatCode")}}</small>
                    </div>
                </div>
                <!-- queryParams -->
                <div *ngIf="parameterInfo.isAutoComplete && (parameterInfo.prType == CONSTANTS.PARAMETER_TYPE.LIST_NUMBER || parameterInfo.prType == CONSTANTS.PARAMETER_TYPE.LIST_STRING)" class="w-full field grid">
                    <label htmlFor="queryParam" class="col-fixed" style="width:180px">
                        {{tranService.translate("report.label.queryParams")}}
                        <span class="text-red-500">*</span>
                        &nbsp;&nbsp;
                        <i class="pi pi-info-circle" [pTooltip]="tranService.translate('report.label.sampleQueryParam')"></i>
                    </label>
                    <div class="col" style="max-width: 400px;">
                        <input class="w-full"
                               pInputText id="queryParam"
                               [(ngModel)]="parameterInfo.queryParam"
                               formControlName="queryParam"
                               [maxLength]="255"
                               pattern="^(\w+=(\$\w+|&quot;[^&quot;]*&quot;)|\w+=\d+)(?:&(\w+=(\$\w+|&quot;[^&quot;]*&quot;)|\w+=\d+))*$"
                               [placeholder]="tranService.translate('report.text.inputQueryParam')"
                        />
                    </div>
                </div>
            </form>
            <div class="w-full">
                <table-input-vnpt [class]="(parameterInfo.prType == objectType.LIST_NUMBER || parameterInfo.prType == objectType.LIST_STRING) && parameterInfo.isAutoComplete == false ? '': 'hidden'"
                    [(value)]="parameterInfo.valueList"
                    [columns]="columnParamInput"
                    [options]="optionParamInput"
                    [control]="paramInputControl"
                    fieldId="id"
                ></table-input-vnpt>
            </div>
        </div>
        <div class="flex flex-row justify-content-center align-items-center mt-3">
            <p-button styleClass="mr-2 p-button-secondary p-button-outlined" [label]="tranService.translate('global.button.cancel')" (click)="isShowDialogParameter = false"></p-button>
            <p-button *ngIf="modeParameter != objectMode.DETAIL" styleClass="p-button-info" [label]="tranService.translate('global.button.save')" [disabled]="checkInvalidFormParameter()" (click)="saveParameter()"></p-button>
        </div>
    </p-dialog>
</div>

<div class="preview flex justify-content-center dialog-vnpt">
    <p-dialog [header]="tranService.translate('global.button.preview')" [(visible)]="isShowPreview" [modal]="true" [style]="{ width: 'fit-content',top: 0 }" [draggable]="false" [resizable]="false">
        <div class="w-full field grid p-0 m-0">
            <dynamic-chart-vnpt [chartConfig]="chartConfigPreview" [control]="dynamicChartController" [width]="600" [height]="300"></dynamic-chart-vnpt>
        </div>
    </p-dialog>
</div>

<div *ngIf="dataConfigForm" class="flex justify-content-center">
    <p-dialog [header]="'Data Config'" [(visible)]="isShowDialogDataConfig" [modal]="true" [style]="{ width: '1200px',top: 0, maxHeight: '700px' }" [draggable]="false" [resizable]="false">
        <form [formGroup]="dataConfigForm">
            <p-divider styleClass="flex" [align]="'center'"><b>DATA</b></p-divider>
            <div class="flex flex-row justify-content-between">
                <div style="width: 49%;">
                    <!-- dataset type -->
                    <div class="w-full field grid">
                        <label htmlFor="typeDataset" class="col-fixed" style="width:180px">{{tranService.translate('chart.label.chartType')}}</label>
                        <div class="col" style="max-width:calc(100% - 180px)">
                            <p-dropdown styleClass="w-full"
                                [showClear]="false"
                                id="typeDataset" [autoDisplayFirst]="false"
                                [(ngModel)]="dataConfig.type"
                                formControlName="type"
                                [options]="keyTypeCharts"
                                optionLabel="name"
                                optionValue="value"
                                [placeholder]="tranService.translate('chart.label.chartType')"
                            ></p-dropdown>
                        </div>
                    </div>
                    <!-- datasetName -->
                    <div class="w-full field grid">
                        <label htmlFor="datasetName" class="col-fixed" style="width:180px;height: fit-content;">{{tranService.translate("chart.label.datasetName")}}<span class="text-red-500">*</span></label>
                        <div class="col">
                            <input class="w-full"
                                    pInputText id="datasetName"
                                    [(ngModel)]="dataConfig.datasetName"
                                    formControlName="datasetName"
                                    [required]="true"
                                    [placeholder]="tranService.translate('chart.label.datasetName')"
                            />
                        </div>
                    </div>
                    <!-- error datasetName -->
                    <div class="w-full field grid text-error-field">
                        <label htmlFor="datasetName" class="col-fixed" style="width:180px"></label>
                        <div class="col">
                            <small class="text-red-500" *ngIf="dataConfigForm.controls.datasetName.dirty && dataConfigForm.controls.datasetName.errors?.required">{{tranService.translate("global.message.required")}}</small>
                        </div>
                    </div>
                    <!-- query -->
                    <div class="w-full field grid">
                        <label htmlFor="query" class="col-fixed" style="width:180px;height: fit-content;">{{tranService.translate("chart.label.query")}}</label>
                        <div class="col">
                            <textarea class="w-full"
                                    pInputText id="query"
                                    [(ngModel)]="dataConfig.query"
                                    formControlName="query"
                                    rows="10"
                                    [placeholder]="tranService.translate('chart.label.query')"
                            ></textarea>
                        </div>
                    </div>
                    <!-- error query -->
                    <div class="w-full field grid text-error-field">
                        <label htmlFor="query" class="col-fixed" style="width:180px"></label>
                        <div class="col">
                            <small class="text-red-500" *ngIf="dataConfigForm.controls.query.dirty && dataConfigForm.controls.query.errors?.required">{{tranService.translate("global.message.required")}}</small>
                        </div>
                    </div>
                    <!-- schema -->
                    <div class="w-full field grid">
                        <label for="schema" class="col-fixed" style="width:180px">{{tranService.translate("report.label.schema")}}</label>
                        <div class="col">
                            <p-dropdown styleClass="w-full"
                                    [showClear]="false"
                                    id="schemaDataConfig" [autoDisplayFirst]="false"
                                    [(ngModel)]="dataConfig.schema"
                                    [options]="schemas"
                                    formControlName="schema"
                                    optionLabel="name"
                                    optionValue="value"
                                    [placeholder]="tranService.translate('report.text.selectSchema')"
                            ></p-dropdown>
                        </div>
                    </div>
                </div>
                <div style="width: 49%;">
                    <!-- typeQuery -->
                    <div class="w-full field grid">
                        <label htmlFor="query" class="col-fixed" style="width:180px">{{tranService.translate("chart.label.typeQuery")}}</label>
                        <div class="col">
                            <p-dropdown styleClass="w-full"
                                    [showClear]="false"
                                    id="typeQuery" [autoDisplayFirst]="false"
                                    [(ngModel)]="dataConfig.typeQuery"
                                    formControlName="typeQuery"
                                    [options]="typeQueries"
                                    optionLabel="name"
                                    optionValue="value"
                                    [placeholder]="tranService.translate('chart.label.typeQuery')"
                            ></p-dropdown>
                        </div>
                    </div>
                    <!-- keyLabel -->
                    <div class="w-full field grid">
                        <label htmlFor="keyLabel" class="col-fixed" style="width:180px">{{tranService.translate("chart.label.keyLabel")}}<span class="text-red-500">*</span></label>
                        <div class="col">
                            <input class="w-full"
                                    pInputText id="keyLabel"
                                    [(ngModel)]="dataConfig.keyLabel"
                                    formControlName="keyLabel"
                                    [required]="true"
                                    [maxLength]="50"
                                    pattern="^[a-zA-Z0-9\-_]*$"
                                    [placeholder]="tranService.translate('chart.label.keyLabel')"
                            />
                        </div>
                    </div>
                    <!-- error keyLabel -->
                    <div class="w-full field grid text-error-field">
                        <label htmlFor="keyLabel" class="col-fixed" style="width:180px"></label>
                        <div class="col">
                            <small class="text-red-500" *ngIf="dataConfigForm.controls.keyLabel.dirty && dataConfigForm.controls.keyLabel.errors?.required">{{tranService.translate("global.message.required")}}</small>
                            <small class="text-red-500" *ngIf="dataConfigForm.controls.keyLabel.errors?.maxLength">{{tranService.translate("global.message.maxLength",{len:50})}}</small>
                            <small class="text-red-500" *ngIf="dataConfigForm.controls.keyLabel.errors?.pattern">{{tranService.translate("global.message.formatCode")}}</small>
                        </div>
                    </div>
                    <!-- keyValue -->
                    <div class="w-full field grid">
                        <label htmlFor="keyValue" class="col-fixed" style="width:180px">{{tranService.translate("chart.label.keyValue")}}<span class="text-red-500">*</span></label>
                        <div class="col">
                            <input class="w-full"
                                    pInputText id="keyValue"
                                    [(ngModel)]="dataConfig.keyValue"
                                    formControlName="keyValue"
                                    [required]="true"
                                    [maxLength]="50"
                                    pattern="^[a-zA-Z0-9\-_]*$"
                                    [placeholder]="tranService.translate('chart.label.keyValue')"
                            />
                        </div>
                    </div>
                    <!-- error keyValue -->
                    <div class="w-full field grid text-error-field">
                        <label htmlFor="keyValue" class="col-fixed" style="width:180px"></label>
                        <div class="col">
                            <small class="text-red-500" *ngIf="dataConfigForm.controls.keyValue.dirty && dataConfigForm.controls.keyValue.errors?.required">{{tranService.translate("global.message.required")}}</small>
                            <small class="text-red-500" *ngIf="dataConfigForm.controls.keyValue.errors?.maxLength">{{tranService.translate("global.message.maxLength",{len:50})}}</small>
                            <small class="text-red-500" *ngIf="dataConfigForm.controls.keyValue.errors?.pattern">{{tranService.translate("global.message.formatCode")}}</small>
                        </div>
                    </div>
                    <!-- keyValueX -->
                    <div class="w-full field grid">
                        <label htmlFor="keyValueX" class="col-fixed" style="width:180px">{{tranService.translate("chart.label.keyValue")}} X</label>
                        <div class="col">
                            <input class="w-full"
                                    pInputText id="keyValueX"
                                    [(ngModel)]="dataConfig.keyValueX"
                                    formControlName="keyValueX"
                                    [maxLength]="50"
                                    pattern="^[a-zA-Z0-9\-_]*$"
                                    [placeholder]="tranService.translate('chart.label.keyValue')+' X'"
                            />
                        </div>
                    </div>
                    <!-- keyValueY-->
                    <div class="w-full field grid">
                        <label htmlFor="keyValueY" class="col-fixed" style="width:180px">{{tranService.translate("chart.label.keyValue")}} Y</label>
                        <div class="col">
                            <input class="w-full"
                                    pInputText id="keyValueY"
                                    [(ngModel)]="dataConfig.keyValueY"
                                    formControlName="keyValueY"
                                    [maxLength]="50"
                                    pattern="^[a-zA-Z0-9\-_]*$"
                                    [placeholder]="tranService.translate('chart.label.keyValue')+' Y'"
                            />
                        </div>
                    </div>
                    <!-- keyValueR -->
                    <div class="w-full field grid">
                        <label htmlFor="keyValueR" class="col-fixed" style="width:180px">{{tranService.translate("chart.label.keyValue")}} R</label>
                        <div class="col">
                            <input class="w-full"
                                    pInputText id="keyValueR"
                                    [(ngModel)]="dataConfig.keyValueR"
                                    formControlName="keyValueR"
                                    [maxLength]="50"
                                    pattern="^[a-zA-Z0-9\-_]*$"
                                    [placeholder]="tranService.translate('chart.label.keyValue')+' R'"
                            />
                        </div>
                    </div>
                    <!-- keyDataset -->
                    <div class="w-full field grid">
                        <label htmlFor="keyDataset" class="col-fixed" style="width:180px">{{tranService.translate("chart.label.keyDataset")}}<span class="text-red-500">*</span></label>
                        <div class="col">
                            <input class="w-full"
                                    pInputText id="keyDataset"
                                    [(ngModel)]="dataConfig.keyDataset"
                                    formControlName="keyDataset"
                                    [required]="true"
                                    [maxLength]="50"
                                    pattern="^[a-zA-Z0-9\-_]*$"
                                    [placeholder]="tranService.translate('chart.label.keyDataset')"
                            />
                        </div>
                    </div>
                    <!-- error keyDataset -->
                    <div class="w-full field grid text-error-field">
                        <label htmlFor="keyDataset" class="col-fixed" style="width:180px"></label>
                        <div class="col">
                            <small class="text-red-500" *ngIf="dataConfigForm.controls.keyDataset.dirty && dataConfigForm.controls.keyDataset.errors?.required">{{tranService.translate("global.message.required")}}</small>
                            <small class="text-red-500" *ngIf="dataConfigForm.controls.keyDataset.errors?.maxLength">{{tranService.translate("global.message.maxLength",{len:50})}}</small>
                            <small class="text-red-500" *ngIf="dataConfigForm.controls.keyDataset.errors?.pattern">{{tranService.translate("global.message.formatCode")}}</small>
                        </div>
                    </div>
<!--                    &lt;!&ndash; keyMaxValue &ndash;&gt;-->
<!--                    <div class="w-full field grid">-->
<!--                        <label htmlFor="keyMaxValue" class="col-fixed" style="width:180px">{{tranService.translate("chart.label.keyMaxValue")}}<span class="text-red-500">*</span></label>-->
<!--                        <div class="col">-->
<!--                            <input class="w-full"-->
<!--                                    pInputText id="keyDataset"-->
<!--                                    [(ngModel)]="dataConfig.keyMaxValue"-->
<!--                                    formControlName="keyMaxValue"-->
<!--                                    [required]="true"-->
<!--                                    [maxLength]="50"-->
<!--                                    pattern="^[a-zA-Z0-9\-_]*$"-->
<!--                                    [placeholder]="tranService.translate('chart.label.keyMaxValue')"-->
<!--                            />-->
<!--                        </div>-->
<!--                    </div>-->
                    <!-- error keyDataset -->
<!--                    <div class="w-full field grid text-error-field">-->
<!--                        <label htmlFor="keyMaxValue" class="col-fixed" style="width:180px"></label>-->
<!--                        <div class="col">-->
<!--                            <small class="text-red-500" *ngIf="dataConfigForm.controls.keyMaxValue.dirty && dataConfigForm.controls.keyMaxValue.errors?.required">{{tranService.translate("global.message.required")}}</small>-->
<!--                            <small class="text-red-500" *ngIf="dataConfigForm.controls.keyMaxValue.errors?.maxLength">{{tranService.translate("global.message.maxLength",{len:50})}}</small>-->
<!--                            <small class="text-red-500" *ngIf="dataConfigForm.controls.keyMaxValue.errors?.pattern">{{tranService.translate("global.message.formatCode")}}</small>-->
<!--                        </div>-->
<!--                    </div>-->
                </div>
            </div>
            <p-divider styleClass="flex" [align]="'center'"><b>STYLE</b></p-divider>
            <div class="flex flex-row justify-content-between">
                <div style="width: 33%;">
                    <!-- animation type -->
                    <div class="w-full field grid">
                        <label htmlFor="datasetColorItem" class="col-fixed" style="width:150px">Animation Type</label>
                        <div class="col" style="min-width: calc(100% - 150px);">
                            <p-dropdown styleClass="w-full"
                                    [showClear]="false"
                                    id="typeAnimation" [autoDisplayFirst]="false"
                                    [(ngModel)]="dataConfig.typeAnimation"
                                    formControlName="typeAnimation"
                                    [options]="typeAnimations"
                                    optionLabel="name"
                                    optionValue="value"
                                    [placeholder]="'Animation Type'"
                            ></p-dropdown>
                        </div>
                    </div>
                    <!-- xAxisID -->
                    <div class="w-full field grid">
                        <label htmlFor="xAxisID" class="col-fixed" style="width:150px">X Axis ID</label>
                        <div class="col flex flex-row justify-content-start align-items-center" style="max-width:calc(100% - 150px)">
                            <input class="w-full"
                                    pInputText id="xAxisID"
                                    [(ngModel)]="dataConfig.xAxisID"
                                    formControlName="xAxisID"
                                    [placeholder]="'X Axis ID'"
                            />
                        </div>
                    </div>
                    <!-- yAxisId -->
                    <div class="w-full field grid">
                        <label htmlFor="yAxisID" class="col-fixed" style="width:150px">Y Axis ID</label>
                        <div class="col flex flex-row justify-content-start align-items-center" style="max-width:calc(100% - 150px)">
                            <input class="w-full"
                                    pInputText id="yAxisId"
                                    [(ngModel)]="dataConfig.yAxisID"
                                    formControlName="yAxisID"
                                    [placeholder]="'y Axis ID'"
                            />
                        </div>
                    </div>
                    <!-- stack -->
                    <div class="w-full field grid">
                        <label htmlFor="stack" class="col-fixed" style="width:150px">Stack</label>
                        <div class="col flex flex-row justify-content-start align-items-center" style="max-width:calc(100% - 150px)">
                            <input class="w-full"
                                    pInputText id="stack"
                                    [(ngModel)]="dataConfig.stack"
                                    formControlName="stack"
                                    [placeholder]="'Stack'"
                            />
                        </div>
                    </div>
                </div>
                <p-divider layout="vertical" styleClass="flex"></p-divider>
                <div style="width: 33%;">
                    <!-- barPercentage -->
                    <div class="w-full field grid">
                        <label htmlFor="barPercentage" class="col-fixed" style="width:150px">Bar Percentage</label>
                        <div class="col flex flex-row justify-content-start align-items-center" style="max-width:calc(100% - 150px)">
                            <p-inputNumber class="flex-1"  [(ngModel)]="dataConfig.barPercentage" inputId="minmax" [min]="0" [max]="1"
                                formControlName="barPercentage" [minFractionDigits]="2"
                            > </p-inputNumber>
                        </div>
                    </div>
                    <!-- barThickness -->
                    <div class="w-full field grid">
                        <label htmlFor="barThickness" class="col-fixed" style="width:150px">Bar Thickness</label>
                        <div class="col flex flex-row justify-content-start align-items-center" style="max-width:calc(100% - 150px)">
                            <p-inputNumber class="flex-1"  [(ngModel)]="dataConfig.barThickness" inputId="minmax" [min]="30"
                                formControlName="barThickness" suffix=" px"
                            > </p-inputNumber>
                        </div>
                    </div>
                    <!-- base -->
                    <div class="w-full field grid">
                        <label htmlFor="base" class="col-fixed" style="width:150px">Base</label>
                        <div class="col flex flex-row justify-content-start align-items-center" style="max-width:calc(100% - 150px)">
                            <p-inputNumber class="flex-1"  [(ngModel)]="dataConfig.base" inputId="minmax" [min]="0"
                                formControlName="base"
                            > </p-inputNumber>
                        </div>
                    </div>
                    <!-- barRadius -->
                    <div class="w-full field grid">
                        <label htmlFor="barRadius" class="col-fixed" style="width:150px">Bar Radius</label>
                        <div class="col flex flex-row justify-content-start align-items-center" style="max-width:calc(100% - 150px)">
                            <p-inputNumber class="flex-1"  [(ngModel)]="dataConfig.barRadius" inputId="minmax" [min]="0"
                                formControlName="barRadius" suffix=" px"
                            > </p-inputNumber>
                        </div>
                    </div>
                    <!-- categoryPercentage -->
                    <div class="w-full field grid">
                        <label htmlFor="categoryPercentage" class="col-fixed" style="width:150px">Category Percentage</label>
                        <div class="col flex flex-row justify-content-start align-items-center" style="max-width:calc(100% - 150px)">
                            <p-inputNumber class="flex-1"  [(ngModel)]="dataConfig.categoryPercentage" inputId="minmax" [min]="0" [max]="1"
                                formControlName="categoryPercentage" [minFractionDigits]="2"
                            > </p-inputNumber>
                        </div>
                    </div>
                    <!-- border dash -->
                    <div class="w-full field grid">
                        <label htmlFor="isLineDash" class="col-fixed" style="width:150px">Border Dash</label>
                        <div class="col flex flex-row justify-content-start align-items-center" style="max-width:calc(100% - 150px)">
                            <p-checkbox [(ngModel)]="dataConfig.isLineDash" [binary]="true" formControlName="isLineDash" inputId="isLineDash"></p-checkbox>
                        </div>
                    </div>
                    <!-- border cap -->
                    <div class="w-full field grid">
                        <label htmlFor="borderCapStyle" class="col-fixed" style="width:150px">Border Cap Style</label>
                        <div class="col" style="max-width:calc(100% - 150px)">
                            <p-dropdown styleClass="w-full"
                                    [showClear]="false"
                                    id="borderCapStyle" [autoDisplayFirst]="false"
                                    [(ngModel)]="dataConfig.borderCapStyle"
                                    formControlName="borderCapStyle"
                                    [options]="borderCapStyles"
                                    optionLabel="name"
                                    optionValue="value"
                                    [placeholder]="'Border Cap Style'"
                            ></p-dropdown>
                        </div>
                    </div>
                    <!-- border join -->
                    <div class="w-full field grid">
                        <label htmlFor="borderJoinStyle" class="col-fixed" style="width:150px">Border Join Style</label>
                        <div class="col" style="max-width:calc(100% - 150px)">
                            <p-dropdown styleClass="w-full"
                                    [showClear]="false"
                                    id="borderJoinStyle" [autoDisplayFirst]="false"
                                    [(ngModel)]="dataConfig.borderJoinStyle"
                                    formControlName="borderJoinStyle"
                                    [options]="borderJoinStyles"
                                    optionLabel="name"
                                    optionValue="value"
                                    [placeholder]="'Border Join Style'"
                            ></p-dropdown>
                        </div>
                    </div>
                    <!-- fill -->
                    <div class="w-full field grid">
                        <label htmlFor="fill" class="col-fixed" style="width:150px">Fill</label>
                        <div class="col flex flex-row justify-content-start align-items-center" style="max-width:calc(100% - 150px)">
                            <p-checkbox [(ngModel)]="dataConfig.fill" [binary]="true" formControlName="fill" inputId="fill"></p-checkbox>
                        </div>
                    </div>
                    <!-- tension -->
                    <div class="w-full field grid">
                        <label htmlFor="tension" class="col-fixed" style="width:150px">Tension</label>
                        <div class="col flex flex-row justify-content-start align-items-center" style="max-width:calc(100% - 150px)">
                            <p-inputNumber class="flex-1"  [(ngModel)]="dataConfig.tension" inputId="minmax" [min]="0" [max]="1" [minFractionDigits]="2"
                                formControlName="tension" suffix=" %"
                            > </p-inputNumber>
                        </div>
                    </div>
                    <!-- point style -->
                    <div class="w-full field grid">
                        <label htmlFor="pointStyle" class="col-fixed" style="width:150px">Point Style</label>
                        <div class="col" style="max-width:calc(100% - 150px)">
                            <p-dropdown styleClass="w-full"
                                    [showClear]="false"
                                    id="pointStyle" [autoDisplayFirst]="false"
                                    [(ngModel)]="dataConfig.pointStyle"
                                    formControlName="pointStyle"
                                    [options]="pointStyles"
                                    optionLabel="name"
                                    optionValue="value"
                                    [placeholder]="'Point Style'"
                            ></p-dropdown>
                        </div>
                    </div>
                    <!-- weight -->
                    <div class="w-full field grid">
                        <label htmlFor="weight" class="col-fixed" style="width:150px">Weight</label>
                        <div class="col flex flex-row justify-content-start align-items-center" style="max-width:calc(100% - 150px)">
                            <p-inputNumber class="flex-1"  [(ngModel)]="dataConfig.weight" inputId="minmax" [min]="0" [minFractionDigits]="2"
                                formControlName="weight"
                            > </p-inputNumber>
                        </div>
                    </div>
                </div>
                <p-divider layout="vertical" styleClass="flex"></p-divider>
                <div style="width: 33%;">
                    <div class="w-full field grid">
                        <label htmlFor="datasetColorItem" class="col-fixed" style="width:150px">{{tranService.translate("chart.label.datasetColor")}}</label>
                        <div class="col flex flex-row flex-wrap justify-content-start align-items-center">

                        </div>
                    </div>
                    <!-- backGroundColor -->
                    <div class="w-full field grid">
                        <label htmlFor="datasetColorItem" class="col-fixed" style="width:150px">Background Color</label>
                        <div class="col flex flex-row flex-wrap justify-content-start align-items-center">
                            <p-button [label]="tranService.translate('global.button.add2')"
                                    styleClass="p-button-secondary p-button-outlined ml-2 w-12"
                                    (click)="openChooseColorBox(null, 'backgroundColors')"></p-button>
                            <p-button [label]="tranService.translate('global.button.addDefault')" *ngIf="dataConfig.backgroundColors.length == 0"
                                    styleClass="p-button-secondary p-button-outlined ml-4 w-12"
                                    (click)="addDefaultBackgroundColor('backgroundColors')"></p-button>
                        </div>
                    </div>
                    <!--  backGroundColor -->
                    <div class="w-full field grid" *ngIf="dataConfig.backgroundColors.length > 0">
                        <label htmlFor="keyDataset" class="col-fixed" style="width:150px"></label>
                        <div class="col flex flex-row flex-wrap justify-content-start align-items-center">
                            <div *ngFor="let color of dataConfig.backgroundColors;let i = index"
                                [style]="{backgroundColor: color, width: '24px', height: '24px'}" class="mr-2"
                                [pTooltip]="tranService.translate('global.button.view')"
                                (click)="openChooseColorBox(i, 'backgroundColors')"
                            ></div>
                            <p-button [pTooltip]="tranService.translate('global.button.clear')" icon="pi pi-times"
                                    styleClass="p-button-secondary p-button-outlined ml-2 p-button-rounded" [style]="{width: '24px', height: '24px'}"
                                    [disabled]="!dataConfig.backgroundColors"
                                    (click)="dataConfig.backgroundColors = []"></p-button>
                        </div>
                    </div>
                    <!-- borderColor -->
                    <div class="w-full field grid">
                        <label htmlFor="datasetColorItem" class="col-fixed" style="width:150px">Border Color</label>
                        <div class="col flex flex-row flex-wrap justify-content-start align-items-center">
                            <p-button [label]="tranService.translate('global.button.add2')"
                                    styleClass="p-button-secondary p-button-outlined ml-2 w-12"
                                    (click)="openChooseColorBox(null, 'borderColors')"></p-button>
                        </div>
                    </div>
                    <!--  borderColor -->
                    <div class="w-full field grid" *ngIf="dataConfig.borderColors.length > 0">
                        <label htmlFor="keyDataset" class="col-fixed" style="width:150px"></label>
                        <div class="col flex flex-row flex-wrap justify-content-start align-items-center">
                            <div *ngFor="let color of dataConfig.borderColors;let i = index"
                                [style]="{backgroundColor: color, width: '24px', height: '24px'}" class="mr-2"
                                [pTooltip]="tranService.translate('global.button.view')"
                                (click)="openChooseColorBox(i, 'borderColors')"
                            ></div>
                            <p-button [pTooltip]="tranService.translate('global.button.clear')" icon="pi pi-times"
                                    styleClass="p-button-secondary p-button-outlined ml-2 p-button-rounded" [style]="{width: '24px', height: '24px'}"
                                    [disabled]="!dataConfig.borderColors"
                                    (click)="dataConfig.borderColors = []"></p-button>
                        </div>
                    </div>
                    <!-- border width -->
                    <div class="w-full field grid">
                        <label htmlFor="borderWidth" class="col-fixed" style="width:150px">Border Width</label>
                        <div class="col flex flex-row justify-content-start align-items-center" style="max-width:calc(100% - 150px)">
                            <p-inputNumber class="flex-1"  [(ngModel)]="dataConfig.borderWidth" inputId="minmax" mode="decimal" [min]="0"
                                formControlName="borderWidth" suffix=" px"
                            > </p-inputNumber>
                        </div>
                    </div>
                    <!-- hoverBackGroundColor -->
                    <div class="w-full field grid">
                        <label htmlFor="datasetColorItem" class="col-fixed" style="width:150px">Hover Background Color</label>
                        <div class="col flex flex-row flex-wrap justify-content-start align-items-center">
                            <p-button [label]="tranService.translate('global.button.add2')"
                                    styleClass="p-button-secondary p-button-outlined ml-2 w-12"
                                    (click)="openChooseColorBox(null, 'hoverBackgroundColors')"></p-button>
                            <p-button [label]="tranService.translate('global.button.addDefault')" *ngIf="dataConfig.hoverBackgroundColors.length == 0"
                                    styleClass="p-button-secondary p-button-outlined ml-4 w-12"
                                    (click)="addDefaultHoverBackgroundColor('hoverBackgroundColors')"></p-button>
                        </div>
                    </div>
                    <!--  hoverBackGroundColor -->
                    <div class="w-full field grid" *ngIf="dataConfig.hoverBackgroundColors.length > 0">
                        <label htmlFor="keyDataset" class="col-fixed" style="width:150px"></label>
                        <div class="col flex flex-row flex-wrap justify-content-start align-items-center">
                            <div *ngFor="let color of dataConfig.hoverBackgroundColors;let i = index"
                                [style]="{backgroundColor: color, width: '24px', height: '24px'}" class="mr-2"
                                [pTooltip]="tranService.translate('global.button.view')"
                                (click)="openChooseColorBox(i, 'hoverBackgroundColors')"
                            ></div>
                            <p-button [pTooltip]="tranService.translate('global.button.clear')" icon="pi pi-times"
                                    styleClass="p-button-secondary p-button-outlined ml-2 p-button-rounded" [style]="{width: '24px', height: '24px'}"
                                    [disabled]="!dataConfig.hoverBackgroundColors"
                                    (click)="dataConfig.hoverBackgroundColors = []"></p-button>
                        </div>
                    </div>
                    <!-- hoverBorderColor -->
                    <div class="w-full field grid">
                        <label htmlFor="datasetColorItem" class="col-fixed" style="width:150px">Hover Border Color</label>
                        <div class="col flex flex-row flex-wrap justify-content-start align-items-center">
                            <p-button [label]="tranService.translate('global.button.add2')"
                                    styleClass="p-button-secondary p-button-outlined ml-2 w-12"
                                    (click)="openChooseColorBox(null, 'hoverBorderColors')"></p-button>
                        </div>
                    </div>
                    <!--  hoverBorderColor -->
                    <div class="w-full field grid" *ngIf="dataConfig.hoverBorderColors.length > 0">
                        <label htmlFor="keyDataset" class="col-fixed" style="width:150px"></label>
                        <div class="col flex flex-row flex-wrap justify-content-start align-items-center">
                            <div *ngFor="let color of dataConfig.hoverBorderColors;let i = index"
                                [style]="{backgroundColor: color, width: '24px', height: '24px'}" class="mr-2"
                                [pTooltip]="tranService.translate('global.button.view')"
                                (click)="openChooseColorBox(i, 'hoverBorderColors')"
                            ></div>
                            <p-button [pTooltip]="tranService.translate('global.button.clear')" icon="pi pi-times"
                                    styleClass="p-button-secondary p-button-outlined ml-2 p-button-rounded" [style]="{width: '24px', height: '24px'}"
                                    [disabled]="!dataConfig.hoverBorderColors"
                                    (click)="dataConfig.hoverBorderColors = []"></p-button>
                        </div>
                    </div>
                    <!-- hover border width -->
                    <div class="w-full field grid">
                        <label htmlFor="hoverBorderWidth" class="col-fixed" style="width:150px">Hover Border Width</label>
                        <div class="col flex flex-row justify-content-start align-items-center" style="max-width:calc(100% - 150px)">
                            <p-inputNumber class="flex-1"  [(ngModel)]="dataConfig.hoverBorderWidth" inputId="minmax" mode="decimal" [min]="0"
                                formControlName="hoverBorderWidth" suffix=" px"
                            > </p-inputNumber>
                        </div>
                    </div>
                    <!-- pointColor -->
                    <div class="w-full field grid">
                        <label htmlFor="datasetColorItem" class="col-fixed" style="width:150px">Point Color</label>
                        <div class="col flex flex-row flex-wrap justify-content-start align-items-center">
                            <p-button [label]="tranService.translate('global.button.add2')"
                                    styleClass="p-button-secondary p-button-outlined ml-2 w-12"
                                    (click)="openChooseColorBox(null, 'pointColors')"></p-button>
                        </div>
                    </div>
                    <!--  pointColor -->
                    <div class="w-full field grid" *ngIf="dataConfig.pointColors.length > 0">
                        <label htmlFor="keyDataset" class="col-fixed" style="width:150px"></label>
                        <div class="col flex flex-row flex-wrap justify-content-start align-items-center">
                            <div *ngFor="let color of dataConfig.pointColors;let i = index"
                                [style]="{backgroundColor: color, width: '24px', height: '24px'}" class="mr-2"
                                [pTooltip]="tranService.translate('global.button.view')"
                                (click)="openChooseColorBox(i, 'pointColors')"
                            ></div>
                            <p-button [pTooltip]="tranService.translate('global.button.clear')" icon="pi pi-times"
                                    styleClass="p-button-secondary p-button-outlined ml-2 p-button-rounded" [style]="{width: '24px', height: '24px'}"
                                    [disabled]="!dataConfig.pointColors"
                                    (click)="dataConfig.pointColors = []"></p-button>
                        </div>
                    </div>
                    <!-- pointBorderColor -->
                    <div class="w-full field grid">
                        <label htmlFor="datasetColorItem" class="col-fixed" style="width:150px">Point Border Color</label>
                        <div class="col flex flex-row flex-wrap justify-content-start align-items-center">
                            <p-button [label]="tranService.translate('global.button.add2')"
                                    styleClass="p-button-secondary p-button-outlined ml-2 w-12"
                                    (click)="openChooseColorBox(null, 'pointBorderColors')"></p-button>
                        </div>
                    </div>
                    <!--  pointBorderColor -->
                    <div class="w-full field grid" *ngIf="dataConfig.pointBorderColors.length > 0">
                        <label htmlFor="keyDataset" class="col-fixed" style="width:150px"></label>
                        <div class="col flex flex-row flex-wrap justify-content-start align-items-center">
                            <div *ngFor="let color of dataConfig.pointBorderColors;let i = index"
                                [style]="{backgroundColor: color, width: '24px', height: '24px'}" class="mr-2"
                                [pTooltip]="tranService.translate('global.button.view')"
                                (click)="openChooseColorBox(i, 'pointBorderColors')"
                            ></div>
                            <p-button [pTooltip]="tranService.translate('global.button.clear')" icon="pi pi-times"
                                    styleClass="p-button-secondary p-button-outlined ml-2 p-button-rounded" [style]="{width: '24px', height: '24px'}"
                                    [disabled]="!dataConfig.pointBorderColors"
                                    (click)="dataConfig.pointBorderColors = []"></p-button>
                        </div>
                    </div>
                    <!-- point border width -->
                    <div class="w-full field grid">
                        <label htmlFor="pointBorderWidth" class="col-fixed" style="width:150px">Point Border Width</label>
                        <div class="col flex flex-row justify-content-start align-items-center" style="max-width:calc(100% - 150px)">
                            <p-inputNumber class="flex-1"  [(ngModel)]="dataConfig.pointBorderWidth" inputId="minmax" mode="decimal" [min]="0"
                                formControlName="pointBorderWidth" suffix=" px"
                            > </p-inputNumber>
                        </div>
                    </div>
                </div>
            </div>
        </form>
        <p-divider styleClass="flex mt-4" [align]="'center'"></p-divider>
        <div class="w-full flex flex-row justify-content-center align-items-center">
            <p-button [label]="tranService.translate('global.button.save')"
                        styleClass="p-button-info ml-2 w-auto"
                        [disabled]="dataConfigForm.invalid"
                        (click)="saveDataConfig()"
            ></p-button>
        </div>
    </p-dialog>
</div>

<div class="chooseColor flex justify-content-center dialog-vnpt" *ngIf="colorConfigForm">
    <p-dialog header="Color Box" [(visible)]="isShowChooseColor" [modal]="true" [style]="{ width: '500px',top: 0 }" [draggable]="false" [resizable]="false">
        <div class="w-full field grid p-0 m-0">
            <form [formGroup]="colorConfigForm" class="w-full">
                <div class="w-full field grid">
                    <label htmlFor="color" class="col-fixed" style="width:150px">Color</label>
                    <div class="col flex flex-row flex-wrap justify-content-start align-items-center">
                        <input class="flex-1" style="height: 36px; border-radius: 8px; border: 1px solid #ced4da; background-color: white; padding: 0px 10px;"
                                type="color" id="color"
                                [(ngModel)]="colorConfig.color"
                                formControlName="color"
                        />
                    </div>
                </div>
                <div class="w-full field grid">
                    <label htmlFor="opacity" class="col-fixed" style="width:150px">Opacity</label>
                    <div class="col flex flex-row flex-wrap justify-content-start align-items-center">
                        <p-inputNumber class="flex-1" [(ngModel)]="colorConfig.opacity" inputId="minmax" mode="decimal" [min]="0" [max]="255"
                            formControlName="opacity"
                        > </p-inputNumber>
                    </div>
                </div>
                <div class="w-full field grid">
                    <p-button [label]="tranService.translate('global.button.delete')" *ngIf="colorConfig.index"
                                styleClass="p-button-secondary p-button-outlined ml-2 w-12"
                                [disabled]="!dataConfig.datasetColorItem"
                                (click)="dataConfig[colorConfig.key].splice(colorConfig.index, 1);isShowChooseColor = false;"></p-button>
                    <p-button [label]="tranService.translate('global.button.save')"
                                styleClass="p-button-info ml-2 w-12"
                                (click)="saveColor()"></p-button>
                </div>
            </form>
        </div>
    </p-dialog>
</div>

<div class="configFont flex justify-content-center dialog-vnpt" *ngIf="fontConfigForm">
    <p-dialog header="Color Box" [(visible)]="isShowConfigFont" [modal]="true" [style]="{ width: '700px',top: 0 }" [draggable]="false" [resizable]="false">
        <div class="w-full field grid p-0 m-0">
            <form [formGroup]="fontConfigForm" class="w-full">
                <!-- family -->
                <div class="w-full field grid">
                    <label htmlFor="family" class="col-fixed" style="width:150px">Font Family</label>
                    <div class="col">
                        <p-dropdown styleClass="w-full"
                                [showClear]="false"
                                id="family" [autoDisplayFirst]="false"
                                [(ngModel)]="fontConfig.family"
                                formControlName="family"
                                [options]="fontFamilys"
                                optionLabel="name"
                                optionValue="value"
                                [placeholder]="'Font Family'"
                        ></p-dropdown>
                    </div>
                </div>
                <!-- lineHeight -->
                <div class="w-full field grid">
                    <label htmlFor="lineHeight" class="col-fixed" style="width:150px">Line Height</label>
                    <div class="col ">
                        <p-inputNumber class="flex-1" [(ngModel)]="fontConfig.lineHeight"  mode="decimal" [min]="1" [minFractionDigits]="2"
                            formControlName="lineHeight"
                        > </p-inputNumber>
                    </div>
                </div>
                 <!-- size -->
                 <div class="w-full field grid">
                    <label htmlFor="size" class="col-fixed" style="width:150px">Font Size</label>
                    <div class="col">
                        <p-inputNumber class="flex-1" [(ngModel)]="fontConfig.size"  mode="decimal" [min]="11"
                            formControlName="size" suffix="px"
                        > </p-inputNumber>
                    </div>
                </div>
                <!-- style -->
                <div class="w-full field grid">
                    <label htmlFor="style" class="col-fixed" style="width:150px">Font Style</label>
                    <div class="col">
                        <p-dropdown styleClass="w-full"
                                [showClear]="false"
                                id="style" [autoDisplayFirst]="false"
                                [(ngModel)]="fontConfig.style"
                                formControlName="style"
                                [options]="fontStyles"
                                optionLabel="name"
                                optionValue="value"
                                [placeholder]="'Font Style'"
                        ></p-dropdown>
                    </div>
                </div>
                <!-- weight -->
                <div class="w-full field grid">
                    <label htmlFor="weight" class="col-fixed" style="width:150px">Font Weight</label>
                    <div class="col">
                        <p-dropdown styleClass="w-full"
                                [showClear]="false"
                                id="weight" [autoDisplayFirst]="false"
                                [(ngModel)]="fontConfig.weight"
                                formControlName="weight"
                                [options]="fontWeights"
                                optionLabel="name"
                                optionValue="value"
                                [placeholder]="'Font Weight'"
                        ></p-dropdown>
                    </div>
                </div>
                <div class="w-full field grid">
                    <p-button [label]="tranService.translate('global.button.save')"
                                styleClass="p-button-info ml-2 w-12"
                                (click)="saveFont()"></p-button>
                </div>
            </form>
        </div>
    </p-dialog>
</div>

