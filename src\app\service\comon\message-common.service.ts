import { ObservableService } from "./observable.service";
import { Injectable, Inject } from "@angular/core";
import { CONSTANTS } from "./constants";
@Injectable({ providedIn: 'root'})
export class MessageCommonService {
    public isloading: boolean;

    constructor(@Inject(ObservableService) private observableService: ObservableService) { 
        
    }

    public confirm(title:string, message: string, action:{ok?:Function, cancel?:Function}){
        this.observableService.next(CONSTANTS.OBSERVABLE.KEY_MESSAGE_COMMON,{message,title, action, type: "confirm", isShow: true})
    }

    public success(message: string, summary?:string, timeout?:number){
        this.observableService.next(CONSTANTS.OBSERVABLE.KEY_MESSAGE_COMMON,{message, summary, timeout, type: "success", isShow: true})
    }

    public info(message: string, summary?:string, timeout?:number){
        this.observableService.next(CONSTANTS.OBSERVABLE.KEY_MESSAGE_COMMON,{message, summary, timeout, type: "info", isShow: true})
    }

    public warning(message: string, summary?:string, timeout?:number){
        this.observableService.next(CONSTANTS.OBSERVABLE.KEY_MESSAGE_COMMON,{message, summary, timeout, type: "warning", isShow: true})
    }

    public error(message: string, summary?:string, timeout?:number){
        this.observableService.next(CONSTANTS.OBSERVABLE.KEY_MESSAGE_COMMON,{message, summary, timeout, type: "error", isShow: true})
    }

    public onload(){
        this.isloading = true;
        this.observableService.next(CONSTANTS.OBSERVABLE.KEY_MESSAGE_COMMON,{type: "load", isShow: true})
    }

    public offload(){
        this.isloading = false;
        this.observableService.next(CONSTANTS.OBSERVABLE.KEY_MESSAGE_COMMON,{type: "load", isShow: false})
    }
}