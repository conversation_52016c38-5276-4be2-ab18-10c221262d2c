import { AbstractControl, AsyncValidatorFn, ValidationErrors, ValidatorFn } from '@angular/forms';
import { Observable, of } from 'rxjs';
import { map, catchError, debounceTime, switchMap, take } from 'rxjs/operators';

export function digitValidator(digitCount: number): ValidatorFn {
  return (control: AbstractControl): ValidationErrors | null => {
    const value = control.value;
    const regex = new RegExp(`^\\d{${digitCount}}$`);
    const isValid = regex.test(value);
    return isValid ? null : { digitCount: true };
  };
}

export function numericLengthValidator(minLength: number, maxLength: number): ValidatorFn {
  return (control: AbstractControl): ValidationErrors | null => {
    const value = control.value;

    // Kiểm tra nếu giá trị không tồn tại hoặc không phải là chuỗi
    if (!value || typeof value !== 'string') {
      return { numericLength: { requiredLength: `${minLength}-${maxLength}`, actualLength: 0 } };
    }

    const regex = new RegExp(`^\\d{${minLength},${maxLength}}$`);
    const isValid = regex.test(value);
    return isValid ? null : { numericLength: { requiredLength: `${minLength}-${maxLength}`, actualLength: value.length } };
  };
}

export function numericMinLengthValidator(minLength: number): ValidatorFn {
  return (control: AbstractControl): ValidationErrors | null => {
    const value = control.value;
    if (!value || typeof value !== 'string') {
      return { numericLength: { requiredLength: minLength, actualLength: 0 } };
    }
    const isValid = value.length >= minLength && /^\d+$/.test(value);
    return isValid ? null : { numericMinLength: { requiredLength: minLength, actualLength: value.length } };
  };
}

export function numericMaxLengthValidator(maxLength: number): ValidatorFn {
  return (control: AbstractControl): ValidationErrors | null => {
    const value = control.value;

    // Allow empty values or values that are not strings
    if (!value || typeof value !== 'string') {
      return null;
    }

    // Check if the value is numeric
    if (/^\d+$/.test(value)) {
      const isValid = value.length <= maxLength;
      return isValid ? null : { numericMaxLength: { requiredLength: maxLength, actualLength: value.length } };
    }

    // If the value is not numeric, do not apply any validation errors
    return null;
  };
}

export function checkExistedStaticList(list: string[]): ValidatorFn {
    return (control: AbstractControl): { [key: string]: any } | null => {
      const value = control.value;
      if (!value) {
        return null; // Không kiểm tra nếu giá trị là null hoặc undefined
      }
      const duplicateItem = list.find(item => item === value);
      return duplicateItem ? { 'duplicateItem': { value: value } } : null;
    };
}

function validateData(data: any, service: any): Observable<boolean> {
  console.log("Trigger API call");
  let isValid = true;
  return new Observable<boolean>((observer) => {
    service.checkExisted([data], (response) => {
      console.log(response);
      if (response === 0) {
        observer.next(true);
      } else if(response > 0) {
        observer.next(false);
      }
      observer.complete();
    });
  });
}

export function checkExistedDynamicListNumber(service: any, debounceTimeValue: number = 300): AsyncValidatorFn {
  return (control: AbstractControl): Observable<ValidationErrors | null> => {
    const value = control.value;
    if (!value) {
      return of(null); // Không kiểm tra nếu giá trị là null hoặc undefined
    }
    return validateData(control.value, service).pipe(
      map((isValid: boolean) => {
        return isValid ? null : { 'existed': true };
      })
    )
  };
}

export function checkExistedDynamicListArray(service: any, debounceTimeValue: number = 300): AsyncValidatorFn {
  return (control: AbstractControl): Observable<ValidationErrors | null> => {
    const value = control.value;
    if (!value) {
      return of(null); // Không kiểm tra nếu giá trị là null hoặc undefined
    }
    return control.valueChanges.pipe(
      debounceTime(debounceTimeValue), // Chờ 300ms sau khi người dùng nhập xong trước khi gọi service
      switchMap(val => {
        let obs = new Observable(observer => {
          service.checkExisted([val], (response) => {
            observer.next(response);
            observer.complete();
          });
        });
        return obs
      }),
      take(1),
      map((res: any) => {
        if (Array.isArray(res) && res.length === 0) {
          return null;
        }else{
          return { 'existed': true };
        }
      }),
      catchError((error) => {
        console.log(error)
        return error
      })
    );
  };
}

export function noneWhitespaceValidator(): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
        const isWhitespace = (control.value || '').trim().length === 0;
        const isValid = !isWhitespace;
        return isValid ? null : { whitespace: true };
    };
}
