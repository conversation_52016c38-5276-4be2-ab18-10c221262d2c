import {Injectable} from '@angular/core';
import {HttpClient} from '@angular/common/http';
import {debounceTime, map, switchMap} from 'rxjs/operators';
import {Observable, Subject} from 'rxjs';

@Injectable({providedIn: 'root'})
export class PhotonService {
    private searchSubject = new Subject<string>();

    constructor(private http: HttpClient) {
    }

    search(query: string): Observable<any> {
        return this.http.get(`https://photon.komoot.io/api/?q=${encodeURIComponent(query)}&limit=10`)
            .pipe(
                map((response: any) => {
                    return response.features.map((feature: any) => {
                        const props = feature.properties;
                        const coords = feature.geometry.coordinates;

                        return {
                            name: props.name + (props.locality ? ', ' + props.locality : '') + (props.district ? ', ' + props.district : '') + (props.city ? ', ' + props.city : ''),
                            coord: coords,
                        };
                    });
                })
            );
    }

    searchStream(): Observable<any> {
        return this.searchSubject.pipe(
            debounceTime(300),
            switchMap(query => this.search(query))
        );
    }

    updateQuery(query: string) {
        this.searchSubject.next(query);
    }
}
