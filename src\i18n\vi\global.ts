export default {
    text:{
        templateTextPagination: "Hiển thị {first} tới {last} của {totalRecords} bản ghi",
        stt: "STT",
        page: "Trang",
        action: "<PERSON><PERSON> tác",
        nodata: "Không có kết quả phù hợp",
        itemselected: "Đã chọn:",
        filter: "Bộ lọc",
        advanceSearch: "Tìm kiếm nâng cao",
        createGroupSim: "Tạo nhóm thuê bao",
        createGroupSimAndPushSimToGroup: "Tạo nhóm thuê bao và gán thuê bao vào nhóm",
        all: "Tất cả",
        resultRegister: "Kết quả đăng ký",
        inputText: "Nhập dữ liệu chuỗi",
        inputNumber: "Nhập dữ liệu số",
        selectValue: "Chọn giá trị",
        inputDate: "Chọn ngày",
        inputTimestamp: "Chọn thời gian",
        homepage: "Trang chủ",
        selectOption: "Chọn giá trị",
        selectMoreItem: "+${maxDisplay} giá trị được chọn",
        clearSelected:"Xoá các mục đã chọn",
        textCaptcha: "Kéo sang để hoàn thành câu đố",
        readandagree: "Tôi đã đọc và đồng ý với",
        changeManageData: "Chuyển quyền quản lý dữ liệu khách hàng cho GDV",
    },
    field: {

    },
    lang: {
        vi: "Tiếng Việt",
        en: "Tiếng Anh"
    },
    menu:{
        accountmgmt: "Quản lý khách hàng",
        listaccount: "Danh sách khách hàng",
        listpermission: "Danh sách quyền",
        billmgmt: "Quản lý hợp đồng",
        listbill: "Danh sách hợp đồng",
        configuration: "Cấu hình",
        customermgmt: "Quản lý khách hàng",
        listcustomer: "Danh sách khách hàng",
        dashboard: "Dashboard",
        devicemgmt: "Quản lý thiết bị",
        listdevice: "Danh sách thiết bị",
        extraservice: "Dịch vụ mở rộng",
        guide: "Hướng dẫn",
        manual: "Hướng dẫn sử dụng",
        log: "Nhật ký",
        ordermgmt: "Quản lý đơn hàng",
        listorder: "Danh sách đơn hàng",
        report: "Báo cáo",
        dynamicreport: "Cấu hình báo cáo động",
        dynamicreportgroup: "Nhóm báo cáo động",
        simmgmt: "Quản lý thuê bao",
        listsim: "Danh sách thuê bao",
        subscriptionmgmt: "Quản lý gói cước",
        listsubscription: "Danh sách gói cước",
        troubleshoot: "Sự cố",
        listroles: "Danh sách nhóm quyền",
        listpermissions: "Danh sách quyền",
        detailroles: "Chi tiết nhóm quyền",
        editroles: "Chỉnh sửa nhóm quyền",
        groupSim: "Nhóm thuê bao",
        contract:"Danh sách hợp đồng",
        ratingplanmgmt: "Quản lý gói cước",
        listplan: "Danh sách gói cước",
        registerplan: "Đăng ký gói cước",
        detailplan: "Chi tiết gói cước",
        historyRegister: "Lịch sử đăng ký",
        apnsim: "APN thuê bao",
        apnsimlist: "Danh sách APN thuê bao",
        apnsimdetail: "Chi tiết APN thuê bao",
        account : "Tài khoản",
        detailAccount : "Thông tin tài khoản",
        editAccount : "Cập nhật",
        rule: "Quản lý cảnh báo",
        alerts: "Cảnh báo",
        alertreceivinggroup: "Nhóm nhận cảnh báo",
        alerthistory: "Lịch sử cảnh báo",
        devicedetail: "Chi tiết thiết bị",
        deviceupdate: "Cập nhật thiết bị",
        devicecreate: "Tạo mới thiết bị",
        changePass: "Đổi mật khẩu",
        alert: "Cảnh báo",
        alertSettings: "Danh sách cảnh báo",
        alertReceivingGroup: "Nhóm nhận cảnh báo",
        alertHistory: "Lịch sử cảnh báo",
        alertList: "Danh sách cảnh báo",
        groupReceiving: "Nhóm nhận cảnh báo",
        groupReceivingList: "Danh sách nhóm nhận cảnh báo",
        reportGroupReceivingList: "Danh sách nhóm nhận báo cáo động",
        termpolicy: "Điều khoản và chính sách",
        termpolicyhistory: "Lịch sử xác nhận chính sách",
        cmpManagement: "",
        charts: "Cấu hình biểu đồ",
        chartList: "Danh sách cấu hình biểu đồ",
        trafficManagement: "Quản lý chia sẻ lưu lượng",
        subTrafficManagement: "Quản lý ví lưu lượng",
        walletList: "Danh sách ví lưu lượng",
        shareManagement: "Quản lý chia sẻ",
        shareList:"Danh sách chia sẻ",
        walletConfig: "Cấu hình quản lý lưu lượng",
        historyWallet: "Lịch sử hoạt động",
        listGroupSub: "Nhóm chia sẻ",
        autoShareGroup: "Nhóm chia sẻ tự động",
        apiLogs: "Nhật ký sử dụng API",
        detailDevice: "Chi tiết thiết bị"
    },
    button: {
        export: "Xuất file",
        exportSelect: "Xuất CSV các mục được chọn",
        exportFilter: "Xuất CSV toàn bộ danh sách",
        exportExelSelect: "Xuất Excel các mục được chọn",
        exportExelFilter: "Xuất Excel toàn bộ danh sách",
        pushGroupSim: "Gán thuê bao vào nhóm",
        pushToGroupAvailable: "Gán vào nhóm có sẵn",
        pushToNewGroup: "Gán vào nhóm mới",
        cancel: "Hủy",
        registerRatingPlan: "Đăng ký gói cước",
        changeRatingPlan: "Đổi gói cước",
        cancelRatingPlan: "Hủy gói cước",
        assignPlan: "Gán gói cước",
        historyRegisterPlan: "Lịch sử đăng ký gói cước",
        registerPlanForGroup: "Đăng ký gói cước cho nhóm thuê bao",
        registerPlanByFile: "Đăng ký gói cước bằng nhập file",
        create: "Tạo mới",
        edit: "Sửa",
        yes: "Có",
        agree: "Đồng Ý",
        no: "Không",
        save: "Lưu",
        changeStatus: "Chuyển trạng thái",
        delete: "Xóa",
        active: "Kích hoạt",
        approve: "Phê duyệt",
        suspend: "Tạm ngưng",
        uploadFile: "Kéo thả file hoặc chọn file trong hệ thống",
        upFile: "Upload File",
        downloadTemp: "Tải file mẫu",
        back: "Quay lại",
        add: "Tạo mới",
        add2: "Thêm",
        addDefault: "Mặc định",
        view: "Xem chi tiết",
        viewDeviceOwner: "Xem danh sách thiết bị sở hữu",
        import: "Nhập bằng file",
        changePass : "Đổi mật khẩu",
        update: "Cập nhật",
        copy: "Sao chép",
        reset: "Khôi phục",
        clear: "Xóa tất cả",
        preview: "Xem trước",
        pushUp: "Đẩy lên",
        pushDown: "Đẩy xuống",
        confirm: "Xác nhận",
        changeManageData: "Chuyển quyền quản lý dữ liệu",
        addSubToGroup: "Thêm từng thuê bao",
        deleteSubInGroup: "Xóa thuê bao",
        control: "Điều khiển",
    },
    message:{
        copied: "Đã sao chép",
        required: "Trường này là bắt buộc",
        requiredField: "${field} là bắt buộc",
        maxLength: "Trường này không được vượt quá ${len} ký tự",
        minLength: "Trường này không được ít hơn ${len} ký tự",
        max: "Trường này có giá trị tối đa là ${value}",
        min: "Trường này có giá trị tối thiểu là ${value}",
        numbericMin: "Trường này không được ít hơn ${length} ký tự số",
        numbericMax: "Trường này không được vượt quá ${length} ký tự số",
        duplicated: "Dữ liệu đã tồn tại",
        invalidValue: "Dữ liệu không đúng định dạng",
        formatContainVN: "Sai định dạng. Chỉ cho phép (a-z, A-Z, 0-9, . - _, dấu cách, tiếng Việt)",
        formatTaxCode: "Mã số thuế không hợp lệ. Vui lòng nhập 10 số hoặc 10 số kèm theo - và 3 số",
        formatPassword: "Sai định dạng. Mật khẩu phải có ít nhất 1 ký tự số, 1 ký tự viết hoa, 1 ký tự đặc biệt và không cho phép dấu cách",
        formatCode: "Sai định dạng. Chỉ cho phép (a-z, A-Z, 0-9, - _)",
        formatCodeNotSub: "Sai định dạng. Chỉ cho phép (a-z, A-Z, 0-9, _)",
        formatBusinessName: "Tên doanh nghiệp chỉ được chứa chữ, số, khoảng trắng và các ký tự như . , - / & ( ).')",
        invalidEmail: "Email sai định dạng",
        invalidAddress: "Địa chỉ sai định dạng",
        formatEmail: "Định dạng email phải là <EMAIL>",
        invalidPhone: "Số điện thoại sai định dạng",
        formatPhone: "Số điện thoại phải là số có đầu 0 (10-11 kí tự) hoặc 84 (11-12 kí tự)",
        invalidSubsciption: "Thuê bao không đúng định dạng",
        exists: "Đã tồn tại ${type} này",
        success: "Thao tác thành công",
        error: "Thao tác thất bại",
        saveSuccess: "Lưu thành công",
        saveError: "Lưu thất bại",
        addGroupSuccess: "Gán thuê bao vào nhóm thành công",
        timeout: "Thao tác quá thời gian",
        errorMatchCaptcha: "Đặt miếng ghép vào đúng vị trí của nó",
        confirmDeleteAccount: "Bạn có chắc chắc muốn xóa khách hàng này không?",
        titleConfirmDeleteAccount: "Xóa khách hàng",
        confirmDeletePlan: "Bạn có chắc chắc muốn xóa gói cước này không?",
        titleConfirmDeletePlan: "Xóa gói cước",
        deleteSuccess: "Xóa thành công",
        deleteFail: "Xóa thất bại",
        confirmChangeStatusAccount: "Bạn có chắc chắn muốn chuyển trạng thái cho tài khoản này không?",
        confirmChangeStatusAlert: "Bạn có chắc chắn muốn chuyển trạng thái cho cảnh báo này không?",
        titleConfirmChangeStatusAccount: "Chuyển trạng thái tài khoản",
        titleConfirmChangeStatusAlert: "Chuyển trạng thái cảnh báo",
        changeStatusSuccess: "Chuyển trạng thái thành công",
        changeStatusFail: "Chuyển trạng thái thất bại",
        titleConfirmDeleteRoles: "Xóa nhóm quyền",
        titleConfirmDeleteAlert: "Xoá cảnh báo",
        confirmDeleteRoles: "Bạn có chắc chắn muốn xóa nhóm quyền này không?",
        confirmDeleteAlert: "Bạn có chắc chắn muốn xóa cảnh báo này không?",
        titleConfirmChangeStatusRole: "Chuyển trạng thái nhóm quyền",
        confirmChangeStatusRole: "Bạn có chắc chắn muốn chuyển trạng thái cho nhóm quyền này không?",
        conditionExportChoose: "Giới hạn 1 triệu dòng",
        conditionExportFilter: "Số lượng thuê bao tối đa không được vượt quá 1 triệu",
        conditionExportExelFilter: "Danh sách xuất file vượt quá 100 nghìn bản ghi",
        conditionExportFilterEmpty: "Chưa có thuê bao nào được chọn",
        titleConfirmActivePlan: "Bạn có chắc chắn muốn kích hoạt gói cước này không?",
        confirmActivePlan: "Kích hoạt gói cước",
        titleConfirmApprovePlan: "Bạn có chắc chắn muốn phê duyệt gói cước này không?",
        confirmApprovePlan: "Phê duyệt gói cước",
        titleConfirmSuspendPlan: "Bạn có chắc chắn muốn tạm ngưng gói cước này không?",
        confirmSuspendPlan: "Tạm ngưng gói cước",
        titleConfirmDeleteDevice: "Xóa thiết bị",
        confirmDeleteDevice: "Bạn có chắc chắn muốn xóa thiết bị này không",
        activeSuccess: "Kích hoạt thành công",
        approveSuccess: "Phê duyệt thành công",
        suspendSuuccess: "Tạm ngưng thành công",
        activeError: "Kích hoạt không thành công",
        approveError: "Phê duyệt không thành công",
        maxSizeRecordRow: "Số lượng bản ghi quá giới hạn ${row}",
        wrongFileExcel: "Sai định dạng file, hãy nhập file excel",
        invalidFile: "Định dạng file không hợp lệ",
        planNotExists: "Gói cước không tồn tại",
        planNoPermit: "Không có quyền trên gói cước",
        titleConfirmDeleteAlertReceivingGroup: "Xoá nhóm nhận cảnh báo",
        titleConfirmDeleteReportReceivingGroup: "Xoá nhóm nhận báo cáo động",
        titleConfirmDeleteShareGroup: "Xoá nhóm chia sẻ",
        confirmDeleteAlertReceivingGroup: "Bạn có chắc muốn xoá cảnh báo nhóm nhận cảnh báo này không?",
        confirmDeleteReportReceivingGroup: "Bạn có chắc muốn xoá nhóm nhận báo cáo động này không?",
        confirmDeleteShareGroup: "Bạn có chắc muốn xoá nhóm chia sẻ này không?",
        invalidinformation64: "Thông tin không hợp lệ. Vui lòng nhập từ 2 đến 64 ký tự không bao gồm ký  tự đặc biệt",
        invalidinformation32: "Thông tin không hợp lệ. Vui lòng nhập từ 2 đến 32 ký tự không bao gồm ký  tự đặc biệt",
        invalidPasswordFomat : "Mật khẩu 6-20 ký tự, bao gồm ít nhất 1 chữ cái và 1 số và 1 ký tự đặc biệt",
        passwordNotMatch: "Mật khẩu không khớp",
        wrongCurrentPassword : "Mật khẩu hiện tại sai!",
        forgotPassSendMailSuccess : "Mật khẩu khôi phục đã được gửi tới email của bạn. Xin vui lòng kiểm tra email!.",
        notPermissionMisidn: "Số thuê bao đã được gán cho thiết bị khác hoặc Không có quyền đối với thuê bao, vui lòng nhập lại",
        titleConfirmDeleteReport: "Xoá báo cáo",
        confirmDeleteReport: "Bạn có chắc muốn xoá báo cáo này?",
        titleConfirmChangeStatusReport: "Thay đổi trạng thái báo cáo",
        confirmChangeStatusReport: "Bạn có chắc muốn thay đổi trạng thái báo cáo này?",
        confirmCancelPlan: "Bạn có chắc muốn hủy gói cước \"${planName}\" cho thuê bao ${msisdn}?",
        accuracySuccess:"Xác thực ví thành công",
        accuracyFail: "Xác thực ví thất bại",
        twentydigitlength: "Trường này không được vượt quá 10 ký tự số",
        oneHundredLength : "Trường này không được vượt quá giá trị 100",
        onlySelectGroupOrSub : "Bạn chỉ được phép nhập Nhóm thuê bao hoặc Thuê bao",
        limitEmail: "Chỉ được nhập tối đa ${limit} email",
        limitSms : "Chỉ được nhập tối đa ${limit} số điện thoại",
        emailExist : "Email đã tồn tại",
        phoneExist : "Số điện thoại đã tồn tại",
        urlNotValid : "URL không đúng định dạng",
        onlyPositiveInteger : "Chỉ cho phép nhập số nguyên dương",
        maxsizeupload:"Dung lượng vượt quá dung lượng tối đa",
        titleRejectPolicy: "XÁC NHẬN VỀ VIỆC PHẢN ĐỐI - HẠN CHẾ - RÚT LẠI SỰ ĐỒNG Ý XỬ LÝ DỮ LIỆU CÁ NHÂN",
        messageRejectPolicy1: "Kính gửi Quý khách hàng,",
        messageRejectPolicy2: "Khách hàng có quyền phản đối, hạn chế hoặc rút lại sự đồng ý xử lý Dữ liệu cá nhân của Khách hàng. Tuy nhiên, việc phản đối, hạn chế hoặc rút lại sự đồng ý xử lý Dữ liệu cá nhân của Khách hàng có thể dẫn tới việc VNPT/Công ty con của VNPT không thể cung cấp Sản phẩm, dịch vụ cho Khách hàng, điều này đồng nghĩa với việc VNPT/Công ty con của VNPT có thể đơn phương chấm dứt hợp đồng mà không cần phải bồi thường cho Khách hàng do các điều kiện để thực hiện hợp đồng đã thay đổi. Do đó, VNPT/Công ty con của VNPT khuyến nghị Khách hàng cân nhắc kĩ lưỡng trước khi phản đối, hạn chế hoặc rút lại sự đồng ý xử lý Dữ liệu cá nhân của Khách hàng",
        messageRejectPolicy3: " Tôi đã đọc và đồng ý với việc Phản đối, hạn chế, rút lại sự đồng ý xử lý dữ liệu cá nhân",
        confirmationHistory: "Lịch sử xác nhận Chính sách bảo vệ dữ liệu cá nhân",
        confirmationUserInfo: "Thông tin tài khoản xác nhận",
        confirmationDevice: "Thông tin thiết bị xác nhận",
        wrongFormatName: "Sai định dạng. Chỉ cho phép dấu cách, chữ tiếng Việt (a-z, A-Z, 0-9, - _)",
        wrongFormatNameVietNam: "Sai định dạng. Chỉ cho phép dấu cách, chữ tiếng Việt",
        notChartData: "Không có dữ liệu",
        isErrorQuery: "Lỗi query",
        errorLoading: "Có lỗi trong quá trình hiển thị dữ liệu, mời thử lại"
    },
    searchSeperate:{
        button:{
            add:"Thêm bộ lọc",
            reset:"Reset bộ lọc"
        },
        placeholder:{
            dropdownFlter:"Chọn bộ lọc",
            input:"Tìm kiếm",
            dropdown:"Chọn giá trị",
            calendar:"Chọn ngày",
            rangeCalendar:"Chọn khoảng ngày"
        }
    },
    titlepage: {
        createAlertReceivingGroup: "Tạo nhóm nhận cảnh báo",
        listAlertReceivingGroup: "Nhóm nhận cảnh báo",
        detailAlertReceivingGroup: "Chi tiết nhóm nhận cảnh báo",
        editAlertReceivingGroup: "Sửa nhóm nhận cảnh báo",
        deleteAlertReceivingGroup: "Xoá nhóm nhận cảnh báo",
        listApnSim: "APN Thuê bao",
        detailApnSim: "Chi tiết APN Thuê bao",
        listAlertHistory: "Lịch sử cảnh báo",
        listDevice: "Thiết bị",
        createDevice: "Tạo mới Thiết bị",
        detailDevice: "Chi tiết thiết bị",
        editDevice: "Cập nhật thiết bị",
        deleteDevice: "Xoá thiết bị",
        createaccount: "Tạo tài khoản",
        editaccount: "Chỉnh sửa tài khoản",
        detailaccount: "Thông tin chi tiết tài khoản",
        createRole: "Tạo nhóm quyền",
        detailCustomer: "Thông tin chi tiết khách hàng",
        editCustomer: "Chỉnh sửa thông tin khách hàng",
        detailsim: "Thông tin chi tiết thuê bao",
        listGroupSim: "Danh sách nhóm thuê bao",
        createGroupSim: "Tạo nhóm thuê bao",
        detailGroupSim: "Thông tin chi tiết nhóm thuê bao",
        editGroupSim: "Chỉnh sửa nhóm thuê bao",
        listContract: "Danh sách hợp đồng",
        createRatingPlan: "Tạo gói cước",
        editRatingPlan: "Chỉnh sửa gói cước",
        historyRegisterPlan: "Danh sách lịch sử đăng ký gói cước",
        createAlarm: "Tạo cảnh báo",
        detailAlarm: "Thông tin chi tiết cảnh báo",
        editAlarm: "Cập nhật cảnh báo",
        reportDynamic: "Danh sách báo cáo động",
        listGroupReportDynamic: "Danh sách nhóm báo cáo động",
        editGroupReportDynamic: "Chỉnh sửa nhóm báo cáo động",
        detailGroupReportDynamic: "Chi tiết nhóm báo cáo động",
        createGroupReportDynamic: "Tạo nhóm báo cáo động",
        m2SubscriptionManagementSystem: "ONE METER",
        apiLogs: "Log API"
    }
}
