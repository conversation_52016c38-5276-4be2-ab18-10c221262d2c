var VNPT = VNPT || {};
VNPT.q = VNPT.q || [];

VNPT.app_key = 'd85b4c47db5bb7abb7236fbbdb4f7ca740226e58';

VNPT.url = 'https://console-smartux.vnpt.vn';

VNPT.q.push(['track_sessions']);
VNPT.q.push(['track_pageview']);
VNPT.q.push(['track_clicks']);
VNPT.q.push(['track_scrolls']);
VNPT.q.push(['track_errors']);
VNPT.q.push(['track_links']);
VNPT.q.push(['track_forms']);
VNPT.q.push(['collect_from_forms']);

(function () {
    const paths = ['https://console-smartux.vnpt.vn/sdk/web/core-track.js', 'https://console-smartux.vnpt.vn/sdk/web/minify.min.js'];
    for (let i in paths) {
        var cly = document.createElement('script');
        cly.type = 'text/javascript';
        cly.async = true;
        cly.src = paths[i];
        cly.onload = i == 0 ? function () {
            VNPT.init()
        } : function () {
            window.minify = require("html-minifier").minify;
        };
        var s = document.getElementsByTagName('script')[0];
        s.parentNode.insertBefore(cly, s);
    }
})();
