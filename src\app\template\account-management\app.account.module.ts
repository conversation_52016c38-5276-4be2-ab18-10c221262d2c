import { NgModule } from "@angular/core";
import { AppAccountListComponent } from "./list/app.account.list.component";
import { AccountService } from "src/app/service/account/AccountService";
import { AppAccountCreateComponent } from "./create/app.account.create.component";
import { AppAccountEditComponent } from "./edit/app.account.edit.component";
import { AppAccountRoutingModule } from "./app.account-routing";
import { CommonModule } from "@angular/common";
import { BreadcrumbModule } from "primeng/breadcrumb";
import { FieldsetModule } from "primeng/fieldset";
import { FormsModule, ReactiveFormsModule } from "@angular/forms";
import { InputTextModule } from "primeng/inputtext";
import { ButtonModule } from "primeng/button";
import { CommonVnptModule } from "../common-module/common.module";
import { SplitButtonModule } from "primeng/splitbutton";
import { AutoCompleteModule } from "primeng/autocomplete";
import { CalendarModule } from "primeng/calendar";
import { DropdownModule } from "primeng/dropdown";
import { CardModule } from "primeng/card";
import { DialogModule } from "primeng/dialog";
import { InputTextareaModule } from 'primeng/inputtextarea';
import { MultiSelectModule } from 'primeng/multiselect';
import { PanelModule } from 'primeng/panel';
import { RadioButtonModule } from 'primeng/radiobutton';
import { CustomerService } from "src/app/service/customer/CustomerService";
import { RolesService } from "src/app/service/account/RolesService";
import {TabViewModule} from "primeng/tabview";
import {APILogService} from "../../service/api-log/APILogService";
import { ProgressSpinnerModule } from 'primeng/progressspinner';
import {InputSwitchModule} from "primeng/inputswitch";
import {PasswordModule} from "primeng/password";
@NgModule({
    imports: [
        AppAccountRoutingModule,
        CommonModule,
        BreadcrumbModule,
        FieldsetModule,
        FormsModule,
        ReactiveFormsModule,
        InputTextModule,
        ButtonModule,
        CommonVnptModule,
        SplitButtonModule,
        AutoCompleteModule,
        CalendarModule,
        DropdownModule,
        CardModule,
        DialogModule,
        InputTextareaModule,
        MultiSelectModule,
        PanelModule,
        RadioButtonModule,
        TabViewModule,
        ProgressSpinnerModule,
        InputSwitchModule,
        PasswordModule
    ],
    declarations: [
        AppAccountListComponent,
        AppAccountCreateComponent,
        AppAccountEditComponent,
    ],
    providers: [
        AccountService,
        CustomerService,
        RolesService,
        APILogService
    ]
})
export class AppAccountModule{}
