export default {
    label: {
        imei: "IMEI/SERIAL",
        address: "Address",
        subcriber: "Subscriber",
        country: "Country",
        category: "Category",
        expireFrom: "Expiration date from",
        expireTo: "Expiration date to",
        expireDate: "Expiration date",
        type: "Device Type",
        msisdn: "Subscription Number",
        note: "Note",
        importByFile: "Import by file",
        iotLink: "Managed by IoT platform",
        name: "Device Name",
        serial: "Serial",
        individualName: "Individual Name",
        businessName: "Business Name",
        lastConnected: "Last Connected",
        statusConnect: "Status Connect",
        info: "Device Info",
        infoMngt: "Info management",
        infoBusinessMngt: "Management business information",
        infoIndividualMngt: "Management personal information",
        businessMngt: "Business management",
        individualMngt: "Individua management",
        manufacturer: "Manufacturer",
        description: "Description",
        model: "Model",
        custInfo: "Customer Info",
        listCmd: "List of Commands",
        telemetryData: "Telemetry Data",
        alert: "Warning",
        location: "Location",
        taxCode: "Tax code",
        headOffice: "Head office",
        representativeName: "Representative name",
        contactAddress: "Contact address",
        email: "Email",
        timeSent: "Time sent",
        timeSentFrom: "Time sent from",
        timeSentTo: "Time sent to",
        cmdCode: "Command code",
        cmdName: "Name",
        cmdType: "Command type",
        data: "Data",
        status: "Status",
        archived: "Archive",
        submitted: "Sent",
        processed: "Processed",
        rejected: "Rejected",
        sampleTime: "Sampling time",
        consumptionValue: "Consumption value (m3)",
        pressure: "Pressure (MPa)",
        batteryVoltage: "Battery voltage (V)",
        valveStatus: "Valve status",
        gSMSignalStrength: "GSM signal strength",
        historyAlert: "Alert history",
        currentMonthlyFlow: "Total consumption this month (m3)",
        estimatedCost: "Estimated Cost (VNĐ)",
        sampleValue: "Water consumption value (m3)",
        csq: "GSM signal strength",
        reportTime: "Report sending time",
        detailId: "Record detail code",
        alertName: "Alert Name",
        alertType: "Alert Type",
        alertEmail: "Alert Email",
        alertContent: "Alert Content",
        alertTime: "Time",
        alertStatus: "Status",
        deviceLocation: "Device Location",
        dashboard: "Dashboard",
        sendCommmad: "Send Command",
        optionCommand: "Option Command",
    },
    input: {
        imei: "Enter IMEI/SERIAL",
        address: "Enter installation address",
        subscriber: "Enter subscriber number",
        type: "Enter device type",
        msisdn: "Enter phone number on device",
        name: "Enter device name",
        serial: "Enter serial",
        individualname: "Enter customer name",
        businessName: "Enter business name",
        lastConnected: "Enter last connection time",
        statusConnect: "Enter connection status",
        info: "Enter device information",
        infoMngt: "Enter management information",
        businessMngt: "Select Business",
        individualMngt: "Select individual customer",
        manufacturer: "Enter manufacturer",
        description: "Enter description",
        model: "Enter device model",
        location: "Enter to search address",
        optionCommand: "Option Command",
    },
    text: {
        messageSuccess: "Save Success",
        textResultImportByFile: "The error registration list is being downloaded",
        wrongFormat: "File must be a excel (xlsx)",
        tooBig: "Import file to big",
        columnInvalid: "file is not in correct format",
        msisdnEmpty: "Msisdn is emptly",
        msisdnNotExists: "Msisdn not exists",
        msisdnAssign: "The subscription number has been assigned to another device",
        msisdnInvalid: "Invalid subscription number",
        msisdnIsEmptly: "Subscription number cannot be empty",
        msisdnIsDuplicate: "Duplicate subscriber number in file",
        imeiIsDuplicate: "Duplicate IMEI in file",
        expiredDateInvalid: "Expiration date must be in dd/mm/yyyy format",
        msisdnNotPermission: "The subscription number does not exist or there are no permissions on the subscription number",
        imeiIsExist: "IMEI has been assigned to another device",
        maxRowImport: "File cannot exceed 1000 lines",
        imeiLen: "Invalid information IMEI . Please enter 2 to 64 characters excluding special characters",
        deviceTypeLen: "Invalid information DEVICE TYPE. Please enter 2 to 64 characters excluding special characters",
        countryLen: "Invalid information MADEIN. Please enter 2 to 32 characters excluding special characters",
        notFoundLocation: "Not found location",
        exception: "Exception",
        errorInputShema: "Error Input Schema",
        sendCommandSucces: "Send command success",
        oneDevice: "Each customer account can only create one device",
        storedCommand: "The command has been stored",
    },
    button: {
      sendCommand: "Send Command",
    },
    status: {
        registered: "Register",
        connected: "Connecting",
        lostConnection: "Lost Connection",
    },
    statusCmd: {
        archived: "Stored",
        submitted: "Sent",
        processed: "Processed",
        rejected: "Reject",
        conflict: "Conflict",
        accepted: "Accepted",
    },
}
