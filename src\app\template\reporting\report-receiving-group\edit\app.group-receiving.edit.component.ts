import {AfterContentChecked, Component, Inject, Injector, OnInit} from '@angular/core';
import {FormBuilder} from "@angular/forms";
import {MenuItem} from "primeng/api";
import {ColumnInfo, OptionTable} from "../../../common-module/table/table.component";
import { ComponentBase } from 'src/app/component.base';
import { ReportReceivingGroupService } from 'src/app/service/report-receiving-group/ReportReceivingGroup';

@Component({
  selector: 'report.group-receiving.edit',
  templateUrl: './app.group-receiving.edit.component.html',
})
export class ReportGroupReceivingEditComponent extends ComponentBase implements OnInit, AfterContentChecked{
    constructor(@Inject(ReportReceivingGroupService) private reportReceivingGroupService: ReportReceivingGroupService,
                private formBuilder: FormBuilder,injector: Injector) {
                    super(injector)
    }
    items: MenuItem[];
    home: MenuItem;
    formReceivingGroup : any;
    formMailInput : any;
    receivingGroupInfo: {
        name: string|null,
        description: string|null,
        emails: string|null,
    };
    receivingGroupInfoOld: any;
    myEmails: Array<any>|null;
    selectItems: Array<any> = [];
    columns: Array<ColumnInfo>;
    dataSet: {
        content: Array<any>,
        total: number
    };
    optionTable: OptionTable;
    email: {}
    isRGNameExisted: boolean = false;

    rgId = parseInt(this.route.snapshot.paramMap.get("id"));
    isRGEmailExisted = false;

    ngOnInit(): void {
        let me = this;
        this.items = [{ label: this.tranService.translate("global.menu.dynamicreportgroup") }, { label: this.tranService.translate("global.menu.reportGroupReceivingList"), routerLink:"/reports/group-report-dynamic"  }, { label: this.tranService.translate("global.button.edit") }];
        this.home = { icon: 'pi pi-home', routerLink: '/' };

        this.receivingGroupInfo = {
            name: null,
            description: null,
            emails:"",
        }
        this.formReceivingGroup = this.formBuilder.group(this.receivingGroupInfo);
        this.formMailInput = this.formBuilder.group({email: ""});
        this.dataSet = {
            content: [],
            total: 0
        }
        this.myEmails= []
        this.selectItems = [];
        this.columns = [
            {
                name: this.tranService.translate("report.receiving.emails"),
                key: "emails",
                size: "80%",
                align: "left",
                isShow: true,
                isSort: false,
            },
        ];
        this.optionTable = {
            hasClearSelected: true,
            hasShowChoose: false,
            hasShowIndex: true,
            hasShowToggleColumn: false,
            action: [
                {
                    icon: "pi pi-trash",
                    tooltip: this.tranService.translate("global.button.registerRatingPlan"),
                    func: function(id, item){
                        me.removeEmail(item)
                    },
                }
            ]
        };
        this.dataSet = {
            content: [],
            total: 0
        }
        this.search();

        this.reportReceivingGroupService.getDetailReceivingGroup(this.rgId,(response)=>{
            me.receivingGroupInfo = response;
            me.receivingGroupInfo.emails = response.emails
            me.receivingGroupInfoOld = {...response};
            if (response.emails != null){
                for (let i = 0; i <response.emails.split(", ").length; i++) {
                    me.dataSet.content.push({emails :response.emails.split(", ")[i]})
                    me.myEmails.push(response.emails.split(", ")[i])
                }
            }
            
        })
    }
    ngAfterContentChecked(): void {
    }
    onSubmitCreate(){
        let dataBody = {
            id: this.rgId,
            name: this.receivingGroupInfo.name,
            description: this.receivingGroupInfo.description,
            emails: this.receivingGroupInfo.emails,
        }
        console.error(dataBody)
        this.messageCommonService.onload();
        let me = this;
        this.reportReceivingGroupService.updateReportReceivingGroup(this.rgId,dataBody, (response)=>{
            me.messageCommonService.success(me.tranService.translate("global.message.saveSuccess"));
            me.router.navigate(["/reports/group-report-dynamic"]);
        }, null, ()=>{
            me.messageCommonService.offload();
        })
    }
    closeForm(){
        this.router.navigate(['/reports/group-report-dynamic'])
    }

    addEmail(val){
        let me = this;
        me.dataSet.content.push({emails :val})
        me.myEmails.push(val)
        me.receivingGroupInfo.emails = me.myEmails.join(', ').toString();
        me.formMailInput.reset();
    }
    search(){
        let me = this
        me.dataSet = {
            content: [],
            total: 0
        }
    }
    removeEmail(val){
        // console.log(val)
        let me = this
        me.dataSet.content.splice(me.dataSet.content.indexOf(val), 1)
        me.myEmails.splice(me.myEmails.indexOf(val), 1)
        me.receivingGroupInfo.emails = me.myEmails.join(', ').toString()
    }

    nameChanged(query){
        let me = this
        if(this.receivingGroupInfo.name != null && this.receivingGroupInfo.name != "" && this.receivingGroupInfo.name != this.receivingGroupInfoOld.name)
        this.debounceService.set("name",me.reportReceivingGroupService.checkName.bind(me.reportReceivingGroupService),{name:this.formReceivingGroup.value['name']},(response)=>{
            if (response > 0){
                me.isRGNameExisted = true
            }
            else {
                me.isRGNameExisted = false
            }
            // me.isRGNameExisted = response == 1;
        })
        // this.reportReceivingGroupService.checkName({name:me.receivingGroupInfo.name}, (response) =>{
        //     if (response > 0){
        //         me.isRGNameExisted = true
        //     }
        //     else {
        //         me.isRGNameExisted = false
        //     }
        // })newtestname
    }
    emailChanged(query){
        let me = this;
        for (let i = 0; i < me.myEmails.length; i++) {
            if (me.myEmails[i] == query){
                this.isRGEmailExisted = true
                return
            }
            else {
                this.isRGEmailExisted = false
            }
        }
    }
}
