import { NgModule } from "@angular/core";
import { RouterModule } from "@angular/router";
import { AppProfileEditComponent } from "./edit/app.profile.edit.component";
import { AppProfileDetailComponent } from "./detail/app.profile.detail.component";
import {AppProfileChangePasswordComponent} from "./change-password/app.profile.change-password.component";
import DataPage from "../../service/data.page";
import {CONSTANTS} from "../../service/comon/constants";

@NgModule({
    imports:[
        RouterModule.forChild([
            {path: "edit", component: AppProfileEditComponent, data: new DataPage("login.label.updateProfile", [CONSTANTS.PERMISSIONS.PROFILE.UPDATE])},
            {path: "", component: AppProfileDetailComponent, data: new DataPage("login.label.getProfileUser", [CONSTANTS.PERMISSIONS.PROFILE.VIEW])},
            {path: "change-password", component: AppProfileChangePasswordComponent, data: new DataPage("login.label.changePassword")}
        ])
    ],
    exports: [RouterModule]
})
export class AppProfileRoutingModule{}
