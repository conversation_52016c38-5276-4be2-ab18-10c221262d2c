import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { BreadcrumbModule } from 'primeng/breadcrumb';
import { FieldsetModule } from 'primeng/fieldset';
import { FormsModule, ReactiveFormsModule } from "@angular/forms";
import { InputTextModule } from 'primeng/inputtext';
import { ButtonModule } from 'primeng/button';
import {SplitButtonModule} from "primeng/splitbutton";
import {CalendarModule} from "primeng/calendar";
import {DropdownModule} from "primeng/dropdown";
import {CommonVnptModule} from "../common-module/common.module";
import {CardModule} from "primeng/card";
import {PanelModule} from "primeng/panel";
import AppConfigChartRouting from "./app.config.chart.routing";
import ConfigChartListComponent from "./list/app.config.chart.list.component";
import { ConfigChartService } from "src/app/service/charts/ConfigChartService";
import ConfigChartCreateComponent from "./create/app.config.chart.create.component";
import { CheckboxModule } from "primeng/checkbox";
import { DialogModule } from "primeng/dialog";
import { MultiSelectModule } from "primeng/multiselect";
import { DividerModule } from "primeng/divider";
import { InputNumberModule } from "primeng/inputnumber";
import { TabViewModule } from 'primeng/tabview';
import ConfigChartEditComponent from "./edit/app.config.chart.edit.component";

@NgModule({
    imports: [
        CommonModule,
        AppConfigChartRouting,
        BreadcrumbModule,
        FieldsetModule,
        FormsModule, ReactiveFormsModule,
        InputTextModule,
        ButtonModule, SplitButtonModule, CalendarModule, DropdownModule, CommonVnptModule, CardModule, PanelModule,
        CheckboxModule, DialogModule, MultiSelectModule, DividerModule, InputNumberModule,TabViewModule
    ],
    declarations: [
        ConfigChartListComponent,
        ConfigChartCreateComponent,
        ConfigChartEditComponent
    ],
    providers: [
        ConfigChartService
    ]
})
export class AppConfigChartModule {};