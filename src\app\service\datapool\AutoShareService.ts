import { Inject, Injectable } from "@angular/core";
import { HttpService } from "../comon/http.service";

@Injectable()
export class AutoShareService {
    private prefixApi: string;
    constructor(@Inject(HttpService) private httpService:HttpService) {
        this.prefixApi = "";
    }

    public searchShareInfo(params, callback?:Function, errorCallBack?:Function, finallyCallback?:Function){
        this.httpService.get(`${this.prefixApi}/share/search-auto-share-info`,{}, params, callback, errorCallBack, finallyCallback);
    }

    public getDetail(params, callback?: Function, errorCallback?:Function, finallyCallback?: Function){
        this.httpService.get(`${this.prefixApi}/wallet/auto-group-wallet/getOne`, {}, params, callback, errorCallback, finallyCallback);
    }

    public deleteShareInfo(body, callback?:Function, errorCallBack?:Function, finallyCallback?:Function){
        this.httpService.post(`${this.prefixApi}/wallet/delete-auto-share-info`,{}, body, {},callback, errorCallBack, finallyCallback);
    }

    public updateAutoShareInfo(body, callback?:Function, errorCallBack?:Function, finallyCallback?:Function){
        this.httpService.post(`${this.prefixApi}/share/update-auto-share-info`,{}, body, {},callback, errorCallBack, finallyCallback);
    }
}
