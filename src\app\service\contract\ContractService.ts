import { Inject, Injectable } from "@angular/core";
import { HttpService } from "../comon/http.service";

@Injectable()
export class ContractService {
    private prefixApi: string;
    constructor(@Inject(HttpService) private httpService:HttpService) {
        this.prefixApi = "/contract";
    }

    public searchContract(params:{[key: string]: string},callback:Function, errorCallBack?:Function, finallyCallback?:Function){
        this.httpService.get(this.prefixApi+"/search",{timeout: 180000},params, callback,errorCallBack,finallyCallback);
    }

    public search(params:{[key: string]: string},callback:Function){
        this.httpService.get(this.prefixApi+"/search",{timeout: 180000},params, callback);
    }

    public getById(id,callback:Function){
        this.httpService.get(this.prefixApi+"/"+id,{},{}, callback);
    }

    public getByKey(key: string, value: string, callback?: Function, errorCallback?:Function, finallyCallback?: Function){
        this.httpService.get(`${this.prefixApi}/getByKey`,{}, {key, value}, callback, errorCallback, finallyCallback);
    }

    // public getAllSumaryCustomer(callback: Function, errorCallBack?: Function, finallyCallback?: Function){
    //     this.httpService.get(`${this.prefixApi}/get-list-customer`,{},{},callback,errorCallBack,finallyCallback);
    // }
    public quickSearchContract(params:{[key: string]:string}, body ,callback?:Function, errorCallback?:Function, finallyCallback?: Function ){
        this.httpService.post(`${this.prefixApi}/quick-search`, {timeout: 180000}, body, params, callback, errorCallback, finallyCallback)
    }
}
