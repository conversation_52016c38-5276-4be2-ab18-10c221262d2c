import { NgModule } from "@angular/core";
import { RouterModule } from "@angular/router";
import { AppAccountListComponent } from "./list/app.account.list.component";
import { AppAccountCreateComponent } from "./create/app.account.create.component";
import { AppAccountEditComponent } from "./edit/app.account.edit.component";
import DataPage from "src/app/service/data.page";
import { CONSTANTS } from "src/app/service/comon/constants";

@NgModule({
    imports:[
        RouterModule.forChild([
            {path: "", component: AppAccountListComponent, data: new DataPage("global.menu.listaccount", [CONSTANTS.PERMISSIONS.ACCOUNT.VIEW_LIST])},
            {path: "create", component: AppAccountCreateComponent,data: new DataPage("global.titlepage.createaccount", [CONSTANTS.PERMISSIONS.ACCOUNT.CREATE])},
            {path: "edit/:id", component: AppAccountEditComponent, data: new DataPage("global.titlepage.editaccount", [CONSTANTS.PERMISSIONS.ACCOUNT.UPDATE])},
        ])
    ],
    exports: [RouterModule]
})
export class AppAccountRoutingModule{}
