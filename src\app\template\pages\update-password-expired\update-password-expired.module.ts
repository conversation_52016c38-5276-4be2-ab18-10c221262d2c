
import { NgModule, CUSTOM_ELEMENTS_SCHEMA } from "@angular/core";
import {FormUpdatePasswordExpired} from "./update-password-expired.component";
import {CommonVnptModule} from "../../common-module/common.module";
import {ButtonModule} from "primeng/button";
import {DialogModule} from "primeng/dialog";
import {ConfirmDialogModule} from "primeng/confirmdialog";
import {AccountService} from "../../../service/account/AccountService";
import {TranslateService} from "../../../service/comon/translate.service";
import {MessageCommonService} from "../../../service/comon/message-common.service";
import {ObservableService} from "../../../service/comon/observable.service";
import {TermPolicyRoutingModule} from "../../term-policy/app.term.policy.routing";
import {CommonModule} from "@angular/common";
import {FormsModule, ReactiveFormsModule} from "@angular/forms";
import {TabViewModule} from "primeng/tabview";
import {BreadcrumbModule} from "primeng/breadcrumb";
import {PanelModule} from "primeng/panel";
import {InputTextModule} from "primeng/inputtext";
import {CheckboxModule} from "primeng/checkbox";
import {CardModule} from "primeng/card";

@NgModule({
    imports: [
        TermPolicyRoutingModule,
        CommonModule,
        CommonVnptModule,
        FormsModule,
        ReactiveFormsModule,
        TabViewModule,
        BreadcrumbModule,
        PanelModule,
        InputTextModule,
        ButtonModule,
        DialogModule,
        ConfirmDialogModule,
        CheckboxModule,
        CardModule
    ],
    declarations: [
        FormUpdatePasswordExpired
    ],
    providers: [
        AccountService, TranslateService, MessageCommonService, ObservableService
    ],
    schemas: [
        CUSTOM_ELEMENTS_SCHEMA
    ],
    exports: [
        FormUpdatePasswordExpired
    ]
})
export class UpdatePasswordExpiredModule {};
