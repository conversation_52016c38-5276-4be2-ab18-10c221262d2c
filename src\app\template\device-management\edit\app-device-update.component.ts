import {Component, Inject, Injector, OnInit} from "@angular/core";
import {MenuItem} from "primeng/api";
import {DeviceService} from "../../../service/device/device-service.service";
import {FormBuilder, FormGroup} from "@angular/forms";
import {ComponentBase} from "../../../component.base";
import {CONSTANTS} from "../../../service/comon/constants";
import {<PERSON><PERSON><PERSON><PERSON><PERSON>, SafeResourceUrl} from "@angular/platform-browser";
import {ComboLazyControl} from "../../common-module/combobox-lazyload/combobox.lazyload";
import {ColumnInfo} from "../../common-module/table/table.component";
import {DeviceTypeService} from "../../../service/device/device-type.service";
import {ActivatedRoute} from "@angular/router";

declare let L: any;

@Component({
    selector: "app-device-edit",
    templateUrl: './app-device-update.component.html',
})
export class AppDeviceUpdateComponent extends ComponentBase implements OnInit {
    id = this.activeroute.snapshot.paramMap.get("id");
    items: MenuItem[];
    deviceInfo: {
        deviceName: string | null,
        typeCode: string | null,
        imei: string | null,
        serialNumber: string | null,
        msisdn: string | null,
        description: string | null,
        userEnterpriseId: number | null,
        userCustomerId: number | null,
        manufacture: string | null,
        latitude: string | null,
        longitude: string | null,
        fullAddress: string | null,
        modelCode: string | null,
        deviceTypeId: number | null,
    }
    home: MenuItem;
    formCreateDevice: FormGroup;
    msisdn: number;
    listStatus: Array<any>;
    listType: Array<any>;
    safeUrl: SafeResourceUrl;
    userType: number;
    controlComboSelectIndividual: ComboLazyControl = new ComboLazyControl()
    controlComboSelectBusiness: ComboLazyControl = new ComboLazyControl()
    controlComboSelectType: ComboLazyControl = new ComboLazyControl();
    controlComboSelectModel: ComboLazyControl = new ComboLazyControl()
    paramSearchType: {
        modelCode: any;
    }
    paramSearchModel: {
        typeCode: any;
    }
    paramSearchBusiness: {
        type: 2,
        sort: 'id,asc'
        customerId: number | -1
    }
    paramSearchIndividual: {
        type: 3,
        sort: 'id,asc'
        managerId: number | -1,
        forDevice: 1,
        editingUserIdForDevice: number | -1,
    }
    deviceTypeDetail: {
        id: number | null,
        typeCode: string | null,
        typeName: string | null,
        modelCode: string | null,
        modelDescription: string | null,
        telemetryConfigSchema: string | null,
    }
    defaultLat = "21.046303";
    defaultLng = "105.791648";
    defaultAddress = '124 Hoàng Quốc Việt, Cầu Giấy, Hà Nội';
    address: string = 'Chưa chọn';
    lat: number | string | null
    lng: number | string | null
    map: any;
    marker: any;
    normalPattern = /^[a-zA-Z0-9\-_]*$/;
    vietnamesePattern = /^[^~`!@#\$%\^&*\(\)=\+\[\]\{\}\|\\,<>\/?]*$/;
    msisdnPattern = /^(?:0|84)\d{9,10}(?:, ?(?:0|84)\d{9,10})*$/
    isShowExistsImei: boolean = false;
    originalImei: string = "";
    constructor(@Inject(DeviceService) private deviceService: DeviceService,
                @Inject(DeviceTypeService) private deviceTypeService: DeviceTypeService,
                private formBuilder: FormBuilder,
                private sanitizer: DomSanitizer,
                private activeroute: ActivatedRoute,
                injector: Injector,
    ) {
        super(injector);
    }

    ngOnInit(): void {
        if (!this.checkAuthen([CONSTANTS.PERMISSIONS.DEVICE.UPDATE])) {
            window.location.hash = "/access";
        }
        this.listStatus = [
            {name: this.tranService.translate("device.status.registered"), value: CONSTANTS.DEVICE_STATUS.REGISTERED},
            {name: this.tranService.translate("device.status.connected"), value: CONSTANTS.DEVICE_STATUS.CONNECTED},
            {
                name: this.tranService.translate("device.status.lostConnection"),
                value: CONSTANTS.DEVICE_STATUS.LOST_CONNECTION
            }
        ]
        this.home = {icon: 'pi pi-home', routerLink: '/'};
        this.userType = this.sessionService.userInfo.type;
        this.items = [{label: this.tranService.translate("global.menu.devicemgmt"), routerLink: '/devices'},
            {label: this.tranService.translate("global.menu.listdevice"), routerLink: "/devices"},
            {label: this.tranService.translate("global.menu.deviceupdate")}];
        this.deviceInfo = {
            deviceName: null,
            typeCode: null,
            imei: null,
            serialNumber: null,
            msisdn: null,
            description: null,
            userEnterpriseId: null,
            userCustomerId: null,
            manufacture: null,
            latitude: null,
            longitude: null,
            fullAddress: null,
            modelCode: null,
            deviceTypeId: null,
        }
        this.paramSearchType = {
            modelCode: "",
        }
        this.paramSearchModel = {
            typeCode: "",
        }
        this.paramSearchBusiness = {
            type: 2,
            sort: 'id,asc',
            customerId: -1
        }
        this.deviceTypeDetail = {
            id: null,
            typeCode: null,
            typeName: null,
            modelCode: null,
            modelDescription: null,
            telemetryConfigSchema: null
        }
        this.paramSearchIndividual = {
            type: 3,
            sort: 'id,asc',
            managerId: -1,
            forDevice: 1,
            editingUserIdForDevice: -1,
        }

        const url = `https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3723.645764495224!2d105.78919517584175!3d21.046855287155687!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x3135ab303bc5f991%3A0x938485f81ec15900!2zMTI0IEhvw6BuZyBRdeG7kWMgVmnhu4d0LCBD4buVIE5odeG6vywgQ-G6p3UgR2nhuqV5LCBIw6AgTuG7mWkgMTAwMDAsIFZp4buHdCBOYW0!5e0!3m2!1svi!2s!4v1749690944689!5m2!1svi!2s`;
        this.safeUrl = this.sanitizer.bypassSecurityTrustResourceUrl(url);

        this.formCreateDevice = this.formBuilder.group(this.deviceInfo)

        this.lat = this.defaultLat;
        this.lng = this.defaultLng;
        this.address = this.defaultAddress;

        this.getDetailDevice();
    }

    getDetailDevice() {
        let me = this;
        me.messageCommonService.onload()
        this.deviceService.detailDevice(Number(me.id), (response) => {
            me.deviceInfo = {
                ...response
            }
            me.originalImei = me.deviceInfo.imei;
            if (me.deviceInfo.deviceTypeId) {
                me.getDeviceType(me.deviceInfo.deviceTypeId)
            }
            if (me.deviceInfo.userCustomerId) {
                me.paramSearchIndividual.editingUserIdForDevice = me.deviceInfo.userCustomerId;
            }
            if (me.deviceInfo.latitude != null && me.deviceInfo.longitude != null && me.deviceInfo.fullAddress != null) {
                me.lat = me.deviceInfo.latitude
                me.lng = me.deviceInfo.longitude
                me.address = me.deviceInfo.fullAddress;
            }
            me.initMap();
        }, null, () => {
            me.messageCommonService.offload();
        })
    }

    initMap(): void {
        this.map = L.map('osm-map').setView([this.lat, this.lng], 13);

        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
            attribution: '&copy; OpenStreetMap contributors'
        }).addTo(this.map);

        // Gắn marker mặc định với tooltip
        this.marker = L.marker([this.lat, this.lng])
            .addTo(this.map)
            .bindTooltip(this.address, {permanent: true, direction: 'top'})
            .openTooltip();

        this.map.on('click', async (e: any) => {
            const lat = e.latlng.lat;
            const lng = e.latlng.lng;
            const address = await this.reverseGeocode(lat, lng);
            this.setMarker(lat, lng, address);
        });
    }

    async search(event: KeyboardEvent) {
        let me = this;
        event.preventDefault();
        let query = (event.target as HTMLInputElement).value;
        const res = await fetch(`https://nominatim.openstreetmap.org/search?format=json&q=${encodeURIComponent(query)}`);
        const results = await res.json();
        if (results.length > 0) {
            const r = results[0];
            const lat = parseFloat(r.lat);
            const lng = parseFloat(r.lon);
            this.setMarker(lat, lng, r.display_name);
            this.map.setView([lat, lng], 15);
        } else {
            me.messageCommonService.warning(me.tranService.translate('device.text.notFoundLocation'))
        }
    }

    async reverseGeocode(lat: number, lng: number): Promise<string> {
        const res = await fetch(`https://nominatim.openstreetmap.org/reverse?format=json&lat=${lat}&lon=${lng}`);
        const data = await res.json();
        return data.display_name || 'Không tìm thấy địa chỉ';
    }

    setMarker(lat: number, lng: number, address: string) {
        this.lat = lat;
        this.lng = lng;
        this.address = address;
        let me = this;
        me.deviceInfo.fullAddress = address
        me.deviceInfo.longitude = String(lng)
        me.deviceInfo.latitude = String(lat)
        if (this.marker) this.marker.remove();

        this.marker = L.marker([lat, lng])
            .addTo(this.map)
            .bindTooltip(address, {permanent: true, direction: 'top'})
            .openTooltip();
    }


    getDeviceType(id) {
        let me = this;
        me.messageCommonService.onload()
        this.deviceTypeService.getDeviceType(id, (response) => {
            me.deviceTypeDetail = {
                ...response
            }
            me.deviceInfo.modelCode = me.deviceTypeDetail.modelCode;
            me.deviceInfo.typeCode = me.deviceTypeDetail.typeCode;
            me.updateParams();
        }, null, () => {
            me.messageCommonService.offload();
        })
    }

    goBack() {
        window.history.back();
    }

    onSubmitUpdateDevice() {
        let me = this;
        let dataBody = me.deviceInfo;
        me.messageCommonService.onload();
        me.deviceService.updateDevice(me.id, dataBody, (response) => {
            me.messageCommonService.success(me.tranService.translate("global.message.saveSuccess"));
            me.router.navigate(['/devices']);
        }, null, () => {
            me.messageCommonService.offload();
        })
    }

    onSelectedType() {
        let me = this;
        me.paramSearchModel.typeCode = me.deviceInfo.typeCode ? me.deviceInfo.typeCode : "";
    }

    onSelectedModel() {
        let me = this;
        me.paramSearchType.modelCode = me.deviceInfo.modelCode ? me.deviceInfo.modelCode : "";
    }

    onSelectedBusiness() {
        let me = this;
        me.paramSearchIndividual.managerId = me.deviceInfo.userEnterpriseId ? me.deviceInfo.userEnterpriseId : -1;
    }

    onSelectedIndividual() {
        let me = this;
        me.paramSearchBusiness.customerId = me.deviceInfo.userCustomerId ? me.deviceInfo.userCustomerId : -1;
    }

    disableButtonSubmit() {
        let me = this;
        if (me.formCreateDevice.invalid || me.isShowExistsImei) {
            console.log(me.isShowExistsImei)
            return true;
        }
        if (me.controlComboSelectBusiness.error.required && me.userType == CONSTANTS.USER_TYPE.ADMIN) {
            return true;
        }
        if (me.controlComboSelectType.error.required || me.controlComboSelectModel.error.required || me.controlComboSelectIndividual.error.required) {
            return true;
        }
        return false;
    }
    checkExistsImei() {
        const currentImei = (this.deviceInfo.imei || "").trim();

        if (currentImei === this.originalImei) return; // Không thay đổi thì không check

        this.isShowExistsImei = false;

        if (currentImei !== "") {
            this.debounceService.set("checkExistsImei",
                this.deviceService.checkExistsImeiDevice.bind(this.deviceService),
                currentImei,
                (response) => {
                    this.isShowExistsImei = response >= 1;
                }
            );
        }
    }

    updateParams() {
        let me = this;
        me.onSelectedType();
        me.onSelectedModel();
        me.onSelectedBusiness();
        me.onSelectedIndividual();
    }

    protected readonly
    CONSTANTS = CONSTANTS;
}
