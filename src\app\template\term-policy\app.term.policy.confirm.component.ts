import { Component, Injector, OnInit } from "@angular/core";
import { FormBuilder } from "@angular/forms";
import { dA } from "@fullcalendar/core/internal-common";
import { ConfirmationService } from "primeng/api";
import { Subscription } from "rxjs";
import { ComponentBase } from "src/app/component.base";
import { CONSTANTS } from "src/app/service/comon/constants";
import { ObservableService } from "src/app/service/comon/observable.service";
import {AccountService} from "../../service/account/AccountService";

@Component({
    providers: [ConfirmationService],
    selector: 'term-policy-confirm',
    templateUrl: "./app.term.policy.confirm.component.html"
})
export class TermPolicyConfirmComponent extends ComponentBase implements OnInit{
    constructor(
        private confirmationService: ConfirmationService,
        private accountService : AccountService,
        private formBuilder: FormBuilder,
        private injector: Injector) {
            super(injector);
    }

    isShowConfirmPersonalDataProtectionPolicy: boolean = false;
    subscriptionConfirmPolicy: Subscription;
    listPolicyChecked: Array<Number>;
    isForceClose: boolean = false;
    timeNotify: number = 0;

    ngOnInit(): void {
        this.subscriptionConfirmPolicy = this.observableService.subscribe(CONSTANTS.OBSERVABLE.KEY_LOAD_CONFIRM_POLICY_HISTORY, {
            next: this.loadData.bind(this)
        })
    }

    loadData(data): void {
        if(data){
            if(data.forceClose){
                if(this.isShowConfirmPersonalDataProtectionPolicy){
                    this.isShowConfirmPersonalDataProtectionPolicy = false;
                    this.isForceClose = true;
                }
                return;
            }
        }
        this.listPolicyChecked = JSON.parse(localStorage.getItem("listPolicyChecked") || "[]");
        let me = this;
        if(this.sessionService.userInfo.type == CONSTANTS.USER_TYPE.INDIVIDUAL || this.sessionService.userInfo.type == CONSTANTS.USER_TYPE.BUSINESS){
            this.isShowConfirmPersonalDataProtectionPolicy = true;
            this.sessionService.confirmPolicyHistory.forEach(policy => {
                if(policy.policyId == CONSTANTS.POLICY.PERSONAL_DATA_PROTECTION_POLICY){
                    if(policy.status == CONSTANTS.POLICY_STATUS.AGREE) me.isShowConfirmPersonalDataProtectionPolicy = false;
                }
            });
            if(this.isShowConfirmPersonalDataProtectionPolicy){
                if(this.listPolicyChecked.includes(CONSTANTS.POLICY.PERSONAL_DATA_PROTECTION_POLICY)){
                    this.isShowConfirmPersonalDataProtectionPolicy = false;
                }
            }
        }
    }

    closePersonalDataProtectionPolicy(event){
        if(this.isForceClose){
            this.isForceClose = false;
            return;
        }
        let me = this;
        let policy = null;
        console.log(this.sessionService.confirmPolicyHistory)
        for(let i = 0; i < this.sessionService.confirmPolicyHistory.length; i++){
            if(this.sessionService.confirmPolicyHistory[i].policyId == CONSTANTS.POLICY.PERSONAL_DATA_PROTECTION_POLICY){
                policy = this.sessionService.confirmPolicyHistory[i];
                break;
            }
        }
        let isNotify = false;
        if(policy == null){
            let timeCheck = new Date(2023, 3, 17).getTime();
            if(new Date(this.sessionService.userInfo.createdDate).getTime() > timeCheck){
                isNotify = true;
            }
        }else if(policy.status != CONSTANTS.POLICY_STATUS.AGREE){
            isNotify = true;
        }
        if(isNotify){
            this.timeNotify++;
            if(this.timeNotify >= 3){
                this.messageCommonService.onload();
                setTimeout(()=>{
                    localStorage.clear();
                    me.messageCommonService.offload();
                    window.location.hash = '/login';
                }, 500)
                this.timeNotify = 0;
                //call api
                this.accountService.disagreePolicy(CONSTANTS.POLICY.PERSONAL_DATA_PROTECTION_POLICY, null);
                return;
            }
            this.confirmationService.confirm({
                target: event.target as EventTarget,
                message: "Nếu bạn không đồng ý với Điều khoản và Chính sách sẽ không được tiếp tục sử dụng dịch vụ này. Xin vui lòng đọc Điều khoản và Chính sách, tích chọn Đồng ý để tiếp tục sử dụng dịch vụ.",
                header: this.tranService.translate("global.menu.termpolicy"),
                icon: 'pi pi-exclamation-circle text-6xl text-primary-500',
                acceptIcon:"none",
                rejectVisible: false,
                acceptButtonStyleClass: "p-button-info",
                acceptLabel: "OK",
                accept: () => {
                    me.isShowConfirmPersonalDataProtectionPolicy = true;
                },
            });
        }else{
            this.timeNotify = 0;
            this.listPolicyChecked.push(CONSTANTS.POLICY.PERSONAL_DATA_PROTECTION_POLICY);
            this.sessionService.setData("listPolicyChecked", JSON.stringify(this.listPolicyChecked));
        }
    }

    goToContentDataPersonalProtectionPolicy(){
        this.isForceClose = true;
        this.isShowConfirmPersonalDataProtectionPolicy = false;
        this.router.navigate(["/policies"], { queryParams: {index: 0}});
    }

    ngOnDestroy(): void {
        this.subscriptionConfirmPolicy.unsubscribe();
    }
}
