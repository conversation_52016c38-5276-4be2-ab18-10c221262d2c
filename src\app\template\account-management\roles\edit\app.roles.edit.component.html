<div class="vnpt flex flex-row justify-content-between align-items-center bg-white p-2 border-round">
    <div class="">
        <div class="text-xl font-bold mb-1">{{this.tranService.translate("global.menu.listroles")}}</div>
        <p-breadcrumb class="max-w-full col-7" [model]="items" [home]="home"></p-breadcrumb>
    </div>
    <div class="col-5 flex flex-row justify-content-end align-items-center">

    </div>
</div>
<p-card styleClass="mt-3">
    <div>
        <form [formGroup]="formRole" (ngSubmit)="onSubmitEdit()">
            <div class="flex flex-row justify-content-between">
                <div style="width: 49%;">
                    <!-- username -->
                    <div class="w-full field grid">
                        <label htmlFor="name" class="col-fixed" style="width:180px">{{tranService.translate("roles.label.rolename")}}<span class="text-red-500">*</span></label>
                        <div class="col">
                            <input class="w-full"
                                   pInputText id="name"
                                   [(ngModel)]="roleInfo.name"
                                   formControlName="name"
                                   [required]="true"
                                   [maxLength]="255"
                                   pattern="^[a-zA-Z0-9\-_ ÀÁÂÃÈÉÊÌÍÒÓÔÕÙÚĂĐĨŨƠàáâãèéêìíòóôõùúăđĩũơƯĂẠẢẤẦẨẪẬẮẰẲẴẶẸẺẼỀỀỂưăạảấầẩẫậắằẳẵặẹẻẽềềểỄỆỈỊỌỎỐỒỔỖỘỚỜỞỠỢỤỦỨỪễệỉịọỏốồổỗộớờởỡợụủứừỬỮỰỲỴÝỶỸửữựỳỵỷỹ\s]*$"
                                   [placeholder]="tranService.translate('roles.text.inputRoleName')"
                                   (keyup)="nameChanged($event)"
                            />
                        </div>
                    </div>
                    <!-- error username -->
                    <div class="w-full field grid text-error-field">
                        <label htmlFor="name" class="col-fixed" style="width:180px"></label>
                        <div class="col">
                            <small class="text-red-500" *ngIf="formRole.controls.name.dirty && formRole.controls.name.errors?.required">{{tranService.translate("global.message.required")}}</small>
                            <small class="text-red-500" *ngIf="formRole.controls.name.errors?.maxLength">{{tranService.translate("global.message.maxLength",{len:255})}}</small>
                            <small class="text-red-500" *ngIf="formRole.controls.name.errors?.pattern">{{tranService.translate("roles.label.errorPattern")}}</small>
                            <small class="text-red-500" *ngIf="isRoleNameExisted">{{tranService.translate("global.message.exists",{type: tranService.translate("roles.label.rolename").toLowerCase()})}}</small>
                        </div>
                    </div>
                    <!-- loai tai khoan -->
                    <div class="w-full field grid">
                        <label for="type" class="col-fixed" style="width:180px">{{tranService.translate("roles.label.usertype")}}<span class="text-red-500">*</span></label>
                        <div class="col">
                            <p-dropdown styleClass="w-full"
                                        [showClear]="true"
                                        id="type" [autoDisplayFirst]="false"
                                        [(ngModel)]="roleInfo.type"
                                        [required]="true"
                                        formControlName="type"
                                        [options]="userTypes"
                                        optionLabel="name"
                                        optionValue="value"
                                        [placeholder]="tranService.translate('roles.text.selectUserType')"
                            ></p-dropdown>
                        </div>
                    </div>
                    <!-- error loai tai khoan -->
                    <div class="w-full field grid text-error-field">
                        <label htmlFor="type" class="col-fixed" style="width:180px"></label>
                        <div class="col">
                            <small class="text-red-500" *ngIf="formRole.controls.type.dirty && formRole.controls.type.errors?.required">{{tranService.translate("global.message.required")}}</small>
                        </div>
                    </div>
                    <!-- trang thai -->
                    <div class="w-full field grid">
                        <label for="status" class="col-fixed" style="width:180px">{{tranService.translate("roles.label.status")}}<span class="text-red-500">*</span></label>
                        <div class="col">
                            <p-dropdown styleClass="w-full"
                                        [showClear]="true"
                                        id="status" [autoDisplayFirst]="false"
                                        [(ngModel)]="roleInfo.status"
                                        [required]="true"
                                        formControlName="status"
                                        [options]="statusRoles"
                                        optionLabel="name"
                                        optionValue="value"
                                        [placeholder]="tranService.translate('roles.text.status')"
                            ></p-dropdown>
                        </div>
                    </div>
                    <!-- error trang thai -->
                    <div class="w-full field grid text-error-field">
                        <label htmlFor="status" class="col-fixed" style="width:180px"></label>
                        <div class="col">
                            <small class="text-red-500" *ngIf="formRole.controls.status.dirty && formRole.controls.status.errors?.required">{{tranService.translate("global.message.required")}}</small>
                        </div>
                    </div>
                </div>
                <div style="width: 49%;">
                    <label for="roles" class="col-fixed" style="width:180px">{{tranService.translate("roles.label.rolelist")}}<span class="text-red-500">*</span></label>
                    <div class="col">
                        <p-tree
                            id="roles"
                            [value]="dataSet.content"
                            selectionMode="checkbox"
                            class="w-full md:w-30rem"
                            [(selection)]="roleInfo.roles"
                            [style]="{'max-height':'500px', 'overflow-y':'scroll'}"
                        ></p-tree>
                    </div>
                    <div class="w-full field grid text-error-field">
                        <div class="col">
                            <small style="padding-left: 15px" class="text-red-500" *ngIf="roleInfo.roles != null && roleInfo.roles.length == 0">{{tranService.translate("global.message.required")}}</small>
                        </div>
                    </div>
                </div>
            </div>
            <div class="flex flex-row justify-content-center align-items-center gap-3 mt-6 mb-3">
                <p-button [label]="tranService.translate('global.button.cancel')" styleClass="p-button-secondary p-button-outlined" (click)="closeForm()"></p-button>
                <p-button [label]="tranService.translate('global.button.save')" styleClass="p-button-info" type="submit" [disabled]="checkInvalidCreate()"></p-button>
            </div>
        </form>
    </div>
</p-card>
