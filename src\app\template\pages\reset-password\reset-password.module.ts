import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ResetPasswordRoutingModule } from './reset-password-routing.module';
import { ButtonModule } from 'primeng/button';
import { CheckboxModule } from 'primeng/checkbox';
import { FormsModule, ReactiveFormsModule } from "@angular/forms";
import { PasswordModule } from 'primeng/password';
import { InputTextModule } from 'primeng/inputtext';
import { CommonVnptModule } from '../../common-module/common.module';
import { DialogModule } from "primeng/dialog";
import {ResetPasswordComponent} from "../reset-password/reset-password.component";

@NgModule({
    imports: [
        CommonModule,
        ResetPasswordRoutingModule,
        ButtonModule,
        CheckboxModule,
        InputTextModule,
        FormsModule,
        PasswordModule,
        CommonVnptModule,
        DialogModule,
        CommonModule,
        FormsModule,
        ReactiveFormsModule,
        CommonVnptModule,
        DialogModule
    ],
    declarations: [ResetPasswordComponent]
})
export class ResetPasswordModule { }
