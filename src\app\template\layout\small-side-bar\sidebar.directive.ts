import { Directive, ElementRef, HostListener, Input, Renderer2 } from '@angular/core';
import { Router } from '@angular/router';

@Directive({
  selector: '[appSmallSidebarElement]'
})
export class SidebarElementDirective {
  @Input() itemInfo: any = {};
  private displayElement: HTMLDivElement | null = null;

  constructor(private el: ElementRef, private renderer: Renderer2, private router: Router) {}

  @HostListener('click', ['$event']) onClick(event: MouseEvent) {
    event.stopPropagation(); // Prevent the click event from reaching the document click handler

    if (this.itemInfo.url) {
      if (this.itemInfo.target == "_blank") {
        window.open(this.itemInfo.url, '_blank');
      } else {
        window.location.href = this.itemInfo.url;
      }
    } else if (this.itemInfo.routerLink) {
      this.router.navigate(this.itemInfo.routerLink);
      this.hideElement()
    } else if (!this.itemInfo.url && this.itemInfo.items && this.itemInfo.items.length > 0) {
      this.toggleElement(event);
    }
  }

  @HostListener('mouseenter', ['$event']) onMouseEnter(event: MouseEvent) {
    let node = document.querySelector(".display-subElement");
    if (node) {
      event.stopPropagation();
      if (!this.itemInfo.url && this.itemInfo.items && this.itemInfo.items.length > 0) {
        this.toggleElement(event);
      } else {
        this.hideElement();
      }
    }
  }

  @HostListener('document:click', ['$event']) onDocumentClick(event: MouseEvent) {
    if (this.displayElement && !this.displayElement.contains(event.target as Node)) {
      this.hideElement();
    }
  }

  private toggleElement(event: MouseEvent) {
    let node = document.querySelector(".display-subElement");
    if (node) {
      this.hideElement();
      
    }
    this.showElement(event);
  }

  private showElement(event: MouseEvent) {
    const rect = this.el.nativeElement.getBoundingClientRect();
    // console.log(rect)

    // const top = rect.top + window.scrollY;
    // const left = rect.left + window.scrollX + rect.width + 30;

    this.displayElement = document.createElement('div');
    this.displayElement.className = 'display-subElement';
    this.displayElement.style.position = 'fixed';
    this.displayElement.style.top = `${rect.top}px`;
    this.displayElement.style.left = `${rect.left + 55}px`;
    // this.displayElement.style.backgroundColor = "white";
    // this.displayElement.style.padding = "10px"
    this.displayElement.style.zIndex = "10"
    // this.renderer.addClass(this.el.nativeElement, 'active-route-small');

    // this.displayElement

    // Check if the itemInfo contains sub-items
    if (this.itemInfo.items && this.itemInfo.items.length > 0) {
      this.createSubMenu(this.itemInfo.items, this.displayElement);
    }

    document.body.appendChild(this.displayElement);
  }

  private hideElement() {
    let node = document.querySelector(".display-subElement");
    if (node) {
      // document.body.removeChild(this.displayElement);
      node.remove()
      // this.displayElement = null;
    }
  }

  private createSubMenu(items: any[], parentElement: HTMLDivElement) {
    items.forEach((item: any) => {
      const subItemDiv = document.createElement('div');
      subItemDiv.className = 'submenu-item';
      subItemDiv.innerHTML = item.label

      // Check if the sub-item has routerLink
      if (item.routerLink) {
        const url = item.routerLink.join('/') || '';
        // console.log(url)
        // console.log(this.router.url)
        if ( this.router.url==url|| this.router.url+"/"==url ) {
          this.renderer.addClass(subItemDiv, 'active-route-subitem');
        }
        this.renderer.listen(subItemDiv, 'click', () => {
          this.router.navigate(item.routerLink);
          this.hideElement();
        });
      } else {
        // this.renderer.listen(subItemDiv, 'click', () => this.createSubMenu(item.items, this.displayElement!));
      }

      parentElement.appendChild(subItemDiv);
    });
  }
}
