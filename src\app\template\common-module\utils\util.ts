import {ElementRef} from '@angular/core';
import {FormGroup} from '@angular/forms';
import {TreeNode} from 'primeng/api';

export function calculateTableHeight(elTable: ElementRef, elFooter: ElementRef) {
    const windowHeight = window.innerHeight;
    const tableStyleTop = elTable.nativeElement.getBoundingClientRect().top;
    const footerHeight = elFooter.nativeElement.offsetHeight;
    const height = windowHeight - tableStyleTop - footerHeight - 11;
    elTable.nativeElement.style.height = `${height}px`;
}

export function calculateTableHeightWithoutFooter(elTable: ElementRef) {
    const windowHeight = window.innerHeight;
    const tableStyleTop = elTable.nativeElement.getBoundingClientRect().top;
    const height = windowHeight - tableStyleTop - 9;
    elTable.nativeElement.style.height = `${height}px`;
}

export function calculateHeightContentBody(elTable: ElementRef) {
    const windowHeight = window.innerHeight;
    const tableStyleTop = elTable.nativeElement.getBoundingClientRect().top;
    const height = windowHeight - tableStyleTop;
    elTable.nativeElement.style.height = `${height}px`;
}

export function convertToTreeData(flatData: any[], hasNullParent: boolean = true): TreeNode[] {
    const nodeMap = new Map<number, TreeNode>();
    let treeData: TreeNode[] = [];
    const rootNodes: TreeNode[] = [];
    const sortedFlatData = flatData.sort((a, b) => (a.parentId - b.parentId));
    // Tạo map chứa node theo id để sau này dễ dàng truy cập khi tạo mối quan hệ cha - con
    sortedFlatData.forEach(item => {
        const node: TreeNode = {
            data: {...item},
            children: []
        };
        nodeMap.set(item.id, node);
        if (hasNullParent) {
            if (item.parentId === null) {
                rootNodes.push(node); // Lưu trữ tất cả các nút gốc vào mảng rootNodes
            }
        } else {
            const parentId = item.parentId;
            if (!nodeMap.has(parentId)) {
                rootNodes.push(node); // Lưu trữ tất cả các nút gốc vào mảng rootNodes
            }
        }
    });

    // Tạo cây dữ liệu
    sortedFlatData.forEach(item => {
        const node = nodeMap.get(item.id);
        const parentId = item.parentId;

        if (parentId !== null) {
            // Nếu không phải node gốc, thì tìm node cha tương ứng và thêm node vào mảng children của node cha
            const parentNode = nodeMap.get(parentId);
            if (parentNode) {
                parentNode.children.push(node);
            }
        }
    });

    // Nếu không có nút gốc nào (có parentId là null), giữ lại tất cả các nút trong flatData làm nút gốc
    if (rootNodes.length === 0) {
        const nonRootNodes = new Set(rootNodes.map(node => node.data.id));
        treeData = rootNodes.filter(node => !nonRootNodes.has(node.data.parentId));
    } else {
        // Nếu có nút gốc thì đưa các nút gốc vào mảng treeData
        treeData.push(...rootNodes);
    }
    treeData.forEach((node: TreeNode) => {
        node.expanded = true;
    });

    return treeData;
}

export function transformDataTreeNode(data: [], isDisable: boolean) {
    return data.map((node: any) => transformNode(node, null, isDisable));
}

function transformNode(node: any, parentKey: string | null, isDisable: boolean): TreeNode {
    const treeNode: TreeNode = {
        label: node.title,
        key: node.key, // Thêm key của nút hiện tại vào TreeNode
        selectable: !isDisable,
        children: node.children ? node.children.map((child: any) => transformNode(child, node.key, isDisable)) : []
    };
    return treeNode;
}

export function getItemsSelected(arrayA: TreeNode[], arrayB: number[]): TreeNode[] {
    const resultArray: TreeNode[] = [];

    for (const item of arrayA) {
        const hasMatchingChild = (item.children || []).some(child => arrayB.includes(parseInt(child.key)));
        if (arrayB.includes(parseInt(item.key)) || hasMatchingChild) {
            resultArray.push(item);
            if (hasMatchingChild) {
                const matchingChildren = (item.children || []).filter(child => arrayB.includes(parseInt(child.key)));
                resultArray.push(...matchingChildren);
            }
        }
    }
    return resultArray;
}

export function validate(form: FormGroup, fieldName: string, error: string) {
    return form.controls[fieldName].dirty && form.controls[fieldName].errors && form.controls[fieldName].errors[error];
}

export function isInvalid(form: FormGroup, fieldName: string) {
    return form.controls[fieldName].dirty && form.controls[fieldName].invalid;
}

export function validateNestedForm(form: FormGroup, fieldName: string, error: string) {
    return form.get(fieldName).dirty && form.get(fieldName).errors && form.get(fieldName).errors[error];
}

// kiểm tra custom validation ở cả form
export function validateWholeForm(form: FormGroup, errorName: string) {
    return form.dirty && form.errors && form.errors[errorName];
}

export function convertStringToDate(dateString: string) {
    if (!dateString) {
        return null;
    }
    const arrDateTime = dateString.split(' - ');
    if (!arrDateTime[0]) {
        return null;
    }
    const arrDate = arrDateTime[0].split('/');
    if (arrDate.length !== 3) {
        return null;
    }
    const result = new Date(parseInt(arrDate[0]), parseInt(arrDate[1]) - 1, parseInt(arrDate[2]));
    if (arrDateTime[1]) {
        const arrTime = arrDateTime[1].split(':');
        if (arrTime.length === 3) {
            result.setHours(parseInt(arrTime[0]), parseInt(arrTime[1]), parseInt(arrTime[2]));
        }
    }
    return result;
}

export function convertStringToDateEnd(dateString: string) {
    if (!dateString) {
        return null;
    }
    const arrDateTime = dateString.split(' - ');
    if (!arrDateTime[0]) {
        return null;
    }
    const arrDate = arrDateTime[0].split('/');
    if (arrDate.length !== 3) {
        return null;
    }
    const result = new Date(parseInt(arrDate[0]), parseInt(arrDate[1]) - 1, parseInt(arrDate[2]));
    if (arrDateTime[1]) {
        const arrTime = arrDateTime[1].split(':');
        if (arrTime.length === 3) {
            result.setHours(parseInt(arrTime[0]), parseInt(arrTime[1]), parseInt(arrTime[2]));
        } else {
            result.setHours(23, 59, 59);
        }
    } else {
        result.setHours(23, 59, 59);
    }
    return result;
}

export function convertDateToStringDate(date: Date) {
    return `${date.getFullYear()}/${date.getMonth() + 1}/${date.getDate()}`;
}

export function titleCaseWord(word: string) {
    if (!word) {
        return word;
    }
    return word[0].toUpperCase() + word.substr(1).toLowerCase();
}

export function onInput(event: KeyboardEvent) {
    if (event.key === '.') {
        event.preventDefault();
    }
}


export function checkNull(newData: any, oldData: any): boolean {
    oldData = oldData === undefined ? null : oldData;
    newData = newData === null ? '' : newData;
    oldData = oldData === null ? '' : oldData;
    return newData !== oldData;
}

export function checkNullBoolean(newData: any, oldData: any): boolean {
    if (oldData === undefined) {
        return true;
    }
    newData = newData === null ? false : newData;
    oldData = oldData === null ? false : oldData;
    return newData !== oldData;
}

function compareObjects(obj1: any, obj2: any): boolean {
    return Object.keys(obj1).every(key => obj1[key] === obj2[key]);
}

export function checkNullArray(newData: any[] | null | undefined, oldData: any[] | null | undefined): boolean {
    oldData = oldData ?? [];
    newData = newData ?? [];
    const allElementsIncluded = newData.every(newItem => oldData.some(oldItem => compareObjects(newItem, oldItem)));
    if (allElementsIncluded && newData.length === oldData.length) {
        return false;
    }
    return true;
}


export enum Type {
    string, boolean, array
}

export function checkDataChanged(type, newData, oldData) {
    switch (type) {
        case Type.string: {
            return checkNull(newData, oldData);
        }
        case Type.boolean: {
            return checkNullBoolean(newData, oldData);
        }
        case Type.array: {
            return checkNullArray(newData, oldData);
        }
        default : {
            return true;
        }
    }
}

export function isAdd(oldData) {
    return oldData !== undefined && oldData === null;
}

export function isEdit(oldData) {
    return oldData !== undefined && oldData !== null;
}

export function trimString(stringToTrim: string): string {
    return stringToTrim.trim();
}

export function convertLabelIdsToNames(labelIds, labelList) {
    const label = labelList.find(label => label.id === labelIds);
    return label ? label.name.replaceAll('-', '').trim() : '';
}

export function getFieldDataTable(value) {
    return value = value !== undefined ? value : null;
}

export function isEmptyList(dataList: any): boolean {
    return !dataList || dataList.length === 0;
}

export function preventSpecialCharacter(keyboardEvent: KeyboardEvent): void {
    if (keyboardEvent.key === 'e' || keyboardEvent.key === 'E') {
        keyboardEvent.preventDefault();
    }
}

export function isValidPhoneNumber(phoneNumber) {
    const phonePattern = /^0\d{9,10}$/;

    if (phonePattern.test(phoneNumber)) {
        return true;
    }
    return false;
}

export function convert84to0PhoneNumber(phoneNumber :string) {
    if(phoneNumber.startsWith("84")){
        phoneNumber = "0" + phoneNumber.substring(2);
    }
    return phoneNumber;
}
