export default {
    label: {
        name: "<PERSON><PERSON><PERSON> cảnh báo",
        customer: "<PERSON><PERSON><PERSON><PERSON> hàng",
        contractCode: "<PERSON><PERSON> hợp đồng",
        statusSIM: "Tình trạng thuê bao",
        subscriptionNumber: "<PERSON><PERSON> thuê bao",
        group: "<PERSON><PERSON><PERSON><PERSON> thuê bao",
        status: "Trạng thái",
        afterTime: "Sau thời gian",
        afterCount: "Sau số lần",
        unit: "Đơn vị",
        unitValue: "Giá trị",
        description: "<PERSON><PERSON> tả",
        level: "Mức độ",
        groupReceiving: "Nhóm nhận cảnh báo",
        url: "URL:",
        emails: "Email nhận cảnh báo",
        topic: "Ch<PERSON> đề email",
        contentEmail: "Nội dung cảnh báo",
        contentSms: "Nội dung SMS",
        sms: "Số điện thoại nhận SMS",
        alertreceivinggroup: "Tên nhóm nhận cảnh báo",
        simstatus: "<PERSON>ì<PERSON> trạng Sim",
        time: "Thời gian",
        fromdate: "Thời gian từ",
        todate: "Thời gian đến",
        minutes: "<PERSON>út",
        news: "Bản tin",
        rule: "Loại cảnh báo",
        event: "Điều kiện kích hoạt",
        action: "Loại hành động",
        appliedPlan: "Gói cước áp dụng",
        frequency:"Tần suất lặp",
        repeat:"Lặp lại",
        exceededPakage:"Ngưỡng gói cước (%)",
        exceededValue: "Ngưỡng giá trị (MB)",
        smsExceededPakage:"Ngưỡng SMS (%)",
        smsExceededValue:"Ngưỡng giá trị (Số lượng)",
        inactivePopup: "Bấm để ngừng hoạt động" ,
        activePopup :"Bấm để kích hoạt",
        wallet: "Ví lưu lượng",
        thresholdValue: "Giá trị ngưỡng",
        walletEmail: "Email chủ ví nhận cảnh báo",
        walletPhone: "SĐT chủ ví nhận cảnh báo",
        fromDate: "Ngày tạo từ",
        toDate: "Ngày tạo đến",
        userCreated: "Tên đăng nhập tạo",
        deviceType: "Loại thiết bị",
        model: "Mẫu thiết bị",
        createdDate: "Ngày tạo",
        info: "Thông tin chung",
        alertInfoConfig: "Thông tin cấu hình cảnh báo",
        filterInforApplied: "Lọc thông tin áp dụng",
        individual: "Khách hàng cá nhân",
        business: "Khách hàng doanh nghiệp",
        sendMail: "Gửi Email",
        sendSMS: "Gửi SMS",
        sendZalo: "Gửi tin nhắn Zalo",
        sendAlertImmediate: "Gửi cảnh báo ngay",
        zalo: "Số điện thoại nhận tin nhắn Zalo",
        contentZalo: "Nội dung tin nhắn Zalo",
    },
    text: {
        inputName: "Nhập tên cảnh báo",
        inputStatusSIM: "Chọn tình trạng thuê bao",
        inputCustomer: "Chọn khách hàng",
        inputContractCode: "Chọn mã hợp đồng",
        inputSubscriptionNumber: "Chọn số thuê bao",
        inputGroup: "Chọn nhóm thuê bao",
        inputafterTime: "Nhập thời gian",
        inputafterCount: "Nhập số lần",
        inputunit: "Chọn đơn vị",
        inputunitValue: "Nhập giá trị",
        inputDescription: "Nhập mô tả",
        inputlevel: "Chọn mức độ",
        inputgroupReceiving: "Chọn nhóm nhận cảnh báo",
        inputurl: "Nhập URL:",
        inputemails: "Nhập email nhận cảnh báo, ngăn cách nhau bởi dấu phẩy ",
        inputtopic: "Nhập chủ đề email",
        inputcontentEmail: "[EventType] thiết bị [DeviceName] - IMEI: [IMEI]",
        inputcontentSms: "Nhập nội dung SMS",
        inputsms: "Nhập số điện thoại nhận SMS",
        headerAPI: "Nhận cảnh báo qua API",
        headerEmail: "Nhận cảnh báo qua Email",
        headerSMS: "Nhận cảnh báo qua SMS",
        labelAlert: "Thông tin nhận cảnh báo",
        inputNameReceiving: "Nhập tên nhóm nhận cảnh báo",
        removeAlert: "Xóa Email",
        removeSms: "Xóa SMS",
        rule: "Chọn loại",
        eventType: "Chọn điều kiện kích hoạt",
        appliedPlan: "Nhập gói cước áp dụng",
        actionType: "Chọn phương thức nhập cảnh bảo",
        filterApplieInfo: "Lọc thông tin áp dụng",
        sendNotifyExpiredData : "Gửi thông báo khi gói sắp hết hiệu lực trước",
        hour: "giờ",
        day : "Ngày",
        sendType : "Hình thức gửi thông báo",
        inputZalo: "Nhập số điện thoại nhận tin nhắn Zalo",
        noSchema: "Không có dữ liệu đầu vào của loại thiết bị này",
    },
    status: {
        active: "Hoạt động",
        inactive: "Tạm ngưng",
    },
    type: {
        admin: "Admin",
        customer: "Customer",
        province: "Province",
        district: "District",
        agency: "Agency",
    },


    receiving: {
        name: "Tên nhóm nhận cảnh báo",
        description: "Mô tả",
        emails: "Email",
        sms: "SMS",
    },
    event: {
        thresholddatapacket: "Data chạm ngưỡng gói",
        thresholddatavalue: "Data chạm ngưỡng giá trị",
        lostconnecting: "Mất kết nối",
        connecting: "Phát sinh kết nối",
        purgesim: "Huỷ SIM",
    },
    severity: {
        critical: "Nghiêm trọng",
        major: "Cao",
        minor: "Trung bình",
        info: "Thấp",
    },
    eventType:{
        deviceAlert: "Cảnh báo từ thiết bị",
        exceededValue: "Vượt ngưỡng giá trị",
    },
    actionType:{
        alert:"Cảnh báo",
        api:"API"
    },
    ruleCategory:{
        monitoring:"Giám sát sử dụng",
        management:"Quản lý sử dụng gói cước",
    },
    message:{
        existedPlan:"Gói cước đã được sử dụng",
        checkboxRequired : "Vui lòng chọn ít nhất 1 check box",
        exceededPakage: "Cảnh báo thuê bao [msisdn] sử dụng Data chạm ngưỡng [value] % của gói cước [plan_name]",
        smsExceededPakage: "Cảnh báo thuê bao [msisdn] sử dụng SMS chạm ngưỡng [value] % của gói cước [plan_name]",
        exceededValue: "Cảnh báo thuê bao [msisdn] sử dụng Data chạm ngưỡng giá trị [value] MB",
        smsExceededValue: "Cảnh báo thuê bao [msisdn] sử dụng số lượng SMS chạm ngưỡng giá trị [value]",
        status: "Cảnh báo thuê bao [msisdn] đã [status]",
        requiredValue: "Phải nhập tối thiểu 1 cấu hình cảnh báo"
    }

}
