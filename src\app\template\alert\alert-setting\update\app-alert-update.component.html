<form action="" [formGroup]="formAlert" (submit)="onSubmitUpdate()">
    <div class="vnpt flex flex-row justify-content-between align-items-center bg-white p-2 border-round">
        <div class="">
            <div class="text-xl font-bold mb-1">{{ tranService.translate("global.titlepage.editAlarm") }}</div>
            <p-breadcrumb class="max-w-full col-7" [model]="items" [home]="home"></p-breadcrumb>
        </div>
        <div class="col-5 flex flex-row justify-content-end align-items-center">
            <p-button styleClass="p-button-info mr-2"
                      [disabled]="checkDisableSave()"
                      type="submit"
                      [label]="tranService.translate('global.button.save')"
                      icon=""
                      *ngIf="checkAuthen([CONSTANTS.PERMISSIONS.DEVICE.UPDATE])"></p-button>
            <p-button styleClass="p-button-secondary p-button-outlined mr-2" (click)="goBack()">
                {{ tranService.translate("global.button.cancel") }}
            </p-button>
        </div>
    </div>

    <p-card class="p-4">
        <div class="flex flex-row">
            <h4 class="ml-2 mr-4">{{ tranService.translate("alert.label.info") }}</h4>
            <div class="flex-1">
                <div class="field-radiobutton">
                    <p-radioButton
                        name="timeSend"
                        value="immediate"
                        [(ngModel)]="alertInfo.timeSend"
                        formControlName="timeSend"
                        [label]="tranService.translate('alert.label.sendAlertImmediate')">
                    </p-radioButton>
                </div>
            </div>
        </div>

        <div class="shadow-2 border-round-md m-1 flex p-fluid p-formgrid grid">
            <div class="col-6">
                <!-- ten canh bao -->
                <div class="col-9 flex flex-row justify-content-between align-items-start pb-0">
                    <label htmlFor="name" style="width:200px">{{ tranService.translate("alert.label.name") }}<span
                        class="text-red-500">*</span></label>
                    <div style="width: calc(100% - 200px)" class="relative">
                        <input class="w-full"
                               pInputText id="name"
                               [(ngModel)]="alertInfo.name"
                               formControlName="name"
                               [required]="true"
                               [maxLength]="50"
                               [placeholder]="tranService.translate('alert.text.inputName')"
                               [pKeyFilter]="vietnamesePattern"
                        />
                        <div class="field">
                            <small class="text-red-500 block"
                                   *ngIf="formAlert.controls.name.dirty && formAlert.controls.name.errors?.required">{{ tranService.translate("global.message.required") }}</small>
                            <small class="text-red-500 block"
                                   *ngIf="formAlert.controls.name.errors?.pattern">{{ tranService.translate("global.message.formatPhone") }}</small>
                        </div>
                    </div>
                </div>
                <div class="col-9 flex flex-row justify-content-between align-items-start pb-0">
                    <label htmlFor="name" style="width:200px">{{ tranService.translate("device.label.model") }}<span
                        class="text-red-500">*</span></label>
                    <div style="width: calc(100% - 200px)" class="relative">
                        <vnpt-select
                            [control]="controlComboSelectModel"
                            class="w-full"
                            [(value)]="alertInfo.model"
                            [placeholder]="tranService.translate('device.label.model')"
                            objectKey="deviceModel"
                            paramKey="modelCode"
                            keyReturn="modelCode"
                            displayPattern="${modelCode}"
                            typeValue="primitive"
                            [required]="true"
                            [isMultiChoice]="false"
                            (onSelectItem)="onSelectedModel()"
                            (onClear)="onClearDeivceTypeOrModel()"
                            [paramDefault]="paramSearchModel"
                            [pKeyFilter]="vietnamesePattern"
                        ></vnpt-select>
                        <div class="field">
                            <small class="text-red-500 block"
                                   *ngIf="controlComboSelectModel.dirty && controlComboSelectModel.error?.required">{{ tranService.translate("global.message.required") }}</small>
                        </div>
                    </div>
                </div>
                <!-- muc do -->
                <div class="col-9 flex flex-row justify-content-between align-items-start pb-0 pt-3">
                    <label for="severity" style="width:200px">{{ tranService.translate("alert.label.level") }}<span
                        class="text-red-500">*</span></label>
                    <div style="width: calc(100% - 200px)">
                        <p-dropdown styleClass="w-full"
                                    id="severity" [autoDisplayFirst]="false"
                                    [(ngModel)]="alertInfo.severity"
                                    [required]="true"
                                    formControlName="severity"
                                    [options]="severityOptions"
                                    optionLabel="name"
                                    optionValue="value"
                                    [placeholder]="tranService.translate('alert.text.inputlevel')"
                        ></p-dropdown>
                    </div>
                </div>
            </div>
            <div class="col-6">
                <div class="col-9 flex flex-row justify-content-between align-items-start pb-0">
                    <label htmlFor="name" style="width:200px">{{ tranService.translate("device.label.type") }}<span
                        class="text-red-500">*</span></label>
                    <div style="width: calc(100% - 200px)" class="relative">
                        <vnpt-select
                            [control]="controlComboSelectType"
                            class="w-full"
                            [(value)]="alertInfo.deviceType"
                            [placeholder]="tranService.translate('device.label.type')"
                            objectKey="deviceType"
                            paramKey="typeName"
                            keyReturn="typeCode"
                            displayPattern="${typeName}"
                            [required]="true"
                            typeValue="primitive"
                            [isMultiChoice]="false"
                            (onchange)="onSelectedType()"
                            (onClear)="onClearDeivceTypeOrModel()"
                            [paramDefault]="paramSearchType"
                            [pKeyFilter]="vietnamesePattern"
                        ></vnpt-select>
                        <div class="field">
                            <small class="text-red-500 block"
                                   *ngIf="controlComboSelectType.dirty && controlComboSelectType.error?.required">{{ tranService.translate("global.message.required") }}</small>
                        </div>
                    </div>
                </div>

                <!-- loai -->
                <div class="col-9 flex flex-row justify-content-between align-items-start pb-0">
                    <label for="ruleCategory" style="width:200px">{{ tranService.translate("alert.label.rule") }}<span
                        class="text-red-500">*</span></label>
                    <div style="width: calc(100% - 200px)">
                        <p-dropdown styleClass="w-full"
                                    id="ruleCategory" [autoDisplayFirst]="false"
                                    [(ngModel)]="alertInfo.eventType"
                                    [required]="true"
                                    formControlName="eventType"
                                    [options]="eventOptions"
                                    optionLabel="name"
                                    optionValue="value"
                                    [placeholder]="tranService.translate('alert.text.rule')"
                                    (onChange)="changeEventType()"
                        ></p-dropdown>
                        <div class="field"></div>
                    </div>
                </div>
                <div class="col-9 flex flex-row justify-content-center align-items-start pb-2">
                    <label htmlFor="description"
                           style="width:200px">{{ tranService.translate("alert.label.description") }}</label>
                    <div style="width: calc(100% - 200px)">
                    <textarea pInputText
                              class="col-8"
                              pInputText id="description"
                              [(ngModel)]="alertInfo.description"
                              formControlName="description"
                              name="description"
                              [maxlength]="255"
                    ></textarea>
                    </div>
                </div>
            </div>
        </div>


        <div *ngIf="inputSchema.length > 0">
            <h4 class="ml-2">{{ tranService.translate("alert.label.alertInfoConfig") }}<span
                class="text-red-500">*</span></h4>
            <div class="shadow-2 border-round-md m-1 flex p-fluid p-formgrid grid">

                <div *ngFor="let input of inputSchema"
                     class="justify-content-between align-items-center pb-2 col-6">
                    <div
                        *ngIf="alertInfo.eventType == CONSTANTS.ALERT_EVENT_TYPE.EXCEEDED_VALUE">
                        <div class="col-9 flex flex-row justify-content-between align-items-center pb-0">

                            <label
                                style="width:200px">{{ utilService.getLabel(input) }}<span
                                class="text-red-500"></span></label>
                            <div style="width: calc(100% - 200px)" class="relative">
                                <input class="w-full"
                                       [id]="utilService.getKey(input.key)"
                                       [formControlName]="utilService.getKey(input.key)"
                                       pInputText
                                       [pKeyFilter]="numberWithOneDot"
                                       [placeholder]="utilService.getLabel(input)"
                                       (keypress)="preventMultipleDot($event)"
                                />
                            </div>
                        </div>
                    </div>
                    <div *ngIf="alertInfo.eventType == CONSTANTS.ALERT_EVENT_TYPE.DEVICE_ALERT">
                        <div class="col-9 flex flex-row justify-content-between align-items-center pb-2">
                            <span>{{utilService.getLabel(input)}}</span>
                            <p-inputSwitch
                                class="mb-1"
                                [id]="utilService.getKey(input.key)"
                                [formControlName]="utilService.getKey(input.key)"
                            >
                            </p-inputSwitch>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div *ngIf="alertInfo.deviceTypeId">
        <h4 class="ml-2">{{ tranService.translate("alert.label.filterInforApplied") }}<span
            class="text-red-500"></span></h4>
        <div class="p-3 pt-0 shadow-2 border-round-md m-1 flex p-fluid p-formgrid grid">
            <!--            <div style="width: 49%">-->
            <div class="col-6" *ngIf="userType == CONSTANTS.USER_TYPE.ADMIN">
                <div class="col-9 flex flex-row justify-content-between align-items-start pb-0">
                    <label htmlFor="name" style="width:200px">{{ tranService.translate("alert.label.business") }}<span
                        class="text-red-500">*</span></label>
                    <div style="width: calc(100% - 200px)" class="relative">
                        <vnpt-select
                            [control]="controlComboSelectBusiness"
                            class="w-full"
                            [(value)]="alertInfo.userEnterpriseId"
                            [placeholder]="tranService.translate('device.input.businessName')"
                            objectKey="account"
                            paramKey="name"
                            keyReturn="id"
                            displayPattern="${name}"
                            typeValue="primitive"
                            [isMultiChoice]="false"
                            [paramDefault]="paramSearchBusiness"
                            (onClear)="onClearBusiness()"
                            (onSelectItem)="onSelectedBusiness()"
                            [pKeyFilter]="vietnamesePattern"
                            [required]="true"
                        ></vnpt-select>
                        <div>
                                <small class="text-red-500 block"
                                       *ngIf="controlComboSelectBusiness.dirty && controlComboSelectBusiness.error?.required">{{ tranService.translate("global.message.required") }}</small>
                        </div>
                    </div>
                </div>
            </div>
            <!--            </div>-->
            <div class="col-6"
                 *ngIf="userType == CONSTANTS.USER_TYPE.ADMIN || userType == CONSTANTS.USER_TYPE.BUSINESS">
                <div class="col-9 flex flex-row justify-content-between align-items-start pb-0">
                    <label htmlFor="name" style="width:200px">{{ tranService.translate("alert.label.individual") }}<span
                        class="text-red-500">*</span></label>
                    <div style="width: calc(100% - 200px)" class="relative">
                        <vnpt-select
                            [control]="controlComboSelectIndividual"
                            class="w-full"
                            [(value)]="alertInfo.userCustomerId"
                            [placeholder]="tranService.translate('device.label.individualName')"
                            objectKey="account"
                            paramKey="name"
                            keyReturn="id"
                            displayPattern="${name}"
                            typeValue="primitive"
                            [isMultiChoice]="false"
                            [paramDefault]="paramSearchIndividual"
                            (onClear)="onClearIndividual()"
                            (onchange)="onSelectedIndividual()"
                            [pKeyFilter]="vietnamesePattern"
                            [required]="true"
                        ></vnpt-select>
                        <div>
                            <small class="text-red-500 block"
                                   *ngIf="controlComboSelectIndividual.dirty && controlComboSelectIndividual.error?.required">{{ tranService.translate("global.message.required") }}</small>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-6">
                <div class="col-9 flex flex-row justify-content-between align-items-start pb-0">
                    <label htmlFor="name" style="width:200px">{{ tranService.translate("permission.Device.Device") }}
                        <span
                            class="text-red-500">*</span></label>
                    <div style="width: calc(100% - 200px)" class="relative">
                        <vnpt-select
                            [control]="controlComboSelectDevice"
                            class="w-full"
                            [(value)]="alertInfo.deviceId"
                            [placeholder]="tranService.translate('permission.Device.Device')"
                            objectKey="device"
                            paramKey="deviceName"
                            keyReturn="id"
                            displayPattern="${deviceName} - ${imei}"
                            typeValue="primitive"
                            [isMultiChoice]="false"
                            [pKeyFilter]="vietnamesePattern"
                            [paramDefault]="paramSearchDevice"
                            [required]="true"
                        ></vnpt-select>
                        <div>
                            <small class="text-red-500 block"
                                   *ngIf="controlComboSelectDevice.dirty && controlComboSelectDevice.error?.required">{{ tranService.translate("global.message.required") }}</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        </div>

        <div class="ml-2 my-4 flex flex-row justify-content-start align-items-center gap-3">
            <h4 class="ml-2 mr-2">{{ tranService.translate("alert.text.labelAlert") }}<span
                class="text-red-500">*</span></h4>
            <div>
                <p-multiSelect styleClass="w-full"
                               id="sendingMethod"
                               [(ngModel)]="alertInfo.sendingMethod"
                               [required]="true"
                               formControlName="sendingMethod"
                               [options]="optionSend"
                               [placeholder]="tranService.translate('alert.text.actionType')"
                               (onChange)="changeTypeSendAlert()"
                               [showHeader]="false"
                ></p-multiSelect>
            </div>
        </div>


        <div class="p-3 pt-0 shadow-2 border-round-md m-1  " *ngIf="alertInfo.sendingMethod.length > 0">
            <p-tabView class="w-full">
                <p-tabPanel *ngIf="alertInfo.sendingMethod.includes('Email')"
                            [header]="tranService.translate('alert.label.sendMail')">
                    <div class="flex flex-column w-full">
                        <div class="flex w-full justify-content-start align-items-center">
                            <!-- email -->
                            <div class="col">
                                <div class="col-9 flex flex-row justify-content-start pb-0">
                                    <label class="col-fixed" htmlFor="emailList"
                                           style="width:200px; height: fit-content;">{{ tranService.translate("alert.label.emails") }}
                                        <span class="text-red-500">*</span></label>
                                    <div style="width: calc(100% - 200px)">
                            <textarea class="w-full" style="resize: none;"
                                      rows="3"
                                      [autoResize]="false"
                                      pInputTextarea id="emailList"
                                      [(ngModel)]="alertInfo.emailList"
                                      formControlName="emailList"
                                      [placeholder]="tranService.translate('alert.text.inputemails')"
                                      pattern="^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}(?:, ?[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})*$"
                                      [required]="true"
                            ></textarea>
                                    </div>
                                </div>
                                <!-- emailList-->
                                <div class="col-9 flex justify-content-start pb-0"
                                     *ngIf="formAlert.controls.emailList.dirty && formAlert.controls.emailList.errors?.required ||
formAlert.controls.emailList.dirty && checkExistEmailList() || formAlert.controls.emailList.dirty && checkLimitEmail() || formAlert.controls.emailList.errors?.pattern"
                                >
                                    <label htmlFor="emailList" class="col-fixed" style="width:200px"></label>
                                    <div class="">
                                        <small class="text-red-500 block"
                                               *ngIf="formAlert.controls.emailList.dirty && formAlert.controls.emailList.errors?.required">{{ tranService.translate("global.message.required") }}</small>
                                        <small class="text-red-500 block"
                                               *ngIf="formAlert.controls.emailList.dirty && checkExistEmailList()">{{ tranService.translate("global.message.emailExist") }}</small>
                                        <small class="text-red-500 block"
                                               *ngIf="formAlert.controls.emailList.dirty && checkLimitEmail()">{{ tranService.translate("global.message.limitEmail", {limit: CONSTANTS.ALERT_LIMIT.EMAIL}) }}</small>
                                        <small class="text-red-500 block"
                                               *ngIf="formAlert.controls.emailList.errors?.pattern">{{ tranService.translate("global.message.formatEmail") }}</small>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col">
                            <!-- noi dung email -->
                            <div class="col-9 flex flex-row justify-content-start pb-0">
                                <label class="col-fixed" htmlFor="emailContent"
                                       style="width:200px; height: fit-content;">{{ tranService.translate("alert.label.contentEmail") }}
                                    <span class="text-red-500">*</span></label>
                                <div style="width: calc(100% - 200px);">
                            <textarea class="w-full" style="resize: none;"
                                      rows="5"
                                      [autoResize]="false"
                                      pInputTextarea id="emailContent"
                                      [(ngModel)]="alertInfo.emailContent"
                                      formControlName="emailContent"
                                      [maxlength]="255"
                                      [placeholder]="tranService.translate('alert.text.inputcontentEmail')"
                                      [required]="true"
                            ></textarea>
                                    <div class="field"
                                         *ngIf="formAlert.controls.emailContent.dirty && formAlert.controls.emailContent.errors?.required">
                                        <small class="text-red-500"
                                               *ngIf="formAlert.controls.emailContent.dirty && formAlert.controls.emailContent.errors?.required">{{ tranService.translate("global.message.required") }}</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </p-tabPanel>
                <p-tabPanel *ngIf="alertInfo.sendingMethod.includes('SMS')"
                            [header]="tranService.translate('alert.label.sendSMS')">
                    <div class="flex flex-column w-full">
                        <!--                <div class="flex col-3 justify-content-start align-items-center">-->
                        <!--                    <div class="field-radiobutton">-->
                        <!--                        <p-radioButton-->
                        <!--                            name="sendingMethod"-->
                        <!--                            value="email"-->
                        <!--                            [(ngModel)]="alertInfo.sendingMethod"-->
                        <!--                            formControlName="sendingMethod"-->
                        <!--                            [label]="tranService.translate('alert.label.sendMail')">-->
                        <!--                        </p-radioButton>-->
                        <!--                    </div>-->
                        <!--                </div>-->
                        <div class="flex w-full justify-content-start align-items-center">
                            <!-- SMS -->
                            <div class="col">
                                <div class="col-9 flex flex-row justify-content-start pb-0">
                                    <label class="col-fixed" htmlFor="msisdnsNotify"
                                           style="width:200px; height: fit-content;">{{ tranService.translate("alert.label.sms") }}
                                        <span class="text-red-500">*</span></label>
                                    <div style="width: calc(100% - 200px)">
                            <textarea class="w-full" style="resize: none;"
                                      rows="1"
                                      [autoResize]="false"
                                      pInputTextarea id="msisdnsNotify"
                                      [(ngModel)]="alertInfo.msisdnsNotify"
                                      formControlName="msisdnsNotify"
                                      [placeholder]="tranService.translate('alert.text.inputsms')"
                                      pattern="^(?:0|84)\d{9,10}(?:, ?(?:0|84)\d{9,10})*$"
                                      [required]="true"
                            ></textarea>
                                    </div>
                                </div>
                                <!-- msisdnsNotify-->
                                <div class="col-9 flex justify-content-start pb-0"
                                     *ngIf="formAlert.controls.msisdnsNotify.dirty && formAlert.controls.msisdnsNotify.errors?.required ||
formAlert.controls.msisdnsNotify.dirty && checkExistSmsList() || formAlert.controls.msisdnsNotify.dirty && checkLimitSms() || formAlert.controls.msisdnsNotify.errors?.pattern"
                                >
                                    <label htmlFor="msisdnsNotify" class="col-fixed" style="width:200px"></label>
                                    <div class="">
                                        <small class="text-red-500 block"
                                               *ngIf="formAlert.controls.msisdnsNotify.dirty && formAlert.controls.msisdnsNotify.errors?.required">{{ tranService.translate("global.message.required") }}</small>
                                        <small class="text-red-500 block"
                                               *ngIf="formAlert.controls.msisdnsNotify.dirty && checkExistSmsList()">{{ tranService.translate("global.message.smsExist") }}</small>
                                        <small class="text-red-500 block"
                                               *ngIf="formAlert.controls.msisdnsNotify.dirty && checkLimitSms()">{{ tranService.translate("global.message.limitSms", {limit: CONSTANTS.ALERT_LIMIT.SMS}) }}</small>
                                        <small class="text-red-500 block"
                                               *ngIf="formAlert.controls.msisdnsNotify.errors?.pattern">{{ tranService.translate("global.message.formatPhone") }}</small>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col">
                            <!-- noi dung sms -->
                            <div class="col-9 flex flex-row justify-content-start pb-0">
                                <label class="col-fixed" htmlFor="smsContent"
                                       style="width:200px; height: fit-content;">{{ tranService.translate("alert.label.contentSms") }}
                                    <span class="text-red-500">*</span></label>
                                <div style="width: calc(100% - 200px);">
                            <textarea class="w-full" style="resize: none;"
                                      rows="5"
                                      [autoResize]="false"
                                      pInputTextarea id="smsContent"
                                      [(ngModel)]="alertInfo.smsContent"
                                      formControlName="smsContent"
                                      [maxlength]="255"
                                      [placeholder]="tranService.translate('alert.text.inputcontentEmail')"
                                      [required]="true"
                            ></textarea>
                                    <div class="field"
                                         *ngIf="formAlert.controls.smsContent.dirty && formAlert.controls.smsContent.errors?.required">
                                        <small class="text-red-500"
                                               *ngIf="formAlert.controls.smsContent.dirty && formAlert.controls.smsContent.errors?.required">{{ tranService.translate("global.message.required") }}</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </p-tabPanel>
                <p-tabPanel *ngIf="alertInfo.sendingMethod.includes('Zalo')"
                            [header]="tranService.translate('alert.label.sendZalo')">
                    <div class="flex flex-column w-full">
                        <div class="flex w-full justify-content-start align-items-center">
                            <!-- SMS -->
                            <div class="col">
                                <div class="col-9 flex flex-row justify-content-start pb-0">
                                    <label class="col-fixed" htmlFor="zaloNotify"
                                           style="width:200px; height: fit-content;">{{ tranService.translate("alert.label.zalo") }}
                                        <span class="text-red-500">*</span></label>
                                    <div style="width: calc(100% - 200px)">
                            <textarea class="w-full" style="resize: none;"
                                      rows="1"
                                      [autoResize]="false"
                                      pInputTextarea id="zaloNotify"
                                      [(ngModel)]="alertInfo.zaloNotify"
                                      formControlName="zaloNotify"
                                      [placeholder]="tranService.translate('alert.text.inputZalo')"
                                      pattern="^(?:0|84)\d{9,10}(?:, ?(?:0|84)\d{9,10})*$"
                                      [required]="true"
                            ></textarea>
                                    </div>
                                </div>
                                <!--                         zaloNotify-->
                                <div class="col-9 flex justify-content-start pb-0"
                                     *ngIf="formAlert.controls.zaloNotify.dirty && formAlert.controls.zaloNotify.errors?.required ||
formAlert.controls.zaloNotify.dirty && checkExistZaloList() || formAlert.controls.zaloNotify.dirty && checkLimitZalo() || formAlert.controls.zaloNotify.errors?.pattern"
                                >
                                    <label htmlFor="zaloNotify" class="col-fixed" style="width:200px"></label>
                                    <div class="">
                                        <small class="text-red-500 block"
                                               *ngIf="formAlert.controls.zaloNotify.dirty && formAlert.controls.zaloNotify.errors?.required">{{ tranService.translate("global.message.required") }}</small>
                                        <small class="text-red-500 block"
                                               *ngIf="formAlert.controls.zaloNotify.dirty && checkExistZaloList()">{{ tranService.translate("global.message.smsExist") }}</small>
                                        <small class="text-red-500 block"
                                               *ngIf="formAlert.controls.zaloNotify.dirty && checkLimitZalo()">{{ tranService.translate("global.message.limitSms", {limit: CONSTANTS.ALERT_LIMIT.ZALO}) }}</small>
                                        <small class="text-red-500 block"
                                               *ngIf="formAlert.controls.zaloNotify.errors?.pattern">{{ tranService.translate("global.message.formatPhone") }}</small>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col">
                            <!-- noi dung sms -->
                            <div class="col-9 flex flex-row justify-content-start pb-0">
                                <label class="col-fixed" htmlFor="smsContent"
                                       style="width:200px; height: fit-content;">{{ tranService.translate("alert.label.contentZalo") }}
                                    <span class="text-red-500">*</span></label>
                                <div style="width: calc(100% - 200px);">
                            <textarea class="w-full" style="resize: none;"
                                      rows="5"
                                      [autoResize]="false"
                                      pInputTextarea id="zaloContent"
                                      [(ngModel)]="alertInfo.zaloContent"
                                      formControlName="zaloContent"
                                      [maxlength]="255"
                                      [placeholder]="tranService.translate('alert.text.inputcontentEmail')"
                                      [required]="true"
                            ></textarea>
                                    <div class="field"
                                         *ngIf="formAlert.controls.zaloContent.dirty && formAlert.controls.zaloContent.errors?.required">
                                        <small class="text-red-500"
                                               *ngIf="formAlert.controls.zaloContent.dirty && formAlert.controls.zaloContent.errors?.required">{{ tranService.translate("global.message.required") }}</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </p-tabPanel>
            </p-tabView>

        </div>
    </p-card>
</form>
