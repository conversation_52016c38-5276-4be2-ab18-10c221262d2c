.item-select{
    min-width: 100%;
    width: fit-content;
    white-space: nowrap;
    color: black;
    padding: 0px 12px;
}

.item-select-content {
    font-size: 14px;
    line-height: 14px;
    padding: 12px 0px;
    max-width: 750px;
    overflow: hidden;
    text-overflow: ellipsis;
}

.even {
    background-color: white;
}

.odd {
    background-color: #dee2e6;
}

.item-select-focus {
    background-color: #DDDDDD;
}

.item-select:hover{
    background-color: #e9ecef;
}

.box-select {
    position: relative;
    width: fit-content;
    box-sizing: border-box;
    border: 1px solid #DDDDDD;
    border-radius: 5px;
    position: absolute;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    z-index: 2102;
}

.box-item {
    min-width: 100%;
    width: fit-content;
    max-height: 190px;
    overflow-y: scroll;
    position: relative;
}

.block-box-item{
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: rgba(190, 190, 190, 0.8);
    position: absolute;
    z-index: 1;
}

.box-item-empty {
    width: 100%;
    display: flex;
    box-sizing: border-box;
    justify-content: center;
    align-items: center;
    height: 190px;
}

.select-zero{
    cursor: pointer;
    border: 2px solid #ced4da;
    background: #ffffff;
    width: 22px;
    height: 22px;
    color: #495057;
    border-radius: 6px;
    transition: background-color 0.2s, color 0.2s, border-color 0.2s, box-shadow 0.2s;
}
.select-zero:hover{
    border-color: var(--primary-color);
}

.select-some{
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    border: 2px solid #ced4da;
    background: #ffffff;
    width: 22px;
    height: 22px;
    color: #495057;
    border-radius: 6px;
    transition: background-color 0.2s, color 0.2s, border-color 0.2s, box-shadow 0.2s;
}
.select-some:hover{
    border-color: var(--primary-color);
}

.select-all{
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    border: 2px solid var(--primary-color);
    background: var(--primary-color);
    width: 22px;
    height: 22px;
    color: white;
    border-radius: 6px;
    transition: background-color 0.2s, color 0.2s, border-color 0.2s, box-shadow 0.2s;
}
.select-all:hover{
    border-color: var(--indigo-700);
    background: var(--indigo-700);
}

.box-value-single{
    box-sizing: border-box;
    font-size: 14px;
    max-width: 100%;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    padding: 7.5px 12px;
}

.box-value-multi{
    display: flex;
    justify-content: flex-start;
    align-items: center;
    flex-wrap: nowrap;
    max-width: 100%;
    box-sizing: border-box;
    padding: 6px 12px;
    overflow: hidden;
}

.box-value-display{
    display: flex;
    justify-content: flex-start;
    align-items: center;
    font-size: 14px;
    line-height: 14px;
    padding: 5px;
    background-color: #EEF2FF;
    border-radius: 14px;
    color: var(--primary-color);
}

.box-value-display-disable{
    background-color: rgba(220,220,220,0.1);
    color: #ced4da;
}

.value-display{
    max-width: 150px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.box-dropdown{
    box-sizing: border-box;
    border: 1px solid #ced4da;
    border-radius: 8px;
}

.box-disable{
    background-color: #999999;
    color: #999999;
    border-color: #ced4da !important;
}

.box-focus{
    outline: 0 none;
    outline-offset: 0;
    box-shadow: 0 0 0 0.2rem #C7D2FE;
    border-color: #6366f1;
}

.box-dropdown:hover{
    border-color: var(--primary-color);
}

.box-invalid{
    border-color: #e24c4c !important;
}

.float-label{
    font-size: 12px;
    padding-left: 12px;
    color: #6c757d;
    display: block;
    position: absolute;
    top: -20px;
}
