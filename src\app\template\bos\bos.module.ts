import { NgModule } from "@angular/core";

import { CommonModule } from "@angular/common";
import { BreadcrumbModule } from "primeng/breadcrumb";
import { FieldsetModule } from "primeng/fieldset";
import { FormsModule, ReactiveFormsModule } from "@angular/forms";
import { InputTextModule } from "primeng/inputtext";
import { ButtonModule } from "primeng/button";
import { CommonVnptModule } from "../common-module/common.module";
import { SplitButtonModule } from "primeng/splitbutton";
import { AutoCompleteModule } from "primeng/autocomplete";
import { CalendarModule } from "primeng/calendar";
import { DropdownModule } from "primeng/dropdown";
import { CardModule } from "primeng/card";
import { DialogModule } from "primeng/dialog";
import { InputTextareaModule } from 'primeng/inputtextarea';
import { MultiSelectModule } from 'primeng/multiselect';
import {TabViewModule} from "primeng/tabview";
import { RadioButtonModule } from 'primeng/radiobutton';
import {InputSwitchModule} from "primeng/inputswitch";
import {PasswordModule} from "primeng/password";
import {BosSsoComponent} from "./bos-sso.component";
import {BosService} from "../../service/sso/BosService";
import {BosRoutingModule} from "./bos.routing";
import {ProgressSpinnerModule} from "primeng/progressspinner";

@NgModule({
    imports: [
        BosRoutingModule,
        CommonModule,
        BreadcrumbModule,
        FieldsetModule,
        FormsModule,
        ReactiveFormsModule,
        InputTextModule,
        ButtonModule,
        CommonVnptModule,
        SplitButtonModule,
        AutoCompleteModule,
        CalendarModule,
        DropdownModule,
        CardModule,
        DialogModule,
        InputTextareaModule,
        MultiSelectModule,
        TabViewModule,
        RadioButtonModule,
        InputSwitchModule,
        PasswordModule,
        ProgressSpinnerModule
    ],
    declarations: [
        BosSsoComponent
    ],
    providers: [
        BosService,
    ]
})
export class BosModule {}
