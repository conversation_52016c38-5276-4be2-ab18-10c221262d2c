import {Injectable} from '@angular/core';
import {BehaviorSubject, Observable, ReplaySubject, Subject, Subscription} from 'rxjs';
import {filter} from 'rxjs/operators';

@Injectable({
    providedIn: 'root'
})
export class MessageManagementService {

    private messageManager = new Subject<any>();
    private replayMessageManager = new ReplaySubject<any>();
    private behaviourSubjectManager = new BehaviorSubject<any>({});

    constructor() {
    }

    onMessage(idMessage: string, handleMessage: any): Subscription {
        return this.messageManager.pipe(filter(obj => obj.idMessage === idMessage)).subscribe((data: EventMessage) => {
            if (handleMessage && typeof handleMessage === 'function') {
                handleMessage(data.message);
            }
        });
    }

    onMessages(idMessages: string[], handleMessage: any): Subscription {
        return this.messageManager.pipe(filter(obj => idMessages.includes(obj.idMessage))).subscribe((data: EventMessage) => {
            if (handleMessage && typeof handleMessage === 'function') {
                handleMessage(data);
            }
        });
    }

    sendMessage(idMessage: string, message: any) {
        this.messageManager.next({idMessage, message});
    }

    getReplayObservable(idMessage: string): Observable<any> {
        return this.replayMessageManager.pipe(filter(message => message.idMessage === idMessage));
    }

    sendReplayMessage(idMessage: string, message: any) {
        this.replayMessageManager.next({idMessage, message});
    }

    onReplayMessage(idMessage: string, handleMessage): Subscription {
        return this.replayMessageManager.pipe(filter(obj => obj.idMessage === idMessage)).subscribe((data: EventMessage) => {
            if (handleMessage && typeof handleMessage === 'function') {
                handleMessage(data);
            }
        });
    }

    getBehaviorObservable(idMessage: string): Observable<any> {
        return this.behaviourSubjectManager.pipe(filter(message => message.idMessage === idMessage));
    }

    sendBehaviorMessage(idMessage: string, message: any) {
        this.behaviourSubjectManager.next({idMessage, message});
    }

    onBehaviorMessage(idMessage: string, handleMessage): Subscription {
        return this.behaviourSubjectManager.pipe(filter(obj => obj.idMessage === idMessage)).subscribe((data: EventMessage) => {
            if (handleMessage && typeof handleMessage === 'function') {
                handleMessage(data);
            }
        });
    }

    removeBehaviorMessage() {
        this.behaviourSubjectManager.next({});
    }
}

export interface EventMessage {
    idMessage: string;
    message: any;
}
