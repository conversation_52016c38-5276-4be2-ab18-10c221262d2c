<style>
    :host ::ng-deep {
        [pDraggable] {
            cursor: move;
            .button-scale-x {
                cursor: e-resize !important;
            }
            .button-scale-y {
                cursor: n-resize !important;
            }
            .button-scale-z {
                cursor: se-resize !important;
            }
        }
        [pDroppable] {
            .button-scale-x {
                cursor: e-resize !important;
            }
            .button-scale-y {
                cursor: n-resize !important;
            }
            .button-scale-z {
                cursor: se-resize !important;
            }
        }
    }
</style>

<div class="relative">
    <div id="boxWrapper" [style]="{
        minWidth: '100%',
        height: heightBoxDrag+'px',
        width: widthBoxDrag + 'px',
        padding: '12px',
        border: '1px dashed #777777',
        backgroundColor: '#CCCCCC',
        position: 'relative',
        boxSizing: 'content-box'
    }" pDroppable (onDrop)="drop($event)" (window:resize)="onResize($event)">
        <div *ngFor="let item of listObjectDrag" class="box-chart" [style]="{
            position: 'absolute',
            width: item.width+'px',
            height: item.height+'px',
            top: item.top+'px',
            left: item.left+'px',
            backgroundColor: item.backgroundColor,
            border: '1px dashed gray',
            zIndex: item.zIndex
        }" [id]="'abc'+item.id" pDraggable dragEffect="move" (onDragStart)="dragStart($event, item, 'object')" (onDragEnd)="dragEnd($event)" (onDrag)="drag($event)">
            <div class="w-full" style="height: 100%;position: relative;">
                <div [style.zIndex]="100 + item.zIndex + 1" class="button-scale-x" dragEffect="move" 
                    pDraggable (onDragStart)="dragXStart($event, item)" (onDragEnd)="dragEnd($event)" (onDrag)="dragX($event)"
                    style="width: 10px;height: 100%; position: absolute; top: 0;left: calc(100% - 5px);background-color: transparent;"></div>
                <div [style.zIndex]="100 + item.zIndex + 1" class="button-scale-y" pDraggable dragEffect="move"
                (onDragStart)="dragYStart($event, item)" (onDragEnd)="dragEnd($event)" (onDrag)="dragY($event)"
                 style="width: 100%;height: 10px; position: absolute; left: 0;top: calc(100% - 5px);background-color: transparent;"></div>
                <div [style.zIndex]="100 + item.zIndex + 2" class="button-scale-z" pDraggable dragEffect="move" 
                (onDragStart)="dragXYStart($event, item)" (onDragEnd)="dragEnd($event)" (onDrag)="dragXY($event)"
                style="width: 10px;height: 10px; position: absolute; top: calc(100% - 5px);left: calc(100% - 5px);background-color: transparent;">
                    
                </div>
                <div class="button-scale-z" style="width: 10px;height: 10px; position: absolute; top: calc(100% - 10px);left: calc(100% - 10px);background-color: transparent;">
                    <i class="pi pi-chevron-down" style="font-size: 10px;
                    transform: rotate(-45deg);
                    vertical-align: top;"></i>
                </div>
            </div>
        </div>
    </div>
</div>