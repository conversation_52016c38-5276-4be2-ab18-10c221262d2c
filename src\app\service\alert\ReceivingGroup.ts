import {Inject, Injectable} from "@angular/core";
import {HttpService} from "../comon/http.service";

@Injectable({
    providedIn: 'root'
})
export class ReceivingGroupService {
    private prefixApi: string;

    constructor(@Inject(HttpService) private httpService: HttpService) {
        this.prefixApi = "/receiving-group";
    }

    public createReceivingGroup(body,callback?: Function, errorCallback?:Function, finallyCallback?: Function){
        this.httpService.post(`${this.prefixApi}`, {},body,{}, callback, errorCallback, finallyCallback);
    }

    public getById(id: number, callback?: Function, errorCallback?:Function, finallyCallback?: Function){
        this.httpService.get(`${this.prefixApi}/${id}`,{}, {}, callback, errorCallback, finallyCallback);
    }
    public deleteById(id:number, callback?:Function, errorCallBack?:Function, finallyCallback?:Function){
        this.httpService.delete(`${this.prefixApi}/${id}`,{}, {},callback, errorCallBack, finallyCallback);
    }
    //
    // public changeStatus(body,callback?: Function, errorCallback?:Function, finallyCallback?: Function){
    //     this.httpService.post(`${this.prefixApi}/status`, {},body,{}, callback, errorCallback, finallyCallback);
    // }
    public updateReceivingGroup(id,body,callback?: Function, errorCallback?:Function, finallyCallback?: Function){
        this.httpService.put(`${this.prefixApi}/${id}`, {},body,{}, callback, errorCallback, finallyCallback);
    }
    public checkName(query:{[key:string]:any}, callback?:Function, errorCallBack?:Function, finallyCallback?:Function){
        this.httpService.get(`${this.prefixApi}/check-exist`,{}, query,callback, errorCallBack, finallyCallback);
    }

    public getListAlertReceivingGroup(params: {
        [key: string]: any
    }, callback?: Function, errorCallBack?: Function, finallyCallback?: Function) {
        this.httpService.get(`${this.prefixApi}/search`, {}, params, callback, errorCallBack, finallyCallback);
    }

    public search(params: {
        [key: string]: any
    }, callback?: Function) {
        this.httpService.get(`${this.prefixApi}/search`, {}, params, callback);
    }

    public deleleAlertReceivingGroup(id: number, callback?: Function, errorCallBack?: Function, finallyCallback?: Function) {
        this.httpService.delete(`${this.prefixApi}/${id}`, {}, {}, callback, errorCallBack, finallyCallback);
    }

    public deleleListAlertReceivingGroup(listId: Array<number>, callback?: Function, errorCallBack?: Function, finallyCallback?: Function) {
        this.httpService.post(`${this.prefixApi}/delete-many`, {}, listId,{}, callback, errorCallBack, finallyCallback);
    }
}
