import { NgModule } from "@angular/core";
import DataPage from "../../service/data.page";
import {CONSTANTS} from "../../service/comon/constants";
import { AppDashboardComponent } from "./app.dashboard.component";
import { RouterModule } from "@angular/router";
import { AppDashboard2Component } from "./app.dashboard2.component";

@NgModule({
    imports: [
        RouterModule.forChild([
            {path: "", component: AppDashboard2Component, data: new DataPage("Dashboard", [CONSTANTS.PERMISSIONS.DYNAMICCHART.VIEW_LIST])},
            {path: "2", component: AppDashboard2Component, data: new DataPage("Dashboard", [CONSTANTS.PERMISSIONS.DYNAMICCHART.VIEW_LIST])},
        ])
    ],
    exports: [RouterModule]
})
export class DashboardRoutingModule {}
