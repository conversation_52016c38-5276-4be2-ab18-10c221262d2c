import { Component } from '@angular/core';
import { FormBuilder } from "@angular/forms";
import { LayoutService } from 'src/app/service/app.layout.service';
import { TranslateService } from "src/app/service/comon/translate.service";
import {SessionService} from "../../../service/session/SessionService";
import {ActivatedRoute, Router} from "@angular/router";
import {MessageCommonService} from "../../../service/comon/message-common.service";
import {AccountService} from "../../../service/account/AccountService";
import {b} from "@fullcalendar/core/internal-common";

@Component({
    selector: 'app-reset-password',
    templateUrl: './reset-password.component.html',
    styles: [`
        :host ::ng-deep .pi-eye,
        :host ::ng-deep .pi-eye-slash {
            transform:scale(1.6);
            margin-right: 1rem;
            color: var(--primary-color) !important;
        }
    `]
})
export class ResetPasswordComponent{

    resetPasswordInfo : any
    isLinkExpired : boolean
    formResetPassword: any;

    constructor(public layoutService: LayoutService,
        public tranService: TranslateService,
        private formBuilder: FormBuilder,
        private sessionService: SessionService,
        private activatedRoute: ActivatedRoute,
        private route: Router,
        public messageCommonService: MessageCommonService,
        public accountService: AccountService
    ) { }
        ngOnInit(): void {
            let me = this;
            this.isLinkExpired = false;



            let token = this.activatedRoute.snapshot.queryParams['token']
            let email = this.activatedRoute.snapshot.queryParams['email']
            this.resetPasswordInfo = {
                email : email,
                forgotPasswordToken : token,
                newPassword : null,
                confirmPassword : null
            }
            this.accountService.validateTokenEmail(this.resetPasswordInfo,() =>{
                me.formResetPassword = me.formBuilder.group(me.resetPasswordInfo);
                // document.getElementById("email1").click();
            },()=>{
                me.isLinkExpired = true;
            })
        }

    resetPassword() {
        let me = this;
        me.messageCommonService.onload();
        this.accountService.forgotPasswordFinish(me.resetPasswordInfo,(resp) =>{
            this.route.navigate(['/login'])
        }, null, ()=>{
            me.messageCommonService.offload();
        })
    }
}


