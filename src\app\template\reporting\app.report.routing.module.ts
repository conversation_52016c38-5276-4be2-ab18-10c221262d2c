import { NgModule } from "@angular/core";
import { RouterModule } from "@angular/router";
import DataPage from "src/app/service/data.page";
import { ReportReceivingGroupComponent } from "./report-receiving-group/report.receiving.group.component";
import { ReportGroupReceivingEditComponent } from "./report-receiving-group/edit/app.group-receiving.edit.component";
import { ReportGroupReceivingDetailComponent } from "./report-receiving-group/detail/app.group-receiving.detail.component";
import { ReportGroupReceivingCreateComponent } from "./report-receiving-group/create/app.group-receiving.create.component";
import {ReportDynamicListComponent} from "./report-dynamic/list/report.dynamic.list.component";
import { CONSTANTS } from "src/app/service/comon/constants";
import { ReportDynamicContentComponent } from "./report-dynamic/content/report.dynamic.content.component";
import { ReportDynamicListContentComponent } from "./report-dynamic/list-content/report.dynamic.list.content";

@NgModule({
    imports: [
        RouterModule.forChild([
            {path: "report-dynamic", component: ReportDynamicListComponent, data: new DataPage("global.titlepage.reportDynamic", [CONSTANTS.PERMISSIONS.REPORT_DYNAMIC.VIEW_LIST])},
            {path: "report-dynamic/report-content/:id", component: ReportDynamicContentComponent, data: new DataPage("permission.RptContent.RptContent", ["getReport"])},
            {path: "report-dynamic/report-content", component: ReportDynamicListContentComponent, data: new DataPage("permission.RptContent.RptContent")},
            {path: "group-report-dynamic", component: ReportReceivingGroupComponent, data: new DataPage("global.titlepage.listGroupReportDynamic", [CONSTANTS.PERMISSIONS.GROUP_REPORT_DYNAMIC.VIEW_LIST])},
            {path: "group-report-dynamic/edit/:id", component: ReportGroupReceivingEditComponent, data: new DataPage("global.titlepage.editGroupReportDynamic", [CONSTANTS.PERMISSIONS.GROUP_REPORT_DYNAMIC.UPDATE])},
            {path: "group-report-dynamic/detail/:id", component: ReportGroupReceivingDetailComponent, data: new DataPage("global.titlepage.detailGroupReportDynamic", [CONSTANTS.PERMISSIONS.GROUP_REPORT_DYNAMIC.VIEW_DETAIL])},
            {path: "group-report-dynamic/create", component: ReportGroupReceivingCreateComponent, data: new DataPage("global.titlepage.createGroupReportDynamic", [CONSTANTS.PERMISSIONS.GROUP_REPORT_DYNAMIC.CREATE])}
        ])
    ],
    exports: [
        RouterModule
    ]
})
export class ReportRoutingModule{}
