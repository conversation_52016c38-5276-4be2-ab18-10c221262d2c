import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { LoginRoutingModule } from './login-routing.module';
import { LoginComponent } from './login.component';
import { ButtonModule } from 'primeng/button';
import { CheckboxModule } from 'primeng/checkbox';
import { FormsModule, ReactiveFormsModule } from "@angular/forms";
import { PasswordModule } from 'primeng/password';
import { InputTextModule } from 'primeng/inputtext';
import { CommonVnptModule } from '../../common-module/common.module';
import { DialogModule } from "primeng/dialog";
import {RecaptchaModule} from "ng-recaptcha";
@NgModule({
    imports: [
        CommonModule,
        LoginRoutingModule,
        ButtonModule,
        CheckboxModule,
        InputTextModule,
        FormsModule,
        PasswordModule,
        CommonVnptModule,
        DialogModule,
        CommonModule,
        FormsModule,
        ReactiveFormsModule,
        CommonVnptModule,
        CommonVnptModule,
        RecaptchaModule,
    ],
    declarations: [LoginComponent]
})
export class LoginModule { }
