<div class="flex justify-content-center dialog-push-group">
    <p-dialog (onHide)="onHide($event)" [header]="tranService.translate('datapool.text.importReceive')" [(visible)]="isShowDialogImportByFile" [modal]="true" [style]="{ width: '700px' }" [draggable]="false" [resizable]="false">
        <div class="w-full field grid">
            <div class="col-10 flex flex-row justify-content-start align-items-center">
                <input-file-vnpt class="w-full" [(fileObject)]="fileObject" [clearFileCallback]="clearFileCallback.bind(this)"
                                 [options]="optionInputFile"
                ></input-file-vnpt>
            </div>
            <div class="col-2 flex flex-row justify-content-end align-items-center">
                <p-button icon="pi pi-download" [pTooltip]="tranService.translate('global.button.downloadTemp')" styleClass="p-button-outlined p-button-secondary" (click)="downloadTemplate()"></p-button>
            </div>
        </div>
        <div class="grid"><div class="col pt-0"><small class="text-red-500" *ngIf="isShowErrorUpload">{{messageErrorUpload}}</small></div></div>
    </p-dialog>
</div>
