import { Component, ElementRef, Injector, OnInit, ViewChild } from '@angular/core';
import { MenuItem } from 'primeng/api';
import { ComponentBase } from 'src/app/component.base';
import { LayoutService } from "src/app/service/app.layout.service";
import {CONSTANTS} from "../../service/comon/constants";

@Component({
    selector: 'app-topbar',
    templateUrl: './app.topbar.component.html',
})
export class AppTopBarComponent extends ComponentBase implements OnInit{

    items!: MenuItem[];

    @ViewChild('menubutton') menuButton!: ElementRef;

    @ViewChild('topbarmenubutton') topbarMenuButton!: ElementRef;

    @ViewChild('topbarmenu') menu!: ElementRef;

    constructor(public layoutService: LayoutService, injector: Injector) {
        super(injector);
    }

    userInfo: any;
    ngOnInit(): void {
        this.userInfo = this.sessionService.userInfo;
    }

    logout(){
        let me = this;
        this.messageCommonService.onload();
        setTimeout(()=>{
            localStorage.clear();
            me.messageCommonService.offload();
            window.location.hash = '/login';
        }, 500)
    }

    protected readonly CONSTANTS = CONSTANTS;
}
