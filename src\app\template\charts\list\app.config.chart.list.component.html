<div class="vnpt flex flex-row justify-content-between align-items-center bg-white p-2 border-round">
    <div class="">
        <div class="text-xl font-bold mb-1">{{tranService.translate("global.menu.chartList")}}</div>
        <p-breadcrumb class="max-w-full col-7" [model]="items" [home]="home"></p-breadcrumb>
    </div>
    <div class="col-5 flex flex-row justify-content-end align-items-center">
        <p-button styleClass="p-button-info"
                    [label]="tranService.translate('global.button.create')"
                    icon="" [routerLink]="['/config-chart/create']"
                    routerLinkActive="router-link-active"></p-button>
    </div>
</div>
<form [formGroup]="formSearch" (ngSubmit)="onSubmitSearch()" class="pb-2 pt-3 vnpt-field-set">
    <p-panel [toggleable]="true" [header]="tranService.translate('global.text.filter')">
        <div class="grid">
            <!-- chart name -->
            <div class="col-3">
                <span class="p-float-label">
                    <input class="w-full"
                           pInputText id="name"
                           [(ngModel)]="searchInfo.name"
                           formControlName="name"
                    />
                    <label htmlFor="name">{{tranService.translate("chart.label.chartName")}}</label>
                </span>
            </div>
            <!-- chart type -->
            <div class="col-3">
                <span class="p-float-label">
                    <p-dropdown styleClass="w-full" [showClear]="true"
                        id="type" [autoDisplayFirst]="false"
                        [(ngModel)]="searchInfo.type" 
                        [options]="typeCharts"
                        optionLabel="name"
                        optionValue="value"
                        formControlName="type"
                    ></p-dropdown>
                    <label htmlFor="type">{{tranService.translate("chart.label.chartType")}}</label>
                </span>
            </div>
            <!-- chart sub type -->
            <!-- <div class="col-3">
                <span class="p-float-label">
                    <p-dropdown styleClass="w-full" [showClear]="true"
                        id="subType" [autoDisplayFirst]="false"
                        [(ngModel)]="searchInfo.subType" 
                        [options]="subTypeCharts"
                        optionLabel="name"
                        optionValue="value"
                        formControlName="subType"
                    ></p-dropdown>
                    <label htmlFor="subType">{{tranService.translate("chart.label.chartSubType")}}</label>
                </span>
            </div> -->
            <!--            button search-->
            <div class="col-3 pb-0">
                <p-button icon="pi pi-search"
                          styleClass="p-button-rounded p-button-secondary p-button-text button-search"
                          type="submit"
                ></p-button>
            </div>
        </div>
    </p-panel>
</form>
<!--table-->
<table-vnpt
    [tableId]="'listConfigChart'"
    [fieldId]="'id'"
    [(selectItems)]="selectItems"
    [columns]="columns"
    [dataSet]="dataSet"
    [options]="optionTable"
    [loadData]="search.bind(this)"
    [pageNumber]="pageNumber"
    [pageSize]="pageSize"
    [sort]="sort"
    [params]="searchInfo"
    [labelTable]="this.tranService.translate('global.menu.chartList')"
></table-vnpt>
