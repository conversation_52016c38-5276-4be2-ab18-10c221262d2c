import { NgModule } from '@angular/core';
import { RouterModule } from '@angular/router';
import { LoginComponent } from './login.component';
import {FormUpdatePasswordExpired} from "../update-password-expired/update-password-expired.component";

@NgModule({
    imports: [RouterModule.forChild([
        { path: '', component: LoginComponent},
    ])],
    exports: [RouterModule]
})
export class LoginRoutingModule { }
