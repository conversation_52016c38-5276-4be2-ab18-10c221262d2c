import {AfterContentChecked, Component, Inject, OnInit} from "@angular/core";
import {MenuItem} from "primeng/api";
import {FormBuilder} from "@angular/forms";
import {AlertService} from "../../../service/alert/AlertService";
import {ColumnInfo, OptionTable} from "../../common-module/table/table.component";
import {Router} from "@angular/router";
import { ComponentBase } from "src/app/component.base";
import { Injector } from '@angular/core';
import { ReportReceivingGroupService } from "src/app/service/report-receiving-group/ReportReceivingGroup";

@Component({
    selector: "report-receiving-group",
    templateUrl: "./report.receiving.group.component.html",
})
export class ReportReceivingGroupComponent extends ComponentBase implements OnInit, AfterContentChecked {
    items: MenuItem[];
    home: MenuItem;
    optionTable: OptionTable;
    optionTableDetail: OptionTable;
    columns: Array<ColumnInfo>;
    columnsDetail: Array<ColumnInfo>;
    formSearchAlertReceivingGroup: any;
    searchInfoStandard: any;
    selectItems: Array<{ id: number, name: string | null, [key: string]: any }>;
    selectItemsDetail: Array<{ id: number, name: string | null, [key: string]: any }>;
    searchInfo: {
        name: string | null,
        description: string | null,
    }
    pageNumber: number;
    pageSize: number;
    sort: string;
    dataSet: {
        content: Array<any>,
        total: number,
    };
    dataSetDetail: {
        content: Array<any>,
        total: number
    };
    isShowModalDetail: boolean = false;
    formReceivingGroup : any;
    receivingGroupInfo: {
        name: string|null,
        description: string|null,
        emails: Array<any>|null,
    };
    rgId: number;

    constructor(
                @Inject(ReportReceivingGroupService) private receivingGroupService: ReportReceivingGroupService,
                private formBuilder: FormBuilder, private injector:Injector) {
                    super(injector)
    }

    ngOnInit(): void {
        let me = this;
        this.selectItems = []
        this.home = {icon: 'pi pi-home', routerLink: '/'};
        this.items = [{label: this.tranService.translate("global.menu.dynamicreportgroup")},{ label: this.tranService.translate("global.menu.reportGroupReceivingList"), routerLink:"/reports/group-report-dynamic"  }];
        this.searchInfoStandard = {
            name: null,
            description: null,
        }
        this.searchInfo = {
            name: null,
            description: null,
        };
        this.receivingGroupInfo = {
            name: "nhom1",
            description: null,
            emails: [],
        };
        this.dataSetDetail = {
            content: [],
            total: 0
        };
        me.columnsDetail = [
            {
                name: me.tranService.translate("report.receiving.emails"),
                key: "emails",
                size: "150px",
                align: "left",
                isShow: true,
                isSort: false,
            },
        ];
        this.selectItemsDetail = [];
        this.formReceivingGroup = this.formBuilder.group(this.receivingGroupInfo);
        this.formReceivingGroup.get('name').disable();
        this.formReceivingGroup.get('description').disable();
        this.optionTableDetail = {
            hasClearSelected: true,
            hasShowChoose: false,
            hasShowIndex: true,
            hasShowToggleColumn: false,
        };
        this.optionTable = {
            hasClearSelected: false,
            hasShowChoose: true,
            hasShowIndex: true,
            hasShowToggleColumn: false,
            action: [
                {
                    icon: "pi pi-file-edit",
                    tooltip: this.tranService.translate("global.button.edit"),
                    func: function (id, item) {
                        me.router.navigate([`reports/group-report-dynamic/edit/${id}`]);
                    },
                    funcAppear: function (id, item) {
                        return true;
                    }
                },
                {
                    icon: "pi pi-trash",
                    tooltip: this.tranService.translate("global.button.delete"),
                    func: function (id, item) {
                        me.messageCommonService.confirm(
                            me.tranService.translate("global.message.titleConfirmDeleteReportReceivingGroup"),
                            me.tranService.translate("global.message.confirmDeleteReportReceivingGroup"),
                            {
                                ok: () => {
                                    me.receivingGroupService.deleteReportGroup(id, (response) => {
                                        me.messageCommonService.success(me.tranService.translate("global.message.deleteSuccess"));
                                        me.search(me.pageNumber, me.pageSize, me.sort, me.searchInfo);
                                    })
                                },
                                cancel: () => {

                                }
                            }
                        )
                    },
                    funcAppear: function (id, item) {
                        return true;
                    }
                },

            ]
        }
        this.columns = [{
            name: this.tranService.translate("report.label.reportreceivinggroup"),
            key: "name",
            size: "45%",
            align: "left",
            isShow: true,
            isSort: true,
            style: {
                cursor: "pointer",
             color: "var(--mainColorText)"
            },
            funcClick(id, item) {
                me.rgId = id;
                me.getDetailRg();
                me.isShowModalDetail = true;
            },
        }, {
            name: this.tranService.translate("report.label.description"),
            key: "description",
            size: "45%",
            align: "left",
            isShow: true,
            isSort: false,
        },
        ]
        this.formSearchAlertReceivingGroup = this.formBuilder.group(this.searchInfo);
        this.pageNumber = 0;
        this.pageSize = 10;
        this.sort = "name,desc";
        this.dataSet = {
            content: [],
            total: 0
        }
        this.search(this.pageNumber, this.pageSize, this.sort, this.searchInfo);
    }

    onSubmitSearch() {
        this.pageNumber = 0;
        this.search(0, this.pageSize, this.sort, this.searchInfo);
    }

    search(page, limit, sort, params) {
        this.pageNumber = page;
        this.pageSize = limit;
        this.sort = sort;
        let me = this;
        let dataParams = {
            page,
            size: limit,
            sort
        }
        this.updateParams(dataParams);
        me.messageCommonService.onload();
        this.receivingGroupService.searchReportReceivingGroup(dataParams, (response) => {
            me.dataSet = {
                content: response.content,
                total: response.totalElements
            }
            me.searchInfoStandard = {...me.searchInfo}
        }, null, ()=>{
            me.messageCommonService.offload();
        })
        // me.dataSet = {
        //     content: [
        //         {
        //             id: "3",
        //             groupName: "Khu vuc Bac Nha Trang",
        //             description: "Canh bao M2M Bac Nha Trang"
        //         },
        //         {
        //             id: "10",
        //             groupName: "Data chạm ngưỡng IDO",
        //             description: "Data chạm ngưỡng gói - Cty CP năng lượng điện dầu khí"
        //         }
        //     ],
        //     total: 2,
        // }
    }

    searchDetail(){
        let me = this
        me.dataSetDetail = {
            content: [],
            total: 0
        }
    }

    updateParams(dataParams) {
        Object.keys(this.searchInfo).forEach(key => {
            if (this.searchInfo[key] != null) {
                dataParams[key] = this.searchInfo[key];
            }
        })
    }

    hasItemsSelected(): boolean {
        return this.selectItems && this.selectItems.length > 0;
    }

    removeMany() {
        this.messageCommonService.confirm(
            this.tranService.translate("global.message.titleConfirmDeleteReportReceivingGroup"),
            this.tranService.translate("global.message.confirmDeleteReportReceivingGroup"),
            {
                ok: () => {
                    this.receivingGroupService.deleleListReceivingGroup(this.selectItems.map(item => item.id), (response) => {
                        this.messageCommonService.success(this.tranService.translate("global.message.deleteSuccess"));
                        this.search(this.pageNumber, this.pageSize, this.sort, this.searchInfo);
                    })
                },
                // cancel: () => {
                //
                // }
            }
        )
    }

    deleteReceivingGroup(){
        let me = this;
        me.messageCommonService.confirm(
            me.tranService.translate("global.message.titleConfirmDeleteReportReceivingGroup"),
            me.tranService.translate("global.message.confirmDeleteReportReceivingGroup"),
            {
                ok:()=>{
                    me.receivingGroupService.deleteReportGroup(me.rgId, (response) => {
                        me.messageCommonService.success(me.tranService.translate("global.message.deleteSuccess"));
                        me.router.navigate(['/reports/group-report-dynamic'])
                    })
                },
                cancel: ()=>{
                    // me.messageCommonService.error(me.tranService.translate("global.message.deleteFail"));
                }
            }
        )
    }

    onEdit(){
        let me = this;
        me.router.navigate([`/reports/group-report-dynamic/edit/${me.rgId}`]);
    }

    getDetailRg() {
        let me = this;
        me.receivingGroupService.getDetailReceivingGroup(me.rgId,(response)=>{
            me.receivingGroupInfo = response;
            me.receivingGroupInfo.emails = response?.emails

            if (response.emails != null){
                for (let i = 0; i <response?.emails.split(", ").length; i++) {
                    me.dataSetDetail.content.push({emails :response?.emails.split(", ")[i]})
                    // me.myEmails.push(response.emails.split(", ")[i])
                }
            }

        }, null, ()=>{
            me.messageCommonService.offload();
        })
    }

    ngAfterContentChecked(): void {
    }
}
