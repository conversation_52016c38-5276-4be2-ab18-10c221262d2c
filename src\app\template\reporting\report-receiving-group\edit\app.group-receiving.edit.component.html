<div class="vnpt flex flex-row justify-content-between align-items-center bg-white p-2 border-round">
    <div class="">
        <div class="text-xl font-bold mb-1">{{tranService.translate("global.menu.reportGroupReceivingList")}}</div>
        <p-breadcrumb class="max-w-full col-7" [model]="items" [home]="home"></p-breadcrumb>
    </div>
    <div class="col-5 flex flex-row justify-content-end align-items-center">
        <!--        <p-button styleClass="p-button-info" [label]="tranService.translate('global.button.create')" icon="" [routerLink]="['/alert/create']" routerLinkActive="router-link-active" ></p-button>-->
    </div>
</div>

<p-card class="p-4">
    <form action="" [formGroup]="formReceivingGroup" (submit)="onSubmitCreate()">
        <div class="pt-0 shadow-2 border-round-md m-1 flex p-fluid p-formgrid grid">
            <div class="col-8">
                <div class="w-full field grid">
                    <!--  name -->
                    <label htmlFor="name" class="col-fixed" style="width:250px">{{tranService.translate("report.receiving.name")}}<span class="text-red-500">*</span></label>
                    <div class="col">
                        <input class="w-full"
                               pInputText id="name"
                               [(ngModel)]="receivingGroupInfo.name"
                               formControlName="name" 
                               [required]="true"
                               [maxLength]="255"
                               pattern="^[a-zA-Z0-9\-_]*$"
                               [placeholder]="tranService.translate('report.text.inputNameReceiving')"
                               (ngModelChange)="nameChanged($event)"
                        />
                        <!-- error name -->
                        <label htmlFor="name" class="col-fixed" style="width:180px"></label>
                        <div class="col">
                            <small class="text-red-500" *ngIf="formReceivingGroup.controls.name.dirty && formReceivingGroup.controls.name.errors?.required">{{tranService.translate("global.message.required")}}</small>
                            <small class="text-red-500" *ngIf="formReceivingGroup.controls.name.errors?.maxLength">{{tranService.translate("global.message.maxLength",{len:255})}}</small>
                            <small class="text-red-500" *ngIf="formReceivingGroup.controls.name.errors?.pattern">{{tranService.translate("global.message.formatCode")}}</small>
                            <small class="text-red-500" *ngIf="isRGNameExisted">{{tranService.translate("global.message.exists",{type: tranService.translate("report.receiving.name").toLowerCase()})}}</small>
                        </div>
                    </div>
                </div>
                <div class="w-full field grid">
                    <!--  description -->
                    <label for="description" class="col-fixed" style="width:250px">{{tranService.translate("report.receiving.description")}}</label>
                    <div class="col">
                        <input class="w-full"
                               pInputText id="description"
                               [(ngModel)]="receivingGroupInfo.description"
                               formControlName="description"
                               [maxLength]="255"
                               [placeholder]="tranService.translate('report.text.inputDescription')"
                        />
                    </div>
                </div>
            </div>
        </div>
        <h4 class="ml-2"></h4>
        <div class="pt-0 shadow-2 border-round-md m-1 flex p-fluid p-formgrid grid">
            <div class="flex-1">
                <div class="field  px-4 pt-4  flex-row ">
                    <!-- email -->
                    <form [formGroup]="formMailInput">
                        <div class="field grid px-4 pt-4 flex flex-row flex-nowrap">
                            <!-- email -->
                            <label htmlFor="email" class="col-fixed" style="width:180px">{{tranService.translate("report.receiving.emails")}}<span class="text-red-500">*</span></label>
                            <div class="col-8">
                                <input class="w-full"
                                       pInputText id="email"
                                       formControlName="email"
                                       [(ngModel)]="email"
                                       [required]="true"
                                       [maxLength]="255"
                                       [placeholder]="tranService.translate('report.text.inputemails')"
                                       pattern="^[a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]+\.[a-zA-Z0-9-.]+$"
                                       (ngModelChange)="emailChanged($event)"
                                />
                                <!-- error email -->
                                <label htmlFor="name" class="col-fixed" style="width:180px"></label>
                                <div class="col">
                                    <!--                                    <small class="text-red-500" *ngIf="formMailInput.controls.email.dirty && formReceivingGroup.controls.name.errors?.required">{{tranService.translate("global.message.required")}}</small>-->
                                    <small class="text-red-500" *ngIf="formMailInput.controls.email.errors?.maxLength">{{tranService.translate("global.message.maxLength",{len:255})}}</small>
                                    <small class="text-red-500" *ngIf="formMailInput.controls.email.errors?.pattern">{{tranService.translate("global.message.formatEmail")}}</small>
                                    <small class="text-red-500" *ngIf="isRGEmailExisted">{{tranService.translate("global.message.exists",{type: tranService.translate("alert.receiving.emails").toLowerCase()})}}</small>
                                </div>
                            </div>
                            <button pButton class="p-button-outlined" type="button" icon="pi pi-plus-circle add-icon-size" (click)="addEmail(email)" [disabled]="formMailInput.invalid || isRGEmailExisted"></button>
                        </div>
                    </form>
                    <div class="field  px-4 pt-4  flex-row ">
                        <table-vnpt
                            [fieldId]="'id'"
                            [(selectItems)]="selectItems"
                            [columns]="columns"
                            [dataSet]="dataSet"
                            [options]="optionTable"
                            [loadData]="search.bind(this)"
                            [scrollHeight]="'200px'"
                        ></table-vnpt>
                    </div>
                </div>
            </div>
        </div>
        <div class="flex flex-row gap-3 ml-2 mt-4 mb-3">
        </div>
        <div class="flex flex-row justify-content-center gap-3 p-2">
            <button pButton class="p-button-info" type="submit" [disabled]="formReceivingGroup.invalid || dataSet.content.length == 0 || isRGNameExisted">Lưu</button>
            <button pButton class="p-button-outlined p-button-secondary" type="button" (click)="closeForm()">Huỷ</button>
        </div>
    </form>
</p-card>

