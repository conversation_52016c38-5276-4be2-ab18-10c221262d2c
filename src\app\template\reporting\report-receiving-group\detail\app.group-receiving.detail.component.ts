import {AfterContentChecked, Component, Inject, Injector, OnInit} from '@angular/core';
import {FormBuilder} from "@angular/forms";
import {MenuItem} from "primeng/api";
import {ColumnInfo, OptionTable} from "../../../common-module/table/table.component";
import { ComponentBase } from 'src/app/component.base';
import { ReportReceivingGroupService } from 'src/app/service/report-receiving-group/ReportReceivingGroup';

@Component({
  selector: 'report.group-receiving.detail',
  templateUrl: './app.group-receiving.detail.component.html',
})
export class ReportGroupReceivingDetailComponent extends ComponentBase implements OnInit, AfterContentChecked{
    constructor(@Inject(ReportReceivingGroupService) private reportReceivingGroupService: ReportReceivingGroupService,
                private formBuilder: FormBuilder, injector: Injector) {
                    super(injector)
    }
    items: MenuItem[];
    home: MenuItem;
    formReceivingGroup : any;
    formMailInput : any;

    receivingGroupInfo: {
        name: string|null,
        description: string|null,
        emails: Array<any>|null,
    };
    selectItems: Array<any> = [];
    columns: Array<ColumnInfo>;
    dataSet: {
        content: Array<any>,
        total: number
    };
    optionTable: OptionTable;
    email: {}
    rgId = parseInt(this.route.snapshot.paramMap.get("id"));

    ngOnInit(): void {
        let me = this;
        this.messageCommonService.onload()
        this.items = [{ label: this.tranService.translate("global.menu.dynamicreportgroup")},{ label: this.tranService.translate("global.menu.reportGroupReceivingList"), routerLink:"/reports/group-report-dynamic"  }, { label: this.tranService.translate("global.button.view") }];
        this.home = { icon: 'pi pi-home', routerLink: '/' };

        this.receivingGroupInfo = {
            name: "nhom1",
            description: null,
            emails: [],
        }
        this.formReceivingGroup = this.formBuilder.group(this.receivingGroupInfo);
        this.formMailInput = this.formBuilder.group({email: ""});
        this.dataSet = {
            content: [],
            total: 0
        }
        this.selectItems = [];
        this.columns = [
            {
                name: this.tranService.translate("report.receiving.emails"),
                key: "emails",
                size: "150px",
                align: "left",
                isShow: true,
                isSort: false,
            },
        ];
        this.optionTable = {
            hasClearSelected: true,
            hasShowChoose: false,
            hasShowIndex: true,
            hasShowToggleColumn: false,
        };

        this.formReceivingGroup.get('name').disable()
        this.formReceivingGroup.get('description').disable()
        this.messageCommonService.onload()
        this.reportReceivingGroupService.getDetailReceivingGroup(this.rgId,(response)=>{
            me.receivingGroupInfo = response;
            me.receivingGroupInfo.emails = response.emails
            
            if (response.emails != null){
                for (let i = 0; i <response.emails.split(", ").length; i++) {
                    me.dataSet.content.push({emails :response.emails.split(", ")[i]})
                    // me.myEmails.push(response.emails.split(", ")[i])
                }
            }
            
        }, null, ()=>{
            me.messageCommonService.offload();
        })
        this.dataSet = {
            content: [],
            total: 0
        }
    }
    ngAfterContentChecked(): void {
    }
    // onSubmitCreate(){
    //     let dataBody = {
    //         // username: this.accountInfo.accountName,

    //     }
    //     this.messageCommonService.onload();
    //     let me = this;
    //     this.reportReceivingGroup.createAccount(dataBody, (response)=>{
    //         me.messageCommonService.success(me.tranService.translate("global.message.saveSuccess"));
    //         // me.router.navigate(['/accounts/edit/'+response.id]);
    //     })
    // }
    closeForm(){
        this.router.navigate(['/reports/group-report-dynamic'])
    }

    addEmail(val){
        let me = this;


        me.dataSet.content.push({emails :val})
        me.receivingGroupInfo.emails.push({emails :val})
        // me.dataSet.content.push(me.receivingGroupInfo)
    }
    search(){
        let me = this
        me.dataSet = {
            content: [],
            total: 0
        }
    }
    removeEmail(val){
        let me = this
        me.dataSet.content.splice(me.dataSet.content.indexOf(val), 1)
        me.receivingGroupInfo.emails.splice(me.receivingGroupInfo.emails.indexOf(val), 1)
    }

    deleteReceivingGroup(){
        let me = this;
        me.messageCommonService.confirm(
            me.tranService.translate("global.message.titleConfirmDeleteReportReceivingGroup"),
            me.tranService.translate("global.message.confirmDeleteReportReceivingGroup"),
            {
                ok:()=>{
                    me.reportReceivingGroupService.deleteReportGroup(me.rgId, (response) => {
                        me.messageCommonService.success(me.tranService.translate("global.message.deleteSuccess"));
                        me.router.navigate(['/reports/group-report-dynamic'])
                    })
                },
                cancel: ()=>{
                    // me.messageCommonService.error(me.tranService.translate("global.message.deleteFail"));
                }
            }
        )
    }

    onEdit(){
        let me = this;
        let receivingGroupId = this.route.snapshot.paramMap.get("id");
        me.router.navigate([`/reports/group-report-dynamic/edit/${receivingGroupId}`]);
    }
}
