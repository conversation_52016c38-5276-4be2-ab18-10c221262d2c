import { Inject, Injectable } from "@angular/core";
import { HttpService } from "../comon/http.service";
@Injectable()
export class ApnSimService {
    private prefixApi: string;

    constructor(@Inject(HttpService) private httpService: HttpService) {
        this.prefixApi = "/apn";
    }

    public search(params: {
        [key: string]: any
    }, callback?: Function, errorCallBack?: Function, finallyCallback?: Function) {
        this.httpService.get(`${this.prefixApi}/search`, {timeout: 120000}, params, callback, errorCallBack, finallyCallback);
    }
    public detail(apnId: number|string, callback?:Function, errorCallBack?:Function, finallyCallback?:Function){
        this.httpService.get(`${this.prefixApi}/${apnId}`,{},{}, callback, errorCallBack, finallyCallback);
    }
}
