import { Component, Inject, inject, Injector } from '@angular/core';
import { AbstractControl, FormControl, FormGroup, ValidationErrors, ValidatorFn, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { MenuItem } from 'primeng/api';
import { ComponentBase } from 'src/app/component.base';
import { CONSTANTS } from 'src/app/service/comon/constants';
import { MessageCommonService } from 'src/app/service/comon/message-common.service';
import { TranslateService } from 'src/app/service/comon/translate.service';
import { CustomerService } from 'src/app/service/customer/CustomerService';
import { ColumnInfo, OptionTable } from '../../common-module/table/table.component';
import { AccountService } from 'src/app/service/account/AccountService';

@Component({
  selector: 'app-detail-customer',
  templateUrl: './detail-customer.component.html',
  styleUrls: ['./detail-customer.component.scss']
})
export class DetailCustomerComponent extends ComponentBase {
  idForEdit:number;

  customerInfo: any = null;

  isSubmit : boolean = false
  items: MenuItem[]=[{ label: this.tranService.translate(`global.menu.customermgmt`), routerLink:'../../' }, { label: this.tranService.translate(`customer.label.infoCustomer`)}];
  home: MenuItem={ icon: 'pi pi-home', routerLink: '/' };
  typeList:any = [
    {name:this.tranService.translate("ratingPlan.customerType.personal"), value: CONSTANTS.CUSTOMER_TYPE.PERSONAL},
    {name:this.tranService.translate('ratingPlan.customerType.enterprise'), value: CONSTANTS.CUSTOMER_TYPE.INTERPRISE},
    {name:this.tranService.translate('ratingPlan.customerType.agency'), value: CONSTANTS.CUSTOMER_TYPE.AGENCY}
  ]

  statusList:any= [
    {name:this.tranService.translate("customer.label.active"), value:CONSTANTS.CUSTOMER_STATUS.ACTIVE},
    {name:this.tranService.translate('customer.label.inActive'), value:CONSTANTS.CUSTOMER_STATUS.INACTIVE}
  ]

  generalHeader: string = this.tranService.translate("customer.label.generalInfo");
  contactHeader: string = this.tranService.translate("customer.label.billingContact")
  paymentHeader: string = this.tranService.translate('customer.label.billingAddress');
  note: string = this.tranService.translate("customer.label.note")
  isShowListAccount: boolean = false;
  columns: Array<ColumnInfo> = [];
  optionTable: OptionTable = {hasClearSelected: true};
  listProvince: [];
  dataSet: {
    content: Array<any>,
    total: number | null
  }
  constructor(@Inject(CustomerService) private customerService: CustomerService, private accountService: AccountService, injector: Injector) {super(injector)}

  customCharacterValidator(): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
      const value = control.value;
      const isValid = /^[a-zA-Z0-9 \-_\!\#\$\%\&\'\*\+\-\/\=\?\^\_\`\.\{\|\}\~]*$/.test(value);
      return isValid ? null : { 'invalidCharacters': { value } };
    };
  }
  regularCharacterValidator(): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
      const value = control.value;
      const isValid = /^[a-zA-Z0-9 ]*$/.test(value);
      return isValid ? null : { 'invalidCharacters': { value } };
    };
  }

  updateCustomerForm = new FormGroup({
    customerCode : new FormControl({value:"",disabled:true}, [Validators.required]),
    taxId : new FormControl({value:"",disabled:true}, [Validators.required, Validators.minLength(2), Validators.maxLength(255), this.regularCharacterValidator()]),
    provinceCode : new FormControl({value:"", disabled:true}),
    customerType: new FormControl({value:"",disabled:true}),
    status : new FormControl({value:"", disabled:true}),
    // Thông tin liên hệ chính
    customerName : new FormControl({value:"",disabled:true},[Validators.required, Validators.minLength(2), Validators.maxLength(255), this.customCharacterValidator()]),
    phone : new FormControl({value:"",disabled:true}),
    email : new FormControl({value:"",disabled:true}, [Validators.email, Validators.maxLength(255)]),
    birthday : new FormControl({value:"",disabled:true}),
    // Thông tin thanh toán
    billName : new FormControl({value:"",disabled:true}, [Validators.minLength(2), Validators.maxLength(255), this.regularCharacterValidator()]),
    billPhone : new FormControl({value:null,disabled:true}),
    billEmail : new FormControl({value:"",disabled:true}, [Validators.email, Validators.maxLength(255)]),
    billBirthday : new FormControl({value:null,disabled:true}),
    // Địa chỉ
    addrStreet : new FormControl({value:"",disabled:true}, [Validators.minLength(2), Validators.maxLength(255), this.regularCharacterValidator()]),
    addrDist : new FormControl({value:"",disabled:true}, [Validators.minLength(2), Validators.maxLength(255), this.regularCharacterValidator()]),
    addrProvince : new FormControl({value:"",disabled:true}, [Validators.minLength(2), Validators.maxLength(255), this.regularCharacterValidator()]),
    //Ghi chú
    note : new FormControl({value:"",disabled:true}, [Validators.minLength(2), Validators.maxLength(255), this.regularCharacterValidator()])
  })

  ngOnInit(){
    let me = this
    me.messageCommonService.onload();
    me.idForEdit = Number(this.route.snapshot.params["id"]);
    // console.log(this.idForEdit)
    this.customerService.getCustomerById(me.idForEdit, (response)=>{
      me.customerInfo = response;
      response.phone = response.phone != null ? ((response.phone || "").substring(2)) : null;
      response.billPhone = response.billPhone != null ? ((response.billPhone || "").substring(2)): null;
      response.birthday = new Date(response.birthday)
      response.billBirthday = new Date(response.billBirthday)
      this.updateCustomerForm.patchValue(response);
    }, null, ()=>{
      me.messageCommonService.offload();
    })

    this.optionTable = {
      hasClearSelected: true,
      hasShowChoose: false,
      hasShowIndex: false,
      hasShowToggleColumn: false,
      paginator: false
    },
    this.columns = [
        {
            name: this.tranService.translate("account.label.username"),
            key: "username",
            size: "250px",
            align: "left",
            isShow: true,
            isSort: false,
            style:{
                cursor: "pointer",
             color: "var(--mainColorText)"
            },
            funcGetRouting(item) {
                return [`/accounts/detail/${item.id}`]
            },
        },
        {
            name: this.tranService.translate("account.label.fullname"),
            key: "fullName",
            size: "300px",
            align: "left",
            isShow: true,
            isSort: false,
        },
        {
            name: this.tranService.translate("account.label.email"),
            key: "email",
            size: "300px",
            align: "left",
            isShow: true,
            isSort: false,
        },
    ]
    this.getListProvince();
  }

  getListProvince(){
    this.accountService.getListProvince((response)=>{
        this.listProvince = response.map(el => {
            return {
                ...el,
                display: `${el.code} - ${el.name}`
            }
        })
    })
}



  openListAccount(){
      let me = this;

      this.customerService.getListAccount(this.customerInfo.id, (response)=>{
          me.dataSet = {
              content: response,
              total: response ? response.length : 0
          };
          me.isShowListAccount = true;
      })
  }
}
