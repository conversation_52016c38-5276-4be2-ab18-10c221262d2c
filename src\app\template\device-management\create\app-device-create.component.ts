import {Component, Inject, Injector, OnInit} from "@angular/core";
import {MenuItem} from "primeng/api";
import {DeviceService} from "../../../service/device/device-service.service";
import {FormBuilder, FormGroup} from "@angular/forms";
import {ComponentBase} from "../../../component.base";
import {CONSTANTS} from "../../../service/comon/constants";
import {<PERSON><PERSON><PERSON><PERSON><PERSON>, SafeResourceUrl} from "@angular/platform-browser";
import {ComboLazyControl} from "../../common-module/combobox-lazyload/combobox.lazyload";
import {PhotonService} from "../../../service/device/photon.service";
declare let L: any;

@Component({
    selector: "app-device-create",
    templateUrl: './app-device-create.component.html',
})
export class AppDeviceCreateComponent extends ComponentBase implements OnInit {
    items: MenuItem[];
    deviceInfo: {
        deviceName: string | null,
        typeCode: number | null,
        imei: string | null,
        serialNumber: string | null,
        msisdn: string | null,
        description: string | null,
        userEnterpriseId: number | null,
        userCustomerId: number | null,
        manufacture: string | null,
        latitude: string | null,
        longitude: string | null,
        fullAddress: string | null,
        modelCode: string | null,
    }
    // deviceType: {
    //     id: number | null,
    //     typeCode: string | null,
    //     typeName: string | null,
    //     modelCode: string | null,
    //     modelDescription: string | null,
    //     telemetryConfigSchema: string | null,
    // } | null
    home: MenuItem;
    formCreateDevice: FormGroup;
    msisdn: number;
    listStatus: Array<any>;
    listType: Array<any>;
    safeUrl: SafeResourceUrl;
    userType: number;
    controlComboSelectIndividual: ComboLazyControl = new ComboLazyControl()
    controlComboSelectBusiness: ComboLazyControl = new ComboLazyControl()
    controlComboSelectType: ComboLazyControl = new ComboLazyControl();
    controlComboSelectModel: ComboLazyControl = new ComboLazyControl()
    paramSearchType: {
        modelCode: any;
    }
    paramSearchModel: {
        typeCode: any;
    }
    paramSearchBusiness: {
        type: 2,
        sort: 'id,asc'
        customerId: number | -1
    }
    paramSearchIndividual: {
        type: 3,
        sort: 'id,asc'
        managerId: number | -1,
        forDevice: 1,
    }

    constructor(@Inject(DeviceService) private deviceService: DeviceService,
                @Inject(PhotonService) private photonService: PhotonService,
                private formBuilder: FormBuilder,
                private sanitizer: DomSanitizer,
                injector: Injector,
    ) {
        super(injector);
    }

    defaultLat = "21.046303";
    defaultLng = "105.791648";
    defaultAddress = '124 Hoàng Quốc Việt, Cầu Giấy, Hà Nội';
    address: string = 'Chưa chọn';
    lat: number | string | null
    lng: number | string | null
    map: any;
    marker: any;
    normalPattern = /^[a-zA-Z0-9\-_]*$/;
    vietnamesePattern = /^[^~`!@#\$%\^&*\(\)=\+\[\]\{\}\|\\,<>\/?]*$/;
    msisdnPattern = /^(?:0|84)\d{9,10}(?:, ?(?:0|84)\d{9,10})*$/
    isShowExistsImei: boolean = false;
    dataSet: {
        content: Array<any>,
        total: number
    };
    pageNumber: number;
    pageSize: number;
    sort: string;
    ngOnInit(): void {
        if (!this.checkAuthen([CONSTANTS.PERMISSIONS.DEVICE.CREATE])) {
            window.location.hash = "/access";
        }
        this.listStatus = [
            {name: this.tranService.translate("device.status.registered"), value: CONSTANTS.DEVICE_STATUS.REGISTERED},
            {name: this.tranService.translate("device.status.connected"), value: CONSTANTS.DEVICE_STATUS.CONNECTED},
            {
                name: this.tranService.translate("device.status.lostConnection"),
                value: CONSTANTS.DEVICE_STATUS.LOST_CONNECTION
            }
        ]
        this.home = {icon: 'pi pi-home', routerLink: '/'};
        this.userType = this.sessionService.userInfo.type;
        this.pageNumber = 0;
        this.pageSize = 10;
        this.sort = "";
        this.dataSet = {
            content: [],
            total: 0,
        }
        this.checkNavigateToListDevice(this.pageNumber, this.pageSize, this.sort);
        this.items = [{label: this.tranService.translate("global.menu.devicemgmt"), routerLink: '/devices'},
            {label: this.tranService.translate("global.menu.listdevice"), routerLink: "/devices"},
            {label: this.tranService.translate("global.menu.devicecreate")}];
        this.deviceInfo = {
            deviceName: null,
            typeCode: null,
            imei: null,
            serialNumber: null,
            msisdn: null,
            description: null,
            userEnterpriseId: null,
            userCustomerId: null,
            manufacture: null,
            latitude: null,
            longitude: null,
            fullAddress: null,
            modelCode: null,
        }
        // this.deviceType = null,
        // this.deviceType = {
        //     id: null,
        //     typeCode: null,
        //     typeName: null,
        //     modelCode: null,
        //     modelDescription: null,
        //     telemetryConfigSchema: null
        // }
        this.paramSearchType = {
            modelCode: "",
        }
        this.paramSearchModel = {
            typeCode: "",
        }
        this.paramSearchBusiness = {
            type: 2,
            sort: 'id,asc',
            customerId: -1
        }
        this.paramSearchIndividual = {
            type: 3,
            sort: 'id,asc',
            managerId: -1,
            forDevice: 1,
        }
        const url = `https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3723.645764495224!2d105.78919517584175!3d21.046855287155687!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x3135ab303bc5f991%3A0x938485f81ec15900!2zMTI0IEhvw6BuZyBRdeG7kWMgVmnhu4d0LCBD4buVIE5odeG6vywgQ-G6p3UgR2nhuqV5LCBIw6AgTuG7mWkgMTAwMDAsIFZp4buHdCBOYW0!5e0!3m2!1svi!2s!4v1749690944689!5m2!1svi!2s`;
        this.safeUrl = this.sanitizer.bypassSecurityTrustResourceUrl(url);

        this.formCreateDevice = this.formBuilder.group(this.deviceInfo)
        this.lat = this.defaultLat;
        this.lng = this.defaultLng;
        this.address = this.defaultAddress;
        // this.photonService.searchStream().subscribe(res => {
        //     console.log(res)
        //     this.results = res;
        // });
        this.initMap();
    }

    // onInputChange(query: string) {
    //     this.inputValue = query;
    //     this.photonService.updateQuery(query);
    // }
    //
    // async onSelect(place: any) {
    //     if (place) {
    //         const lat = place.coord[1];
    //         const lng = place.coord[0];
    //         console.log(place)
    //         const address = await this.reverseGeocode(lat, lng);
    //         this.setMarker(lat, lng, address);
    //     }
    // }

    initMap(): void {
        this.map = L.map('osm-map').setView([this.lat, this.lng], 13);

        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
            attribution: '&copy; OpenStreetMap contributors'
        }).addTo(this.map);

        // Gắn marker mặc định với tooltip
        this.marker = L.marker([this.lat, this.lng])
            .addTo(this.map)
            .bindTooltip(this.address, {permanent: true, direction: 'top'})
            .openTooltip();

        this.map.on('click', async (e: any) => {
            const lat = e.latlng.lat;
            const lng = e.latlng.lng;
            const address = await this.reverseGeocode(lat, lng);
            this.setMarker(lat, lng, address);
        });
    }

    async search(event: KeyboardEvent) {
        let me = this;
        event.preventDefault();
        let query = (event.target as HTMLInputElement).value;
        const res = await fetch(`https://nominatim.openstreetmap.org/search?format=json&q=${encodeURIComponent(query)}`);
        const results = await res.json();
        if (results.length > 0) {
            const r = results[0];
            const lat = parseFloat(r.lat);
            const lng = parseFloat(r.lon);
            this.setMarker(lat, lng, r.display_name);
            this.map.setView([lat, lng], 15);
        } else {
            me.messageCommonService.warning(me.tranService.translate('device.text.notFoundLocation'))
        }
    }

    async reverseGeocode(lat: number, lng: number): Promise<string> {
        let me = this;
        const res = await fetch(`https://nominatim.openstreetmap.org/reverse?format=json&lat=${lat}&lon=${lng}`);
        const data = await res.json();
        return data.display_name || 'Không tìm thấy địa chỉ';
    }

    setMarker(lat: number, lng: number, address: string) {
        this.lat = lat;
        this.lng = lng;
        this.address = address;
        let me = this;
        me.deviceInfo.fullAddress = address
        me.deviceInfo.longitude = String(lng)
        me.deviceInfo.latitude = String(lat)
        if (this.marker) this.marker.remove();

        this.marker = L.marker([lat, lng])
            .addTo(this.map)
            .bindTooltip(address, {permanent: true, direction: 'top'})
            .openTooltip();
    }

    goBack() {
        window.history.back();
    }

    create() {
        let me = this;
        let dataBody = me.deviceInfo;
        me.messageCommonService.onload();
        me.deviceService.createDevice(dataBody, (response) => {
            me.messageCommonService.success(me.tranService.translate("global.message.saveSuccess"));
            me.router.navigate(['/devices']);
        }, null, () => {
            me.messageCommonService.offload();
        })
    }

    onSelectedType() {
        let me = this;
        me.paramSearchModel.typeCode = me.deviceInfo.typeCode ? me.deviceInfo.typeCode : "";
    }

    onSelectedModel() {
        let me = this;
        me.paramSearchType.modelCode = me.deviceInfo.modelCode ? me.deviceInfo.modelCode : "";
    }

    onSelectedBusiness() {
        let me = this;
        me.paramSearchIndividual.managerId = me.deviceInfo.userEnterpriseId ? me.deviceInfo.userEnterpriseId : -1;
    }

    onSelectedIndividual() {
        let me = this;
        me.paramSearchBusiness.customerId = me.deviceInfo.userCustomerId ? me.deviceInfo.userCustomerId : -1;
    }

    disableButtonSubmit() {
        let me = this;
        if (me.formCreateDevice.invalid || me.isShowExistsImei) {
            return true;
        }
        if (me.controlComboSelectBusiness.error.required && me.userType == CONSTANTS.USER_TYPE.ADMIN) {
            return true;
        }
        if (me.controlComboSelectType.error.required || me.controlComboSelectModel.error.required || me.controlComboSelectIndividual.error.required) {
            return true;
        }
        return false;
    }

    protected readonly CONSTANTS = CONSTANTS;

    checkExistsImei(){
        let me = this;
        me.isShowExistsImei = false
        if((this.deviceInfo.imei || "").trim() != ""){
            this.debounceService.set("checkExistsImei", this.deviceService.checkExistsImeiDevice.bind(this.deviceService), this.deviceInfo.imei.trim(), (response) => {
                if (response >= 1) {
                    me.isShowExistsImei = true;
                } else {
                    me.isShowExistsImei = false;
                }
            })
        }
    }
    checkNavigateToListDevice(page, limit, sort) {
        let me = this;
        if (me.userType == CONSTANTS.USER_TYPE.INDIVIDUAL) {
            this.pageNumber = page;
            this.pageSize = limit;
            this.sort = sort;
            let me = this;
            let dataParams = {
                page,
                size: limit,
                sort
            }
            me.messageCommonService.onload();
            this.deviceService.search(dataParams, (response) => {
                me.dataSet = {
                    content: response.content,
                    total: response.totalElements
                }
                if (me.dataSet.total >= 1) {
                    me.messageCommonService.warning(me.tranService.translate('device.text.oneDevice'))
                    this.router.navigate(['/devices']);
                }
            }, null, () => {
                me.messageCommonService.offload();
            })
        }
    }
}
