import {Inject, Injectable} from "@angular/core";
import {HttpService} from "../comon/http.service";

@Injectable({providedIn: 'root'})
export class CommandService {
    private prefixApi: string;

    constructor(@Inject(HttpService) private httpService: HttpService) {
        this.prefixApi = "/command";
    }

    public search(id: number,params: {
        [key: string]: any
    }, callback?: Function, errorCallBack?: Function, finallyCallback?: Function) {
        this.httpService.get(`${this.prefixApi}/search-command/${id}`, {}, params, callback, errorCallBack, finallyCallback);
    }

    public getBydeviceType(type: number, callback?: Function, errorCallback?: Function, finallyCallback?: Function) {
        this.httpService.get(`${this.prefixApi}/get-by-device-type/${type}`, {}, {}, callback, errorCallback, finallyCallback);
    }

        public sencCommand(body, callback?: Function, errorCallback?: Function, finallyCallback?: Function) {
        this.httpService.post(`${this.prefixApi}/send-command`, {}, body, {}, callback, errorCallback, finallyCallback);
    }
}
