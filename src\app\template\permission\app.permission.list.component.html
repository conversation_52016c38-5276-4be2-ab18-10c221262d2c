<style>
    /* .col-3{
        padding: 10px;
    } */
</style>

<div class="vnpt flex flex-row justify-content-between align-items-center bg-white p-2 border-round">
    <div class="">
        <div class="text-xl font-bold mb-1">{{this.tranService.translate("global.menu.listpermission")}}</div>
        <p-breadcrumb class="max-w-full col-7" [model]="items" [home]="home"></p-breadcrumb>
    </div>
    <div class="col-5 flex flex-row justify-content-end align-items-center">
        
    </div>
</div>

<form [formGroup]="formSearchPermission" (ngSubmit)="onSubmitSearch()" class="pb-2 pt-3 vnpt-field-set">
    <p-panel [toggleable]="true" [header]="tranService.translate('global.text.filter')">
        <div class="grid">
            <!-- doi tuong tac dong -->
            <div class="col-3">
                <span class="p-float-label">
                    <p-dropdown styleClass="w-full" [showClear]="true"
                            id="objectKey" [autoDisplayFirst]="false"
                            [(ngModel)]="searchInfo.objectKey" 
                            formControlName="objectKey"
                            [options]="listObjectKey"
                            optionLabel="name"
                            optionValue="value"
                            (ngModelChange)="onSubmitSearch()"
                    ></p-dropdown>
                    <label for="type">{{tranService.translate("account.label.permission.object")}}</label>
                </span>
            </div>
             <!-- ten quyen -->
             <div class="col-3">
                <span class="p-float-label">
                    <input class="w-full" 
                            pInputText id="permissionName" 
                            [(ngModel)]="searchInfo.permissionName" 
                            formControlName="permissionName"
                    />
                    <label htmlFor="permissionName">{{tranService.translate("account.label.permission.name")}}</label>
                </span>
            </div>
            <div class="col-3 pb-0">
                <p-button icon="pi pi-search" 
                            styleClass="p-button-rounded p-button-secondary p-button-text button-search"
                            type="submit"
                ></p-button>
            </div>
        </div>
    </p-panel>
</form>

<table-vnpt 
    [fieldId]="'id'"
    [(selectItems)]="selectItems" 
    [columns]="columns" 
    [dataSet]="dataSet" 
    [options]="optionTable"
    [loadData]="search.bind(this)"
    [pageNumber]="pageNumber"
    [pageSize]="pageSize"
    [sort]="sort"
    [params]="searchInfo"
    [labelTable]="this.tranService.translate('global.menu.listpermission')"
></table-vnpt>
