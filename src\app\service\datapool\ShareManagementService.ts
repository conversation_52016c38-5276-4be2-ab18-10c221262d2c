import { callback } from 'chart.js/types/helpers';
import { Inject, Injectable } from "@angular/core";
import { HttpService } from "../comon/http.service";

@Injectable()
export class ShareManagementService {
    private prefixApi: string;
    constructor(@Inject(HttpService) private httpService:HttpService) {
        this.prefixApi = "/share";
    }

    public search(body:{[key:string]:any}, callback?:Function, errorCallBack?:Function, finallyCallback?:Function){
        this.httpService.post(`${this.prefixApi}/search`,{}, body,{},callback, errorCallBack, finallyCallback);
    }

    public shareTraffic(body,callback?: Function, errorCallback?:Function, finallyCallback?: Function){
        this.httpService.post(`${this.prefixApi}`, {timeout : 180000},body,{}, callback, errorCallback, finallyCallback);
    }

    public shareTrafficFromDate(body,callback?: Function, errorCallback?:Function, finallyCallback?: Function){
        this.httpService.post(`${this.prefixApi}/auto-share-from-date`,{},body,{}, callback, errorCallback, finallyCallback);
    }

    public shareTrafficByGroup(body,callback?: Function, errorCallback?:Function, finallyCallback?: Function){
        this.httpService.post(`${this.prefixApi}/by-group`, {},body,{}, callback, errorCallback, finallyCallback);
    }

    public create(body,callback?:Function, errorCallBack?:Function, finallyCallback?: Function){
        this.httpService.post(this.prefixApi+"/create", {}, body, {}, callback, errorCallBack, finallyCallback);
    }

    public getListShareInfoCbb(param, callback?: Function, errorCallback?:Function, finallyCallback?: Function){
        this.httpService.get(`${this.prefixApi}/cbb/get-list-phone`, {}, param, callback, errorCallback, finallyCallback);
    }
    public sendOTP(body,callback?: Function, errorCallback?:Function, finallyCallback?: Function){
        this.httpService.post(`${this.prefixApi}/send-otp`, {},body,{}, callback, errorCallback, finallyCallback);
    }
    public downloadTemplate(){
        this.httpService.downloadLocal(`/assets/data/share-info-import-file.xlsx`, "Mẫu_file_thông_tin_người_chia_sẻ.xlsx");
    }

    public downloadTemplateReceiveInfo(){
        this.httpService.downloadLocal(`/assets/data/phone-info-import-file.xlsx`, "Mau_import_SDT_nguoi_duoc_chia_se.xlsx");
    }

    public uploadFileShareInfo(objectFile, callback:Function,errorCallback?:Function, finallyCallback?: Function){
        this.httpService.upload(`${this.prefixApi}/import/share_info`, objectFile,{}, {}, callback, errorCallback, finallyCallback);
    }
    public getByMsisdn(msisdn: string, callback?: Function, errorCallback?:Function, finallyCallback?: Function){
        this.httpService.get(`${this.prefixApi}/shareInfo/getOne`,{}, {msisdn: msisdn}, callback, errorCallback, finallyCallback);
    }

    public updateShared(body, callback?: Function, errorCallback?:Function, finallyCallback?: Function){
        this.httpService.put(this.prefixApi+"/update",{}, body,{}, callback, errorCallback, finallyCallback)
    }

    public deleteShared(id:number, callback?: Function, errorCallback?: Function, finallyCallback?: Function){
        this.httpService.delete(`${this.prefixApi}/delete/${id}`,{},{},callback,errorCallback,finallyCallback)
    }

    public checkExisted(body,callback?: Function, errorCallBack?: Function, finallyCallback?: Function){
        this.httpService.post(this.prefixApi+"/check-phone", {}, body, {}, callback, errorCallBack, finallyCallback)
    }

}
