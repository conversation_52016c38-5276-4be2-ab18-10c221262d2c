export default {
    label: {
        tabGeneral: "General Info",
        tabCrontab: "Summary Schedule",
        tabSend: "Report Sending",
        reportName: "Report Name",
        reportStatus: "Report Status",
        reportEnablePreview: "Report Preview",
        description: "Description",
        tableList: "List Table",
        paramList: "List Parameters",
        tableName: "Table Name",
        paramKey: "Parameter Key",
        paramType: "Parameter Type",
        paramDisplay: "Parameter Display Name",
        query: "Query",
        schema: "Schema",
        reportreceivinggroup: "Dynamic report receiving group",
        updateDate: "Update Date",
        fromDate: "From Date",
        toDate: "To Date",
        fieldDisplay: "Field Display Name",
        fieldData: "Field Data Name",
        valueDisplay: "Value Display Name",
        valueDB: "Value Database Name",
        timeOnce: "Summary Hour",
        startTime: "Start Hour",
        endTime: "End Hour",
        typeSchedule: "Report Type",
        runOne: "Run Once",
        runRepeat: "Run Repeatedly",
        numberRepeatHour: "Number of repeat hours",
        dayInMonth: "Day in Month",
        dayInWeek: "Day in Week",
        monthInYear: "Month in Year",
        monday: "Monday",
        tuesday: "Tuesday",
        wednesday: "Wednesday",
        thursday: "Thursday",
        friday: "Friday",
        saturday: "Saturday",
        sunday: "Sunday",
        january: "January",
        february: "February",
        march: "March",
        april: "April",
        may: "May",
        june: "June",
        july: "July",
        august: "August",
        september: "September",
        october: "October",
        november: "November",
        december: "December",
        emailSubject: "Email Subject",
        emailGroups: "Group Receive Report",
        emailReceive: "Email Receive Report",
        hourSend: "Sending Hour",
        dateType: "Display Type",
        isAutoComplete: "Call Query",
        isMultiChoice: "Multi Choice",
        objectKey: "Object Query",
        input: "Param Name",
        output: "Key Get Value",
        displayPattern: "Display Pattern",
        queryParams:"Query Params",
        required: "Required",
        sampleQueryParam:"Sample: customerCode=$customerCode&userType=1 (customerCode,userType: searchField; $customerCode: customize customerCode; userType=1: hash field)",
        recentlyDateFrom: "Recently Date From",
        recentlyDateTo: "Recently Date To",
        showDateDefault: "Show Date defalt",
    },
    status: {
        active: "ACTIVE",
        inactive: "INACTIVE",
    },
    text: {
        inputReportName: "Input Report Name",
        createTable: "Create Table",
        updateTable: "Update Table",
        detailTable: "Detail Table",
        createParameter: "Create Parameter",
        updateParameter: "Update Parameter",
        detailParameter: "Detail Parameter",
        inputQuery: "Input Query",
        inputTableName: "Input Table Name",
        selectSchema: "Select schema",
        inputParamKey: "Input Parameter Key",
        inputDisplayName: "Input Display Name",
        selectParamType: "Select Parameter Type",
        inputDescription: "Input Description",
        inputemails: "Input Emails",
        inputsms: "Input SMS",
        inputNameReceiving: "Input Name Dynamic Report Receiving Group",
        selectCycle: "Select Number Of Repeat Hours",
        selectHourSummary: "Select Summary Hour",
        selectStartTime: "Select Start Hour",
        selectEndTime: "Select End Hour",
        selectEmailGroup: "Select Email Group",
        selectHourSend: "Select Sending Hour",
        inputEmailSubject: "Input Email Subject",
        selectDateType: "Select Display Type",
        inputObjectKey: "Input Object Key",
        inputInput: "Input Param Name",
        inputOutput: "Input Key Get Value",
        inputDisplayPattern: "Input Display Pattern",
        errorExportLimit:"Export file data exceeds 1 milion line",
        inputQueryParam : "Input Query Param",
        recentlyDateFrom: "Recently Date From",
        recentlyDateTo: "Recently Date To",
    },
    button: {
        addTable: "Add Table",
        addParam: "Add Parameter",
    },
    paramType: {
        number: "Number",
        string: "String",
        date: "Date",
        listNumber: "Enum Number",
        listString: "Enum String",
        timestamp: "Timestamp",
        recentlyDateFrom: "Recently Date From",
        recentlyDateTo: "Recently Date To",
    },
    schema: {
        core: "COREMGMT",
        sim: "SIMMGMT",
        rule: "RULEMGMT",
        bill: "BILLING",
        log: "LOGGING",
        monitor: "MONITORING",
        report: "REPORTING"
    },
    receiving: {
        name: "Name Dynamic Report Group",
        description: "Description",
        emails: "Email",
        sms: "SMS",
    },
    datetype: {
        month: "Month",
        date: "Date",
        datetime: "Date & time"
    },
    message : {
        wrongQueryParamFormat: "Wrong Query Param Format. Check the description lie next to label"
    }
}
