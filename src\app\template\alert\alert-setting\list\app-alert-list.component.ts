import {Component, Inject, Injector, OnInit} from '@angular/core';
import {AccountService} from "../../../../service/account/AccountService";
import {FormBuilder} from "@angular/forms";
import {MenuItem} from "primeng/api";
import {ColumnInfo, OptionTable} from "../../../common-module/table/table.component";
import {CONSTANTS} from "../../../../service/comon/constants";
import {AlertService} from "../../../../service/alert/AlertService";
import {ComponentBase} from "../../../../component.base";
import {CustomerService} from "../../../../service/customer/CustomerService";
import {ComboLazyControl} from "../../../common-module/combobox-lazyload/combobox.lazyload";

@Component({
    selector: 'app-app.alert.list',
    templateUrl: './app-alert-list.component.html',
})
export class AppAlertListComponent extends ComponentBase implements OnInit {
    constructor(
        @Inject(AccountService) private accountService: AccountService,
        @Inject(CustomerService) private customerService: CustomerService,
        private formBuilder: FormBuilder,
        @Inject(AlertService) private alertService: AlertService,
        private injector: Injector
    ) {
        super(injector);
    }

    statusAlert: Array<any>;
    statusSim: Array<any>;
    items: MenuItem[];
    home: MenuItem;
    formSearchAlert: any
    searchInfo: {
        name: string | null,
        deviceTypeId: string | null,
        modelCode: string | null,
        typeCode: string | null,
        eventType: number | null,
        status: number | null,
        severity: number | null,
        fromDate: Date | null,
        toDate: Date | null,
        username: string | null,
    }
    pageNumber: number;
    pageSize: number;
    sort: string;
    dataSet: {
        content: Array<any>,
        total: number
    };
    repeat: boolean;
    comboSelectCustomerControl: ComboLazyControl = new ComboLazyControl();
    statusAlertForDetail: any;
    selectItems: Array<any>;
    columns: Array<ColumnInfo>;
    optionTable: OptionTable;
    severityOptions: Array<any>;
    eventOptions: Array<any>;
    userInfo: any = {};
    isShowConfimChangeStatus: boolean;
    controlComboSelectType: ComboLazyControl = new ComboLazyControl();
    controlComboSelectModel: ComboLazyControl = new ComboLazyControl()
    paramSearchType: {
        modelCode: any;
    }
    paramSearchModel: {
        typeCode: any;
    }
    paramSearchUserCreated = {
        sort: "id,desc",
    }
    normalPattern = /^[a-zA-Z0-9 ]*$/;
    vietnamesePattern = /^[^~`!@#\$%\^&*\(\)=\+\[\]\{\}\|\\,<>\/?]*$/;
    maxDateFrom: Date | number | string | null = null;
    minDateTo: Date | number | string | null = null;
    maxDateTo: Date | number | string | null = null;

    ngOnInit(): void {
        let me = this;
        this.items = [{label: this.tranService.translate("global.menu.alertList")}];
        this.home = {icon: 'pi pi-home', routerLink: '/'};
        this.paramSearchType = {
            modelCode: "",
        }
        this.paramSearchModel = {
            typeCode: "",
        }
        this.searchInfo = {
            name: null,
            deviceTypeId: null,
            modelCode: null,
            typeCode: null,
            fromDate: null,
            toDate: null,
            eventType: null,
            username: null,
            status: null,
            severity: null
        }
        this.isShowConfimChangeStatus = false;
        this.formSearchAlert = this.formBuilder.group(this.searchInfo);
        this.selectItems = [];
        this.pageNumber = 0;
        this.pageSize = 10;
        this.sort = "id,desc";
        this.userInfo = this.sessionService.userInfo;
        this.statusAlertForDetail = CONSTANTS.ALERT_STATUS;
        this.statusAlert = [
            {name: this.tranService.translate("alert.status.active"), value: CONSTANTS.ALERT_STATUS.ACTIVE},
            {name: this.tranService.translate("alert.status.inactive"), value: CONSTANTS.ALERT_STATUS.INACTIVE},
        ]

        this.severityOptions = [
            {name: this.tranService.translate("alert.severity.critical"), value: CONSTANTS.ALERT_SEVERITY.CRITICAL},
            {name: this.tranService.translate("alert.severity.major"), value: CONSTANTS.ALERT_SEVERITY.MAJOR},
            {name: this.tranService.translate("alert.severity.minor"), value: CONSTANTS.ALERT_SEVERITY.MINOR},
            {name: this.tranService.translate("alert.severity.info"), value: CONSTANTS.ALERT_SEVERITY.INFO}
        ]
        this.eventOptions = [
            {
                name: this.tranService.translate("alert.eventType.exceededValue"),
                value: CONSTANTS.ALERT_EVENT_TYPE.EXCEEDED_VALUE
            },
            {
                name: this.tranService.translate("alert.eventType.deviceAlert"),
                value: CONSTANTS.ALERT_EVENT_TYPE.DEVICE_ALERT
            },
        ];
        this.columns = [
            {
                name: this.tranService.translate("alert.label.name"),
                key: "name",
                size: "200px",
                align: "left",
                isShow: true,
                isSort: true,
                style: {
                    cursor: me.checkAuthen([CONSTANTS.PERMISSIONS.ALERT.VIEW_DETAIL]) ? "pointer" : "auto",
                    color: me.checkAuthen([CONSTANTS.PERMISSIONS.ALERT.VIEW_DETAIL]) ? "var(--mainColorText)" : "",
                    display: 'inline-block',
                    maxWidth: '300px',
                    overflow: 'hidden',
                    textOverflow: 'ellipsis'
                },
                isShowTooltip: true,
                funcClick(id, item) {
                    if (me.checkAuthen([CONSTANTS.PERMISSIONS.ALERT.VIEW_DETAIL])) {
                        me.router.navigate([`/alerts/detail/${item.id}`]);
                    }
                },
            },
            {
                name: this.tranService.translate("device.label.type"),
                key: "deviceTypeName",
                size: "fit-content",
                align: "left",
                isShow: true,
                isSort: true,
            },
            {
                name: this.tranService.translate("device.label.model"),
                key: "modelCode",
                size: "fit-content",
                align: "left",
                isShow: true,
                isSort: true,
            },
            {
                name: this.tranService.translate("alert.label.rule"),
                key: "eventType",
                size: "250px",
                align: "left",
                funcConvertText(value) {
                    const eventType = me.eventOptions.find(item => item.value === value)
                    if (eventType) {
                        return eventType.name
                    }
                    return ""
                },
                isShow: true,
                isSort: true,
            },
            {
                name: this.tranService.translate("alert.label.level"),
                key: "severity",
                size: "200px",
                align: "left",
                isShow: true,
                isSort: true,
                funcConvertText(value) {
                    if (value == CONSTANTS.ALERT_SEVERITY.CRITICAL) {
                        return me.tranService.translate("alert.severity.critical");
                    } else if (value == CONSTANTS.ALERT_SEVERITY.MAJOR) {
                        return me.tranService.translate("alert.severity.major");
                    } else if (value == CONSTANTS.ALERT_SEVERITY.MINOR) {
                        return me.tranService.translate("alert.severity.minor");
                    } else if (value == CONSTANTS.ALERT_SEVERITY.INFO) {
                        return me.tranService.translate("alert.severity.info");
                    } else {
                        return "";
                    }
                },
            },
            {
                name: this.tranService.translate("alert.label.userCreated"),
                key: "createdByName",
                size: "fit-content",
                align: "left",
                isShow: true,
                isSort: true,
            },
            {
                name: this.tranService.translate("alert.label.createdDate"),
                key: "createdDate",
                size: "fit-content",
                align: "left",
                isShow: true,
                isSort: true,
                funcConvertText(value) {
                    if (value == null) return "";
                    return me.utilService.convertLongDateToString(value);
                }
            },
            {
                name: this.tranService.translate("alert.label.status"),
                key: "status",
                size: "180px",
                align: "left",
                isShow: true,
                isSort: true,
                funcConvertText(value) {
                    if (value == CONSTANTS.ALERT_STATUS.ACTIVE) {
                        return me.tranService.translate("alert.status.active");
                    } else if (value == CONSTANTS.ALERT_STATUS.INACTIVE) {
                        return me.tranService.translate("alert.status.inactive");
                    } else {
                        return "";
                    }
                },
                funcGetClassname: (value) => {
                    if (value == CONSTANTS.ALERT_STATUS.ACTIVE) {
                        return ['p-2', "text-green-800", "bg-green-100", "border-round", "inline-block"];
                    } else if (value == CONSTANTS.ALERT_STATUS.INACTIVE) {
                        return ['p-2', 'text-red-700', "bg-red-100", "border-round", "inline-block"];
                    }
                    return [];
                },
                style: {
                    color: "white"
                }
            }
        ]

        this.optionTable = {
            hasClearSelected: true,
            hasShowChoose: false,
            hasShowIndex: true,
            hasShowToggleColumn: true,
            action: [
                {
                    icon: "pi pi-pencil",
                    tooltip: this.tranService.translate('global.button.edit'),
                    func: function (id, item) {
                        me.router.navigate([`/alerts/update/${id}`]);
                    },
                    funcAppear: function (id, item) {
                        return me.checkAuthen([CONSTANTS.PERMISSIONS.ALERT.UPDATE]) && item.status == CONSTANTS.ALERT_STATUS.INACTIVE
                    }
                },
                {
                    icon: "pi pi-lock",
                    tooltip: this.tranService.translate("global.button.changeStatus"),
                    func: function (id, item) {
                        me.messageCommonService.confirm(
                            me.tranService.translate("global.message.titleConfirmChangeStatusAlert"),
                            me.tranService.translate("global.message.confirmChangeStatusAlert"),
                            {
                                ok: () => {
                                    let dataBody = {
                                        id: id,
                                        status: Math.abs(1 - item.status || 0)
                                    }
                                    me.alertService.changeStatus(dataBody, (response) => {
                                        me.messageCommonService.success(me.tranService.translate("global.message.changeStatusSuccess"));
                                        me.search(me.pageNumber, me.pageSize, me.sort, me.searchInfo);
                                    })
                                },
                                cancel: () => {
                                }
                            }
                        )
                    },
                    funcAppear: function (id, item) {
                        return me.checkAuthen([CONSTANTS.PERMISSIONS.ALERT.CHANGE_STATUS]);
                    }
                },
                {
                    icon: "pi pi-trash",
                    tooltip: this.tranService.translate("global.button.delete"),
                    func: function (id, item) {
                        me.messageCommonService.confirm(
                            me.tranService.translate("global.message.titleConfirmDeleteAlert"),
                            me.tranService.translate("global.message.confirmDeleteAlert"),
                            {
                                ok: () => {
                                    me.alertService.deleteById(parseInt(id), (response) => {
                                        me.messageCommonService.success(me.tranService.translate("global.message.deleteSuccess"));
                                        me.search(me.pageNumber, me.pageSize, me.sort, me.searchInfo);
                                    })
                                },
                                cancel: () => {

                                }
                            }
                        )
                    },
                    funcAppear: function (id, item) {
                        return me.checkAuthen([CONSTANTS.PERMISSIONS.ALERT.DELETE]) && item.status == CONSTANTS.ALERT_STATUS.INACTIVE
                    }
                },

            ]
        }
        this.search(this.pageNumber, this.pageSize, this.sort, this.searchInfo);
    }

    onChangeDateFrom(value) {
        if (value) {
            this.minDateTo = value;
        } else {
            this.minDateTo = null
        }
    }

    onChangeDateTo(value) {
        if (value) {
            this.maxDateFrom = value;
        } else {
            this.maxDateFrom = new Date();
        }
    }

    onSelectedType() {
        let me = this;
        me.paramSearchModel.typeCode = me.searchInfo.typeCode ? me.searchInfo.typeCode : "";
    }

    onSelectedModel() {
        let me = this;
        me.paramSearchType.modelCode = me.searchInfo.modelCode ? me.searchInfo.modelCode : "";
    }

    onSubmitSearch() {
        this.pageNumber = 0;
        this.search(this.pageNumber, this.pageSize, this.sort, this.searchInfo);
    }

    search(page, limit, sort, params) {
        let me = this;
        this.pageNumber = page;
        this.pageSize = limit;
        this.sort = sort;
        let dataParams = {
            page,
            size: limit,
            sort
        }
        Object.keys(this.searchInfo).forEach(key => {
            if (this.searchInfo[key] != null) {
                if (key == "fromDate") {
                    dataParams["fromDate"] = me.searchInfo.fromDate.getTime();
                } else if (key == "toDate") {
                    dataParams["toDate"] = me.searchInfo.toDate.getTime()
                } else {
                    dataParams[key] = this.searchInfo[key];
                }
            }
        })
        me.messageCommonService.onload();
        this.alertService.search(dataParams, (response) => {
            me.dataSet = {
                content: response.content,
                total: response.totalElements
            }
        }, null, () => {
            me.messageCommonService.offload();
        })
    }

    protected readonly CONSTANTS = CONSTANTS;
}
