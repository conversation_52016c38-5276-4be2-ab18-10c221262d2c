export default{
    label:{
        customerCode:"Customer Code",
        customerName:"Customer Name",
        taxCode:"Tax Code",
        contact:"Contact",
        phoneNumber:"Phone Number",
        type:"Type",
        status:"Status",
        email:"Email",
        contract:"Contract",
        provinceCode:"Province Code",
        active:"Active",
        inActive:"Inactive",
        companyName:"Company Name",
        birthday:"Birthday",
        establishmentDate:"Establishment Date",
        street:"Street",
        district:"District",
        city:"Province/City",
        note:"Note",
        fullName:"Full name",
        listCustomer:"List Customer",
        contractHeader:"Contract",
        infoCustomer:"Detail",
        editCustomer:"Update",
        generalInfo: "General Infomation",
        billingContact: "Billing Contact",
        billingAddress:"Billing Address",
        viewAccount:"View Account",
        viewContract:"View Contract"
    },
    dropdown:{
        personal:"Personal",
        company:"Company",
        agency:"Agency"
    },
    error:{
        length:"This field should have 2 - 255 characters ",
        required:"This field is required",
        character:"Wrong Format. Only Accept (a-z, A-Z, 0-9, Vietnamese, ! # $ % & ' * + - / = ?  ^ _ ` . { | } ~)",
        regular:"Wrong Format. Only Accept (a-z, A-Z, 0-9, -_, Vietnamese)",
        note: "Wrong Format. Only Accept (chữ, số, Tiếng Việt, khoảng trắng, ! # $ % & ' * + - / = ?  ^ _ ` . , ( ) { | } ~ :)",
    }
}