import { Component, Inject, Injector, OnInit } from "@angular/core";
import { FormBuilder } from "@angular/forms";
import { MenuItem } from "primeng/api";
import { ComponentBase } from "src/app/component.base";
import { ConfigChartService } from "src/app/service/charts/ConfigChartService";
import { ColumnInfo, OptionTable } from "../../common-module/table/table.component";
import { CONSTANTS } from "src/app/service/comon/constants";

@Component({
    templateUrl: './app.config.chart.list.component.html',
    selector: "app-config-chart-list"
})
export default class ConfigChartListComponent extends ComponentBase implements OnInit {
    items: MenuItem[];
    home: MenuItem
    searchInfo: {
        name: string|null,
        type: string|null
    };
    formSearch: any;
    pageNumber: number;
    pageSize: number;
    sort: string;
    dataSet: {
        content: Array<any>,
        total: number
    };
    columns: Array<ColumnInfo>;
    optionTable: OptionTable;
    selectItems: Array<{imsi:number,groupName:string|null,[key:string]:any}>;
    typeCharts: Array<{name: string, value: string}> = [];
    subTypeCharts: Array<{name: string, value: string}> = [];
    constructor(
        @Inject(ConfigChartService) private configChartService: ConfigChartService,
        private injector: Injector,
        private formBuilder: FormBuilder) {
        super(injector);
    }

    ngOnInit(): void {
        let me = this;
        this.selectItems = [];
        this.home = { icon: 'pi pi-home', routerLink: '/' };
        this.items =[{label: this.tranService.translate("global.menu.charts")}, {label: this.tranService.translate("global.menu.chartList")}]
        this.searchInfo = {
            name: "",
            type: ""
        };
        this.formSearch = this.formBuilder.group(this.searchInfo);
        this.pageNumber = 0;
        this.pageSize= 10;
        this.sort = "name,asc"
        this.dataSet ={
            content: [{
                id: 1,
                name: "Bieu do 1",
                type: "line",
                subType: ""
            },{
                id: 1,
                name: "Bieu do 2",
                type: "bar",
                subType: "vertical"
            },
            {
                id: 1,
                name: "Bieu do 3",
                type: "bar",
                subType: "vertical"
            },{
                id: 1,
                name: "Bieu do 4",
                type: "bar",
                subType: "horizontal"
            },{
                id: 1,
                name: "Bieu do 5",
                type: "bar",
                subType: "stack"
            },{
                id: 1,
                name: "Bieu do 6",
                type: "bar",
                subType: "multiAxis"
            },{
                id: 1,
                name: "Bieu do 7",
                type: "bubble",
                subType: ""
            },{
                id: 1,
                name: "Bieu do 8",
                type: "doughnut",
                subType: ""
            },{
                id: 1,
                name: "Bieu do 9",
                type: "pie",
                subType: ""
            },{
                id: 1,
                name: "Bieu do 10",
                type: "line",
                subType: "multiAxis"
            },{
                id: 1,
                name: "Bieu do 11",
                type: "combo",
                subType: "multiAxis"
            },{
                id: 1,
                name: "Bieu do 12",
                type: "polarArea",
                subType: ""
            },{
                id: 1,
                name: "Bieu do 13",
                type: "radar",
                subType: ""
            },{
                id: 1,
                name: "Bieu do 14",
                type: "scatter",
                subType: ""
            }],
            total: 14
        }

        this.columns = [{
            name: this.tranService.translate("chart.label.chartName"),
            key: "name",
            size: "850px",
            align: "left",
            isShow: true,
            isSort: true,
            style:{
                cursor: "pointer",
             color: "var(--mainColorText)"
            },
            funcGetRouting(item) {
                return [`/config-chart/update/${item.id}`]
            },
        },
        {
            name: this.tranService.translate("chart.label.chartType"),
            key: "type",
            size: "250px",
            align: "left",
            isShow: true,
            isSort: false,
            funcConvertText: (value)=>{
                if(value == CONSTANTS.CHART_TYPE.BAR){
                    return me.tranService.translate("chart.type.bar");
                }else if(value == CONSTANTS.CHART_TYPE.BUBBLE){
                    return me.tranService.translate("chart.type.bubble");
                }else if(value == CONSTANTS.CHART_TYPE.COMBO){
                    return me.tranService.translate("chart.type.combo");
                }else if(value == CONSTANTS.CHART_TYPE.DOUGHNUT){
                    return me.tranService.translate("chart.type.doughnut");
                }else if(value == CONSTANTS.CHART_TYPE.LINE){
                    return me.tranService.translate("chart.type.line");
                }else if(value == CONSTANTS.CHART_TYPE.PIE){
                    return me.tranService.translate("chart.type.pie");
                }else if(value == CONSTANTS.CHART_TYPE.POLAR){
                    return this.tranService.translate("chart.type.polar");
                }else if(value == CONSTANTS.CHART_TYPE.RADAR){
                    return this.tranService.translate("chart.type.radar");
                }else if(value == CONSTANTS.CHART_TYPE.SCATTER){
                    return this.tranService.translate("chart.type.scatter");
                }
                return "";
            }
        },
        {
            name: this.tranService.translate("chart.label.chartSubType"),
            key: "subType",
            size: "250px",
            align: "left",
            isShow: true,
            isSort: false,
            funcConvertText: (value)=>{
                if(value == CONSTANTS.CHART_SUB_TYPE.HORIZONTAL_BAR){
                    return me.tranService.translate("chart.subType.horizontalBar");
                }else if(value == CONSTANTS.CHART_SUB_TYPE.VERTICAL_BAR){
                    return me.tranService.translate("chart.subType.verticalBar");
                }else if(value == CONSTANTS.CHART_SUB_TYPE.STACKED_BAR){
                    return me.tranService.translate("chart.subType.stackedBar");
                }else if(value == CONSTANTS.CHART_SUB_TYPE.MULTI_AXIS){
                    return me.tranService.translate("chart.subType.multiAxis");
                }
                return "";
            }
        }];

        this.optionTable = {
            hasClearSelected:false,
            hasShowChoose: false,
            hasShowIndex: true,
            hasShowToggleColumn: false,
            action: [
                {
                    icon: "pi pi-pencil",
                    tooltip: this.tranService.translate("global.button.edit"),
                    func: function(id, item){
                        me.router.navigate([`/config-chart/update/${id}`]);
                    }
                },
                {
                    icon: "pi pi-trash",
                    tooltip: this.tranService.translate("global.button.delete"),
                    func: function(id, item){
                        me.messageCommonService.confirm(
                            me.tranService.translate("global.message.titleConfirmDeleteAccount"),
                            me.tranService.translate("global.message.confirmDeleteAccount"),
                            {
                                ok:()=>{
                                    me.messageCommonService.onload();
                                    me.configChartService.delete(id, ()=> {
                                        me.search(me.pageNumber, me.pageSize, me.sort, me.searchInfo);
                                        me.messageCommonService.offload();
                                    }, null, (typeFinish)=>{
                                        if(typeFinish == "error"){
                                            me.messageCommonService.offload();
                                        }
                                    })
                                    me.messageCommonService.success(me.tranService.translate("global.message.deleteSuccess"));
                                },
                                cancel: ()=>{
                                    me.messageCommonService.error(me.tranService.translate("global.message.deleteFail"));
                                }
                            }
                        )
                    },
                    funcAppear(id, item) {
                        return me.checkAuthen([CONSTANTS.PERMISSIONS.CONFIG_DYNAMIC_CHART.DELETE])
                    },
                },
            ]
        }
        this.pageNumber = 0;
        this.pageSize= 10;
        this.sort = "name,asc";

        this.typeCharts = [
            {
                name: this.tranService.translate("chart.type.bar"),
                value: CONSTANTS.CHART_TYPE.BAR
            },
            {
                name: this.tranService.translate("chart.type.bubble"),
                value: CONSTANTS.CHART_TYPE.BUBBLE
            },
            {
                name: this.tranService.translate("chart.type.combo"),
                value: CONSTANTS.CHART_TYPE.COMBO
            },
            {
                name: this.tranService.translate("chart.type.doughnut"),
                value: CONSTANTS.CHART_TYPE.DOUGHNUT
            },
            {
                name: this.tranService.translate("chart.type.line"),
                value: CONSTANTS.CHART_TYPE.LINE
            },
            {
                name: this.tranService.translate("chart.type.pie"),
                value: CONSTANTS.CHART_TYPE.PIE
            },
            {
                name: this.tranService.translate("chart.type.polar"),
                value: CONSTANTS.CHART_TYPE.POLAR
            },
            {
                name: this.tranService.translate("chart.type.radar"),
                value: CONSTANTS.CHART_TYPE.RADAR
            },
            {
                name: this.tranService.translate("chart.type.scatter"),
                value: CONSTANTS.CHART_TYPE.SCATTER
            }
        ]

        this.subTypeCharts = [
            {
                name: this.tranService.translate("chart.subType.horizontalBar"),
                value: CONSTANTS.CHART_SUB_TYPE.HORIZONTAL_BAR
            },
            {
                name: this.tranService.translate("chart.subType.verticalBar"),
                value: CONSTANTS.CHART_SUB_TYPE.VERTICAL_BAR
            },
            {
                name: this.tranService.translate("chart.subType.stackedBar"),
                value: CONSTANTS.CHART_SUB_TYPE.STACKED_BAR
            },
            {
                name: this.tranService.translate("chart.subType.multiAxis"),
                value: CONSTANTS.CHART_SUB_TYPE.MULTI_AXIS
            }
        ]

        this.search(this.pageNumber, this.pageSize, this.sort, this.search);
    }

    search(page, limit, sort, params){
        this.pageNumber = page;
        this.pageSize = limit;
        this.sort = sort;
        let me = this;
        let pr = {...params};
        Object.keys(pr).forEach(key => {
            if(pr[key] == null || pr[key].length == 0){
                delete pr[key];
            }
        })
        let dataParams = {
            page,
            size: limit,
            sort,
            ...pr
        }
        me.messageCommonService.onload();
        this.configChartService.search(dataParams, (response)=>{
            me.dataSet = {
                content: response.content,
                total: response.totalElements,
            }
        }, null, ()=>{
            me.messageCommonService.offload();
        })
    }

    onSubmitSearch(){
        this.search(this.pageNumber, this.pageSize, this.sort, this.searchInfo);
    }
}
