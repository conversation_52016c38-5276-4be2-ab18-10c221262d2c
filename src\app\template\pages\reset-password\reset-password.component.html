<div style="background-image: url('../../../../assets/images/login/login_moi.png');background-size: cover;" class="surface-ground flex align-items-center justify-content-center min-h-screen min-w-screen overflow-hidden">
    <div class="flex flex-column align-items-center justify-content-center">
        <!-- <img src="assets/images/m2m.png" alt="ONEIOT Platform logo" class="mb-5 w-20rem flex-shrink-0">                 -->
        <div>
            <div class="w-full surface-card py-8 px-5 sm:px-8" style="border-radius:25px;position: relative;background-color: rgba(225,225,225,85%) !important;;">
                <choose-language style="position: absolute;right: 28px;top: 30px;"></choose-language>
                <div class="mb-5 text-center" style="color: #204887">
                    <div class="blue-400 text-4xl font-medium mb-2">
                        VNPT-Technology
                    </div>
                    <p class="mt-2">
                        <span class="text-2xl">{{tranService.translate('account.label.cmpForgotPass')}}</span>
                    </p>
                </div>

                <div *ngIf="!isLinkExpired && formResetPassword">
                    <form [formGroup]="formResetPassword" (ngSubmit)="resetPassword()">
                        <label for="email1" class="block text-900 text-xl font-medium mb-2">{{tranService.translate("login.label.email")}}</label>
                        <input id="email1" [readonly]="true" type="text" [(ngModel)]="resetPasswordInfo.email" formControlName="email" [disabled]="true" [placeholder]="tranService.translate('login.label.email')" pInputText class="w-full md:w-30rem mb-5" style="padding:1rem">

                        <div>
                            <label for="newPassword" class="block text-900 font-medium text-xl mb-2">{{tranService.translate("account.label.newPass")}}</label>
                            <p-password id="newPassword"
                                [(ngModel)]="resetPasswordInfo.newPassword"
                                [feedback]=false [placeholder]="tranService.translate('account.label.newPass')"
                                [toggleMask]="true"
                                inputStyleClass="w-full p-3 md:w-30rem"
                                formControlName="newPassword"
                                [required]="true"
                                pattern="^(?=.*[0-9])(?=.*[a-zA-Z])(?=.*[!@#$%^&*,./?;:`~'&quot;\[\]+_=-\>\<\)\(\}\{|\\]).{6,20}$"
                            ></p-password>
                        </div>
                        <!-- error new passs -->
                        <div class="w-full text-error-field" class="mb-2">
                            &nbsp;
                            <div>
                            <small class="text-red-500 block"
                                    *ngIf="formResetPassword.controls.newPassword.dirty && formResetPassword.controls.newPassword.errors?.required">{{tranService.translate(
                                "global.message.required")}}</small>
                            <small class="text-red-500 block"
                                    *ngIf="formResetPassword.controls.newPassword.errors?.pattern">{{tranService.translate(
                                "global.message.invalidPasswordFomat")}}</small>
                            </div>
                        </div>

                        <div>
                            <label for="confirmPassword" class="block text-900 font-medium text-xl mb-2">{{tranService.translate("account.label.confirmPass")}}</label>
                            <p-password id="confirmPassword"
                                        [(ngModel)]="resetPasswordInfo.confirmPassword"
                                        [feedback]=false [placeholder]="tranService.translate('account.label.confirmPass')"
                                        [toggleMask]="true"
                                        inputStyleClass="w-full p-3 md:w-30rem"
                                        formControlName="confirmPassword"
                                        [required]="true"
                                        pattern="^(?=.*[0-9])(?=.*[a-zA-Z])(?=.*[!@#$%^&*,./?;:`~'&quot;\[\]+_=-\>\<\)\(\}\{|\\]).{6,20}$"
                            ></p-password>
                        </div>

                        <!-- error confirm passs -->
                        <div class="w-full text-error-field" class="mb-2">
                            &nbsp;
                            <div>
                            <small class="text-red-500 block"
                                    *ngIf="formResetPassword.controls.confirmPassword.dirty && formResetPassword.controls.confirmPassword.errors?.required">{{tranService.translate(
                                "global.message.required")}}</small>
                            <small class="text-red-500 block"
                                    *ngIf="formResetPassword.controls.confirmPassword.errors?.pattern">{{tranService.translate(
                                "global.message.invalidPasswordFomat")}}</small>
                                <small class="text-red-500 block"
                                    *ngIf="!formResetPassword.controls.confirmPassword.errors?.required && !formResetPassword.controls.newPassword.errors?.pattern
                                                && !formResetPassword.controls.newPassword.errors?.required && !formResetPassword.controls.confirmPassword.errors?.pattern
                                                && resetPasswordInfo.confirmPassword != resetPasswordInfo.newPassword">{{tranService.translate(
                                    "global.message.passwordNotMatch")}}</small>
                            </div>
                        </div>

                        <div class="pt-2">
                            <p-button styleClass="p-button-info w-full p-3 bg-blue-800" type="submit" [label]="tranService.translate('account.label.submitChangePass')"
                                [disabled]="resetPasswordInfo.invalid || resetPasswordInfo.confirmPassword != resetPasswordInfo.newPassword"
                            ></p-button>
                        </div>
                    </form>
                </div>

                <div *ngIf="isLinkExpired">
                    <span class="text-red-500 text-2xl">{{tranService.translate('account.label.linkExpired')}}</span>
                </div>
            </div>
        </div>
    </div>
</div>

