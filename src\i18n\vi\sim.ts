export default {
    label: {
        sothuebao: "<PERSON><PERSON> thuê bao",
        sosim: "<PERSON><PERSON> Sim",
        imsi: "IMSI",
        maapn: "Mã APN",
        goicuoc: "<PERSON><PERSON><PERSON>",
        mahopdong: "<PERSON><PERSON>ợp <PERSON>ồ<PERSON>",
        nguoilamhopdong: "<PERSON>ư<PERSON><PERSON>",
        dient<PERSON><PERSON><PERSON><PERSON>: "Điện Thoại Liê<PERSON>",
        trangthaisim: "Trạng Thái",
        nhomsim: "Nhóm thuê bao",
        makhachhang: "Mã Khách Hàng",
        khachhang: "<PERSON>h<PERSON>ch <PERSON>àng",
        khachhangvathue: "<PERSON>h<PERSON><PERSON> / <PERSON>ã số thuế",
        ngaylamhopdongtu: "<PERSON><PERSON><PERSON>",
        ngaylamhopdongden: "Ngày <PERSON>àm <PERSON>ồ<PERSON>",
        simid: "Sim ID",
        dungluong: "Dung Lượng <PERSON> (MB)",
        tengoicuoc: "<PERSON>ên <PERSON>",
        trangthaiket<PERSON>i : "<PERSON>r<PERSON>ng thái kết nối",
        ngaykichhoat : "<PERSON><PERSON><PERSON> kích ho<PERSON>",
        ngaylamhopdong: "<PERSON><PERSON><PERSON>",
        matrungtam: "Mã Trung Tâm",
        diachilienhe: "Địa Chỉ Liên Hệ",
        paymentName: "Tên thanh toán",
        paymentAddress: "Địa chỉ thanh toán",
        routeCode: "Mã đường truyền",
        customerBirth: "Ngày sinh khách hàng",
        customerCode: "Mã khách hàng",
        iccid: "ICCID",
        description: "Mô tả",
        groupName: "Tên nhóm",
        groupKey: "Mã nhóm",
        pointAccess: "Điểm truy cập",
        imeiDevice: "IMEI thiết bị",
        dataUseMax: "Dung lượng sử dụng tối đa",
        dataUseInMonth: "Dung lượng đã sử dụng trong tháng",
        dataUse: "Dung lượng sử dụng",
        typeConnection: "Loại kết nối",
        staticIp: "IP Tĩnh",
        dynamicIp: "IP Động",
        rangeIp: "Dải IP",
        apnStatus: "Trạng thái APN",
        vpnchannelname: "Kênh 3G-VPN",
        pdpcp: "PDPCP",
        epsprofileid: "EPSProfileID",
        iptype: "Loại IP",
        ip: "IP",
        note: "Ghi chú",
        overData: "Quá dung lượng",
        dataUseOnRatingPlan: "Dung lượng đã sử dụng theo gói cước",
        dataRemainOnRatingPlan: "Dung lượng còn lại theo gói cước",
        smsIntra: "Tin nhắn nội mạng còn lại theo gói cước",
        smsInter: "Tin nhắn ngoại mạng còn lại theo gói cước",
        chargesIncurred: "Cước phát sinh",
        smsUnit: "Tin nhắn",
        deleteSim : "Xoá sim",
        startDate : "Ngày hòa mạng",
        serviceType : "Loại dịch vụ",
        simType : "Chủng loại sim",
        dataPoolSubCode: "Mã ví",
        dataPoolEmail: "Email người nhận",
        dataPoolPhoneActive: "Sđt người nhận",
        quickSearch: "Tìm kiếm nhanh",
        statusDetach: "Thuê bao tắt máy",
        statusNotAttach: "Đã kết nối, chưa sẵn sàng truyền dữ liệu và paging",
        statusAttach: "Đã kết nối, chưa sẵn sàng truyền dữ liệu nhưng có thể paging",
        statusNotConnect: "Đã kết nối, sẵn sàng truyền dữ liệu nhưng không thể paging",
        statusConnect: "Đã kết nối, sẵn sàng truyền dữ liệu và paging",
        statusNetwork: "Không thể truy cập"
    },
    text:{
        selectCustomer: "Chọn khách hàng",
        selectGroupSim: "Chọn nhóm thuê bao",
        selectRatingPlan: "Chọn gói cước",
        inputGroupName: "Nhập tên nhóm",
        inputGroupKey: "Nhập mã nhóm",
        inputDescription: "Nhập mô tả",
        detailSim: "Chi tiết thuê bao",
        simInfo: "Thông tin thuê bao",
        simStatusInfo: "Thông tin trạng thái dịch vụ thuê bao",
        customerInfo: "Thông tin khách hàng",
        ratingPlanInfo: "Thông tin gói cước",
        contractInfo: "Thông tin hợp đồng",
        apnInfo: "Thông tin APN",
        pushSim: "Gán nhóm thuê bao",
        sameProvince: "Yêu cầu danh sách thuê bao cùng một tỉnh/thành phố",
        sameCustomer: "Yêu cầu danh sách thuê bao cùng một khách hàng",
        deleteSim : "Bạn có chắc muốn xóa sim ?"
    },
    status: {
        all: "Tất cả",
        inventory: "Thuê bao Trắng",
        ready: "Sẵn sàng",
        activationReady: "Sẵn sàng hoạt động",
        activated: "Hoạt động",
        inactivated: "Khóa 1 chiều",
        deactivated: "Khóa 2 chiều",
        purged: "Hủy",
        processing: "Đang xử lý",
        processingChangePlan: "Đang xử lý đổi gói cước",
        processingRegisterPlan: "Đang xử lý đăng ký gói cước",
        waitingCancelPlan: "Đang chờ hủy",
        service:{
            data: "Data",
            callReceived: "Cuộc gọi đến",
            callSent: "Cuộc gọi đi",
            callWorld: "Gọi đi quốc tế",
            smsReceived: "Tin nhắn đến",
            smsSent: "Tin nhắn đi"
        }
    },
    serviceType :{
        prepaid : "Trả trước",
        postpaid : "Trả sau"
    },
    type: {
        unknown: "Unknown",
        esim: "eSIM",
        sim: "SIM thường"
    }
}
