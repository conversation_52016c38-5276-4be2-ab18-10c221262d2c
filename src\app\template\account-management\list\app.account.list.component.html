<style>
    /* .col-3{
        padding: 10px;
    } */
</style>

<div class="vnpt flex flex-row justify-content-between align-items-center bg-white p-2 border-round">
    <div class="">
        <div class="text-xl font-bold mb-1">{{this.tranService.translate("global.menu.listaccount")}}</div>
        <p-breadcrumb class="max-w-full col-7" [model]="items" [home]="home"></p-breadcrumb>
    </div>
    <div class="col-5 flex flex-row justify-content-end align-items-center">
        <p-button styleClass="p-button-info"
                  [label]="tranService.translate('global.button.create')"
                  icon="" [routerLink]="['/accounts/create']"
                  routerLinkActive="router-link-active"
                  *ngIf="checkPermission([allPermissions.ACCOUNT.CREATE])"></p-button>
    </div>
</div>

<form [formGroup]="formSearchAccount" (ngSubmit)="onSubmitSearch()" class="pt-3 pb-2 vnpt-field-set">
    <p-panel [toggleable]="true" [header]="tranService.translate('global.text.filter')">
        <div class="grid">
            <!-- Ten dang nhap -->
            <div class="col-3">
                <span class="p-float-label">
                    <input pInputText
                           class="w-full"
                           pInputText id="username"
                           [(ngModel)]="searchInfo.username"
                           formControlName="username"
                    />
                    <label htmlFor="username">{{tranService.translate("account.label.username")}}</label>
                </span>
            </div>
            <!-- Ho ten -->
            <div class="col-3">
                <span class="p-float-label">
                    <input pInputText
                           class="w-full"
                           pInputText id="cusName"
                           [(ngModel)]="searchInfo.name"
                           formControlName="name"
                    />
                    <label *ngIf="accountCurrentDetail.type===CONSTANTS.USER_TYPE.ADMIN" htmlFor="fullName">{{tranService.translate("account.label.cusName")}}</label>
                    <label *ngIf="accountCurrentDetail.type!==CONSTANTS.USER_TYPE.ADMIN" htmlFor="fullName">{{tranService.translate("account.label.customerName")}}</label>
                </span>
            </div>
            <!-- loai tai khoan -->
            <div class="col-3">
                <span class="p-float-label">
                    <p-dropdown styleClass="w-full" [showClear]="true"
                                id="type" [autoDisplayFirst]="false"
                                [(ngModel)]="searchInfo.type"
                                formControlName="type"
                                [options]="statusAccounts"
                                optionLabel="name"
                                optionValue="value"
                    ></p-dropdown>
                    <label for="type">{{tranService.translate("account.label.cusType")}}</label>
                </span>
            </div>
            <!-- email -->
            <div class="col-3">
                <span class="p-float-label">
                    <input class="w-full"
                           pInputText id="email"
                           [(ngModel)]="searchInfo.email"
                           formControlName="email"
                    />
                    <label htmlFor="email">{{tranService.translate("account.label.email")}}</label>
                </span>
            </div>
            <!-- phone  -->
            <div class="col-3">
                <span class="p-float-label">
<!--                    <p-dropdown styleClass="w-full"-->
                    <!--                            [showClear]="true" [filter]="true" filterBy="display"-->
                    <!--                            id="provinceCode" [autoDisplayFirst]="false"-->
                    <!--                            [(ngModel)]="searchInfo.phone"-->
                    <!--                            formControlName="provinceCode"-->
                    <!--                            [options]="listProvince"-->
                    <!--                            optionLabel="display"-->
                    <!--                            optionValue="code"-->
                    <!--                            [emptyFilterMessage]="tranService.translate('global.text.nodata')"-->
                    <!--                    ></p-dropdown>-->
                    <input class="w-full"
                           pInputText id="phone"
                           [(ngModel)]="searchInfo.phone"
                           formControlName="phone"
                    />
                    <label htmlFor="provinceCode">{{tranService.translate("account.label.phone")}}</label>
                </span>
            </div>
            <!-- trang thai  -->
            <div class="col-3">
                <span class="p-float-label">
                     <p-dropdown styleClass="w-full"
                                 [showClear]="true" [filter]="true" filterBy="display"
                                 id="status" [autoDisplayFirst]="false"
                                 [(ngModel)]="searchInfo.status"
                                 formControlName="status"
                                 [options]="listStatus"
                                 optionLabel="name"
                                 optionValue="value"
                                 [emptyFilterMessage]="tranService.translate('account.label.status')"
                     ></p-dropdown>
                    <label htmlFor="status">{{tranService.translate("account.label.status")}}</label>
                </span>
            </div>
            <!-- tinh  -->
            <div class="col-3">
                <span class="p-float-label">
                     <p-dropdown styleClass="w-full"
                                 [showClear]="true" [filter]="true" filterBy="name"
                                 [autoDisplayFirst]="false"
                                 [(ngModel)]="searchInfo.provinceCodeAddress"
                                 formControlName="provinceCodeAddress"
                                 [options]="listProvinces"
                                 (onChange)="getListWards()"
                                 optionLabel="name"
                                 optionValue="code"
                                 [emptyFilterMessage]="tranService.translate('account.label.provinces')"
                     ></p-dropdown>
                    <label htmlFor="status">{{tranService.translate("account.label.provinces")}}</label>
                </span>
            </div>
            <!-- xa  -->
            <div class="col-3">
                <span class="p-float-label">
                     <p-dropdown styleClass="w-full"
                                 [showClear]="true" [filter]="true" filterBy="name"
                                 [autoDisplayFirst]="false"
                                 [(ngModel)]="searchInfo.wardCodeAddress"
                                 formControlName="status"
                                 [options]="listWards"
                                 optionLabel="name"
                                 optionValue="code"
                                 [emptyFilterMessage]="tranService.translate('account.label.wards')"
                     ></p-dropdown>
                    <label htmlFor="status">{{tranService.translate("account.label.wards")}}</label>
                </span>
            </div>


            <div class="col-3 pb-0">
                <p-button icon="pi pi-search"
                          styleClass="p-button-rounded p-button-secondary p-button-text button-search"
                          type="submit"
                ></p-button>
            </div>
        </div>
    </p-panel>
</form>

<div class="flex justify-content-center dialog-vnpt">
    <p-dialog [(visible)]="isShowModalDetail" [modal]="true" [style]="{ width: '980px' }"
              [draggable]="false" [resizable]="false" *ngIf="isShowModalDetail">
        <ng-template pTemplate="header">
            <div class="flex justify-content-between w-full pr-3 pt-4">
                <span class="p-dialog-title ng-tns-c4292766971-47 ng-star-inserted" id="pr_id_31-label">{{tranService.translate('global.button.view')}}</span>
                <button pripple="" class="p-ripple p-element p-button-info p-button p-component" ng-reflect-ng-class="[object Object]" (click)="onRoutingDevice(accountResponse.id,accountResponse.type)" type="button">
                    <span class="p-button-label ng-star-inserted" aria-hidden="">{{tranService.translate("global.button.viewDeviceOwner")}}</span>
                </button>
            </div>
        </ng-template>
<!--        <div class="flex justify-content-end">-->
<!--        <button pripple="" class="p-ripple p-element p-button-info p-button p-component" ng-reflect-ng-class="[object Object]" type="button">-->
<!--            <span class="p-button-label ng-star-inserted" aria-hidden="">Tạo mới</span>-->
<!--        </button>-->
<!--        </div>-->
        <p-tabView>
            <p-tabPanel header="{{tranService.translate('account.label.generalInfo')}}">
<!--                <div>{{accountResponse.type | json}}</div>-->
                <div class="flex flex-row justify-content-between" *ngIf="accountResponse.type === 2">
                    <div style="width: 49%;">
                        <!-- username -->
                        <div class="w-full field grid">
                            <label htmlFor="accountName" class="col-fixed" style="width:180px">{{tranService.translate(
                                "account.label.username")}}</label>
                            <div class="col">
                                {{accountResponse.username}}
                            </div>
                        </div>
                        <!-- businessName -->
                        <div class="w-full field grid">
                            <label htmlFor="businessName" class="col-fixed" style="width:180px;height: fit-content;">{{tranService.translate(
                                "account.label.businessName")}}</label>
                            <div class="col" style="width: calc(100% - 180px);overflow-wrap: break-word;">
                                {{accountResponse.name}}
                            </div>
                        </div>
                        <!-- taxCode -->
                        <div class="w-full field grid">
                            <label htmlFor="taxCode" class="col-fixed" style="width:180px">{{tranService.translate("account.label.taxCode")}}</label>
                            <div class="col">
                                {{accountResponse.taxCode}}
                            </div>
                        </div>
                        <!-- provincesHeadOffice -->
                        <div class="w-full field grid">
                            <label htmlFor="provincesHeadOffice" class="col-fixed" style="width:180px;height: fit-content;">{{tranService.translate(
                                "account.label.provincesHeadOffice")}}</label>
                            <div class="col" style="width: calc(100% - 180px);overflow-wrap: break-word;">
                                {{accountResponse.provinceOfficeName}}
                            </div>
                        </div>
                        <!-- wardsHeadOffice -->
                        <div class="w-full field grid">
                            <label htmlFor="wardsHeadOffice" class="col-fixed" style="width:180px;height: fit-content;">{{tranService.translate(
                                "account.label.wardsHeadOffice")}}</label>
                            <div class="col" style="width: calc(100% - 180px);overflow-wrap: break-word;">
                                {{accountResponse.wardOfficeName}}
                            </div>
                        </div>
                        <!-- addressHeadOffice -->
                        <div class="w-full field grid">
                            <label htmlFor="addressHeadOffice" class="col-fixed" style="width:180px;height: fit-content;">{{tranService.translate(
                                "account.label.addressHeadOffice")}}</label>
                            <div class="col" style="width: calc(100% - 180px);overflow-wrap: break-word;">
                                {{accountResponse.addressHeadOffice}}
                            </div>
                        </div>
                        <!-- description -->
                        <div class="w-full field grid">
                            <label htmlFor="description" class="col-fixed" style="width:180px">{{tranService.translate(
                                "account.label.description")}}</label>
                            <div class="col">
                                {{accountResponse.description}}
                            </div>
                        </div>
                    </div>
                    <div style="width: 49%;">
                        <!-- trạng thái -->
                        <div class="w-full field grid">
                            <label for="userType" class="col-fixed" style="width:180px">{{tranService.translate("account.label.status")}}</label>
                            <div class="col flex align-items-center gap-6">
                                <label *ngIf="accountResponse.status===1" class="bg-green-100 border-round inline-block p-2 text-center text-green-800 ng-star-inserted">{{tranService.translate("account.userstatus.active")}}</label>
                                <label *ngIf="accountResponse.status===0" class="bg-red-100 border-round inline-block p-2 text-center text-red-700 ng-star-inserted">{{tranService.translate("account.userstatus.lock")}}</label>
                            </div>
                        </div>
                        <!-- loai tai khoan -->
                        <div class="w-full field grid">
                            <label for="userType" class="col-fixed" style="width:180px">{{tranService.translate(
                                "account.label.userType")}}</label>
                            <div class="col">
                                <span>{{getStringUserType(accountResponse.type)}}</span>
                            </div>
                        </div>
                        <!-- role -->
                        <div class="w-full field grid">
                            <label htmlFor="role" class="col-fixed" style="width:180px">{{tranService.translate(
                                "account.label.role")}}</label>
                            <div class="col" style="max-width: calc(100% - 180px) !important;">
                                <div *ngFor="let item of accountResponse.roles">
                                    <div>{{ item.roleName}}</div>
                                </div>
                            </div>
                        </div>
                        <!-- representativeName-->
                        <div class="w-full field grid">
                            <label htmlFor="representativeName" class="col-fixed" style="width:180px">{{tranService.translate(
                                "account.label.representativeName")}}</label>
                            <div class="col">
                                <span>{{accountResponse.representativeName}}</span>
                            </div>
                        </div>
                        <!-- phone -->
                        <div class="w-full field grid align-items-start">
                            <label htmlFor="roles" class="col-fixed" style="width:180px">{{tranService.translate(
                                "account.label.phone")}}</label>
                            <div class="col" style="max-width: calc(100% - 180px) !important;">
                                <span>{{accountResponse.phone}}</span>
                            </div>
                        </div>
                        <!-- email -->
                        <div class="w-full field grid align-items-start">
                            <label htmlFor="roles" class="col-fixed" style="width:180px">{{tranService.translate(
                                "account.label.email")}}</label>
                            <div class="col" style="max-width: calc(100% - 180px) !important;">
                                <span>{{accountResponse.email}}</span>
                            </div>
                        </div>
                        <!-- provincesContact -->
                        <div class="w-full field grid align-items-start">
                            <label htmlFor="provincesContact" class="col-fixed" style="width:180px">{{tranService.translate(
                                "account.label.provincesContact")}}</label>
                            <div class="col" style="max-width: calc(100% - 180px) !important;">
                                <span>{{accountResponse.provinceAddressName}}</span>
                            </div>
                        </div>
                        <!-- wardsContact -->
                        <div class="w-full field grid align-items-start">
                            <label htmlFor="wardsContact" class="col-fixed" style="width:180px">{{tranService.translate(
                                "account.label.wardsContact")}}</label>
                            <div class="col" style="max-width: calc(100% - 180px) !important;">
                                <span>{{accountResponse.wardAddressName}}</span>
                            </div>
                        </div>
                        <!-- addressContact -->
                        <div class="w-full field grid align-items-start">
                            <label htmlFor="roles" class="col-fixed" style="width:180px">{{tranService.translate(
                                "account.label.addressContact")}}</label>
                            <div class="col" style="max-width: calc(100% - 180px) !important;">
                                <span>{{accountResponse.addressContact}}</span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="flex flex-row justify-content-between" *ngIf="accountResponse?.type === 3">
                    <div style="width: 49%;">
                        <!-- username -->
                        <div class="w-full field grid">
                            <label htmlFor="accountName" class="col-fixed" style="width:180px">{{tranService.translate(
                                "account.label.username")}}</label>
                            <div class="col">
                                {{accountResponse.username}}
                            </div>
                        </div>
                        <!-- customerName -->
                        <div class="w-full field grid">
                            <label htmlFor="customerName" class="col-fixed" style="width:180px;height: fit-content;">{{tranService.translate(
                                "account.label.customerName")}}</label>
                            <div class="col" style="width: calc(100% - 180px);overflow-wrap: break-word;">
                                {{accountResponse.name}}
                            </div>
                        </div>
                        <!-- phone -->
                        <div class="w-full field grid">
                            <label htmlFor="phone" class="col-fixed" style="width:180px">{{tranService.translate("account.label.phone")}}</label>
                            <div class="col">
                                {{accountResponse.phone}}
                            </div>
                        </div>
                        <!-- email -->
                        <div class="w-full field grid">
                            <label htmlFor="email" class="col-fixed" style="width:180px;height: fit-content;">{{tranService.translate(
                                "account.label.email")}}</label>
                            <div class="col" style="width: calc(100% - 180px);overflow-wrap: break-word;">
                                {{accountResponse.email}}
                            </div>
                        </div>
                        <!-- provincesContact -->
                        <div class="w-full field grid align-items-start">
                            <label htmlFor="provincesContact" class="col-fixed" style="width:180px">{{tranService.translate(
                                "account.label.provincesContact")}}</label>
                            <div class="col" style="max-width: calc(100% - 180px) !important;">
                                <span>{{accountResponse.provinceAddressName}}</span>
                            </div>
                        </div>
                        <!-- wardsContact -->
                        <div class="w-full field grid align-items-start">
                            <label htmlFor="wardsContact" class="col-fixed" style="width:180px">{{tranService.translate(
                                "account.label.wardsContact")}}</label>
                            <div class="col" style="max-width: calc(100% - 180px) !important;">
                                <span>{{accountResponse.wardAddressName}}</span>
                            </div>
                        </div>
                        <!-- addressContact -->
                        <div class="w-full field grid">
                            <label htmlFor="addressContact" class="col-fixed" style="width:180px">{{tranService.translate(
                                "account.label.addressContact")}}</label>
                            <div class="col">
                                {{accountResponse.addressContact}}
                            </div>
                        </div>
                    </div>
                    <div style="width: 49%;">
                        <!-- trạng thái -->
                        <div class="w-full field grid">
                            <label for="userType" class="col-fixed" style="width:180px">{{tranService.translate("account.label.status")}}</label>
                            <div class="col flex align-items-center gap-6">
                                <label *ngIf="accountResponse.status===1" class="bg-green-100 border-round inline-block p-2 text-center text-green-800 ng-star-inserted">{{tranService.translate("account.userstatus.active")}}</label>
                                <label *ngIf="accountResponse.status===0" class="bg-red-100 border-round inline-block p-2 text-center text-red-700 ng-star-inserted">{{tranService.translate("account.userstatus.lock")}}</label>
                            </div>
                        </div>
                        <!-- loai tai khoan -->
                        <div class="w-full field grid">
                            <label for="userType" class="col-fixed" style="width:180px">{{tranService.translate(
                                "account.label.userType")}}</label>
                            <div class="col">
                                <span>{{getStringUserType(accountResponse.type)}}</span>
                            </div>
                        </div>
                        <!-- role -->
                        <div class="w-full field grid">
                            <label htmlFor="role" class="col-fixed" style="width:180px">{{tranService.translate(
                                "account.label.role")}}</label>
                            <div class="col" style="max-width: calc(100% - 180px) !important;">
                                <div *ngFor="let item of accountResponse.roles">
                                    <div>{{ item.roleName}}</div>
                                </div>
                            </div>
                        </div>
                        <!-- accountRootId-->
                        <div class="w-full field grid">
                            <label htmlFor="accountRootId" class="col-fixed" style="width:180px">{{tranService.translate(
                                "account.label.accountRootId")}}</label>
                            <div class="col">
                                <span>{{accountResponse.manager?.name}}</span>
                            </div>
                        </div>
                        <!-- description -->
                        <div class="w-full field grid align-items-start">
                            <label htmlFor="roles" class="col-fixed" style="width:180px">{{tranService.translate(
                                "account.label.description")}}</label>
                            <div class="col" style="max-width: calc(100% - 180px) !important;">
                                <span>{{accountResponse.description}}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </p-tabPanel>

        </p-tabView>
    </p-dialog>
</div>
<table-vnpt
    [fieldId]="'id'"
    [(selectItems)]="selectItems"
    [columns]="columns"
    [dataSet]="dataSet"
    [options]="optionTable"
    [loadData]="search.bind(this)"
    [pageNumber]="pageNumber"
    [pageSize]="pageSize"
    [sort]="sort"
    [params]="searchInfo"
    [labelTable]="this.tranService.translate('global.menu.listaccount')"
></table-vnpt>

<div class="flex justify-content-center dialog-push-group">
    <p-dialog [header]="tranService.translate('account.text.titleChangeManageLevel')" [(visible)]="isShowDialogChangeManageLevel" [modal]="true"
              [style]="{ width: '500px' }" [draggable]="false" [resizable]="false">
        <div class="w-full field grid">
            <label htmlFor="account" class="col-fixed" style="width:100px">{{tranService.translate("account.text.account")}}</label>
            <div class="col">
                <p-autoComplete styleClass="w-full"
                                id="account"
                                field="name"
                                [(ngModel)]="accountSelected"
                                [suggestions]="listAccounts"
                                (completeMethod)="filterListAccount($event)"
                                [dropdown]="true"
                                [placeholder]="tranService.translate('account.text.selectAccount')"
                ></p-autoComplete>
            </div>
        </div>
        <div class="flex flex-row justify-content-center align-items-center">
            <p-button styleClass="mr-2 p-button-secondary" [label]="tranService.translate('global.button.cancel')"
                      (click)="isShowDialogChangeManageLevel = false"></p-button>
            <p-button styleClass="p-button-info" [label]="tranService.translate('global.button.save')" (click)="changeManageLevel()"
                      [disabled]="accountSelected == null || accountSelected == undefined"></p-button>
        </div>
    </p-dialog>
</div>

<div class="flex justify-content-center dialog-vnpt" *ngIf="formChangeManageData">
    <p-dialog [header]="tranService.translate('global.text.changeManageData')" [(visible)]="isShowDialogChangeManageData" [modal]="true"
              [style]="{ width: '500px' }" [draggable]="false" [resizable]="false">
        <form [formGroup]="formChangeManageData" (ngSubmit)="changeManageData()">
            <!-- giao dich vien -->
            <div class="w-full field grid">
                <label htmlFor="customer" class="col-fixed" style="width:140px">{{tranService.translate("account.usertype.business")}}<span
                    class="text-red-500">*</span></label>
                <div class="col" style="width: calc(100% - 140px)">
                    <vnpt-select
                        [control]="searchUserTellerController"
                        [(value)]="changeManageDataInfo.userId"
                        [required]="true"
                        [isAutoComplete]="false"
                        [isMultiChoice]="false"
                        objectKey="account"
                        paramKey="fullName"
                        [paramDefault]="paramSearchTeller"
                        keyReturn="id"
                        displayPattern="${fullName} - ${email}"
                        [lazyLoad]="true"
                        [listExclude]="listTellerExcludes"
                    ></vnpt-select>
                </div>
            </div>
            <!-- error giao dich vien -->
            <div class="w-full field grid text-error-field">
                <label htmlFor="province" class="col-fixed" style="width:140px"></label>
                <div class="col" style="width: calc(100% - 140px)">
                    <small class="text-red-500"
                           *ngIf="searchUserTellerController.dirty && searchUserTellerController.error.required">{{tranService.translate(
                        "global.message.required")}}</small>
                </div>
            </div>
            <div class="w-full field grid">
                <div class="col">
                    <p-radioButton
                        name="typeSelect"
                        [value]="0"
                        [(ngModel)]="changeManageDataInfo.typeSelect"
                        formControlName="typeSelect"
                        inputId="typeSelectAll"/>
                    <label for="typeSelectAll" class="ml-2">
                        {{tranService.translate("account.text.typeSelectAll")}}
                    </label>
                </div>
                <div class="col">
                    <p-radioButton
                        name="typeSelect"
                        [value]="1"
                        [(ngModel)]="changeManageDataInfo.typeSelect"
                        formControlName="typeSelect"
                        inputId="typeSelectList"/>
                    <label for="typeSelectList" class="ml-2">
                        {{tranService.translate("account.text.typeSelectList")}}
                    </label>
                </div>
            </div>
            <!-- khach hang -->
            <div class="w-full field grid" [class]="changeManageDataInfo.typeSelect == 1 ? '' : 'hidden'">
                <label htmlFor="customer" class="col-fixed" style="width:140px">{{tranService.translate("account.usertype.individual")}}<span
                    class="text-red-500">*</span></label>
                <div class="col" style="width: calc(100% - 140px)">
                    <vnpt-select
                        [control]="searchUserCustomerController"
                        [(value)]="changeManageDataInfo.accountCustomerIds"
                        [required]="true"
                        [isAutoComplete]="false"
                        [isMultiChoice]="true"
                        [loadData]="loadListUserCustomerOfTeller.bind(this)"
                        paramKey="fullName"
                        keyReturn="id"
                        displayPattern="${fullName} - ${email}"
                        [lazyLoad]="true"
                    ></vnpt-select>
                </div>
            </div>
            <!-- error khach hang -->
            <div class="w-full field grid text-error-field" *ngIf="changeManageDataInfo.typeSelect == 1">
                <label htmlFor="province" class="col-fixed" style="width:140px"></label>
                <div class="col" style="width: calc(100% - 140px)">
                    <small class="text-red-500"
                           *ngIf="searchUserCustomerController.dirty && searchUserCustomerController.error.required">{{tranService.translate(
                        "global.message.required")}}</small>
                </div>
            </div>
            <div class="flex flex-row justify-content-center align-items-center">
                <p-button styleClass="mr-2 p-button-secondary p-button-outlined" [label]="tranService.translate('global.button.cancel')"
                          (click)="isShowDialogChangeManageData = false"></p-button>
                <p-button styleClass="p-button-info" [label]="tranService.translate('global.button.save')" type="submit"
                          [disabled]="formChangeManageData.invalid || (searchUserTellerController.invalid === true) || (changeManageDataInfo.typeSelect == 1 && (searchUserCustomerController.invalid === true))"></p-button>
            </div>
        </form>
    </p-dialog>
</div>
