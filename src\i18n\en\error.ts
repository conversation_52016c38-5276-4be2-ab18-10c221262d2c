export default {
    emailIncorect: "Login information is incorrect. Please try again.",
    passwordIncorect: "Login information is incorrect. Please try again.",
    accountInactive: "Login information is incorrect. Please try again.",
    length: "less than ${0} and great than ${1}",
    object: {
        not: {
            found: "Not found field ${0} with value ${1}"
        }
    },
    field: {
        must: {
            be: {
                not: {
                    null: "${0} must be not null"
                }
            }
        }
    },
    duplicate: {
        name: "duplicate name"
    },
    invalid: {
        field: "field ${0} invalid",
        email: "Email does not exist!",
        phone: {
            number: "invalid phone number"
        },
        password: "Wrong current password",
        type: "invalid type",
        isdn: {
            empty: "Subcriber is required",
            format: "Subcriber is invalid format",
            not: {
                permission: "Subcriber is not allowed",
                inact: "Subscriber must have Inactivated or Deactivated status",
                exist: "Subscriber is not existed"
            },
            duplicated: "Subscriber is duplicated",
            status: {
                purge: "Sim đã hủy",
            }
        },
        msisdn: {
            not: {
                active: "Subscriber must have Active status"
            },
            register: {
                rate: {
                    pending: "Subscriber must have Active status"
                }
            }
        },
        rating: {
            empty: "Plan is required",
            format: "Plan is invalid format",
            not: {
                permission: "Plan is not allowed",
                inact: "Plan must have Inactivated or Deactivated status",
                exist: "Plan is not existed"
            },
            customer:{
                not: {
                    permission: "The package does not allow group and file registration. Please register individually!",
                }
            }
        },
        file: {
            form: {
                format: "File is invalid format",
                extension: "File is not excel",
                maxrow: "File has over 1000 rows"
            }
        }
    },
    forbbiden: {
        resource: "forbbiden resource",
    },
    data: {
        format: "Invalid data format"
    },
    user: {
        not: {
            found: "${0} user not found"
        }
    },
    valid: {
        assert: {
            false: "Must be false",
            true: "Must be true"
        },
        decimal: {
            max: "Must be less than ${0}",
            min: "Must be greater than ${0}"
        },
        digits: "Numeric value out of bounds (<${0} digits>.<${1} digits> expected)",
        email: "Must be a well-formed email address",
        max: "Must be less than or equal to ${0}",
        min: "Must be greater than or equal to ${0}",
        negative: {
            "": "Must be less than 0",
            or: {
                zero: "Must be less than or equal to 0"
            }
        },
        not: {
            blank: "Must not be blank",
            empty: "Must not be empty",
            null: "Field cannot NULL."
        },
        null: "Must be null",
        range: "Must be between ${0} and ${1}",
        past: {
            "": "Must be a past date",
            or: {
                present: "Must be a date in the past or in the present"
            }
        },
        pattern: 'Must match "${0}"',
        phone: {
            pattern: "Invalid phone number"
        },
        positive: {
            "": "Must be greater than 0",
            or: {
                zero: "Must be greater than or equal to 0"
            }
        },
        size: "Size must be between ${0} and ${1}",
        length: "Size must be between ${0} and ${1}"
    },
    id: {
        must: {
            be: {
                null: "Id must be null"
            }
        }
    },
    passwords: {
        do: {
            not: {
                match: "Passwords do not match"
            }
        }
    },
    the: {
        new: {
            password: {
                must: {
                    be: {
                        different: {
                            from: {
                                the: {
                                    old: {
                                        one: "the new password must be different from the old one"
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    },
    exists: "${0} exists",
    bad: {
        request: "Bad request"
    },
    role: {
        not: {
            working: "You do not have permission to access the system. Please contact the administrator for support"
        }
    },
    report: {
        query: "query error",
        limit: {
            row: "report limit row error"
        }
    },
    status: {
        400: "Bad request",
        401: "Unauthorized",
        403: "Forbidden",
        404: "Not found",
        406: "Not Acceptable",
        408: "Request Timeout",
        409: "Conflict",
        500: "Internal Server Error"
    },
    register: {
        rate: {
            groupsim: {
                empty: "Group sim is empty"
            },
            in: {
                other: {
                    process: "Subscriber must have Active status"
                }
            }
        }
    }
}
