import { Component, Injector, Input, OnInit } from "@angular/core";
import { FormBuilder } from "@angular/forms";
import { ComponentBase } from "src/app/component.base";
import { CONSTANTS } from "src/app/service/comon/constants";
import { ReportService } from "src/app/service/report/ReportService";
export interface CrontabInfo{
    id: number|null;
    query: string|null;
    schema: string|null;
    timeOnce: Date | null;
    schedule: string | null,
    scheduleDesc: string | null,
    startTime?: number | null;
    endTime?: number | null;
    cycle?: number|null;
    dayInMonth?: Array<number>|null;
    dayInWeek?: Array<number | string>|null;
    month?: Array<number>|null;
    typeSchedule?: 0 | 1;
    allDayInMonth?: number;
    allDayInWeek?: number;
    allMonth?: number;
}
export class TabCrontabDynamicReportControl{
    reload: Function;
}
@Component({
    selector: "tab-report-dynamic-crontab",
    templateUrl: "./tab.report.dynamic.crontab.html"
})
export class TabReportDynamicCrontab extends ComponentBase implements OnInit{
    @Input() crontabInfo!: CrontabInfo;
    @Input() control!: TabCrontabDynamicReportControl;
    @Input() cancel!: Function;
    @Input() modeView!: number;

    constructor(injector: Injector,
        private formBuilder: FormBuilder,
        private reportService: ReportService) {
        super(injector);
    }

    formCrontabInfo: any;
    schemas: Array<any>;
    cycles: Array<any>;
    fullDayInMonth = [1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31];
    fullDayInWeek = [
        {value: "MON", name: this.tranService.translate("report.label.monday")},
        {value: "TUE", name: this.tranService.translate("report.label.tuesday")},
        {value: "WED", name: this.tranService.translate("report.label.wednesday")},
        {value: "THU", name: this.tranService.translate("report.label.thursday")},
        {value: "FRI", name: this.tranService.translate("report.label.friday")},
        {value: "SAT", name: this.tranService.translate("report.label.saturday")},
        {value: "SUN", name: this.tranService.translate("report.label.sunday")},
    ];
    fullMonth = [
        {value: 1, name: this.tranService.translate("report.label.january")},
        {value: 2, name: this.tranService.translate("report.label.february")},
        {value: 3, name: this.tranService.translate("report.label.march")},
        {value: 4, name: this.tranService.translate("report.label.april")},
        {value: 5, name: this.tranService.translate("report.label.may")},
        {value: 6, name: this.tranService.translate("report.label.june")},
        {value: 7, name: this.tranService.translate("report.label.july")},
        {value: 8, name: this.tranService.translate("report.label.august")},
        {value: 9, name: this.tranService.translate("report.label.september")},
        {value: 10, name: this.tranService.translate("report.label.october")},
        {value: 11, name: this.tranService.translate("report.label.november")},
        {value: 12, name: this.tranService.translate("report.label.december")},
    ];

    objectMode = CONSTANTS.MODE_VIEW;

    ngOnInit(): void {
        this.control.reload = this.onload.bind(this);
        this.schemas = [
            // {value: CONSTANTS.SCHEMA.BILL, name: this.tranService.translate("report.schema.bill")},
            {value: CONSTANTS.SCHEMA.CORE, name: this.tranService.translate("report.schema.core")},
            // {value: CONSTANTS.SCHEMA.LOG, name: this.tranService.translate("report.schema.log")},
            // {value: CONSTANTS.SCHEMA.MONITOR, name: this.tranService.translate("report.schema.monitor")},
            // {value: CONSTANTS.SCHEMA.REPORT, name: this.tranService.translate("report.schema.report")},
            // {value: CONSTANTS.SCHEMA.RULE, name: this.tranService.translate("report.schema.rule")},
            // {value: CONSTANTS.SCHEMA.SIM, name: this.tranService.translate("report.schema.sim")},
        ];
        this.cycles = [
            {value: 1, name: 1},
            {value: 2, name: 2},
            {value: 3, name: 3},
            {value: 4, name: 4},
            {value: 6, name: 6},
            {value: 8, name: 8},
            {value: 12, name: 12},
        ]
    }

    onload(){
        let me = this;
        this.formCrontabInfo = undefined;
        setTimeout(function(){
            me.crontabInfo.cycle = null;
            me.crontabInfo.startTime = null;
            me.crontabInfo.endTime = null;
            me.crontabInfo.dayInMonth = null;
            me.crontabInfo.allDayInWeek = null;
            me.crontabInfo.month = null;
            if(me.crontabInfo.timeOnce){
                me.crontabInfo.timeOnce = new Date(me.crontabInfo.timeOnce);
            }else{
                me.crontabInfo.timeOnce = new Date();
            }

            if(me.crontabInfo.schedule == null){
                me.crontabInfo.typeSchedule = 0;
            }else{
                me.crontabInfo.typeSchedule = 1;
                if(me.crontabInfo.scheduleDesc != null){
                    let scheduleInfo = JSON.parse(me.crontabInfo.scheduleDesc);
                    me.crontabInfo.cycle = scheduleInfo.cycleTime;
                    me.crontabInfo.startTime = scheduleInfo.start;
                    me.crontabInfo.endTime = scheduleInfo.end;
                    me.crontabInfo.dayInMonth = scheduleInfo.dayInMonth;
                    me.crontabInfo.dayInWeek = scheduleInfo.dayInWeek;
                    me.crontabInfo.month = scheduleInfo.month;
                }else{
                    me.crontabInfo.cycle = null;
                    me.crontabInfo.startTime = null;
                    me.crontabInfo.endTime = null;
                    me.crontabInfo.dayInMonth = null;
                    me.crontabInfo.dayInWeek = null;
                    me.crontabInfo.month = null;
                }
            }
            if((me.crontabInfo.dayInMonth || []).length == 31){
                me.crontabInfo.allDayInMonth = 1;
            }else{
                me.crontabInfo.allDayInMonth = 0;
            }
            if((me.crontabInfo.dayInWeek || []).length == 7){
                me.crontabInfo.allDayInWeek = 1;
            }else{
                me.crontabInfo.allDayInWeek = 0;
            }
            if((me.crontabInfo.month || []).length == 12){
                me.crontabInfo.allMonth = 1;
            }else {
                me.crontabInfo.allMonth = 0;
            }
            let dayInMonth = me.crontabInfo.dayInMonth;
            let month = me.crontabInfo.month;
            let dayInWeek = me.crontabInfo.dayInWeek;
            me.crontabInfo.dayInMonth = [];
            me.crontabInfo.dayInWeek = [];
            me.crontabInfo.month = [];
            me.formCrontabInfo = me.formBuilder.group(me.crontabInfo);
            me.crontabInfo.dayInMonth = dayInMonth;
            me.crontabInfo.dayInWeek = dayInWeek;
            me.crontabInfo.month = month;
            if(me.modeView == CONSTANTS.MODE_VIEW.DETAIL){
                Object.keys(me.crontabInfo).forEach(key => {
                    me.formCrontabInfo.get(key).disable();
                })
            }
        })
    }

    getMinEndTime(){
        if(this.crontabInfo.startTime != null){
            return this.crontabInfo.startTime + 1 <= 23 ? this.crontabInfo.startTime : 23;
        }else{
            return 1;
        }
    }

    getMaxStartTime(){
        if(this.crontabInfo.endTime != null){
            return this.crontabInfo.endTime - 1 >= 0 ? this.crontabInfo.endTime : 0;
        }else{
            return 22;
        }
    }


    toggleSelect(type){
        if(type == 0){
            if(this.crontabInfo.allDayInMonth == 1){
                this.crontabInfo.dayInMonth = [...this.fullDayInMonth];
            }else{
                this.crontabInfo.dayInMonth = [];
            }
        }else if(type == 1){
            if(this.crontabInfo.allDayInWeek == 1){
                this.crontabInfo.dayInWeek = this.fullDayInWeek.map(el => el.value);
            }else{
                this.crontabInfo.dayInWeek = [];
            }
        }else if(type ==2){
            if(this.crontabInfo.allMonth == 1){
                this.crontabInfo.month = this.fullMonth.map(el => el.value);
            }else{
                this.crontabInfo.month = [];
            }
        }
    }

    getCronJob():string{
        let schedule = "0 0";
        if(this.crontabInfo.startTime != null){
            schedule += ` ${this.crontabInfo.startTime}-${this.crontabInfo.endTime}`;
        }else{
            schedule += " *";
        }
        if(this.crontabInfo.cycle != null){
            schedule += "/"+this.crontabInfo.cycle;
        }
        if(this.crontabInfo.dayInMonth != null && this.crontabInfo.dayInMonth.length > 0 && this.crontabInfo.dayInMonth.length < this.fullDayInMonth.length){
            schedule += " " + this.crontabInfo.dayInMonth.toLocaleString();
        }else{
            schedule += " *";
        }
        if(this.crontabInfo.month != null && this.crontabInfo.month.length > 0 && this.crontabInfo.month.length < this.fullMonth.length){
            schedule += " " + this.crontabInfo.month.toLocaleString();
        }else{
            schedule += " *";
        }
        if(this.crontabInfo.dayInWeek != null && this.crontabInfo.dayInWeek.length > 0 && this.crontabInfo.dayInWeek.length < this.fullDayInWeek.length){
            schedule += " " + this.crontabInfo.dayInWeek.toLocaleString();
        }else{
            schedule += " *";
        }
        return schedule;
    }

    onSubmit(){
        let me = this;
        let scheduleInfo = {
            cycleTime: this.crontabInfo.cycle,
            start: this.crontabInfo.startTime,
            end: this.crontabInfo.endTime,
            dayInMonth: this.crontabInfo.dayInMonth,
            dayInWeek: this.crontabInfo.dayInWeek,
            month: this.crontabInfo.month
        }
        let data = {
            id: this.crontabInfo.id,
            query: this.crontabInfo.query,
            schema: this.crontabInfo.schema,
            timeOnce: this.crontabInfo.typeSchedule == 0 ? this.crontabInfo.timeOnce : null,
            scheduleDesc: JSON.stringify(scheduleInfo),
            schedule: this.crontabInfo.typeSchedule == 1 ? this.getCronJob() : null,
        }
        this.reportService.updateSummaryReportDynamic(this.crontabInfo.id, data, (response)=>{
            me.messageCommonService.success(me.tranService.translate("global.message.saveSuccess"));
        })
    }

    copyText(event){
        let me = this;
        let value = document.querySelector("textarea#query");
        if(value){
            let text = value["value"];
            me.utilService.copyToClipboard(text,() => {
                me.messageCommonService.success(me.tranService.translate("global.message.copied"));
            });
        }else{
            this.messageCommonService.warning(this.tranService.translate("global.message.error"));
        }
    }
}
