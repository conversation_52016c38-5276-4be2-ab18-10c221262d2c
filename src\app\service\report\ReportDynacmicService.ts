import {Inject, Injectable} from "@angular/core";
import {HttpService} from "../comon/http.service";

@Injectable({
    providedIn: 'root'
})
export class ReportDynacmicService {
    private prefixApi: string;

    constructor(@Inject(HttpService) private httpService: HttpService) {
        this.prefixApi = "/report";
    }

    public search(params: {
        [key: string]: any
    }, callback?: Function, errorCallBack?: Function, finallyCallback?: Function) {
        this.httpService.get(`${this.prefixApi}/search`, {}, params, callback, errorCallBack, finallyCallback);
    }
    public deleleReport(id: number, callback?:Function, errorCallBack?:Function, finallyCallback?:Function){
        this.httpService.delete(`${this.prefixApi}/${id}`, {}, {}, callback, errorCallBack, finallyCallback);
    }
    public getById(id: number, callback?: Function, errorCallback?:Function, finallyCallback?: Function){
        this.httpService.get(`${this.prefixApi}/${id}`,{}, {}, callback, errorCallback, finallyCallback);
    }
    public createReport(body,callback?: Function, errorCallback?:Function, finallyCallback?: Function){
        this.httpService.post(`${this.prefixApi}/create`, {},body,{}, callback, errorCallback, finallyCallback);
    }
    public updateReport(id,body,callback?: Function, errorCallback?:Function, finallyCallback?: Function){
        this.httpService.put(`${this.prefixApi}/update/${id}`, {},body,{}, callback, errorCallback, finallyCallback);
    }
    public changeStatus(id: number, params: { [key: string]: any }, callback?:Function, errorCallBack?:Function, finallyCallback?:Function){
        this.httpService.put(`${this.prefixApi}/changeStatus/${id}`,{},{},params,callback, errorCallBack, finallyCallback);
    }
    public getAllReportContent(callback){
        this.httpService.get(`${this.prefixApi}/all/permission`, {}, {}, callback);
    }
}
