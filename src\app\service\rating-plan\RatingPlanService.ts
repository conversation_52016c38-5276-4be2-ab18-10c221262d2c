import { Inject, Injectable } from "@angular/core";
import { HttpService } from "../comon/http.service";
import {f} from "@fullcalendar/core/internal-common";

@Injectable()
export class RatingPlanService {
    private prefixApi: string;
    constructor(@Inject(HttpService) private httpService:HttpService) {
        this.prefixApi = "/rating-plan";
    }

    public getAllSumaryRatingPlan(callback: Function, errorCallBack?: Function, finallyCallback?: Function){
        this.httpService.get(`${this.prefixApi}/get-list-plan`,{},{},callback,errorCallBack,finallyCallback);
    }

    public getAllRatingPlanPushForUser(callback: Function, errorCallBack?: Function, finallyCallback?: Function){
        this.httpService.get(`${this.prefixApi}/get-list-rating-plan`,{},{},callback,errorCallBack,finallyCallback);
    }

    public getListRatingPlanByCustomerCode(params: any, callback: Function, errorCallBack?: Function, finallyCallback?: Function){
        this.httpService.get(`${this.prefixApi}/get-list-rating-plan-for-customercode`,{},params,callback,errorCallBack,finallyCallback);
    }

    public demo(callback: Function){
        this.httpService.get("/assets/data/rating-plan.json",{}, {}, callback);
    }

    public search(params: any, callback: Function, errorCallBack?:Function, finallyCallback?:Function){
        this.httpService.get(`${this.prefixApi}/search`, {}, params,callback, errorCallBack, finallyCallback);
    }

    public getById(id:number, callback?:Function, errorCallBack?:Function, finallyCallback?:Function){
        this.httpService.get(`${this.prefixApi}/${id}`,{}, {},callback, errorCallBack, finallyCallback);
    }

    public deleteById(id:number, callback?:Function, errorCallBack?:Function, finallyCallback?:Function){
        this.httpService.delete(`${this.prefixApi}/${id}`,{}, {},callback, errorCallBack, finallyCallback);
    }

    public editRatingPlan(id:number, ratingPlan:{}, callback?:Function, errorCallBack?:Function, finallyCallback?:Function){
        this.httpService.put(`${this.prefixApi}/${id}`,{}, ratingPlan,{}, callback, errorCallBack, finallyCallback);
    }
    public searchUser(params: any, callback: Function){
        this.httpService.get(`/user-mgmt/search`, {}, params,callback);
    }
    public getListProvince(callback?: Function, errorCallback?:Function, finallyCallback?: Function){
        this.httpService.get(`/user-mgmt/get-province`, {}, {}, callback, errorCallback, finallyCallback);
    }

    public assignPlan(id:number, user: Array<any> ,callback?: Function, errorCallback?:Function, finallyCallback?: Function){
        this.httpService.post(`${this.prefixApi}/assign/${id}`, {}, user, callback, errorCallback, finallyCallback);
    }
    public activePlan(id: number, callback: Function){
        this.httpService.put(`${this.prefixApi}/active/${id}`, {},callback);
    }
    public suspendPlan(id: number, callback: Function){
        this.httpService.put(`${this.prefixApi}/pending/${id}`, {},callback);
    }

    public registerPlanForSim(dataRequest, callback, errorCallback?:Function, finallyCallback?: Function){
        this.httpService.post(`${this.prefixApi}/change-rate`, {}, dataRequest,{}, callback, errorCallback, finallyCallback);
    }

    public registerPlanForGroupSim(dataRequest, callback:Function, errorCallback?:Function, finallyCallback?: Function){
        this.httpService.post(`${this.prefixApi}/register-rate-group-sim`, {}, dataRequest,{}, callback, errorCallback, finallyCallback);
    }

    public uploadRegisterByFile(objectFile, callback:Function){
        this.httpService.uploadFile(`${this.prefixApi}/register-by-file`, objectFile,{timeout:300000}, {}, callback);
    }

    public uploadRegisterByList(data, callback:Function, errorCallback?:Function, finallyCallback?: Function){
        this.httpService.post(`${this.prefixApi}/register-by-list`, {timeout:300000},data,{}, callback,errorCallback, finallyCallback);
    }

    public downloadTemplate(){
        this.httpService.download(`${this.prefixApi}/download`,{},{});
    }

    public createRatingPlan(body:{}, callback:Function, errorCallback?:Function, finallyCallback?: Function){
        this.httpService.post(this.prefixApi,{},body,{},callback, errorCallback, finallyCallback);
    }

    public checkingPlanCodeExisted(params: { [key: string]: any;},callback:Function){
        this.httpService.get(this.prefixApi+"/checkCode",{}, params, callback);
    }

    public checkingPlanNameExisted(params: { [key: string]: any;},callback:Function){
        this.httpService.get(this.prefixApi+"/checkName",{}, params, callback);
    }

    public searchHistory(params: any, callback: Function,errorCallback?:Function, finallyCallback?: Function){
        this.httpService.get(`${this.prefixApi}/history/search`, {}, params,callback, errorCallback, finallyCallback);
    }

    public getAllAccountPlanAssign(idPlan, callback){
        this.httpService.get(`${this.prefixApi}/get-list-user-assigned`, {} ,{ratingPlanId: idPlan}, callback);
    }

    public getUserDropDown(params, callback?: Function, errorCallback?: Function, finalCB?:Function){
        this.httpService.get("/user-mgmt/searchUserForRatingPlan",{},params, callback,errorCallback,finalCB)
    }

    public cancelPlanForSubcriber(msisdn, ratingPlanId, callback){
        this.httpService.post(`${this.prefixApi}/cancel-rate`, {}, {msisdn, ratingPlanId}, {}, callback);
    }

    public getByAccountId(id: number, callback?: Function, errorCallback?:Function, finallyCallback?: Function){
        this.httpService.get(`/user-mgmt/${id}`,{timeout: 1800000}, {}, callback, errorCallback, finallyCallback);
    }

    public getByKey(key: string, value: string, callback?: Function, errorCallback?:Function, finallyCallback?: Function){
        this.httpService.get(`/user-mgmt/getByKey`,{}, {key, value}, callback, errorCallback, finallyCallback);
    }

    public getUserToAddAccount(params, callback?: Function, errorCallback?: Function, finalCB?:Function){
        this.httpService.get("/user-mgmt/searchUserForRatingPlan",{},params, callback,errorCallback,finalCB)
    }
    // public getByKey(key: string, value: string, callback?: Function, errorCallback?:Function, finallyCallback?: Function){
    //     this.httpService.get(`${this.prefixApi}/getByKey`,{}, {key, value}, callback, errorCallback, finallyCallback);
    // }
}
