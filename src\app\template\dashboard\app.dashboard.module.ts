import { CommonModule } from "@angular/common";
import { NgModule } from "@angular/core";
import { DashboardRoutingModule } from "./app.dashboard.routing.module";
import { BreadcrumbModule } from "primeng/breadcrumb";
import { FieldsetModule } from "primeng/fieldset";
import { InputTextModule } from "primeng/inputtext";
import { ButtonModule } from "primeng/button";
import { CommonVnptModule } from "../common-module/common.module";
import { CalendarModule } from "primeng/calendar";
import {DropdownModule} from "primeng/dropdown";
import {AutoCompleteModule} from "primeng/autocomplete";
import {SplitButtonModule} from "primeng/splitbutton";
import {DialogModule} from "primeng/dialog";
import {CardModule} from "primeng/card";
import {PanelModule} from "primeng/panel";
import {CheckboxModule} from "primeng/checkbox";
import { ToolbarModule } from "primeng/toolbar";
import { MultiSelectModule } from "primeng/multiselect";
import { AppDashboardComponent } from "./app.dashboard.component";
import { ConfigChartService } from "src/app/service/charts/ConfigChartService";
import { DragDropModule } from 'primeng/dragdrop';
import { ChartModule } from "primeng/chart";
import { FormsModule, ReactiveFormsModule } from "@angular/forms";
import { InputNumberModule } from "primeng/inputnumber";
import { AppDashboard2Component } from "./app.dashboard2.component";
import {SliderModule} from "primeng/slider";

@NgModule({
    imports: [
        CommonModule,
        DashboardRoutingModule,
        BreadcrumbModule,
        FieldsetModule,
        FormsModule,
        ReactiveFormsModule,
        InputTextModule,
        ButtonModule,
        CommonVnptModule,
        CalendarModule,
        DropdownModule,
        AutoCompleteModule,
        SplitButtonModule,
        DialogModule,
        CardModule,
        PanelModule,
        CheckboxModule,
        ToolbarModule,
        MultiSelectModule,
        ChartModule,
        DragDropModule,
        InputNumberModule,
        SliderModule
    ],
    declarations: [
        AppDashboardComponent,
        AppDashboard2Component
    ],
    providers: [
        ConfigChartService
    ]
})
export class AppDashboardModule{}
