import { NgModule } from "@angular/core";
import { PermissionListComponent } from "./app.permisstion.list.component";
import { CommonModule } from "@angular/common";
import { PermissionRoutingModule } from "./app.permission.routing";
import { PermissionService } from "src/app/service/permission/permission.service";
import { BreadcrumbModule } from "primeng/breadcrumb";
import { FieldsetModule } from "primeng/fieldset";
import { FormsModule, ReactiveFormsModule } from "@angular/forms";
import { InputTextModule } from "primeng/inputtext";
import { ButtonModule } from "primeng/button";
import { CommonVnptModule } from "../common-module/common.module";
import { SplitButtonModule } from "primeng/splitbutton";
import { AutoCompleteModule } from "primeng/autocomplete";
import { CalendarModule } from "primeng/calendar";
import { DropdownModule } from "primeng/dropdown";
import { CardModule } from "primeng/card";
import { DialogModule } from "primeng/dialog";
import { InputTextareaModule } from 'primeng/inputtextarea';
import { MultiSelectModule } from 'primeng/multiselect';
import { PanelModule } from 'primeng/panel';
@NgModule({
    imports:[
        CommonModule,
        PermissionRoutingModule,
        BreadcrumbModule,
        FieldsetModule,
        FormsModule,
        ReactiveFormsModule,
        InputTextModule,
        ButtonModule,
        CommonVnptModule,
        SplitButtonModule,
        AutoCompleteModule,
        CalendarModule,
        DropdownModule,
        CardModule,
        DialogModule,
        InputTextareaModule,
        MultiSelectModule,
        PanelModule
    ],
    declarations: [
        PermissionListComponent
    ],
    providers:[
        PermissionService
    ]
})
export class PermissionListModule {}