<div class="vnpt flex flex-row justify-content-between align-items-center bg-white p-2 border-round">
    <div class="">
        <div class="text-xl font-bold mb-1">{{tranService.translate("global.menu.dynamicreport")}}</div>
        <p-breadcrumb class="max-w-full col-7" [model]="items" [home]="home"></p-breadcrumb>
    </div>
    <div class="col-5 flex flex-row justify-content-end align-items-center">
        <p-button [label]="tranService.translate('global.button.add')" (click)="openCreateReport()"
                  styleClass="p-button-info mr-2"
                  *ngIf="checkAuthen([CONSTANTS.PERMISSIONS.REPORT_DYNAMIC.CREATE])"></p-button>
    </div>
</div>
<form [formGroup]="formSearch" (ngSubmit)="onSubmitSearch()" class="pb-2 pt-3 vnpt-field-set">
    <p-panel [toggleable]="true" [header]="tranService.translate('global.text.filter')">
        <div class="flex flex-row">
            <div class="grid col-11 p-0">
                <!-- reportName -->
                <div class="col-3">
                <span class="p-float-label">
                    <input pInputText
                           class="w-full"
                           pInputText id="name"
                           [(ngModel)]="searchInfo.name"
                           formControlName="name"
                    />
                    <label htmlFor="name">{{tranService.translate("report.label.reportName")}}</label>
                </span>
                </div>
                <!-- status -->
                <div class="col-3">
                <span class="p-float-label">
                    <p-dropdown styleClass="w-full" [showClear]="true"
                                id="status" [autoDisplayFirst]="false"
                                formControlName="status"
                                [(ngModel)]="searchInfo.status"
                                [options]="reportStatus"
                                optionLabel="name"
                                optionValue="value"
                    ></p-dropdown>
                    <label for="status">{{tranService.translate("sim.label.trangthaisim")}}</label>
                </span>
                </div>
                <!--            fromDate-->
                <div class="col-3">
                <span class="p-float-label">
                    <p-calendar styleClass="w-full"
                                id="fromDate"
                                formControlName="fromDate"
                                [(ngModel)]="searchInfo.fromDate"
                                [showIcon]="true"
                                [showClear]="true"
                                dateFormat="dd/mm/yy"
                                [maxDate]="maxDateFrom"
                                (onSelect)="onChangeDateFrom(searchInfo.fromDate)"
                                (onInput)="onChangeDateFrom(searchInfo.fromDate)"
                    ></p-calendar>
                    <label htmlFor="fromDate">{{tranService.translate("report.label.fromDate")}}</label>
                </span>
                </div>
                <!--            dateTo-->
                <div class="col-3">
                <span class="p-float-label">
                    <p-calendar styleClass="w-full"
                                id="toDate"
                                formControlName="toDate"
                                [(ngModel)]="searchInfo.toDate"
                                [showIcon]="true"
                                [showClear]="true"
                                dateFormat="dd/mm/yy"
                                [minDate]="minDateTo"
                                [maxDate]="maxDateTo"
                                (onSelect)="onChangeDateTo(searchInfo.toDate)"
                                (onInput)="onChangeDateTo(searchInfo.toDate)"
                    />
                    <label htmlFor="toDate">{{tranService.translate("report.label.toDate")}}</label>
                </span>
                </div>
            </div>
            <div class="col-1 align-items-center pb-0 pt-0">
                <p-button icon="pi pi-search"
                          styleClass="p-button-rounded p-button-secondary p-button-text button-search"
                          type="submit"></p-button>
            </div>
        </div>
    </p-panel>

</form>
<table-vnpt
    [fieldId]="'id'"
    [(selectItems)]="selectItems"
    [columns]="columns"
    [dataSet]="dataSet"
    [options]="optionTable"
    [loadData]="search.bind(this)"
    [pageNumber]="pageNumber"
    [pageSize]="pageSize"
    [params]="searchInfo"
    [labelTable]="this.tranService.translate('global.menu.dynamicreport')"
></table-vnpt>

<report-dynamic-form [mode]="modeForm" [idReport]="idReport" [control]="reportDynamicFormControl" [loadList]="onSubmitSearch.bind(this)"></report-dynamic-form>
