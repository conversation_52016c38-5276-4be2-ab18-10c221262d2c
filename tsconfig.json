/* To learn more about this file see: https://angular.io/config/tsconfig. */
{
    "compileOnSave": false,
    "compilerOptions": {
      "baseUrl": "./",
      "outDir": "./dist/out-tsc",
      "forceConsistentCasingInFileNames": true,
      "strict": false,
      "noImplicitOverride": true,
      "noPropertyAccessFromIndexSignature": false,
      "noImplicitReturns": true,
      "noFallthroughCasesInSwitch": true,
      "sourceMap": true,
      "declaration": false,
      "downlevelIteration": true,
      "experimentalDecorators": true,
      "moduleResolution": "node",
      "importHelpers": true,
      "target": "ES2022",
      "module": "ES2022",
      "useDefineForClassFields": false,
      "lib": [
        "ES2022",
        "dom"
      ]
    },
    "exclude": ["node_modules", "**/node_modules/*"],
    "angularCompilerOptions": {
      "enableI18nLegacyMessageIdFormat": false,
      "strictInjectionParameters": false,
      "strictInputAccessModifiers": false,
      "strictTemplates": false,
      "allowSyntheticDefaultImports": true,
    }
  }
