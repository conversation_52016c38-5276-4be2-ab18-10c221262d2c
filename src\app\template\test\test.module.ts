import { CommonModule } from "@angular/common";
import { NgModule } from "@angular/core";
import { FormsModule, ReactiveFormsModule } from "@angular/forms";
import { CommonVnptModule } from "../common-module/common.module";
import { TestComponent } from "./test.component";
import { TestRoutingModule } from "./test.routing.module";
import { DragDropModule } from "primeng/dragdrop";
import { DialogModule } from "primeng/dialog";

@NgModule({
    imports: [
        CommonModule,
        TestRoutingModule,
        FormsModule,
        ReactiveFormsModule,
        CommonVnptModule,
        DragDropModule,
        DialogModule
    ],
    declarations:[
        TestComponent
    ],
    providers:[]
})
export class TestModule{

}