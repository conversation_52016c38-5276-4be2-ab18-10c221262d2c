export default{
    label: {
        groupKey:"Subcriber Group Code",
        groupName:"Group Name",
        customer: "Customer",
        contractCode: "Contract Code",
        description: "Description",
        time: "Time",
        action:"Action",
        buttonAddSim:"Add Subcriber",
        buttonAdd:"Add",
        buttonSave:"Save",
        buttonEdit:"Edit",
        buttonDelete:"Delete Subcriber",
        buttonCancel:"Cancel",
        buttonPlan:"Plan",
        buttonPlanRegister:"Register Plan",
        buttonPlanChange:"Change Plan",
        buttonPlanCancel:"Cancel Plan",
        confirmDelete:"Confirm Delete",
        deleteTextGroup:"Are you sure you want to delete this Subcriber Group?",
        deleteTextSim:"Are you sure you want to delete Subcriber from this Subcriber Group?",
        groupScope: "Group Scope",
        addByFile:"Add By File",
        addPhoneByFile:"Add Phone By File",
    },
    placeHolder:{
        customer:"Select customer",
        contractCode:"Select Contract Code",
        groupKey:"Enter Group Code",
        groupName:"Enter Group Name",
        description:"Enter Description",
        addSim:"Select Subscription Number"
    },
    error:{
        requiredError:"This field is required",
        lengthError_255:"This field should have 255 characters or fewer ",
        lengthError_16:"This field should have 16 characters or fewer",
        existedError:"Existed Subcriber Group Code",
        characterError_name:"Wrong Format. Only Accept (a-z, A-Z, 0-9, . -_, space, Vietnamese)",
        characterError_code:"Wrong Format. Only Accept (a-z, A-Z, 0-9, -_)"
    },
    breadCrumb:{
        group:"Group Subcriber",
        detail:"Group Subcriber Detail",
        create:"Create Subcriber Group",
        update:"Update Subcriber Group"
    },
    detail:{
        subNumber:"Subscription Number",
        simNumber:"Subcriber Number",
        status:"Status",
        planName:"Plan Name",
    },
    scope: {
        admin: "Group includes nationwide customers",
        province: "Group includes customers from one province",
        customer: "Group includes one customer"
    }
}
