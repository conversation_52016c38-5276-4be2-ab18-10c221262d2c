<div *ngIf="formSendInfo">
    <form [formGroup]="formSendInfo" (ngSubmit)="onSubmit()">
        <p-card>
            <!-- email subject -->
            <div class="w-full field grid">
                <label htmlFor="emailSubject" class="col-fixed" style="width:180px; height: fit-content;">{{tranService.translate("report.label.emailSubject")}}<span class="text-red-500">*</span></label>
                <div class="col" style="max-width: 500px;">
                    <input class="w-full"  
                            pInputText id="emailSubject" 
                            [(ngModel)]="sendInfo.emailSubject" 
                            formControlName="emailSubject"
                            [required]="true"
                            [maxLength]="1000"
                            [placeholder]="tranService.translate('report.text.inputEmailSubject')"
                    />
                </div>
            </div>
            <!-- error email subject -->
            <div class="w-full field grid text-error-field">
                <label htmlFor="name" class="col-fixed" style="width:180px"></label>
                <div class="col">
                    <small class="text-red-500" *ngIf="formSendInfo.controls.emailSubject.dirty && formSendInfo.controls.emailSubject.errors?.required">{{tranService.translate("global.message.required")}}</small>
                    <small class="text-red-500" *ngIf="formSendInfo.controls.emailSubject.errors?.maxLength">{{tranService.translate("global.message.maxLength",{len:1000})}}</small>
                    <small class="text-red-500" *ngIf="formSendInfo.controls.emailSubject.errors?.pattern">{{tranService.translate("global.message.formatContainVN")}}</small>
                </div>
            </div>
            <!-- group email -->
            <div class="w-full field grid">
                <label htmlFor="roles" class="col-fixed" style="width:180px">{{tranService.translate("report.label.emailGroups")}}</label>
                <div class="col" style="max-width: 500px;">
                    <p-multiSelect styleClass="w-full" 
                        id="emailGroups"
                        [(ngModel)]="sendInfo.emailGroups" 
                        [options]="listEmailGroup"
                        [defaultLabel]="tranService.translate('report.text.selectEmailGroup')"
                        optionLabel="name"
                        display="chip"
                        formControlName="emailGroups"
                    ></p-multiSelect>
                </div>
            </div>
            <!-- error group email -->
            <div class="w-full field grid text-error-field">
                <label htmlFor="province" class="col-fixed" style="width:180px"></label>
                <div class="col">
                    <small class="text-red-500" *ngIf="formSendInfo.controls.emailGroups.dirty && formSendInfo.controls.emailGroups.errors?.required">{{tranService.translate("global.message.required")}}</small>
                </div>
            </div>
            <!-- email  -->
            <div class="w-full field grid">
                <label for="timeOnce" class="col-fixed" style="width:180px;height: fit-content;">{{tranService.translate("report.label.emailReceive")}}</label>
                <div class="col" style="max-width: calc(100% - 180px);">
                    <table-input-vnpt
                        [(value)]="sendInfo.listEmail"
                        [columns]="columnEmailInput"
                        [options]="optionEmailInput"
                        [control]="emailInputControl"
                        fieldId="id"
                    ></table-input-vnpt>
                </div>
            </div>
        </p-card>
        <p-card styleClass="border-1 border-solid surface-border">
            <!-- time  -->
            <div class="w-full field grid">
                <label for="time" class="col-fixed" style="width:180px">{{tranService.translate("report.label.hourSend")}}</label>
                <div class="col" style="max-width: 500px;">
                    <p-calendar styleClass="w-full"
                        id="time"
                        [(ngModel)]="sendInfo.time"
                        [showClear]="true"
                        [showIcon]="true"
                        hourFormat="hh:mm:ss"
                        [showTime]="true" [showSeconds]="true"
                        formControlName="time" appendTo="body" timeOnly="true"
                        icon="pi pi-clock"
                        [placeholder]="tranService.translate('report.text.selectHourSend')"
                    />
                </div>
            </div>
            <!-- error time -->
            <div class="w-full field grid text-error-field">
                <label htmlFor="name" class="col-fixed" style="width:180px"></label>
                <div class="col">
                    <small class="text-red-500" *ngIf="formSendInfo.controls.time.dirty && formSendInfo.controls.time.errors?.required">{{tranService.translate("global.message.required")}}</small>
                </div>
            </div>
            <div class="flex flex-row justify-content-between" style="height: fit-content;">
                <!-- select day in month -->
                <div class="w-3" [style]="{'min-width': '220px'}">
                    <p-card styleClass="border-1 border-solid surface-border h-full w-full">
                        <div class="flex flex-row justify-content-start w-full">
                            <p-checkbox 
                                name="allDayInMonth"
                                binary="true"
                                [(ngModel)]="sendInfo.allDayInMonth"
                                formControlName="allDayInMonth"
                                [trueValue]="1" (onChange)="toggleSelect(0)"
                                [falseValue]="0">
                            </p-checkbox>
                            <label class="ml-2">{{tranService.translate("report.label.dayInMonth")}}</label>
                        </div>
                        <p-divider type="solid"></p-divider>
                        <div class="flex flex-row justify-content-start flex-wrap">
                            <div class="w-4 mb-2" style="min-width: 60px;" *ngFor="let day of fullDayInMonth">
                                <p-checkbox 
                                    name="dayInMonth"
                                    [value]="day"
                                    [(ngModel)]="sendInfo.dayInMonth"
                                    formControlName="dayInMonth">
                                </p-checkbox>
                                <label class="ml-2">{{day}}</label>
                            </div>
                        </div>
                    </p-card>
                </div>
                <!-- select day in week  -->
                <div class="w-3" [style]="{'min-width': '220px'}">
                    <p-card styleClass="border-1 border-solid surface-border h-full w-full">
                        <div class="flex flex-row justify-content-start w-full">
                            <p-checkbox 
                                name="allDayInWeek"
                                binary="true"
                                [(ngModel)]="sendInfo.allDayInWeek"
                                formControlName="allDayInWeek"
                                [trueValue]="1" (onChange)="toggleSelect(1)"
                                [falseValue]="0">
                            </p-checkbox>
                            <label class="ml-2">{{tranService.translate("report.label.dayInWeek")}}</label>
                        </div>
                        <p-divider type="solid"></p-divider>
                        <div class="flex flex-row justify-content-start flex-wrap">
                            <div class="w-full mb-2" style="min-width: 180px;" *ngFor="let day of fullDayInWeek">
                                <p-checkbox 
                                    name="dayInWeek"
                                    [value]="day.value"
                                    [(ngModel)]="sendInfo.dayInWeek"
                                    formControlName="dayInWeek">
                                </p-checkbox>
                                <label class="ml-2">{{day.name}}</label>
                            </div>
                        </div>
                    </p-card>
                </div>
                <!-- select month  -->
                <div class="w-3" [style]="{'min-width': '220px'}">
                    <p-card styleClass="border-1 border-solid surface-border h-full w-full">
                        <div class="flex flex-row justify-content-start w-full">
                            <p-checkbox 
                                name="allMonth"
                                binary="true"
                                [(ngModel)]="sendInfo.allMonth"
                                formControlName="allMonth"
                                [trueValue]="1" (onChange)="toggleSelect(2)"
                                [falseValue]="0">
                            </p-checkbox>
                            <label class="ml-2">{{tranService.translate("report.label.monthInYear")}}</label>
                        </div>
                        <p-divider type="solid"></p-divider>
                        <div class="flex flex-row justify-content-start flex-wrap">
                            <div class="w-full mb-2" style="min-width: 180px;" *ngFor="let month of fullMonth">
                                <p-checkbox 
                                    name="month"
                                    [value]="month.value"
                                    [(ngModel)]="sendInfo.month"
                                    formControlName="month">
                                </p-checkbox>
                                <label class="ml-2">{{month.name}}</label>
                            </div>
                        </div>
                    </p-card>
                </div>
            </div>
        </p-card>

        <div class="flex flex-row justify-content-center align-items-center mt-3">
            <p-button styleClass="mr-2 p-button-secondary p-button-outlined" [label]="tranService.translate('global.button.cancel')" (click)="cancel()"></p-button>
            <p-button type="submit" *ngIf="modeView != objectMode.DETAIL" styleClass="p-button-info" [label]="tranService.translate('global.button.save')" 
                [disabled]="formSendInfo.invalid || (sendInfo?.listEmail?.length + sendInfo?.emailGroups?.length) == 0 || emailInputControl.isUpdating == true"
            ></p-button>
            <!-- [disabled]="formSendInfo.invalid || (sendInfo.listEmail.length + sendInfo.emailGroups.length) == 0 || emailInputControl.isUpdating == true" -->
        </div>
    </form>
</div>