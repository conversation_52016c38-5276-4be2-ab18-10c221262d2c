<div class="vnpt flex flex-row justify-content-between align-items-center bg-white p-2 border-round">
    <p-breadcrumb class="max-w-full col-7" [model]="items" [home]="home"></p-breadcrumb>
</div>
<p-card styleClass="mt-3">
    <p-tabView (onChange)="onTabChange($event)">
        <p-tabPanel header="{{tranService.translate('account.label.loginInfo')}}">
            <div class="flex flex-row justify-content-between">
                <div style="width: 49%;">
                    <!-- username -->
                    <div class="w-full field grid">
                        <label htmlFor="username" class="col-fixed" style="width:180px">{{tranService.translate(
                            "account.label.username")}}</label>
                        <div class="col">
                            {{accountResponse.username}}
                        </div>
                    </div>

                </div>
                <div style="width: 49%;">
<!--                    &lt;!&ndash; trạng thái &ndash;&gt;-->
<!--                    <div class="w-full field grid">-->
<!--                        <label for="userType" class="col-fixed" style="width:180px">{{tranService.translate("account.label.status")}}</label>-->
<!--                        <div class="col flex align-items-center gap-6">-->
<!--                            <label *ngIf="accountResponse.status===1" class="bg-green-100 border-round inline-block p-2 text-center text-green-800 ng-star-inserted">{{tranService.translate("account.userstatus.active")}}</label>-->
<!--                            <label *ngIf="accountResponse.status===0" class="bg-red-100 border-round inline-block p-2 text-center text-red-700 ng-star-inserted">{{tranService.translate("account.userstatus.lock")}}</label>-->
<!--                        </div>-->
<!--                    </div>-->
                    <!-- loai tai khoan -->
                    <div class="w-full field grid">
                        <label for="userType" class="col-fixed" style="width:180px">{{tranService.translate("account.label.userType")}}</label>
                        <div class="col">
                            <span>{{getStringUserType(accountResponse.type)}}</span>
                        </div>
                    </div>
                    <!-- nhom quyen -->
                    <div class="w-full field grid">
                        <label htmlFor="role" class="col-fixed" style="width:180px">{{tranService.translate(
                            "account.label.role")}}</label>
                        <div class="col" style="max-width: calc(100% - 180px) !important;">
                            <div *ngFor="let item of accountResponse.roles">
                                <div>{{ item.roleName}}</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </p-tabPanel>
    </p-tabView>
</p-card>

<p-card styleClass="mt-3">
    <div class="w-full">
        <p-tabView (onChange)="onTabChange($event)">
            <p-tabPanel *ngIf="accountInfo.userType == optionUserType.BUSINESS" header="{{tranService.translate('account.label.businessInfo')}}">
                <div class="flex flex-row justify-content-between" *ngIf="accountResponse.type === 2">
                    <div style="width: 49%;">
                        <!-- businessName -->
                        <div class="w-full field grid">
                            <label htmlFor="businessName" class="col-fixed" style="width:180px;height: fit-content;">{{tranService.translate(
                                "account.label.businessName")}}</label>
                            <div class="col" style="width: calc(100% - 180px);overflow-wrap: break-word;">
                                {{accountResponse.name}}
                            </div>
                        </div>
                        <!-- taxCode -->
                        <div class="w-full field grid">
                            <label htmlFor="taxCode" class="col-fixed" style="width:180px">{{tranService.translate("account.label.taxCode")}}</label>
                            <div class="col">
                                {{accountResponse.taxCode}}
                            </div>
                        </div>
                        <!-- provincesHeadOffice -->
                        <div class="w-full field grid">
                            <label htmlFor="provincesHeadOffice" class="col-fixed" style="width:180px;height: fit-content;">{{tranService.translate(
                                "account.label.provincesHeadOffice")}}</label>
                            <div class="col" style="width: calc(100% - 180px);overflow-wrap: break-word;">
                                {{accountResponse.provinceOfficeName}}
                            </div>
                        </div>
                        <!-- wardsHeadOffice -->
                        <div class="w-full field grid">
                            <label htmlFor="wardsHeadOffice" class="col-fixed" style="width:180px;height: fit-content;">{{tranService.translate(
                                "account.label.wardsHeadOffice")}}</label>
                            <div class="col" style="width: calc(100% - 180px);overflow-wrap: break-word;">
                                {{accountResponse.wardOfficeName}}
                            </div>
                        </div>
                        <!-- addressHeadOffice -->
                        <div class="w-full field grid">
                            <label htmlFor="addressHeadOffice" class="col-fixed" style="width:180px;height: fit-content;">{{tranService.translate(
                                "account.label.addressHeadOffice")}}</label>
                            <div class="col" style="width: calc(100% - 180px);overflow-wrap: break-word;">
                                {{accountResponse.addressHeadOffice}}
                            </div>
                        </div>
                        <!-- description -->
                        <div class="w-full field grid">
                            <label htmlFor="description" class="col-fixed" style="width:180px">{{tranService.translate(
                                "account.label.description")}}</label>
                            <div class="col">
                                {{accountResponse.description}}
                            </div>
                        </div>
                    </div>
                    <div style="width: 49%;">
                        <!-- representativeName-->
                        <div class="w-full field grid">
                            <label htmlFor="representativeName" class="col-fixed" style="width:180px">{{tranService.translate(
                                "account.label.representativeName")}}</label>
                            <div class="col">
                                <span>{{accountResponse.representativeName}}</span>
                            </div>
                        </div>
                        <!-- phone -->
                        <div class="w-full field grid align-items-start">
                            <label htmlFor="roles" class="col-fixed" style="width:180px">{{tranService.translate(
                                "account.label.phone")}}</label>
                            <div class="col" style="max-width: calc(100% - 180px) !important;">
                                <span>{{accountResponse.phone}}</span>
                            </div>
                        </div>
                        <!-- email -->
                        <div class="w-full field grid align-items-start">
                            <label htmlFor="roles" class="col-fixed" style="width:180px">{{tranService.translate(
                                "account.label.email")}}</label>
                            <div class="col" style="max-width: calc(100% - 180px) !important;">
                                <span>{{accountResponse.email}}</span>
                            </div>
                        </div>
                        <!-- provincesContact -->
                        <div class="w-full field grid align-items-start">
                            <label htmlFor="provincesContact" class="col-fixed" style="width:180px">{{tranService.translate(
                                "account.label.provincesContact")}}</label>
                            <div class="col" style="max-width: calc(100% - 180px) !important;">
                                <span>{{accountResponse.provinceAddressName}}</span>
                            </div>
                        </div>
                        <!-- wardsContact -->
                        <div class="w-full field grid align-items-start">
                            <label htmlFor="wardsContact" class="col-fixed" style="width:180px">{{tranService.translate(
                                "account.label.wardsContact")}}</label>
                            <div class="col" style="max-width: calc(100% - 180px) !important;">
                                <span>{{accountResponse.wardAddressName}}</span>
                            </div>
                        </div>
                        <!-- addressContact -->
                        <div class="w-full field grid align-items-start">
                            <label htmlFor="roles" class="col-fixed" style="width:180px">{{tranService.translate(
                                "account.label.addressContact")}}</label>
                            <div class="col" style="max-width: calc(100% - 180px) !important;">
                                <span>{{accountResponse.addressContact}}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </p-tabPanel>
            <p-tabPanel *ngIf="accountInfo.userType == optionUserType.INDIVIDUAL" header="{{tranService.translate('account.label.individualInfo')}}">
                <div class="flex flex-row justify-content-between" *ngIf="accountResponse?.type === 3">
                    <div style="width: 49%;">
                        <!-- customerName -->
                        <div class="w-full field grid">
                            <label htmlFor="customerName" class="col-fixed" style="width:180px;height: fit-content;">{{tranService.translate(
                                "account.label.customerName")}}</label>
                            <div class="col" style="width: calc(100% - 180px);overflow-wrap: break-word;">
                                {{accountResponse.name}}
                            </div>
                        </div>
                        <!-- phone -->
                        <div class="w-full field grid">
                            <label htmlFor="phone" class="col-fixed" style="width:180px">{{tranService.translate("account.label.phone")}}</label>
                            <div class="col">
                                {{accountResponse.phone}}
                            </div>
                        </div>
                        <!-- email -->
                        <div class="w-full field grid">
                            <label htmlFor="email" class="col-fixed" style="width:180px;height: fit-content;">{{tranService.translate(
                                "account.label.email")}}</label>
                            <div class="col" style="width: calc(100% - 180px);overflow-wrap: break-word;">
                                {{accountResponse.email}}
                            </div>
                        </div>
                        <!-- provincesContact -->
                        <div class="w-full field grid align-items-start">
                            <label htmlFor="provincesContact" class="col-fixed" style="width:180px">{{tranService.translate(
                                "account.label.provincesContact")}}</label>
                            <div class="col" style="max-width: calc(100% - 180px) !important;">
                                <span>{{accountResponse.provinceAddressName}}</span>
                            </div>
                        </div>
                        <!-- wardsContact -->
                        <div class="w-full field grid align-items-start">
                            <label htmlFor="wardsContact" class="col-fixed" style="width:180px">{{tranService.translate(
                                "account.label.wardsContact")}}</label>
                            <div class="col" style="max-width: calc(100% - 180px) !important;">
                                <span>{{accountResponse.wardAddressName}}</span>
                            </div>
                        </div>
                        <!-- addressContact -->
                        <div class="w-full field grid">
                            <label htmlFor="addressContact" class="col-fixed" style="width:180px">{{tranService.translate(
                                "account.label.addressContact")}}</label>
                            <div class="col">
                                {{accountResponse.addressContact}}
                            </div>
                        </div>
                    </div>
                    <div style="width: 49%;">
                        <!-- accountRootId-->
                        <div class="w-full field grid">
                            <label htmlFor="accountRootId" class="col-fixed" style="width:180px">{{tranService.translate(
                                "account.label.accountRootId")}}</label>
                            <div class="col">
                                <span>{{accountResponse.manager?.name}}</span>
                            </div>
                        </div>
                        <!-- description -->
                        <div class="w-full field grid align-items-start">
                            <label htmlFor="roles" class="col-fixed" style="width:180px">{{tranService.translate(
                                "account.label.description")}}</label>
                            <div class="col" style="max-width: calc(100% - 180px) !important;">
                                <span>{{accountResponse.description}}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </p-tabPanel>
            <p-tabPanel *ngIf="accountInfo.userType == optionUserType.ADMIN" header="{{tranService.translate('account.label.generalInfo')}}">
                <div class="flex flex-row justify-content-between" *ngIf="accountResponse?.type === 1">
                    <div style="width: 49%;">
                        <!-- customerName -->
                        <div class="w-full field grid">
                            <label htmlFor="customerName" class="col-fixed" style="width:180px;height: fit-content;">{{tranService.translate(
                                "account.label.customerName")}}</label>
                            <div class="col" style="width: calc(100% - 180px);overflow-wrap: break-word;">
                                {{accountResponse.name}}
                            </div>
                        </div>
                        <!-- phone -->
                        <div class="w-full field grid">
                            <label htmlFor="phone" class="col-fixed" style="width:180px">{{tranService.translate("account.label.phone")}}</label>
                            <div class="col">
                                {{accountResponse.phone}}
                            </div>
                        </div>
                        <!-- email -->
                        <div class="w-full field grid">
                            <label htmlFor="email" class="col-fixed" style="width:180px;height: fit-content;">{{tranService.translate(
                                "account.label.email")}}</label>
                            <div class="col" style="width: calc(100% - 180px);overflow-wrap: break-word;">
                                {{accountResponse.email}}
                            </div>
                        </div>
                        <!-- provincesContact -->
                        <div class="w-full field grid align-items-start">
                            <label htmlFor="provincesContact" class="col-fixed" style="width:180px">{{tranService.translate(
                                "account.label.provincesContact")}}</label>
                            <div class="col" style="max-width: calc(100% - 180px) !important;">
                                <span>{{accountResponse.provinceAddressName}}</span>
                            </div>
                        </div>
                        <!-- wardsContact -->
                        <div class="w-full field grid align-items-start">
                            <label htmlFor="wardsContact" class="col-fixed" style="width:180px">{{tranService.translate(
                                "account.label.wardsContact")}}</label>
                            <div class="col" style="max-width: calc(100% - 180px) !important;">
                                <span>{{accountResponse.wardAddressName}}</span>
                            </div>
                        </div>
                        <!-- addressContact -->
                        <div class="w-full field grid">
                            <label htmlFor="addressContact" class="col-fixed" style="width:180px">{{tranService.translate(
                                "account.label.addressContact")}}</label>
                            <div class="col">
                                {{accountResponse.addressContact}}
                            </div>
                        </div>
                    </div>
                    <div style="width: 49%;">
                        <!-- description -->
                        <div class="w-full field grid align-items-start">
                            <label htmlFor="roles" class="col-fixed" style="width:180px">{{tranService.translate(
                                "account.label.description")}}</label>
                            <div class="col" style="max-width: calc(100% - 180px) !important;">
                                <span>{{accountResponse.description}}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </p-tabPanel>
        </p-tabView>
    </div>
    <div class="flex justify-content-center">
        <p-button *ngIf="checkAuthen([CONSTANTS.PERMISSIONS.PROFILE.UPDATE])" [label]="tranService.translate('global.menu.editAccount')"  styleClass="p-button-info" class="mx-5 " (click)="goToEdit()"></p-button>
        <p-button [label]="tranService.translate('global.button.changePass')" styleClass="p-button-info bg-cyan-500 border-none" class="mx-5" (click)="goToChangePass()"></p-button>
<!--        <p-button [label]="tranService.translate('global.button.back')" class="px-5" styleClass="p-button-info"></p-button>-->
    </div>
</p-card>
