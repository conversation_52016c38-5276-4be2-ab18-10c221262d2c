import {Inject, Injectable} from "@angular/core";
import {HttpService} from "../comon/http.service";

@Injectable()
export class RechargeMoneyService {
    private prefixApi: string;

    constructor(@Inject(HttpService) private httpService: HttpService) {
        this.prefixApi = "/recharge-money";
    }

    public search(params: any, callback: Function, errorCallBack?:Function, finallyCallback?:Function){
        this.httpService.get(`${this.prefixApi}/search`, {}, params,callback, errorCallBack, finallyCallback);
    }

    public createRechargeMoney(body:{}, callback:Function, errorCallback?:Function, finallyCallback?: Function){
        this.httpService.post(this.prefixApi,{},body,{},callback, errorCallback, finallyCallback);
    }

    public updateRecharge<PERSON>oney(id:number, ratingPlan:{}, callback?:Function, errorCallBack?:Function, finallyCallback?:Function){
        this.httpService.put(`${this.prefixApi}/${id}`,{}, ratingPlan,{}, callback, errorCallBack, finallyCallback);
    }
    public downloadTemplate(){
        this.httpService.downloadLocal(`/assets/data/recharge.xlsx`, "recharge.xlsx");
    }
    public uploadByFile(objectFile, params, callback:Function){
        this.httpService.upload(`${this.prefixApi}/upload-file`, objectFile,{}, params, callback);
    }
}
