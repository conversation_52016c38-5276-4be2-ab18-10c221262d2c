<div *ngIf="formGeneralInfo">
    <form [formGroup]="formGeneralInfo">
        <!-- report name -->
        <div class="w-full field grid">
            <label htmlFor="name" class="col-fixed" style="width:180px">{{tranService.translate("report.label.reportName")}}<span class="text-red-500">*</span></label>
            <div class="col" style="max-width: 500px;">
                <input class="w-full"
                        pInputText id="name"
                        [(ngModel)]="generalInfo.name"
                        formControlName="name"
                        [required]="true"
                        [maxLength]="255"
                        pattern="^[^~`!@#\$%\^&*\(\)=\+\[\]\{\}\|\\,<>\/?]*$"
                        [placeholder]="tranService.translate('report.text.inputReportName')"
                        (ngModelChange)="checkExistReportName()"
                />
            </div>
        </div>
         <!-- error report name -->
         <div class="w-full field grid text-error-field">
            <label htmlFor="name" class="col-fixed" style="width:180px"></label>
            <div class="col">
                <small class="text-red-500" *ngIf="formGeneralInfo.controls.name.dirty && formGeneralInfo.controls.name.errors?.required">{{tranService.translate("global.message.required")}}</small>
                <small class="text-red-500" *ngIf="formGeneralInfo.controls.name.errors?.maxLength">{{tranService.translate("global.message.maxLength",{len:255})}}</small>
                <small class="text-red-500" *ngIf="formGeneralInfo.controls.name.errors?.pattern">{{tranService.translate("global.message.formatContainVN")}}</small>
                <small class="text-red-500" *ngIf="isReportNameExisted">{{tranService.translate("global.message.exists",{type: tranService.translate("report.label.reportName").toLowerCase()})}}</small>
            </div>
        </div>
        <!-- trang thai bao cao -->
        <div class="w-full field grid">
            <label for="userType" class="col-fixed" style="width:180px">{{tranService.translate("report.label.reportStatus")}}</label>
            <div class="col" style="max-width: 500px;">
                <p-dropdown styleClass="w-full"
                        id="status" [autoDisplayFirst]="false"
                        [(ngModel)]="generalInfo.status"
                        formControlName="status"
                        [options]="statusReports"
                        optionLabel="name"
                        optionValue="value"
                ></p-dropdown>
            </div>
        </div>
        <!-- xem truoc bao cao -->
        <div class="w-full field grid">
            <label for="userType" class="col-fixed" style="width:180px">{{tranService.translate("report.label.reportEnablePreview")}}</label>
            <div class="col" style="max-width: 500px;">
                <p-checkbox
                        name="enablePreview"
                        binary="true"
                        [(ngModel)]="generalInfo.enablePreview"
                        formControlName="enablePreview"
                        [trueValue]="reportPreview.ENABLE"
                        [falseValue]="reportPreview.DISABLE"></p-checkbox>
            </div>
        </div>
        <!-- description -->
        <div class="w-full field grid">
            <label htmlFor="description" class="col-fixed" style="width:180px; height: fit-content;">{{tranService.translate("report.label.description")}}</label>
            <div class="col" style="max-width: 500px;">
                <textarea  class="w-full" style="resize: none;"
                    rows="3"
                    [autoResize]="false"
                    pInputTextarea id="description"
                    [(ngModel)]="generalInfo.description"
                    formControlName="description"
                    [maxlength]="255"
                    [placeholder]="tranService.translate('sim.text.inputDescription')"
                ></textarea>
            </div>
        </div>
        <!-- error description -->
        <div class="w-full field grid text-error-field">
            <label htmlFor="description" class="col-fixed" style="width:180px"></label>
            <div class="col">
                <small class="text-red-500" *ngIf="formGeneralInfo.controls.description.errors?.maxLength">{{tranService.translate("global.message.maxLength",{len:255})}}</small>
            </div>
        </div>
    </form>

    <div>
        <div class="flex flex-row justify-content-between align-items-center pl-4">
            <div><b>{{tranService.translate("report.label.tableList")}}</b></div>
            <div *ngIf="modeView != objectMode.DETAIL" class="text-cyan-500 cursor-pointer" (click)="openCreateTable()"><u>{{tranService.translate("report.button.addTable")}}</u></div>
        </div>
        <table-vnpt
            [fieldId]="'id'"
            [columns]="tableColumns"
            [dataSet]="dataTables"
            [options]="optionTableListTable"
            scrollHeight="300px"
        ></table-vnpt>
    </div>

    <div class="mt-3">
        <div class="flex flex-row justify-content-between align-items-center pl-4">
            <div><b>{{tranService.translate("report.label.paramList")}}</b></div>
            <div *ngIf="modeView != objectMode.DETAIL" class="text-cyan-500 cursor-pointer" (click)="openCreateParameter()"><u>{{tranService.translate("report.button.addParam")}}</u></div>
        </div>
        <table-vnpt
            [fieldId]="'id'"
            [columns]="paramColumns"
            [dataSet]="dataParams"
            [options]="optionTableListParam"
            scrollHeight="300px"
            [isRowDraggable]="true"
        ></table-vnpt>
    </div>

    <div class="flex flex-row justify-content-center align-items-center mt-3">
        <p-button styleClass="mr-2 p-button-secondary p-button-outlined" [label]="tranService.translate('global.button.cancel')" (click)="cancel()"></p-button>
        <p-button *ngIf="modeView != objectMode.DETAIL" (click)="onSubmit()" styleClass="p-button-info" [label]="tranService.translate('global.button.save')" [disabled]="formGeneralInfo.invalid || isReportNameExisted"></p-button>
    </div>

    <!-- table -->
    <div class="flex justify-content-center dialog-push-group" style="top: 0">
        <p-dialog [header]="getHeaderTable()" [(visible)]="isShowDialogTable" [modal]="true" [style]="{ width: '700px', top: 0 }" [draggable]="false" [resizable]="false">
            <div class="w-full field grid p-0 m-0">
                <form [formGroup]="formTable" class="w-full">
                    <!-- table name -->
                    <div class="w-full field grid">
                        <label htmlFor="tableName" class="col-fixed" style="width:180px">{{tranService.translate("report.label.tableName")}}<span class="text-red-500">*</span></label>
                        <div class="col" style="max-width: 400px;">
                            <input class="w-full"
                                    pInputText id="tableName"
                                    [(ngModel)]="tableInfo.tableName"
                                    formControlName="tableName"
                                    [required]="true"
                                    [maxLength]="255"
                                    pattern="^[^~`!@#\$%\^&*\(\)=\+\[\]\{\}\|\\,<>\/?]*$"
                                    [placeholder]="tranService.translate('report.text.inputTableName')"
                                    (ngModelChange)="checkExistTableName()"
                            />
                        </div>
                    </div>
                    <!-- error report name -->
                    <div class="w-full field grid text-error-field">
                        <label htmlFor="name" class="col-fixed" style="width:180px"></label>
                        <div class="col">
                            <small class="text-red-500" *ngIf="formTable.controls.tableName.dirty && formTable.controls.tableName.errors?.required">{{tranService.translate("global.message.required")}}</small>
                            <small class="text-red-500" *ngIf="formTable.controls.tableName.errors?.maxLength">{{tranService.translate("global.message.maxLength",{len:255})}}</small>
                            <small class="text-red-500" *ngIf="formTable.controls.tableName.errors?.pattern">{{tranService.translate("global.message.formatContainVN")}}</small>
                            <small class="text-red-500" *ngIf="isTableNameExisted">{{tranService.translate("global.message.exists",{type: tranService.translate("report.label.tableName").toLowerCase()})}}</small>
                        </div>
                    </div>
                    <!-- schema -->
                    <div class="w-full field grid">
                        <label for="schema" class="col-fixed" style="width:180px">{{tranService.translate("report.label.schema")}}<span class="text-red-500">*</span></label>
                        <div class="col" style="max-width: 400px;">
                            <p-dropdown styleClass="w-full" showClear="true"
                                    id="schema" [autoDisplayFirst]="false"
                                    [(ngModel)]="tableInfo.schema"
                                    formControlName="schema"
                                    [options]="schemas"
                                    optionLabel="name"
                                    optionValue="value"
                                    [required]="true"
                                    [placeholder]="tranService.translate('report.text.selectSchema')"
                            ></p-dropdown>
                        </div>
                    </div>
                    <!-- error schema -->
                    <div class="w-full field grid text-error-field">
                        <label htmlFor="name" class="col-fixed" style="width:180px"></label>
                        <div class="col">
                            <small class="text-red-500" *ngIf="formTable.controls.schema.dirty && formTable.controls.schema.errors?.required">{{tranService.translate("global.message.required")}}</small>
                        </div>
                    </div>
                    <!-- query -->
                    <div class="w-full field grid">
                        <label htmlFor="query" class="col-fixed" style="width:180px; height: fit-content;">{{tranService.translate("report.label.query")}}<span class="text-red-500">*</span></label>
                        <div class="col" style="width: calc(100% - 210px)">
                            <textarea  class="w-full" style="resize: vertical;"
                                rows="3"
                                [autoResize]="false"
                                pInputTextarea id="query"
                                [(ngModel)]="tableInfo.query"
                                formControlName="query"
                                [placeholder]="tranService.translate('report.text.inputQuery')"
                                [required]="true"
                            ></textarea>
                        </div>
                        <span class="col-fixed pi pi-clone text-xl cursor-pointer" style="width:30px;height: fit-content" [pTooltip]="tranService.translate('global.button.copy')" (click)="copyText($event)"></span>
                    </div>
                    <!-- error schema -->
                    <div class="w-full field grid text-error-field">
                        <label htmlFor="name" class="col-fixed" style="width:180px"></label>
                        <div class="col">
                            <small class="text-red-500" *ngIf="formTable.controls.query.dirty && formTable.controls.query.errors?.required">{{tranService.translate("global.message.required")}}</small>
                        </div>
                    </div>
                </form>
                <div class="w-full">
                    <table-input-vnpt
                        [(value)]="tableInfo.columns"
                        [columns]="columnTableInput"
                        [options]="optionTableInput"
                        [control]="tableInputControl"
                        fieldId="id"
                        [showMove]="true"
                    ></table-input-vnpt>
                </div>
            </div>
            <div class="flex flex-row justify-content-center align-items-center mt-3">
                <p-button styleClass="mr-2 p-button-secondary p-button-outlined" [label]="tranService.translate('global.button.cancel')" (click)="isShowDialogTable = false"></p-button>
                <p-button (click)="saveTable()" *ngIf="modeTable != objectMode.DETAIL" styleClass="p-button-info" [label]="tranService.translate('global.button.save')" [disabled]="formTable.invalid || tableInfo.columns == null || tableInfo.columns == undefined || tableInfo.columns.length == 0 || tableInputControl.isUpdating == true || isTableNameExisted"></p-button>
            </div>
        </p-dialog>
    </div>

    <!-- parameter -->
    <div class="flex justify-content-center dialog-push-group">
        <p-dialog [header]="getHeaderParameter()" [(visible)]="isShowDialogParameter" [modal]="true" [style]="{ width: '700px',top: 0 }" [draggable]="false" [resizable]="false">
            <div class="w-full field grid p-0 m-0">
                <form [formGroup]="formParameter" class="w-full">
                    <!-- paramKey -->
                    <div class="w-full field grid">
                        <label htmlFor="prKey" class="col-fixed" style="width:180px">{{tranService.translate("report.label.paramKey")}}<span class="text-red-500">*</span></label>
                        <div class="col" style="max-width: 400px;">
                            <input class="w-full"
                                    pInputText id="prKey"
                                    [(ngModel)]="parameterInfo.prKey"
                                    formControlName="prKey"
                                    [required]="true"
                                    [maxLength]="255"
                                    pattern="^[a-zA-Z0-9_]*$"
                                    [placeholder]="tranService.translate('report.text.inputParamKey')"
                                    (ngModelChange)="checkExistParamKey()"
                            />
                        </div>
                    </div>
                    <!-- error prKey -->
                    <div class="w-full field grid text-error-field">
                        <label htmlFor="prKey" class="col-fixed" style="width:180px"></label>
                        <div class="col">
                            <small class="text-red-500" *ngIf="formParameter.controls.prKey.dirty && formParameter.controls.prKey.errors?.required">{{tranService.translate("global.message.required")}}</small>
                            <small class="text-red-500" *ngIf="formParameter.controls.prKey.errors?.maxLength">{{tranService.translate("global.message.maxLength",{len:255})}}</small>
                            <small class="text-red-500" *ngIf="formParameter.controls.prKey.errors?.pattern">{{tranService.translate("global.message.formatCodeNotSub")}}</small>
                            <small class="text-red-500" *ngIf="isParamKeyExisted">{{tranService.translate("global.message.exists",{type: tranService.translate("report.label.paramKey").toLowerCase()})}}</small>
                        </div>
                    </div>
                    <!-- prDisplayName -->
                    <div class="w-full field grid">
                        <label htmlFor="prDisplayName" class="col-fixed" style="width:180px;">{{tranService.translate("report.label.paramDisplay")}}<span class="text-red-500">*</span></label>
                        <div class="col" style="max-width: 400px;">
                            <input class="w-full"
                                    pInputText id="prDisplayName"
                                    [(ngModel)]="parameterInfo.prDisplayName"
                                    formControlName="prDisplayName"
                                    [required]="true"
                                    [maxLength]="255"
                                    pattern="^[^~`!@#\$%\^&*\(\)=\+\[\]\{\}\|\\,<>\/?]*$"
                                    [placeholder]="tranService.translate('report.text.inputDisplayName')"
                                    (ngModelChange)="checkExistParamDisplay()"
                            />
                        </div>
                    </div>
                    <!-- error prDisplayName -->
                    <div class="w-full field grid text-error-field">
                        <label htmlFor="name" class="col-fixed" style="width:180px"></label>
                        <div class="col">
                            <small class="text-red-500" *ngIf="formParameter.controls.prDisplayName.dirty && formParameter.controls.prDisplayName.errors?.required">{{tranService.translate("global.message.required")}}</small>
                            <small class="text-red-500" *ngIf="formParameter.controls.prDisplayName.errors?.maxLength">{{tranService.translate("global.message.maxLength",{len:255})}}</small>
                            <small class="text-red-500" *ngIf="formParameter.controls.prDisplayName.errors?.pattern">{{tranService.translate("global.message.formatContainVN")}}</small>
                            <small class="text-red-500" *ngIf="isParamDisplayExisted">{{tranService.translate("global.message.exists",{type: tranService.translate("report.label.paramDisplay").toLowerCase()})}}</small>
                        </div>
                    </div>
                    <!-- required -->
                    <div class="w-full field grid">
                        <label for="required" class="col-fixed" style="width:180px">{{tranService.translate("report.label.required")}}</label>
                        <div class="col" style="max-width: 400px;">
                            <p-checkbox
                                binary="true"
                                [trueValue]="true"
                                [falseValue]="false"
                                name="required"
                                [(ngModel)]="parameterInfo.required"
                                formControlName="required"
                            ></p-checkbox>
                        </div>
                    </div>
                    <!-- prType -->
                    <div class="w-full field grid">
                        <label for="prType" class="col-fixed" style="width:180px">{{tranService.translate("report.label.paramType")}}<span class="text-red-500">*</span></label>
                        <div class="col" style="max-width: 400px;">
                            <p-dropdown styleClass="w-full" showClear="true"
                                    id="prType" [autoDisplayFirst]="false"
                                    [(ngModel)]="parameterInfo.prType"
                                    formControlName="prType"
                                    [options]="parameterTypes"
                                    optionLabel="name"
                                    optionValue="value"
                                    [required]="true"
                                    [placeholder]="tranService.translate('report.text.selectParamType')"
                                    (ngModelChange)="changeParamType()"
                            ></p-dropdown>
                        </div>
                    </div>
                    <!-- error prType -->
                    <div class="w-full field grid text-error-field">
                        <label htmlFor="name" class="col-fixed" style="width:180px"></label>
                        <div class="col">
                            <small class="text-red-500" *ngIf="formParameter.controls.prType.dirty && formParameter.controls.prType.errors?.required">{{tranService.translate("global.message.required")}}</small>
                        </div>
                    </div>
                    <!-- date type -->
                    <div class="w-full field grid" [class]="parameterInfo.prType == objectType.DATE || parameterInfo.prType == objectType.TIMESTAMP ? '' : 'hidden'">
                        <label for="dateType" class="col-fixed" style="width:180px">{{tranService.translate("report.label.dateType")}}<span class="text-red-500">*</span></label>
                        <div class="col" style="max-width: 400px;">
                            <p-dropdown styleClass="w-full" showClear="true"
                                    id="dateType" [autoDisplayFirst]="false"
                                    [(ngModel)]="parameterInfo.dateType"
                                    formControlName="dateType"
                                    [options]="dateTypes"
                                    optionLabel="name"
                                    optionValue="value"
                                    [required]="parameterInfo.prType == objectType.DATE || parameterInfo.prType == objectType.TIMESTAMP"
                                    [placeholder]="tranService.translate('report.text.selectDateType')"
                            ></p-dropdown>
                        </div>
                    </div>
                    <!-- error datetype -->
                    <div class="w-full field grid text-error-field">
                        <label htmlFor="dateType" class="col-fixed" style="width:180px"></label>
                        <div class="col">
                            <small class="text-red-500" *ngIf="formParameter.controls.dateType.dirty && formParameter.controls.dateType.errors?.required">{{tranService.translate("global.message.required")}}</small>
                        </div>
                    </div>
                    <!-- is autocomplete, is multichoice -->
                    <div class="w-full flex flex-row justify-content-between align-items-center" [class]="parameterInfo.prType == objectType.LIST_NUMBER || parameterInfo.prType == objectType.STRING || parameterInfo.prType == objectType.LIST_STRING ? '' : 'hidden'">
                        <div class="w-6 field grid" [class]="parameterInfo.prType == objectType.STRING || parameterInfo.prType == objectType.LIST_STRING ? '' : ''">
                            <label for="isAutoComplete" class="col-fixed" style="width:180px">{{tranService.translate("report.label.isAutoComplete")}}</label>
                            <div class="col" style="max-width: 400px;">
                                <p-checkbox
                                    binary="true"
                                    [trueValue]="true"
                                    [falseValue]="false"
                                    name="isAutoComplete"
                                    [(ngModel)]="parameterInfo.isAutoComplete"
                                    formControlName="isAutoComplete" (ngModelChange)="changeIsAutoComplete()"
                                    ></p-checkbox>
                            </div>
                        </div>
                        <div class="w-6 field grid" [class]="parameterInfo.prType == objectType.LIST_NUMBER || parameterInfo.prType == objectType.LIST_STRING ? '' : 'hidden'">
                            <label for="isMultiChoice" class="col-fixed" style="width:180px">{{tranService.translate("report.label.isMultiChoice")}}</label>
                            <div class="col" style="max-width: 400px;">
                                <p-checkbox
                                    name="isMultiChoice"
                                    binary="true"
                                    [trueValue]="true"
                                    [falseValue]="false"
                                    [(ngModel)]="parameterInfo.isMultiChoice"
                                    formControlName="isMultiChoice"></p-checkbox>
                            </div>
                        </div>
                    </div>
                    <!-- objectkey -->
                    <div class="w-full field grid" [class]="parameterInfo.isAutoComplete == true ? '' : 'hidden'">
                        <label htmlFor="objectKey" class="col-fixed" style="width:180px">{{tranService.translate("report.label.objectKey")}}<span class="text-red-500">*</span></label>
                        <div class="col" style="max-width: 400px;">
                            <p-dropdown styleClass="w-full"
                                id="objectKey" [autoDisplayFirst]="false"
                                [(ngModel)]="parameterInfo.objectKey"
                                formControlName="objectKey"
                                [options]="listObjectKey"
                                optionLabel="display"
                                optionValue="value"
                                [required]="parameterInfo.isAutoComplete == true"
                                [placeholder]="tranService.translate('report.text.inputObjectKey')"
                        ></p-dropdown>
                        </div>
                    </div>
                    <!-- error object key -->
                    <div class="w-full field grid text-error-field">
                        <label htmlFor="prKey" class="col-fixed" style="width:180px"></label>
                        <div class="col">
                            <small class="text-red-500" *ngIf="formParameter.controls.objectKey.dirty && formParameter.controls.objectKey.errors?.required">{{tranService.translate("global.message.required")}}</small>
                            <small class="text-red-500" *ngIf="formParameter.controls.objectKey.errors?.maxLength">{{tranService.translate("global.message.maxLength",{len:16})}}</small>
                            <small class="text-red-500" *ngIf="formParameter.controls.objectKey.errors?.pattern">{{tranService.translate("global.message.formatCode")}}</small>
                        </div>
                    </div>
                    <!-- input -->
                    <div class="w-full field grid" [class]="parameterInfo.isAutoComplete == true ? '' : 'hidden'">
                        <label htmlFor="input" class="col-fixed" style="width:180px">{{tranService.translate("report.label.input")}}<span class="text-red-500">*</span></label>
                        <div class="col" style="max-width: 400px;">
                            <input class="w-full"
                                    pInputText id="input"
                                    [(ngModel)]="parameterInfo.input"
                                    formControlName="input"
                                    [required]="parameterInfo.isAutoComplete == true"
                                    [maxLength]="100"
                                    pattern="^[a-zA-Z0-9\-_]*$"
                                    [placeholder]="tranService.translate('report.text.inputInput')"
                            />
                        </div>
                    </div>
                    <!-- error input -->
                    <div class="w-full field grid text-error-field">
                        <label htmlFor="prKey" class="col-fixed" style="width:180px"></label>
                        <div class="col">
                            <small class="text-red-500" *ngIf="formParameter.controls.input.dirty && formParameter.controls.input.errors?.required">{{tranService.translate("global.message.required")}}</small>
                            <small class="text-red-500" *ngIf="formParameter.controls.input.errors?.maxLength">{{tranService.translate("global.message.maxLength",{len:16})}}</small>
                            <small class="text-red-500" *ngIf="formParameter.controls.input.errors?.pattern">{{tranService.translate("global.message.formatCode")}}</small>
                        </div>
                    </div>
                    <!-- output -->
                    <div class="w-full field grid" [class]="parameterInfo.isAutoComplete == true ? '' : 'hidden'">
                        <label htmlFor="output" class="col-fixed" style="width:180px">{{tranService.translate("report.label.output")}}<span class="text-red-500">*</span></label>
                        <div class="col" style="max-width: 400px;">
                            <input class="w-full"
                                    pInputText id="output"
                                    [(ngModel)]="parameterInfo.output"
                                    formControlName="output"
                                    [required]="parameterInfo.isAutoComplete == true"
                                    [maxLength]="16"
                                    pattern="^[a-zA-Z0-9\-_]*$"
                                    [placeholder]="tranService.translate('report.text.inputOutput')"
                            />
                        </div>
                    </div>
                    <!-- error output -->
                    <div class="w-full field grid text-error-field">
                        <label htmlFor="prKey" class="col-fixed" style="width:180px"></label>
                        <div class="col">
                            <small class="text-red-500" *ngIf="formParameter.controls.output.dirty && formParameter.controls.output.errors?.required">{{tranService.translate("global.message.required")}}</small>
                            <small class="text-red-500" *ngIf="formParameter.controls.output.errors?.maxLength">{{tranService.translate("global.message.maxLength",{len:16})}}</small>
                            <small class="text-red-500" *ngIf="formParameter.controls.output.errors?.pattern">{{tranService.translate("global.message.formatCode")}}</small>
                        </div>
                    </div>
                    <!-- displaypattern -->
                    <div class="w-full field grid" [class]="parameterInfo.isAutoComplete == true ? '' : 'hidden'">
                        <label htmlFor="displayPattern" class="col-fixed" style="width:180px">{{tranService.translate("report.label.displayPattern")}}<span class="text-red-500">*</span></label>
                        <div class="col" style="max-width: 400px;">
                            <input class="w-full"
                                    pInputText id="displayPattern"
                                    [(ngModel)]="parameterInfo.displayPattern"
                                    formControlName="displayPattern"
                                    [required]="parameterInfo.isAutoComplete == true"
                                    [maxLength]="255"
                                    placeholder="${var1} - ${var2}"
                            />
                            <!-- [placeholder]="tranService.translate('report.text.inputDisplayPattern')" -->
                        </div>
                    </div>
                    <!-- error displaypattern -->
                    <div class="w-full field grid text-error-field">
                        <label htmlFor="prKey" class="col-fixed" style="width:180px"></label>
                        <div class="col">
                            <small class="text-red-500" *ngIf="formParameter.controls.displayPattern.dirty && formParameter.controls.displayPattern.errors?.required">{{tranService.translate("global.message.required")}}</small>
                            <small class="text-red-500" *ngIf="formParameter.controls.displayPattern.errors?.maxLength">{{tranService.translate("global.message.maxLength",{len:255})}}</small>
                            <small class="text-red-500" *ngIf="formParameter.controls.displayPattern.errors?.pattern">{{tranService.translate("global.message.formatCode")}}</small>
                        </div>
                    </div>
                    <!-- queryParams -->
                    <div *ngIf="parameterInfo.isAutoComplete && (parameterInfo.prType == CONSTANTS.PARAMETER_TYPE.LIST_NUMBER || parameterInfo.prType == CONSTANTS.PARAMETER_TYPE.LIST_STRING)" class="w-full field grid">
                        <label htmlFor="queryParam" class="col-fixed" style="width:180px">
                            {{tranService.translate("report.label.queryParams")}}
                            <span class="text-red-500">*</span>
                            &nbsp;&nbsp;
                            <i class="pi pi-info-circle" [pTooltip]="tranService.translate('report.label.sampleQueryParam')"></i>
                        </label>
                        <div class="col" style="max-width: 400px;">
                            <input class="w-full"
                                   pInputText id="queryParam"
                                   [(ngModel)]="parameterInfo.queryParam"
                                   formControlName="queryParam"
                                   [maxLength]="255"
                                   pattern="^(\w+=(\$\w+|&quot;[^&quot;]*&quot;)|\w+=\d+)(?:&(\w+=(\$\w+|&quot;[^&quot;]*&quot;)|\w+=\d+))*$"
                                   [placeholder]="tranService.translate('report.text.inputQueryParam')"
                            />
                        </div>
                    </div>
                    <!-- error displaypattern -->
                    <div class="w-full field grid text-error-field">
                        <label htmlFor="prKey" class="col-fixed" style="width:180px"></label>
                        <div class="col">
                            <small class="text-red-500" *ngIf="formParameter.controls.queryParam.errors?.maxLength">{{tranService.translate("global.message.maxLength",{len:255})}}</small>
                            <small class="text-red-500" *ngIf="formParameter.controls.queryParam.errors?.pattern">{{tranService.translate("report.message.wrongQueryParamFormat")}}</small>
                        </div>
                    </div>
                </form>
                <div class="w-full">
                    <table-input-vnpt [class]="(parameterInfo.prType == objectType.LIST_NUMBER || parameterInfo.prType == objectType.LIST_STRING) && parameterInfo.isAutoComplete == false ? '': 'hidden'"
                        [(value)]="parameterInfo.valueList"
                        [columns]="columnParamInput"
                        [options]="optionParamInput"
                        [control]="paramInputControl"
                        fieldId="id"
                    ></table-input-vnpt>
                </div>
            </div>
            <div class="flex flex-row justify-content-center align-items-center mt-3">
                <p-button styleClass="mr-2 p-button-secondary p-button-outlined" [label]="tranService.translate('global.button.cancel')" (click)="isShowDialogParameter = false"></p-button>
                <p-button *ngIf="modeParameter != objectMode.DETAIL" styleClass="p-button-info" [label]="tranService.translate('global.button.save')" [disabled]="checkInvalidFormParameter()" (click)="saveParameter()"></p-button>
            </div>
        </p-dialog>
    </div>
</div>
