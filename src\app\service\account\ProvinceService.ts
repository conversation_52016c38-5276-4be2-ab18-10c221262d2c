import { Inject, Injectable } from "@angular/core";
import { HttpService } from "../comon/http.service";

@Injectable({ providedIn: 'root'})
export class ProvinceService{
    private prefixApi: string;
    constructor(@Inject(HttpService) private httpService:HttpService) {
        this.prefixApi = "/user-mgmt";
    }

    public getListProvince(callback?: Function, errorCallback?:Function, finallyCallback?: Function){
        this.httpService.get(`${this.prefixApi}/get-province`, {}, {}, callback, errorCallback, finallyCallback);
    }
}